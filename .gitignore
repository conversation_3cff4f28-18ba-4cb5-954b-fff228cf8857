# These are some examples of commonly ignored file patterns.
# You should customize this list as applicable to your project.
# Learn more about .gitignore:
#     https://www.atlassian.com/git/tutorials/saving-changes/gitignore

# Node artifact files
node_modules/
dist/

# Compiled Java class files
*.class

# Compiled Python bytecode
*.py[cod]

# Log files
*.log

*.iml

# Package files
*.jar

# Maven
target/
dist/

# JetBrains IDE
.idea/

# VSCode IDE
.vscode/

# Unit test reports
TEST*.xml

# Generated by MacOS
.DS_Store

# Generated by Windows
Thumbs.db

# Applications
*.app
*.exe
*.war

# Large media files
*.mp4
*.tiff
*.avi
*.flv
*.mov

test-migrations.sql
agents.csv
**application-dev.yml
**application-uat.yml
**application-prod.yml
**application-local.yml
**application-test.yml

.env

# dev profiles
application-apella.yml