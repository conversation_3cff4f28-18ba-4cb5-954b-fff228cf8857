pipeline {
    agent any
    environment {
        GCR_REPOSITORY="lct-on-prem"
        CLOUDSDK_CORE_PROJECT='lctafrica'  
        GCR_CREDENTIALS_KEY=credentials("gcr.credentials.key")
        GCR_CREDENTIALS_KEY_V2=credentials("gcr.credentials.key.v2")
        GCR_LOCATION='us-west1'
        KUBECONFIG=credentials('k8s.config.file')
        COSIGN_PASSWORD=credentials('cosign.password')
        COSIGN_PRIVATE_KEY=credentials('cosign.private.key')
        COSIGN_PUBLIC_KEY=credentials('cosign.public.key')
        DEV_CONTEXT=credentials('k8s.dev.context')
        UAT_CONTEXT=credentials('k8s.uat.context')
        PROD_CONTEXT=credentials('k8s.prod.context')
        MAVEN_SETTINGS=credentials('settings.xml')
        CACHE_DIR="${env.WORKSPACE}/.m2/repository"
    }

    stages {
        stage("Restore cache") {
            agent {
                docker {
                    image 'maven:3.9.6-eclipse-temurin-17'
                    args '-v $HOME/.m2:/.m2'
                    reuseNode true
                }
            }
            steps {
                sh 'mkdir -p $CACHE_DIR'
                cache(caches: [
                    arbitraryFileCache(
                        path: '$CACHE_DIR',
                        cacheValidityDecidingFile: 'pom.xml',
                        compressionMethod: 'TARGZ'
                    )
                ]) {
                    sh '''
                        mvn -s $MAVEN_SETTINGS dependency:go-offline 
                        mvn -s $MAVEN_SETTINGS verify -DskipTests
                    '''
                }
            }
        }

        stage("Test") {
            agent {
                docker {
                    image 'maven:3.9.6-eclipse-temurin-17'
                    args '-v $HOME/.m2:/.m2'
                    reuseNode true
                }
            }
            steps {
                sh '''
                    mvn -s $MAVEN_SETTINGS org.jacoco:jacoco-maven-plugin:prepare-agent test
                '''
            }
        }

        stage("Package") {
            steps {
                retry(3) {
                    sh '''
                        docker build --no-cache -t membership-apis --label "lctafrica.image.build.number=$env.BUILD_NUMBER" -f Dockerfile.bld  .
                    '''
                }
            }
        }

        stage("Push to GCR") {
            when {
                branch 'dev'
            }
            steps {
                sh '''
                    gcloud auth activate-service-account --key-file=$GCR_CREDENTIALS_KEY
                    docker tag membership-apis $GCR_LOCATION-docker.pkg.dev/$CLOUDSDK_CORE_PROJECT/$GCR_REPOSITORY/membership-apis
                    gcloud auth print-access-token | docker login -u oauth2accesstoken --password-stdin https://$GCR_LOCATION-docker.pkg.dev 
                    docker push $GCR_LOCATION-docker.pkg.dev/$CLOUDSDK_CORE_PROJECT/$GCR_REPOSITORY/membership-apis
                '''
                script {
                    env.DOCKER_IMAGE_DIGEST = sh(
                        script: "docker inspect --format='{{index .RepoDigests 0}}' ${env.GCR_LOCATION}-docker.pkg.dev/${env.CLOUDSDK_CORE_PROJECT}/${env.GCR_REPOSITORY}/membership-apis",
                        returnStdout: true
                    ).trim()
                }
            }
        }

        stage("Sign container image") {
            when {
                branch 'dev'
            }
            steps {
                sh '''
                    cosign version
                    cosign sign --key $COSIGN_PRIVATE_KEY $GCR_LOCATION-docker.pkg.dev/$CLOUDSDK_CORE_PROJECT/$GCR_REPOSITORY/membership-apis:latest
                '''
            }
        }

        stage("Deploy to DEV cluster") {
            when {
                branch 'dev'
            }
            steps {
                sh '''
                    sed -i "s|{LATEST_IMAGE_DIGEST}|$DOCKER_IMAGE_DIGEST|g" deployment-onprem-dev.yaml
                    kubectl --kubeconfig=$KUBECONFIG config use-context $DEV_CONTEXT
                    kubectl --kubeconfig=$KUBECONFIG apply -f deployment-onprem-dev.yaml
                 '''
            }
            post {
                success {
                    cleanWs()
                }
            }
        }

        stage("Deploy to UAT cluster") {
            when {
                branch 'uat'
            }
            steps {
                // SEE If there is need to create deployment for uat/prod or just use the same
                sh '''
                    sed -i "s|{LATEST_IMAGE_DIGEST}|$DOCKER_IMAGE_DIGEST|g" deployment-onprem-dev.yaml
                    kubectl --kubeconfig=$KUBECONFIG config use-context $UAT_CONTEXT
                    kubectl --kubeconfig=$KUBECONFIG apply -f deployment-onprem-dev.yaml
                 '''
            }
            post {
                success {
                    cleanWs()
                }
            }
        }

        stage("Deploy to PROD cluster") {
            when {
                branch 'prod'
            }
            steps {
                // SEE If there is need to create deployment for uat/prod or just use the same
                sh '''
                    sed -i "s|{LATEST_IMAGE_DIGEST}|$DOCKER_IMAGE_DIGEST|g" deployment-onprem-dev.yaml
                    kubectl --kubeconfig=$KUBECONFIG config use-context $PROD_CONTEXT
                    kubectl --kubeconfig=$KUBECONFIG apply -f deployment-onprem-dev.yaml
                 '''
            }
            post {
                success {
                    cleanWs()
                }
            }
        }
    }
}
