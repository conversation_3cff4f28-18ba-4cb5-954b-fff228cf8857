#################################################################################
####################### 1st Ingress Rule - rewrite ##############################
#################################################################################

apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-membership
  namespace: backend
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: 16m
    nginx.ingress.kubernetes.io/proxy-read-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "600"
#    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
#  tls:
#    - hosts:
#        - "api-uat.lctafrica.net"
#      secretName: "api-uat-letsencrypt-prod"
  rules:
    - host: "api-dev.lctafrica.net"
      http:
        paths:
          - path: /api/v1/membership/
            pathType: Prefix
            backend:
              service:
                name: membership-service
                port:
                  number: 8070
          - path: /api/v1/provider(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: membership-service
                port:
                  number: 8070
          - path: /api/v1/device/
            pathType: Prefix
            backend:
              service:
                name: membership-service
                port:
                  number: 8070
          - path: /api/v1/country/
            pathType: Prefix
            backend:
              service:
                name: membership-service
                port:
                  number: 8070
          - path: /api/v1/catalog/
            pathType: Prefix
            backend:
              service:
                name: membership-service
                port:
                  number: 8070
          - path: /api/v1/audit/
            pathType: Prefix
            backend:
              service:
                name: membership-service
                port:
                  number: 8070
          - path: /api/v1/card/
            pathType: Prefix
            backend:
              service:
                name: membership-service
                port:
                  number: 8070
          - path: /api/v1/restriction(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: membership-service
                port:
                  number: 8070
          - path: /api/v1/services(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: membership-service
                port:
                  number: 8070
          - path: /test(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: membership-service
                port:
                  number: 8070
  ingressClassName: nginx


---

#################################################################################
####################### 2nd Ingress Rule - rewrite ##############################
#################################################################################



apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-membership-with-rewrite
  namespace: backend
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    nginx.ingress.kubernetes.io/proxy-body-size: 16m
    nginx.ingress.kubernetes.io/proxy-read-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "600"
#    cert-manager.io/cluster-issuer: letsencrypt-prod

spec:
#  tls:
#    - hosts:
#        - "api-uat.lctafrica.net"
#      secretName: "api-uat-letsencrypt-prod"
  rules:
    - host: "api-dev.lctafrica.net"
      http:
        paths:
          - path: /membership(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: membership-service
                port:
                  number: 8070
  ingressClassName: nginx
