apiVersion: apps/v1
kind: Deployment
metadata:
  name: membership-deployment
  namespace: backend
  labels:
    app: membership-apis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: membership-apis
  template:
    metadata:
      labels:
        app: membership-apis
    spec:
      containers:
        - name: membership-apis
          image: registry.digitalocean.com/lct-registry/membership-apis:${CIRCLE_SHA1}
          ports:
            - containerPort: 8070
          env:
            - name: DB_HOST
              valueFrom:
                configMapKeyRef:
                  name: mysql-config
                  key: host

            - name: DB_PORT
              valueFrom:
                configMapKeyRef:
                  name: mysql-config
                  key: port

            - name: DB_NAME
              valueFrom:
                configMapKeyRef:
                  name: mysql-config
                  key: membershipDbName

            - name: DB_USERNAME
              valueFrom:
                secretKeyRef:
                  name: mysql-secrets
                  key: username

            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: mysql-secrets
                  key: password

            - name: KEY<PERSON><PERSON>K_CLIENT
              valueFrom:
                configMapKeyRef:
                  name: app-config
                  key: keycloakClient

            - name: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_SECRET
              valueFrom:
                configMapKeyRef:
                  name: app-config
                  key: keycloakSecret
---
apiVersion: v1
kind: Service
metadata:
  name: membership-service
  namespace: backend
spec:
  selector:
    app: membership-apis
  ports:
    - port: 8070
      targetPort: 8070
  type: ClusterIP

