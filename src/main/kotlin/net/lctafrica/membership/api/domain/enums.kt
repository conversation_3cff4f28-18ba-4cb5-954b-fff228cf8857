package net.lctafrica.membership.api.domain

enum class BenefitType(val type: String) {
    INSURED("Insured"),
    CAPITATION("Capitation"),
    FUNDED("Funded")
}

enum class CapitationType(val type: String) {
    FIXED("Fixed"),
    REIMBURSEMENT("Reimbursement")
}

enum class CapitationPeriod(val type: String) {
    MONTHLY("Monthly"),
    QUARTERLY("Quarterly"),
    SEMI_ANNUAL("Semi Annual"),
    ANNUAL("Annual"),
    BIENNIAL("Biennial"),
}

enum class VisitCountPeriod(val type: String) {
    WEEKLY("Weekly"),
    MONTHLY("Monthly"),
    QUARTERLY("Quarterly"),
    SEMI_ANNUAL("Semi Annual"),
    ANNUAL("Annual"),
    BIENNIAL("Biennial"),
}

enum class RestrictionType(val type: String) {
    INCLUSIVE("Inclusive"),
    EXCLUSIVE("Exclusive"),
    CUSTOM_RULE("Custom Rule"),
}

enum class BiometricStatus {
    ACTIVE, DELETED
}

enum class VerificationMode {
    BIOMETRIC, OTP
}

enum class ExportFileType {
    PDF, XLS, XLSX, CSV
}

enum class PolicyStatus {
    ACTIVE, EXPIRED
}

enum class AllocationStatus {
    UNALLOCATED, ALLOCATED, DEALLOCATED
}

enum class DeviceStatus {
    AVAILABLE, ALLOCATED, REPAIR, DECOMMISSIONED
}

enum class SimStatus {
    UNASSIGNED, ASSIGNED
}

enum class CheckerStatus {
    PENDING, APPROVED, REJECTED
}

enum class LogAction {
    ADDED_MODEL, ADDED_SIM, ADDED_DEVICE, UPDATED_DEVICE, UPDATED_SIM, UPDATED_ALLOCATION, CREATED_ALLOCATION, ALLOCATED_DEVICE, CREATED_AND_ALLOCATED_DEVICE,
    UPLOADED_SUPPORTING_DOCUMENTS
}

enum class ProviderType {
    MAIN, BRANCH
}

enum class ConfigGroup {
    PAYER_CONFIG, CATEGORY_CONFIG
}

enum class ContactPerson {
    CREDIT_CONTROLLER
}

enum class MaritalStatus {
    SINGLE, MARRIED
}

enum class OnboardPaymentMode {
    MONTHLY, BONUS, ANNUAL, PREMIUM_FINANCING
}

enum class OnboardPrincipalCategory {
    BIRTH_TO_75_YEARS, EXISTING_OVER_75_YEARS
}

enum class OnboardDocumentType {
    APPLICATION_FORM, HOSPITAL_SELECTION_FORM, PROOF_OF_SCHOOL_FORM
}

enum class OnboardBenefitType {
    OUTPATIENT, INPATIENT
}

enum class OnboardReportType {
    DEPENDANTS, PRINCIPALS, PRINCIPALS_AND_DEPENDANTS
}

enum class OnboardDependantBeneficiaryType {
    SPOUSE, CHILD
}