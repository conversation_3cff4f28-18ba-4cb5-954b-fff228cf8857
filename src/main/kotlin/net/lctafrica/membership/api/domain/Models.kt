package net.lctafrica.membership.api.domain

import com.fasterxml.jackson.annotation.JsonBackReference
import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonManagedReference
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.Period
import java.time.ZoneId
import java.time.ZonedDateTime
import javax.persistence.CascadeType
import javax.persistence.CollectionTable
import javax.persistence.Column
import javax.persistence.ElementCollection
import javax.persistence.Entity
import javax.persistence.EnumType
import javax.persistence.Enumerated
import javax.persistence.FetchType
import javax.persistence.GeneratedValue
import javax.persistence.GenerationType
import javax.persistence.Id
import javax.persistence.Index
import javax.persistence.JoinColumn
import javax.persistence.Lob
import javax.persistence.ManyToOne
import javax.persistence.OneToMany
import javax.persistence.Table
import javax.persistence.Transient
import javax.persistence.UniqueConstraint
import javax.validation.constraints.NotNull
import javax.validation.constraints.Size
import net.lctafrica.membership.api.dtos.DataCollectionBenefitType
import net.lctafrica.membership.api.dtos.DataCollectionPaymentMode
import net.lctafrica.membership.api.dtos.DataCollectionRelationship
import org.hibernate.annotations.CreationTimestamp
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedDate


enum class BenefitAccessMode {
    CARD, CARDLESS, HYBRID
}

@Entity
@Table(name = "payer")
class Payer(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "payer_id")
    var id: Long = 0,

    @Column(name = "payer_name", unique = true, nullable = false)
    var name: String,

    @Column(name = "contact")
    var contact: String,

    @Column(name = "email")
    var email: String? = null,

    @Column(name = "website")
    var website: String? = null,

    @Column(name = "street_address")
    var streetAddress: String? = null,

    @Column(name = "postal_address")
    var postalAddress: String? = null,

    @Column(name = "logo")
    var logo: String? = null,

    @Column(name = "type", nullable = false)
    @Enumerated(EnumType.STRING)
    var type: PayerType,

    @ManyToOne
    @JoinColumn(name = "main_payer_id")
    var mainPayer: Payer? = null,

    /*@JsonIgnore
    @OneToMany(mappedBy = "payer")
    var plans: List<Plan>,*/

    @JsonIgnore
    @OneToMany(mappedBy = "payer")
    var benefits: List<Benefit> = mutableListOf(),

    @JsonIgnore
    @OneToMany(mappedBy = "payer")
    var benefitMapping: Set<PayerBenefitMapping> = mutableSetOf(),

    @JsonIgnore
    @OneToMany(mappedBy = "payer")
    var providerMapping: Set<PayerProviderMapping> = mutableSetOf(),

    @JsonIgnore
    @OneToMany(mappedBy = "payer")
    var restrictions: Set<BenefitRestriction> = mutableSetOf(),

    @JsonIgnore
    @OneToMany(mappedBy = "payer")
    var configs: Set<PayerConfig> = mutableSetOf(),

    @JsonIgnore
    @OneToMany(mappedBy = "payer")
    var providerBankAccountMapping: Set<PayerProviderBankAccountMapping> = mutableSetOf(),

    @JsonIgnore
    @OneToMany(mappedBy = "payer")
    var payerProviderUserMapping: Set<PayerProviderUserMapping> = mutableSetOf(),

    @JsonIgnore
    @OneToMany(mappedBy = "payer")
    var policies: List<PayerPolicyMapping> = mutableListOf(),

    @JsonIgnore
    @OneToMany(mappedBy = "payer")
    var benefitPayerAdminMapping: List<BenefitPayerAdmin> = mutableListOf(),

    )


@Entity
@Table(name = "config_catalog", uniqueConstraints = [UniqueConstraint(columnNames = ["cnf_group", "name"])])
class ConfigCatalog(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    var id: Long = 0,

    @Column(name = "cnf_group", nullable = false)
    @Enumerated(EnumType.STRING)
    var cnfGroup: ConfigGroup,

    @Column(name = "name", nullable = false)
    var name: String,

    @Column(name = "value_type")
    var valueType: String? = null,

    var description: String? = null,

    @CreationTimestamp
    @Column(name = "createdAt", nullable = false, updatable = false)
    var createdAt: LocalDateTime = LocalDateTime.now(),

    @JsonIgnore
    @OneToMany(mappedBy = "config")
    var payerConfigs: Set<PayerConfig> = mutableSetOf(),

    @JsonIgnore
    @OneToMany(mappedBy = "config")
    var categoryConfigs: Set<CategoryConfig> = mutableSetOf(),

    @JsonIgnore
    @OneToMany(mappedBy = "config")
    var benefitRules: Set<BenefitRule> = mutableSetOf(),
)


@Entity
@Table(name = "payer_config", uniqueConstraints = [UniqueConstraint(columnNames = ["payer_id", "config_id"])])
class PayerConfig(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    var id: Long = 0,

    @JsonIgnore
    @ManyToOne
    @JoinColumn(name = "payer_id", nullable = false)
    var payer: Payer,

    @ManyToOne
    @JoinColumn(name = "config_id", nullable = false)
    var config: ConfigCatalog,

    @Column(name = "value", nullable = false)
    var value: String,

    var description: String? = null,

    @CreationTimestamp
    @Column(name = "createdAt", nullable = false, updatable = false)
    var createdAt: LocalDateTime = LocalDateTime.now(),
)


@Entity
@Table(name = "benefit_rules", uniqueConstraints = [UniqueConstraint(columnNames = ["restriction_id", "config_id"])])
class BenefitRule(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    var id: Long = 0,

    @JsonIgnore
    @ManyToOne
    @JoinColumn(name = "restriction_id")
    var restriction: BenefitRestriction?,

    @ManyToOne
    @JoinColumn(name = "config_id")
    var config: ConfigCatalog,

    @Column(name = "value", nullable = false)
    var value: String,

    var description: String? = null,

    @CreationTimestamp
    @Column(name = "createdAt", nullable = false, updatable = false)
    var createdAt: LocalDateTime = LocalDateTime.now(),
)


enum class PayerType(val description: String) {
    UNDERWRITER("Insurance service provider"),
    CORPORATE("Self-managing entity"),
    INTERMEDIARY("Broker, Bancassurance or Agency")
}

@Entity
@Table(name = "plan")//, uniqueConstraints = [UniqueConstraint(columnNames = ["plan_name", "payer_id"])])
data class Plan(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "plan_id")
    var id: Long = 0,

    @Column(name = "plan_name", nullable = false)
    var name: String,

    @Column(name = "type", nullable = false)
    @Enumerated(EnumType.STRING)
    var type: PlanType,

    @Column(name = "plan_use_date")
    @Enumerated(EnumType.STRING)
    var planDate: PlanDate? = null,

    @JsonIgnore
    @OneToMany(mappedBy = "plan")
    var policies: List<Policy>,

    @Column(name = "benefit_access_mode", nullable = false)
    @Enumerated(EnumType.STRING)
    var accessMode: BenefitAccessMode
)

enum class PlanType {
    SCHEME, RETAIL
}

enum class PlanDate {
    POLICY_DATE, MEMBER_JOIN_DATE
}

@Entity
@Table(name = "policy")
data class Policy(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "policy_id")
    var id: Long = 0,

    @ManyToOne
    @JoinColumn(name = "plan_id", nullable = false)
    var plan: Plan,

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    @Column(name = "start_date", nullable = false, updatable = false)
    var startDate: LocalDate,

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    @Column(name = "end_date", nullable = false, updatable = false)
    var endDate: LocalDate,

    @JsonIgnore
    @OneToMany(mappedBy = "policy")
    var categories: List<Category> = mutableListOf(),

    @Column(name = "policy_number", unique = true, nullable = false)
    var policyNumber: String,

    @ManyToOne
    @JoinColumn(name = "policy_admin", nullable = true)
    var policyAdmin: Payer? = null,

    @JsonIgnore
    @OneToMany(mappedBy = "policy")
    var cardBatches: List<CardBatch> = mutableListOf(),

    @JsonIgnore
    @OneToMany(mappedBy = "policy")
    var payers: List<PayerPolicyMapping> = mutableListOf(),

    @Transient
    var status: PolicyStatus? = null
) {
    override fun toString(): String {
        return "Policy(id=$id, startDate=$startDate, endDate=$endDate)"
    }

    init {
        require(endDate.isAfter(startDate)) { "Policy start date must come before end date" }
    }
}


@Entity
@Table(
    name = "payer_policy_mapping",
    uniqueConstraints = [
        UniqueConstraint(columnNames = ["payer_id", "policy_id"], name = "payer_policy_mapping_UNQ")
    ]
)
data class PayerPolicyMapping(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "mapping_id")
    var id: Long = 0,

    @ManyToOne
    @JoinColumn(name = "payer_id")
    var payer: Payer,

    @ManyToOne
    @JoinColumn(name = "policy_id")
    var policy: Policy,

    var mappingEnabled: Boolean = true,

    @CreationTimestamp
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd hh:mm:ss")
    @Column(name = "created_at", nullable = false, updatable = false)
    var createdAt: LocalDateTime = LocalDateTime.now(),
)


@Entity
@Table(name = "category", uniqueConstraints = [UniqueConstraint(columnNames = ["policy_id", "category_name"])])
data class Category(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "category_id")
    var id: Long = 0,

    @Column(name = "category_name", nullable = false)
    val name: String,

    @Column(name = "description")
    var description: String?,

    @Column(name = "agakhan_insurance_code")
    var agakhanInsuranceCode: String? = "0",

    @Column(name = "agakhan_scheme_code")
    var agakhanSchemeCode: String? = "0",

    @Column(name = "jic_scheme_code")
    var jicSchemeCode: Int?,

    @Column(name = "apa_scheme_code")
    var apaSchemeCode: Int?,

    @Column(name = "policy_payer_code")
    var policyPayerCode: Long?,

    @ManyToOne
    @JoinColumn(name = "policy_id", nullable = false)
    var policy: Policy,

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    var status: CategoryStatus = CategoryStatus.UNPROCESSED,

    @JsonIgnore
    @OneToMany(mappedBy = "category")
    var benefits: List<Benefit> = mutableListOf(),

    @JsonIgnore
    @OneToMany(mappedBy = "category")
    var configs: List<CategoryConfig> = mutableListOf(),


    @JsonIgnore
    @OneToMany(mappedBy = "category")
    var beneficiaries: List<Beneficiary> = mutableListOf(),

    @Column(columnDefinition = "tinyint(1) default 1")
    var allowOtpVerificationFailOver: Boolean = true,

    @Column(name = "restriction_type")
    @Enumerated(EnumType.STRING)
    var restrictionType: RestrictionType? = null,

    @OneToMany(mappedBy = "category", fetch = FetchType.LAZY)
    var changeLog: List<AuditLog> = emptyList(),
) {
    override fun toString(): String {
        return "Category(id=$id, name=$name, description=$description)"
    }

    init {
        fun checkNameNotBlank() = name.trim().isNotEmpty()
        require(checkNameNotBlank()) {
            "Category name cannot be blank"
        }
    }
}


@Entity
@Table(name = "category_config", uniqueConstraints = [UniqueConstraint(columnNames = ["category_id", "config_id"])])
class CategoryConfig(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    var id: Long = 0,

    @JsonIgnore
    @ManyToOne
    @JoinColumn(name = "category_id", nullable = false)
    var category: Category,

    @ManyToOne
    @JoinColumn(name = "config_id", nullable = false)
    var config: ConfigCatalog,

    @Column(name = "value", nullable = false)
    var value: String,

    var description: String? = null,

    @CreationTimestamp
    @Column(name = "createdAt", nullable = false, updatable = false)
    var createdAt: LocalDateTime = LocalDateTime.now(),
)

enum class CategoryStatus {
    PROCESSED, UNPROCESSED, SUSPENDED
}

@Entity
@Table(
    name = "benefit", uniqueConstraints = [
        UniqueConstraint(name = "UNQ_NAME_PER_CATEGORY", columnNames = ["benefit_name", "category_id"]),
        UniqueConstraint(name = "UNQ_REF_PER_CATEGORY", columnNames = ["benefit_ref", "category_id"])]
)
class Benefit(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "benefit_id")
    var id: Long = 0,

    @Column(name = "benefit_name", nullable = false)
    var name: String,

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "benefit_ref")
    var benefitRef: BenefitCatalog,

    @Column(name = "applicable_gender", nullable = false)
    @Enumerated(EnumType.STRING)
    var applicableGender: ApplicableGender,

    @Column(name = "applicable_member", nullable = false)
    @Enumerated(EnumType.STRING)
    var applicableMember: ApplicableMember,

    @JsonIgnore
    @ManyToOne
    @JoinColumn(name = "category_id", nullable = false)
    var category: Category,

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    var status: BenefitStatus = BenefitStatus.ACTIVE,

    @Column(name = "benefit_limit", nullable = false)
    var limit: BigDecimal,

    @Column(name = "suspension_threshold")
    var suspensionThreshold: BigDecimal,

    @Column(name = "pre_auth_threshold")
    var preAuthThreshold: BigDecimal? = BigDecimal.ZERO,

    @Column(name = "threshold_action")
    @Enumerated(EnumType.STRING)
    var thresholdAction: ThresholdAction? = ThresholdAction.NONE,

    @Column(name = "pre_auth_type", nullable = true)
    @Enumerated(EnumType.STRING)
    var preAuthType: PreAuthType,

    @Column(name = "sharing", nullable = false)
    @Enumerated(EnumType.STRING)
    var sharing: BenefitDistribution,

    @Column(name = "co_payment_required")
    var coPaymentRequired: Boolean,

    @Column(name = "co_payment_amount")
    var coPaymentAmount: BigDecimal,

    @JsonIgnore
    @OneToMany(mappedBy = "parentBenefit", fetch = FetchType.EAGER)
    var subBenefits: List<Benefit> = mutableListOf(),

    @ManyToOne
    @JoinColumn(name = "parent_benefit_id", nullable = true)
    var parentBenefit: Benefit?,

    @Column(name = "waiting_period")
    @Enumerated(EnumType.STRING)
    var waitingPeriod: WaitingPeriod,

    @Column(name = "processed")
    var processed: Boolean,

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd hh:mm:ss")
    @Column(name = "processed_time")
    var processedTime: LocalDateTime?,

    @ManyToOne
    @JoinColumn(name = "payer_id")
    var payer: Payer,

    @Column(name = "benefit_type", nullable = true)
    @Enumerated(EnumType.STRING)
    var benefitType: BenefitType? = null,

    @Column(name = "capitation_type", nullable = true)
    @Enumerated(EnumType.STRING)
    var capitationType: CapitationType? = null,

    @Column(name = "capitation_period", nullable = true)
    @Enumerated(EnumType.STRING)
    var capitationPeriod: CapitationPeriod? = null,

    @Column(name = "visit_count_period", nullable = true)
    @Enumerated(EnumType.STRING)
    var visitCountPeriod: VisitCountPeriod? = null,

    var capitationMaxVisitCount: Int = 0,

    var capitationFacilitiesCount: Int = 0,

    var requireBeneficiaryToSelectProvider: Boolean? = false,

    var visitLimit: BigDecimal? = null,

    var daysOfAdmissionLimit: Int? = null,

    var amountPerDayLimit: BigDecimal = BigDecimal(0.00),

    var applicableMinAge: BigDecimal? = null,
    var applicableMaxAge: BigDecimal? = null,

    var transferable: Boolean? = false,

    var billable: Boolean? = null,

    var aiAdjudicationEnabled: Boolean? = null,

    @ManyToOne
    @JoinColumn(name = "restriction_id")
    var restriction: BenefitRestriction? = null,

    @JsonIgnore
    @OneToMany(mappedBy = "benefit")
    var mappedRestrictions: Set<BenefitRestrictionMapping> = mutableSetOf(),

    @JsonIgnore
    @OneToMany(mappedBy = "benefit")
    var benefitProviderMapping: Set<BenefitProviderMapping> = mutableSetOf(),

    @JsonIgnore
    @OneToMany(mappedBy = "benefit")
    var beneficiaryProviderMapping: Set<BeneficiaryProviderMapping> = mutableSetOf(),

    @JsonIgnore
    @OneToMany(mappedBy = "benefit")
    var benefitPayerAdminMapping: List<BenefitPayerAdmin> = mutableListOf(),

    @OneToMany(mappedBy = "benefit", fetch = FetchType.LAZY)
    var changeLog: List<AuditLog> = emptyList(),

    ) {
    init {
        fun checkCoPay() = ((coPaymentRequired && coPaymentAmount > BigDecimal.ZERO) or
                (!coPaymentRequired && coPaymentAmount <= BigDecimal.ZERO))

        //fun checkPreAuth() = (!requiresPreAuth.or(requiresPreAuth && (preAuthType is PreAuthType)))
        fun checkPreAuth(): Boolean {
            val pb = this.parentBenefit
            if (pb != null) {
                val pbpre = pb.preAuthType
                return (pbpre == this.preAuthType).or(pbpre == PreAuthType.NONE)
            }
            return true
        }

        fun memberAlignment(): Boolean {
            val pb = this.parentBenefit
            if (pb != null) {
                val pbam = pb.applicableMember
                if (pbam == ApplicableMember.ALL || pbam == this.applicableMember) {
                    return true
                } else {
                    if (pbam == ApplicableMember.PRINCIPAL_AND_SPOUSE &&
                        (this.applicableMember == ApplicableMember.SPOUSE || this.applicableMember == ApplicableMember.PRINCIPAL)
                    ) return true
                }
                return false
            }
            return true
        }

        fun genderAlignment(): Boolean {
            val pb = this.parentBenefit
            if (pb != null) {
                val pbag = pb.applicableGender
                if (pbag == ApplicableGender.ALL || pbag == this.applicableGender) return true
                return false
            }
            return true
        }

        fun distributionAlignment(): Boolean {
            val pb = this.parentBenefit
            if (pb != null) {
                val pbd = pb.sharing
                if (pbd == BenefitDistribution.FAMILY || (pbd == this.sharing)) return true
                return false
            }
            return true
        }

        fun limitAlignment(): Boolean {
            val pb = this.parentBenefit
            if ((pb != null) && (pb.limit < this.limit)) return false
            return true
        }

        fun suspensionLowerThanBalance() = (limit >= suspensionThreshold)

        fun preAuthThresholdLowerThanBalance() = (limit >= (preAuthThreshold ?: BigDecimal.ZERO))

        fun checkPayer(): Boolean {
            val pb = this.parentBenefit
            if (pb != null && (pb.payer.id != this.payer.id)) return false
            return true
        }

        require(checkCoPay()) { "Co-payment is required, therefore co-payment amount must be greater than Zero" }
        require(checkPreAuth()) { "Pre-authorization type for main and sub-benefit are in conflict" }
        require(memberAlignment()) { "Applicable member for main and sub-benefit are in conflict" }
        require(genderAlignment()) { "Applicable gender for main and sub-benefit are in conflict" }
        require(distributionAlignment()) { "Benefit sharing for main and sub-benefit are in conflict" }
        require(limitAlignment()) { "Main benefit limit should be greater than sub-benefit limit" }
        require(suspensionLowerThanBalance()) { "Suspension threshold should not be greater than benefit limit" }
        require(preAuthThresholdLowerThanBalance()) { "Pre-Auth threshold should not be greater than benefit limit" }
        require(checkPayer()) { "Main benefit payer should be the same as sub-benefit payer" }
    }
}

enum class BenefitStatus {
    ACTIVE, SUSPENDED
}

enum class ThresholdAction {
    NONE, REIMBURSEMENT, PRE_AUTH
}

enum class WaitingPeriod(val period: Period) {
    ZERO_DAYS(Period.ofDays(0)),
    THIRTY_DAYS(Period.ofDays(30)),
    SIXTY_DAYS(Period.ofDays(60)),
    NINETY_DAYS(Period.ofDays(90)),
    ONE_HUNDRED_EIGHTY_DAYS(Period.ofDays(180)),
    NINE_MONTHS(Period.ofMonths(9))
}

enum class BenefitDistribution {
    FAMILY, INDIVIDUAL
}

enum class PreAuthType(val description: String) {
    HR("HR representative must give the pre-authorization"),
    PAYER("The Underwriter must give the pre-authorization"),
    NONE("No Pre-authorization needed")
}

enum class ApplicableGender {
    MALE, FEMALE, ALL
}

enum class Gender {
    MALE, FEMALE
}

enum class BeneficiaryType {
    PRINCIPAL,
    SPOUSE,
    CHILD,
    PARENT
}

enum class MemberStatus {
    ACTIVE,
    DEACTIVATED,
    SUSPENDED
}

enum class StagingStatus {
    STAGED,
    ACTIVATE
}

enum class MemberPrivilege {
    NORMAL,
    VIP,
    VVIP
}

enum class ApplicableMember(val description: String) {
    PRINCIPAL("Applies to principal only"),
    SPOUSE("Applies to spouse only"),
    CHILD("Applies to child only"),
    PRINCIPAL_AND_SPOUSE("Applies to principal and spouse only"),
    PARENT("Applies to parent only"),
    ALL("Applies to all members")
}

@Entity
@Table(
    name = "benefit_payer_admin", uniqueConstraints = [
        UniqueConstraint(name = "UNQ_payer_benefit_id", columnNames = ["payer_id", "benefit_id"])
    ]
)
class BenefitPayerAdmin(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long = 0,

    @ManyToOne
    @JoinColumn(name = "payer_id")
    var payer: Payer,

    @ManyToOne
    @JoinColumn(name = "benefit_id")
    var benefit: Benefit,

    @Column(name = "mapping_enabled", columnDefinition = "bit(1) default 1")
    var mappingEnabled: Boolean = true,

    @CreationTimestamp
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd hh:mm:ss")
    @Column(name = "created_at", nullable = false, updatable = false)
    var createdAt: LocalDateTime = LocalDateTime.now(),
)

@Entity
@Table(
    name = "beneficiary",
    indexes = [Index(columnList = "phone_number")],
    uniqueConstraints = [
        UniqueConstraint(name = "member_number_UNQ", columnNames = ["member_number", "category_id"])
    ]
)
data class Beneficiary(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "beneficiary_id")
    var id: Long = 0,

    @Column(name = "beneficiary_name", nullable = false)
    var name: String,

    @Column(name = "member_number", nullable = false)
    var memberNumber: String,

    @Column(name = "jic_entity_id", nullable = true)
    var jicEntityId: Int?,

    @Column(name = "apa_entity_id", nullable = true)
    var apaEntityId: Int?,

    @Column(name = "nhif_number", nullable = true)
    var nhifNumber: String?,

    @Column(name = "id_number", nullable = true)
    var idNumber: String? = null,

    var otherNumber: String? = null,

    @Column(name = "dob", columnDefinition = "DATE", nullable = false)
    var dob: LocalDate,

    @Column(name = "gender", nullable = false)
    @Enumerated(EnumType.STRING)
    var gender: Gender,

    @Column(name = "phone_number")
    var phoneNumber: String?,

    @Column(name = "email")
    var email: String?,

    @Column(name = "beneficiary_type", nullable = false)
    @Enumerated(EnumType.STRING)
    var beneficiaryType: BeneficiaryType,

    @ManyToOne
    @JoinColumn(name = "category_id", nullable = false)
    var category: Category,

    var jobGroup: String? = null,

    @JsonIgnore
    @OneToMany(mappedBy = "principal")
    var dependants: List<Beneficiary> = mutableListOf(),

    @ManyToOne
    @JoinColumn(name = "principal_id", nullable = true)
    var principal: Beneficiary?,

    @JoinColumn(name = "can_use_biometrics", nullable = true)
    var canUseBiometrics: Boolean? = null,

    @JoinColumn(name = "exempted_from_cover_age_limit", nullable = true)
    var exemptedFromCoverAgeLimit: Boolean? = null,

    @Column(name = "processed")
    var processed: Boolean,

    @OneToMany(mappedBy = "beneficiary", fetch = FetchType.EAGER)
    var changeLog: List<AuditLog>? = null,

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd hh:mm:ss")
    @Column(name = "processed_time")
    var processedTime: LocalDateTime?,

    @JsonIgnore
    @OneToMany(mappedBy = "beneficiary")
    var cards: List<BeneficiaryCard> = mutableListOf(),

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    var status: MemberStatus? = MemberStatus.ACTIVE,

    @Column(name = "member_privilege")
    @Enumerated(EnumType.STRING)
    var privilege: MemberPrivilege? = MemberPrivilege.NORMAL,

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    @Column(name = "join_date")
    var joinDate: LocalDate? = null,

    var toRenew: Boolean? = null,

    var migrated: Boolean = false,

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd hh:mm:ss")
    @Column(name = "recent_statement_date")
    var recentStatementDate: LocalDateTime? = null,

    @CreationTimestamp
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "created_at", columnDefinition="TIMESTAMP DEFAULT CURRENT_TIMESTAMP", nullable = false)
    var createdAt: LocalDateTime = LocalDateTime.now(),

    @JsonIgnore
    @OneToMany(mappedBy = "beneficiary")
    var beneficiaryProviderMapping: Set<BeneficiaryProviderMapping> = mutableSetOf(),

    @Transient
    var familySize: String? = null,

    @Transient
    var biometricCaptureDate: LocalDateTime? = null,

    @Transient
    var biometricStatus: BiometricStatus? = null,

    @Transient
    var restriction: Boolean? = null,

    @Transient
    var restrictionMsg: String? = null


) {
    override fun toString(): String {
        return "Beneficiary(id=$id, name=$name, memberNumber=$memberNumber)"
    }

    init {

        fun checkPrincipal(): Boolean {
            if (beneficiaryType == BeneficiaryType.PRINCIPAL && principal != null) return false
            if (beneficiaryType != BeneficiaryType.PRINCIPAL && principal == null) return false
            return true
        }

        fun checkNoFutureBirthDay(): Boolean {
            if (dob.isAfter(LocalDate.now())) return false
            return true
        }

        require(checkPrincipal()) {
            "Dependency is incorrectly mapped for $memberNumber. " +
                    "Check that you have included a principal if this is a dependant" +
                    " or no other member has been designated as principal if this is a principal member"
        }

        require(checkNoFutureBirthDay()) { "Date of birth cannot be in the future" }
    }


}

@Entity
@Table(
    name = "beneficiary_upload_error",
    indexes = [Index(columnList = "member_number")],
)
data class BeneficiaryUploadError(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long = 0,

    @Column(name = "member_number", nullable = false)
    var memberNumber: String,

    @Column(name = "error", columnDefinition = "LONGTEXT")
    var error: String,

    @CreationTimestamp
    @Column(name = "createdAt", nullable = false, updatable = false)
    var createdAt: LocalDateTime = LocalDateTime.now(),
)

@Entity
@Table(
    name = "beneficiary_staging",
    uniqueConstraints = [
        UniqueConstraint(
            name = "category_member_number_policy_id_UNQ",
            columnNames = ["category", "member_number", "policy_id"]
        )
    ]
)
data class BeneficiaryStaging(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long = 0,
    val batchNo: String,
    var familyNumber: String? = null,
    var name: String,
    var dob: LocalDate?,
    var dateOfBirth: String? = null,
    @Enumerated(EnumType.STRING)
    var gender: Gender,
    @Enumerated(EnumType.STRING)
    var memberType: BeneficiaryType,
    @Enumerated(EnumType.STRING)
    var status: StagingStatus,
    @Column(name = "member_number")
    var memberNumber: String,
    var email: String? = null,
    var phone: String? = null,
    var nhifNumber: String? = null,
    var idNumber: String? = null,
    var joinDate: LocalDate? = null,
    @Column(name = "policy_id")
    var policyId: Long,
    @Column(name = "category")
    var category: String,
    var processed: Boolean = false,
    var processBenefits: Boolean? = false,
    val jicEntityId: Int? = null,
    val apaEntityId: Int? = null,
    @Column(name = "error", columnDefinition = "LONGTEXT")
    var error: String? = null,
    var toRenew: Boolean? = null,
    @CreationTimestamp
    @Column(name = "createdAt", nullable = false, updatable = false)
    var createdAt: LocalDateTime = LocalDateTime.now(),
)

@Entity
@Table(
    name = "benefit_tracker", uniqueConstraints = [
        UniqueConstraint(columnNames = ["beneficiary_id", "benefit_id"], name = "Benefit_Beneficiary_UNQ"),
        UniqueConstraint(columnNames = ["aggregateId"], name = "BenefitAggregateUNQ")]
)
data class SharedBenefitTracker(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "benefit_tracker_id")
    var id: Long = 0,

    @ManyToOne()
    @JoinColumn(name = "beneficiary_id")
    var beneficiary: Beneficiary,

    @ManyToOne()
    @JoinColumn(name = "benefit_id")
    var benefit: Benefit,

    @Column(name = "aggregateId")
    var aggregateId: String
)


@Entity
@Table(
    name = "benefit_catalog", uniqueConstraints = [
        UniqueConstraint(columnNames = ["service_name"], name = "Service_Name_UNQ"),
        UniqueConstraint(columnNames = ["service_code"], name = "Service_Code_UNQ")
    ]
)
data class BenefitCatalog(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "service_id")
    var id: Long?,

    @Column(name = "service_code")
    var code: String,

    @Column(name = "service_name")
    var name: String,

    @Column(name = "service_group")
    @Enumerated(EnumType.STRING)
    var serviceGroup: ServiceGroup,

    @JsonIgnore
    @OneToMany(mappedBy = "benefit")
    var whitelist: Set<Whitelist> = mutableSetOf(),

    @JsonIgnore
    @OneToMany(mappedBy = "benefit")
    var payerMapping: Set<PayerBenefitMapping> = mutableSetOf(),

    @JsonIgnore
    @OneToMany(mappedBy = "benefitRef")
    var benefits: List<Benefit> = mutableListOf(),

    @JsonIgnore
    @OneToMany(mappedBy = "benefitCatalog")
    var serviceBenefitMapping: Set<ServiceBenefitMapping> = mutableSetOf()
)

enum class ServiceGroup {
    CONSOLIDATED, OUTPATIENT, INPATIENT, DENTAL, OPTICAL, COVID, MATERNITY
}

@Entity
@Table(
    name = "provider", uniqueConstraints = [
        UniqueConstraint(columnNames = ["provider_name"], name = "Service_Name_UNQ")
    ]
)
data class Provider(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "provider_id")
    var id: Long? = 0,

    @Column(name = "provider_name")
    var name: String? = null,

    var email: String? = null,

    @Column(name = "latitude")
    var latitude: Double? = 0.00,

    @Column(name = "longitude")
    var longitude: Double? = 0.00,

    @Enumerated(EnumType.STRING)
    var tier: ProviderTier? = null,

    @JsonIgnore
    @OneToMany(mappedBy = "mainFacility")
    var branches: List<Provider> = mutableListOf(),

    @JsonBackReference
    @ManyToOne
    @JoinColumn(name = "main_facility_id")
    var mainFacility: Provider? = null,

    @JsonIgnore
    @OneToMany
    var whitelist: Set<Whitelist> = mutableSetOf(),

    @ManyToOne
    @JoinColumn(name = "region_id")
    var region: Region? = null,

    @JsonIgnore
    @OneToMany(mappedBy = "provider")
    var payerMapping: Set<PayerProviderMapping> = mutableSetOf(),

    @JsonIgnore
    @OneToMany(mappedBy = "provider")
    var benefitProviderMapping: Set<BenefitProviderMapping> = mutableSetOf(),

    @JsonIgnore
    @OneToMany(mappedBy = "provider")
    var beneficiaryProviderMapping: Set<BeneficiaryProviderMapping> = mutableSetOf(),

    @JsonIgnore
    @OneToMany(mappedBy = "provider")
    var restrictions: Set<BenefitRestrictionProviderSet> = mutableSetOf(),

    @JsonIgnore
    @OneToMany(mappedBy = "provider")
    var serviceProviderMapping: Set<ServiceProviderMapping> = mutableSetOf(),

    @Column(name = "base_url")
    var baseUrl: String? = null,

    var billingStation: Boolean = false,
    var billsOnPortal: Boolean = true,
    var billsOnHmis: Boolean = false,
    var billsOnDevice: Boolean = false,
    var billsOnHmisAutomaticClose: Boolean? = false,
    @Column(columnDefinition = "tinyint(1) default 1")
    var canUseOtpVerificationFailOver: Boolean = true,

    var usesGlobalBatchInvoice: Boolean? = null,

    @Column(name = "verification_type")
    @Enumerated(EnumType.STRING)
    var verificationType: VerificationType? = null,

    @Column(name = "invoice_number_type")
    @Enumerated(EnumType.STRING)
    var invoiceNumberType: InvoiceNumberType? = null,

    @Column(name = "provider_middleware")
    @Enumerated(EnumType.STRING)
    val providerMiddleware: MIDDLEWARENAME? = null,

    @Column(name = "use_mainHosp_middleware", columnDefinition = "tinyint(1) default 0")
    var useMainHospMiddleware: Boolean? = false,

    @Column(name = "use_branch_middleware", columnDefinition = "tinyint(1) default 0")
    var useBranchMiddleware: Boolean? = false,

    @Column(name = "is_intergrated", columnDefinition = "tinyint(1) default 0")
    var isIntergrated: Boolean? = false,

    var createdOn: LocalDateTime = LocalDateTime.now(),

    @JsonIgnore
    @OneToMany(mappedBy = "provider")
    var bankAccounts: Set<ProviderBankAccount> = mutableSetOf(),

    @JsonIgnore
    @OneToMany(mappedBy = "provider")
    var payerProviderUsers: Set<PayerProviderUserMapping> = mutableSetOf(),

    ) {
    override fun toString(): String {
        return "Provider(id=$id) "
    }

}

enum class ProviderTier {
    TIER_ONE, TIER_TWO, TIER_THREE
}

enum class MIDDLEWARENAME {
    AVENUE, MATER, NAIROBIHOSPITAL, GETRUDES, MPSHAH, METROPOLITAN, AGAKHANKISUMU,
    AGAKHANMOMBASA, AGAKHANNAIROBI, AGAKHANNAIROBITEST, NONE, AKUH, GETRUDESTEST, OTHER, COPTIC, PANDYA, VALLEYHOSPITAL, MTRH, HEALTHX, ELDORETHOSPITAL
}

enum class VerificationType {
    BIOMETRIC, OTP
}

enum class BillingStationType {
    MULTIPLE, SINGLE
}

enum class InvoiceNumberType {
    SAME, VARIED
}

@Entity
@Table(
    name = "provider_bank_accounts"
)
data class ProviderBankAccount(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    val id: Long = 0,
    var bankName: String,
    var bankBranch: String?,
    var branchCode: String?,
    var bankCode: String?,
    var swiftCode: String?,
    var accountName: String,
    var accountNumber: String,

    @JsonIgnore
    @ManyToOne
    @JoinColumn(name = "provider_id")
    var provider: Provider,

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd hh:mm:ss")
    @Column(name = "created_at")
    var createdAt: LocalDateTime = LocalDateTime.now(),

    @JsonIgnore
    @OneToMany(mappedBy = "providerBankAccount")
    var providerBankAccountMapping: Set<PayerProviderBankAccountMapping> = mutableSetOf(),

    @JsonIgnore
    @OneToMany(mappedBy = "bankAccount", fetch = FetchType.LAZY)
    var changeLog: List<AuditLog> = emptyList(),
)

@Entity
@Table(
    name = "payer_provider_bank_account_mapping",
    uniqueConstraints = [
        UniqueConstraint(
            columnNames = ["payerId", "providerBankAccountId"],
            name = "payer_provider_bank_account_mapping_UNQ"
        )
    ]
)
data class PayerProviderBankAccountMapping(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "mapping_id")
    var id: Long = 0,

    @ManyToOne
    @JoinColumn(name = "payerId")
    var payer: Payer,

    @ManyToOne
    @JoinColumn(name = "providerBankAccountId")
    var providerBankAccount: ProviderBankAccount,

    var mappingEnabled: Boolean = true,

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd hh:mm:ss")
    @Column(name = "created_at")
    var createdAt: LocalDateTime = LocalDateTime.now()
)

@Entity
@Table(
    name = "payer_provider_user_mapping",
    uniqueConstraints = [
        UniqueConstraint(columnNames = ["payer_id", "provider_id"], name = "payer_provider_user_mapping_UNQ")
    ]
)
data class PayerProviderUserMapping(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "mapping_id")
    var id: Long = 0,

    @ManyToOne
    @JoinColumn(name = "payer_id")
    var payer: Payer,

    @ManyToOne
    @JoinColumn(name = "provider_id")
    var provider: Provider,

    var userId: String,

    @Column(name = "contact_person")
    @Enumerated(EnumType.STRING)
    var contactPerson: ContactPerson,

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd hh:mm:ss")
    @Column(name = "created_at")
    var createdAt: LocalDateTime = LocalDateTime.now(),

    @JsonIgnore
    @OneToMany(mappedBy = "payerProviderUserMapping", fetch = FetchType.LAZY)
    var changeLog: List<AuditLog> = emptyList(),
)

@Entity
@Table(
    name = "provider_exclusion",
    uniqueConstraints = [
        UniqueConstraint(columnNames = ["category_id", "provider_id"], name = "Category_Exclusion_UNQ")
    ]
)
data class ProviderExclusion(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "exclusion_id")
    var id: Long?,

    @ManyToOne
    @JoinColumn(name = "provider_id", nullable = false)
    var provider: Provider,

    @ManyToOne
    @JoinColumn(name = "category_id", nullable = false)
    var category: Category,

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    var status: Status,

    @Column(name = "processed")
    var processed: Boolean = false,

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd hh:mm:ss")
    @Column(name = "processed_time")
    var processedTime: LocalDateTime? = null
)

enum class Status {
    ACTIVE, INACTIVE
}

enum class RestrictionStatus {
    ACTIVE, DELETED
}

@Entity
@Table(
    name = "copayment", uniqueConstraints = [
        UniqueConstraint(columnNames = ["category_id", "provider_id"], name = "Copayment_UNQ")
    ]
)
data class Copayment(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "copayment_id")
    var id: Long?,

    @ManyToOne
    @JoinColumn(name = "provider_id", nullable = false)
    var provider: Provider,

    @ManyToOne
    @JoinColumn(name = "category_id", nullable = false)
    var category: Category,

    @Column(name = "amount", nullable = false)
    var amount: BigDecimal,

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    var status: Status,

    @Column(name = "processed")
    var processed: Boolean,

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd hh:mm:ss")
    @Column(name = "processed_time")
    var processedTime: LocalDateTime?
)

@Entity
@Table(
    name = "service_whitelist"
)
data class Whitelist(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "whitelist_id")
    var id: Long = 0,

    @ManyToOne
    @JoinColumn(name = "benefit_catalog_id", nullable = false)
    var benefit: BenefitCatalog,

    @ManyToOne
    @JoinColumn(name = "provider_id", nullable = false)
    var provider: Provider
)


@Entity
@Table(name = "country")
data class Country(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "country_id")
    var id: Long = 0,

    @Column(name = "country_name", unique = true, nullable = false)
    var name: String,

    @JsonIgnore
    @OneToMany(mappedBy = "country")
    var regions: Set<Region> = mutableSetOf()
)

@Entity
@Table(
    name = "region",
    uniqueConstraints = [
        UniqueConstraint(columnNames = ["region_name", "country_id"], name = "region_UNQ")
    ]
)
data class Region(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "region_id")
    var id: Long = 0,

    @Column(name = "region_name", nullable = false)
    var name: String,

    @ManyToOne
    @JoinColumn(name = "country_id")
    var country: Country,

    @JsonIgnore
    @OneToMany(mappedBy = "region")
    var providers: Set<Provider> = mutableSetOf()

)

@Entity
@Table(
    name = "payer_regions",
    uniqueConstraints = [
        UniqueConstraint(columnNames = ["region_name", "payer_id"], name = "payer_region_UNQ")
    ]
)
data class PayerRegion(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "payer_region_id")
    var id: Long = 0,

    @Column(name = "region_name", nullable = false)
    var name: String,

    @ManyToOne
    @JoinColumn(name = "payer_id")
    var payer: Payer,
)

@Entity
@Table(
    name = "payer_benefit_mapping",
    uniqueConstraints = [
        UniqueConstraint(columnNames = ["payerId", "benefitId"], name = "payer_benefit_mapping_UNQ")
    ]
)
data class PayerBenefitMapping(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "payer_benefit_mapping_id")
    var id: Long = 0,

    @ManyToOne
    @JoinColumn(name = "benefitId")
    var benefit: BenefitCatalog,

    @ManyToOne
    @JoinColumn(name = "payerId")
    var payer: Payer,

    @Column(name = "code", nullable = false)
    var code: String
)

@Entity
@Table(
    name = "payer_provider_mapping",
    uniqueConstraints = [
        UniqueConstraint(columnNames = ["payerId", "providerId"], name = "payer_provider_mapping_UNQ")
    ]
)
data class PayerProviderMapping(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "payer_provider_mapping_id")
    var id: Long = 0,


    @ManyToOne
    @JoinColumn(name = "payerId")
    var payer: Payer? = null,

    @ManyToOne
    @JoinColumn(name = "providerId")
    var provider: Provider? = null,

    @Column(name = "code")
    var code: String? = null,

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    var status: PayerProviderMappingStatus = PayerProviderMappingStatus.ACTIVE,

    @ManyToOne
    @JoinColumn(name = "payerRegionId")
    var payerRegion: PayerRegion? = null,
) {
    override fun toString(): String {
        return "PayerProviderMapping(id=$id) "
    }

    enum class PayerProviderMappingStatus {
        ACTIVE, INACTIVE, SUSPENDED
    }
}

@Entity
@Table(
    name = "category_restrictions",
    uniqueConstraints = [
        UniqueConstraint(columnNames = ["category_id", "provider_id"], name = "Provider_Restriction_UNQ")
    ]
)
data class CategoryRestriction(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "restriction_id")
    var id: Long?,

    @ManyToOne
    @JoinColumn(name = "provider_id", nullable = false)
    var provider: Provider,

    @ManyToOne
    @JoinColumn(name = "category_id", nullable = false)
    var category: Category,

    @Column(name = "restriction_status")
    @Enumerated(EnumType.STRING)
    var restrictionStatus: RestrictionStatus? = RestrictionStatus.ACTIVE,

    @ManyToOne
    @JoinColumn(name = "payer_id", nullable = false)
    var payer: Payer,

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd hh:mm:ss")
    @Column(name = "create_date")
    var createDate: LocalDateTime = LocalDateTime.now()
) {
    override fun toString(): String {
        return "CategoryRestriction(id=$id) "
    }
}


@Entity
@Table(
    name = "benefit_restrictions",
)
data class BenefitRestriction(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    var id: Long? = 0,

    @Column(name = "name", nullable = false)
    var name: String,

    @ManyToOne
    @JoinColumn(name = "payer_id", nullable = false)
    var payer: Payer,

    @Column(name = "restriction_type", nullable = false)
    @Enumerated(EnumType.STRING)
    var restrictionType: RestrictionType,

    @Column(name = "migrated", columnDefinition = "bit(1) default 0")
    var migrated: Boolean = false,

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd hh:mm:ss")
    @Column(name = "create_date")
    var createDate: LocalDateTime = LocalDateTime.now(),

    @JsonIgnore
    @OneToMany(mappedBy = "restriction")
    var benefitRestrictionProviderSet: Set<BenefitRestrictionProviderSet> = mutableSetOf(),

    @JsonIgnore
    @OneToMany(mappedBy = "restriction")
    var benefits: Set<Benefit> = mutableSetOf(),

    @JsonIgnore
    @OneToMany(mappedBy = "restriction")
    var mappedBenefits: Set<BenefitRestrictionMapping> = mutableSetOf(),

    @JsonIgnore
    @OneToMany(mappedBy = "restriction")
    var benefitRules: Set<BenefitRule> = mutableSetOf(),
) {
    override fun toString(): String {
        return "BenefitRestriction(id=$id) "
    }
}


@Entity
@Table(
    name = "benefit_restriction_provider_set",
    uniqueConstraints = [
        UniqueConstraint(columnNames = ["restriction_id", "provider_id"], name = "Provider_Restriction_UNQ")
    ]
)
data class BenefitRestrictionProviderSet(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    var id: Long? = 0,

    @ManyToOne
    @JoinColumn(name = "restriction_id", nullable = false)
    var restriction: BenefitRestriction,

    @ManyToOne
    @JoinColumn(name = "provider_id", nullable = false)
    var provider: Provider,

    @Column(name = "is_active")
    var active: Boolean = true,

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd hh:mm:ss")
    @Column(name = "create_date")
    var createDate: LocalDateTime = LocalDateTime.now()
) {
    override fun toString(): String {
        return "BenefitRestrictionProviderSet(id=$id) "
    }
}

@Entity
@Table(
    name = "benefit_restriction_mapping",
    uniqueConstraints = [
        UniqueConstraint(columnNames = ["benefit_id", "restriction_id"], name = "benefit_restriction_mapping_UNQ")
    ]
)
data class BenefitRestrictionMapping(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "mapping_id")
    var id: Long = 0,

    @ManyToOne
    @JoinColumn(name = "benefit_id")
    var benefit: Benefit,

    @ManyToOne
    @JoinColumn(name = "restriction_id")
    var restriction: BenefitRestriction,

    var mappingEnabled: Boolean = true
)


@Entity
@Table(
    name = "benefit_provider_mapping",
    uniqueConstraints = [
        UniqueConstraint(columnNames = ["benefitId", "providerId"], name = "benefit_provider_mapping_UNQ")
    ]
)
data class BenefitProviderMapping(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "benefit_provider_mapping_id")
    var id: Long = 0,

    @ManyToOne
    @JoinColumn(name = "benefitId")
    var benefit: Benefit? = null,

    @ManyToOne
    @JoinColumn(name = "providerId")
    var provider: Provider? = null,

    var mappingEnabled: Boolean = true,

    @CreationTimestamp
    @Column(name = "mappedOn", nullable = false, updatable = false)
    var mappedOn: LocalDateTime = LocalDateTime.now(),
) {
    override fun toString(): String {
        return "BenefitPayerProviderMapping(id=$id) "
    }
}

@Entity
@Table(
    name = "beneficiary_provider_mapping",
    uniqueConstraints = [
        UniqueConstraint(
            columnNames = ["beneficiaryId", "benefitId", "providerId"],
            name = "beneficiary_benefit_provider_mapping_UNQ"
        )
    ]
)
data class BeneficiaryProviderMapping(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "benefit_provider_mapping_id")
    var id: Long = 0,

    @ManyToOne
    @JoinColumn(name = "beneficiaryId")
    var beneficiary: Beneficiary? = null,

    @ManyToOne
    @JoinColumn(name = "benefitId")
    var benefit: Benefit? = null,

    @ManyToOne
    @JoinColumn(name = "providerId")
    var provider: Provider? = null,

    var mappingEnabled: Boolean = true,

    @CreationTimestamp
    @Column(name = "mappedOn", nullable = false, updatable = false)
    var mappedOn: LocalDateTime = LocalDateTime.now(),
) {
    override fun toString(): String {
        return "BenefitPayerProviderMapping(id=$id) "
    }
}

@Entity
@Table(
    name = "card_batch"
)
data class CardBatch(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "batch_id")
    val id: Long = 0,

    @CreatedDate
    @Column(name = "created_date", nullable = false)
    var createdAt: LocalDate,

    @LastModifiedDate
    @Column(name = "modified_at")
    var modifiedAt: LocalDateTime? = null,

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    var status: CardStatus = CardStatus.REQUESTED,

    @Column(name = "type")
    @Enumerated(EnumType.STRING)
    var type: CardRequestType,

    @ManyToOne
    @JoinColumn
    var policy: Policy,

    @JsonIgnore
    @OneToMany(mappedBy = "batch")
    var beneficiaryCards: List<BeneficiaryCard> = mutableListOf(),

    @Column(name = "payment_ref")
    var paymentRef: String? = null
) {
    private fun checkReprintHasPayment(): Boolean {
        if (type == CardRequestType.REPRINT && paymentRef.isNullOrEmpty()) return false
        return true
    }

    init {
        require(checkReprintHasPayment()) { "Reprint requests must have a payment" }
    }
}

enum class CardStatus {
    REQUESTED, PRINTED, ISSUED
}

enum class ChangeLogType {
    CATEGORY_UPDATE, CATEGORY_STATUS_UPDATE, CATEGORY_CHANGE, MEMBER_UPDATE, MEMBERSTATUS_UPDATE, BIOMETRICS_UPDATE, BENEFIT_UPDATE,
    BENEFIT_STATUS_UPDATE, PREAUTH_UPDATE, NONE, CREATE, UPDATE
}

enum class CardRequestType {
    NEW, REPRINT
}

@Entity
@Table(
    name = "beneficiary_card"
)
data class BeneficiaryCard(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "batch_id")
    val id: Long = 0,

    @ManyToOne
    var beneficiary: Beneficiary,

    @ManyToOne
    var batch: CardBatch
)


@Entity
@Table(
    name = "beneficiary_link",
    uniqueConstraints = [
        UniqueConstraint(columnNames = ["beneficiary_id", "phone_number"], name = "link_UNQ")
    ]
)
data class BeneficiaryLink(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "link_id")
    var linkId: Long? = 0,

    @NotNull
    @Size(min = 1, max = 20)
    @Column(name = "phone_number")
    var phoneNumber: String,

    @NotNull
    var status: Boolean,

    @JsonIgnore
    @JoinColumn(name = "beneficiary_id", referencedColumnName = "beneficiary_id")
    @ManyToOne
    var beneficiary: Beneficiary,

    @NotNull
    @Column(name = "linked_on")
    var linkedOn: LocalDateTime = LocalDateTime.now(),

    @NotNull
    @Column(name = "updated_on")
    var updatedOn: LocalDateTime? = null
)


@Entity
@Table(name = "audit_log")
data class AuditLog(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    var id: Long = 0,

    @Column(name = "action")
    var action: String,

    @Column(name = "user")
    var user: String,

    @CreationTimestamp
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd hh:mm:ss")
    @Column(name = "time", updatable = false, nullable = true)
    val time: LocalDateTime? = LocalDateTime.now(),

    @Column(name = "data", columnDefinition = "LONGTEXT")
    var data: String?,

    @Lob
    var narration: String? = null,

    @Column(name = "organisation")
    var organisation: String?,

    @Column(name = "reason")
    var reason: String?,

    @Column(name = "member_number")
    var memberNumber: String? = null,

    @Column(name = "amount", nullable = true)
    var amount: BigDecimal? = BigDecimal.ZERO,

    @Column(name = "type")
    @Enumerated(EnumType.STRING)
    var changeLogType: ChangeLogType = ChangeLogType.NONE,

    @JsonIgnore
    @ManyToOne()
    @JoinColumn(name = "beneficiary_id", nullable = true)
    val beneficiary: Beneficiary? = null,

    @JsonIgnore
    @ManyToOne()
    @JoinColumn(name = "category_id", nullable = true)
    val category: Category? = null,

    @JsonIgnore
    @ManyToOne()
    @JoinColumn(name = "benefit_id", nullable = true)
    val benefit: Benefit? = null,

    @JsonIgnore
    @ManyToOne()
    @JoinColumn(name = "bank_account_id", nullable = true)
    val bankAccount: ProviderBankAccount? = null,

    @JsonIgnore
    @ManyToOne()
    @JoinColumn(name = "payer_provider_user_id", nullable = true)
    val payerProviderUserMapping: PayerProviderUserMapping? = null,

    @JsonIgnore
    @ManyToOne()
    @JoinColumn(name = "benefit_payer_admin_id", nullable = true)
    val benefitPayerAdmin: BenefitPayerAdmin? = null,

    )

@Entity
@Table(name = "device_models")
data class DeviceModel(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "model_id", nullable = false)
    var id: Long = 0,

    @Column(name = "model", unique = true, nullable = false)
    var model: String,

    var description: String?,

    @JsonBackReference
    @OneToMany(mappedBy = "deviceModel", fetch = FetchType.LAZY)
    var deviceCatalog: List<DeviceCatalog> = mutableListOf(),

    @JsonManagedReference
    @OneToMany(mappedBy = "model", fetch = FetchType.LAZY)
    var logs: List<DeviceManagementAuditLog> = mutableListOf(),
)

@Entity
@Table(name = "device_catalog")
data class DeviceCatalog(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "catalog_id", nullable = false)
    var id: Long = 0,

    @Column(name = "device_id", unique = true, nullable = false)
    var deviceId: String,

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    var status: DeviceStatus = DeviceStatus.AVAILABLE,

    @Column(name = "description", columnDefinition = "LONGTEXT")
    var description: String? = null,

    @JsonManagedReference
    @ManyToOne
    @JoinColumn(name = "model_id", nullable = false)
    var deviceModel: DeviceModel,

    @CreationTimestamp
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "registered_on", nullable = false, updatable = false)
    var registeredOn: LocalDateTime = LocalDateTime.now(),

    @JsonManagedReference
    @OneToMany(mappedBy = "deviceCatalog", fetch = FetchType.LAZY)
    var imei: List<DeviceImei> = mutableListOf(),

    @JsonBackReference
    @OneToMany(mappedBy = "deviceCatalog", fetch = FetchType.LAZY)
    var allocations: List<DeviceAllocation> = mutableListOf(),

    @JsonManagedReference
    @OneToMany(mappedBy = "deviceCatalog", fetch = FetchType.LAZY)
    var accessories: List<DeviceAccessory> = mutableListOf(),

    @JsonManagedReference
    @OneToMany(mappedBy = "catalog", fetch = FetchType.LAZY)
    var logs: List<DeviceManagementAuditLog> = mutableListOf(),
)

@Entity
@Table(name = "device_imei")
data class DeviceImei(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    var id: Long = 0,

    @Column(name = "imei", unique = true)
    var imei: String? = null,

    @JsonBackReference
    @ManyToOne
    @JoinColumn(name = "device_catalog_id", nullable = false)
    var deviceCatalog: DeviceCatalog
)

@Entity
@Table(name = "device_sim_catalog")
data class DeviceSim(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    var id: Long = 0,

    @Column(name = "sim_number", unique = true)
    var simNumber: String? = null,

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    var status: SimStatus = SimStatus.UNASSIGNED,

    @CreationTimestamp
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "added_on", nullable = false, updatable = false)
    var addedOn: LocalDateTime = LocalDateTime.now(),

    @JsonBackReference
    @OneToMany(mappedBy = "deviceSim", fetch = FetchType.LAZY)
    var allocations: List<DeviceAllocation> = mutableListOf(),

    @JsonManagedReference
    @OneToMany(mappedBy = "sim", fetch = FetchType.LAZY)
    var logs: List<DeviceManagementAuditLog> = mutableListOf(),
)

@Entity
@Table(name = "device_allocation")
data class DeviceAllocation(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "allocation_id", nullable = false)
    var id: Long = 0,

    @JsonManagedReference
    @ManyToOne
    @JoinColumn(name = "device_catalog_id", nullable = false)
    var deviceCatalog: DeviceCatalog,

    @JsonManagedReference
    @ManyToOne
    @JoinColumn(name = "provider_id")
    var provider: Provider? = null,

    @JsonManagedReference
    @ManyToOne
    @JoinColumn(name = "sim_id")
    var deviceSim: DeviceSim? = null,

    var enableGeofence: Boolean = false,

    var radius: Long? = null,

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    var status: AllocationStatus = AllocationStatus.UNALLOCATED,

    var createdBy: String,

    var allocatedBy: String? = null,

    var checkedBy: String? = null,

    @Enumerated(EnumType.STRING)
    @Column(name = "checker_status")
    var checkerStatus: CheckerStatus? = null,

    @Column(name = "note", columnDefinition = "LONGTEXT")
    var note: String? = null,

    @CreationTimestamp
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "date_created", nullable = false, updatable = false)
    var dateCreated: LocalDateTime = LocalDateTime.now(),

    @JsonManagedReference
    @OneToMany(mappedBy = "deviceAllocation", fetch = FetchType.LAZY)
    var accessories: List<DeviceAccessory> = mutableListOf(),

    @JsonManagedReference
    @OneToMany(mappedBy = "deviceAllocation", fetch = FetchType.LAZY)
    var documents: List<DeviceDocument> = mutableListOf(),

    @JsonManagedReference
    @OneToMany(mappedBy = "allocation", fetch = FetchType.LAZY)
    var logs: List<DeviceManagementAuditLog> = mutableListOf(),

    )

@Entity
@Table(name = "device_accessories")
data class DeviceAccessory(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "accessory_id", nullable = false)
    var id: Long = 0,

    var accessory: String,

    @Column(name = "note", columnDefinition = "LONGTEXT")
    var note: String? = null,

    @JsonBackReference
    @ManyToOne
    @JoinColumn(name = "device_allocation_id", nullable = true)
    var deviceAllocation: DeviceAllocation? = null,

    @JsonBackReference
    @ManyToOne
    @JoinColumn(name = "device_catalog_id", nullable = true)
    var deviceCatalog: DeviceCatalog? = null

)

@Entity
@Table(name = "device_upload_errors")
data class DeviceUploadError(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    var id: Long = 0,
    val deviceId: String,
    @Column(name = "imei", columnDefinition = "LONGTEXT")
    val imei: String?,
    val description: String?,
    val registeredByUser: String,
    val model: String,
    val error: String?,
    @CreationTimestamp
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "added_on", nullable = false, updatable = false)
    var addedOn: LocalDateTime = LocalDateTime.now()
)


@Entity
@Table(name = "device_sim_upload_errors")
data class SimUploadError(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    var id: Long = 0,

    val simNumber: String,

    val registeredByUser: String,

    val error: String?,

    @CreationTimestamp
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "added_on", nullable = false, updatable = false)
    var addedOn: LocalDateTime = LocalDateTime.now()
)

@Entity
@Table(name = "device_documents")
data class DeviceDocument(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "document_id", nullable = false)
    var id: Long = 0,

    @Lob
    var documentUrl: String,

    @CreationTimestamp
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "uploaded_on", nullable = false, updatable = false)
    var uploadedOn: LocalDateTime = LocalDateTime.now(),

    @JsonBackReference
    @ManyToOne
    @JoinColumn(name = "device_allocation_id", nullable = true)
    var deviceAllocation: DeviceAllocation? = null,

    @JsonManagedReference
    @OneToMany(mappedBy = "document", fetch = FetchType.LAZY)
    var logs: List<DeviceManagementAuditLog> = mutableListOf()
)

@Entity
@Table(name = "device_management_audit_log")
data class DeviceManagementAuditLog(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    var id: Long = 0,

    @JsonBackReference
    @ManyToOne
    @JoinColumn(name = "catalog_id")
    var catalog: DeviceCatalog? = null,

    @JsonBackReference
    @ManyToOne
    @JoinColumn(name = "sim_id")
    var sim: DeviceSim? = null,

    @JsonBackReference
    @ManyToOne
    @JoinColumn(name = "allocation_id")
    var allocation: DeviceAllocation? = null,

    @JsonBackReference
    @ManyToOne
    @JoinColumn(name = "model_id")
    var model: DeviceModel? = null,

    @JsonBackReference
    @ManyToOne
    @JoinColumn(name = "document_id")
    var document: DeviceDocument? = null,

    @Enumerated(EnumType.STRING)
    @Column(name = "action")
    var action: LogAction,

    @Lob
    var previousValues: String? = null,

    @Lob
    var payload: String? = null,

    var actionByUser: String,

    var note: String? = null,

    @Lob
    var reason: String? = null,

    @CreationTimestamp
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "created_on", nullable = false, updatable = false)
    var createdOn: LocalDateTime = LocalDateTime.now(),
)

interface ClaimsBenefit {
    val aggregateId: String
}

@Entity
@Table(
    name = "service_catalog", uniqueConstraints = [
        UniqueConstraint(columnNames = ["service_name"], name = "Service_Name_UNQ")
    ]
)
data class ServiceCatalog(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "service_id")
    var id: Long = 0,

    @Column(name = "service_name")
    var name: String,

    @Column(name = "description", columnDefinition = "LONGTEXT")
    var description: String?,

    var isEnabled: Boolean = true,

    @CreationTimestamp
    @Column(name = "createdOn", nullable = false, updatable = false)
    var createdOn: LocalDateTime = LocalDateTime.now(),

    @JsonIgnore
    @OneToMany(mappedBy = "serviceCatalog")
    var serviceBenefitMapping: Set<ServiceBenefitMapping> = mutableSetOf(),

    @JsonIgnore
    @OneToMany(mappedBy = "serviceCatalog")
    var serviceProviderMapping: Set<ServiceProviderMapping> = mutableSetOf(),
)

@Entity
@Table(
    name = "service_benefit_mapping", uniqueConstraints = [
        UniqueConstraint(columnNames = ["service_catalog_id", "benefit_catalog_id"], name = "Service_Benefit_UNQ")
    ]
)
data class ServiceBenefitMapping(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "mapping_id")
    var id: Long = 0,

    @ManyToOne
    @JoinColumn(name = "service_catalog_id")
    var serviceCatalog: ServiceCatalog? = null,

    @ManyToOne
    @JoinColumn(name = "benefit_catalog_id")
    var benefitCatalog: BenefitCatalog? = null,

    var mappingEnabled: Boolean = true,

    @CreationTimestamp
    @Column(name = "mappedOn", nullable = false, updatable = false)
    var mappedOn: LocalDateTime = LocalDateTime.now()
)

@Entity
@Table(
    name = "service_provider_mapping", uniqueConstraints = [
        UniqueConstraint(columnNames = ["provider_id", "service_catalog_id"], name = "Service_Provider_UNQ")
    ]
)
data class ServiceProviderMapping(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "mapping_id")
    var id: Long = 0,

    @ManyToOne
    @JoinColumn(name = "provider_id")
    var provider: Provider? = null,

    @ManyToOne
    @JoinColumn(name = "service_catalog_id")
    var serviceCatalog: ServiceCatalog? = null,

    var mappingEnabled: Boolean = true,

    @CreationTimestamp
    @Column(name = "mappedOn", nullable = false, updatable = false)
    var mappedOn: LocalDateTime = LocalDateTime.now()
)


@Entity
@Table(
    name = "request_types", uniqueConstraints = [
        UniqueConstraint(columnNames = ["request_name"], name = "Request_Type_UNQ")
    ]
)
data class RequestType(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "request_id")
    var id: Long = 0,

    @Column(name = "request_name")
    var name: String,

    @CreationTimestamp
    @Column(name = "createdOn", nullable = false, updatable = false)
    var createdOn: LocalDateTime = LocalDateTime.now(),

    @JsonIgnore
    @OneToMany(mappedBy = "requestType")
    var requestServices: Set<RequestService> = mutableSetOf(),
)

@Entity
@Table(
    name = "request_services", uniqueConstraints = [
        UniqueConstraint(columnNames = ["service_name", "request_type_id"], name = "Service_Request_Type_UNQ")
    ]
)
data class RequestService(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "service_id")
    var id: Long = 0,

    @Column(name = "service_name")
    var name: String,

    @ManyToOne
    @JoinColumn(name = "request_type_id")
    var requestType: RequestType? = null,

    @CreationTimestamp
    @Column(name = "createdOn", nullable = false, updatable = false)
    var createdOn: LocalDateTime = LocalDateTime.now(),
)


@Entity
@Table(
    name = "data_collection_beneficiary"
)
data class DataCollectionBeneficiary(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    val id: Long = 0,

    @CreatedDate
    @Column(name = "created_date", nullable = false)
    var createdAt: LocalDate,
    @LastModifiedDate
    @Column(name = "modified_at")
    var modifiedAt: LocalDateTime? = null,
    @Column(name = "employeeNumber")
    var employeeNumber: String? = null,
    @Column(name = "policyNumber")
    var policyNumber: String? = null,
    @Column(name = "employeeName")
    var employeeName: String? = null,
    @Column(name = "employeeKrapin")
    var employeeKrapin: String? = null,
    @Column(name = "name")
    var name: String? = null,
    @Column(name = "idNumber")
    var idNumber: String? = null,
    @Column(name = "dateOfBirth")
    var dateOfBirth: LocalDate? = null,
    @Column(name = "mobileNumber")
    var mobileNumber: String? = null,
    @Column(name = "age")
    var age: Long? = null,
    @Column(name = "postalCode")
    var postalCode: String? = null,
    @Column(name = "gender", nullable = true)
    @Enumerated(EnumType.STRING)
    var gender: Gender? = null,
    @Column(name = "poBox")
    var poBox: String? = null,
    @Column(name = "maritalStatus")
    var maritalStatus: String? = null,
    @Column(name = "town")
    var town: String? = null,
    @Column(name = "memberType")
    var memberType: String? = null,
    @Column(name = "benefitType")
    @Enumerated(EnumType.STRING)
    var benefitType: DataCollectionBenefitType? = null,
    @Column(name = "category")
    var category: String? = null,
    @Column(name = "provider")
    var provider: String? = null,
    @Column(name = "relationship")
    @Enumerated(EnumType.STRING)
    var relationship: DataCollectionRelationship? = DataCollectionRelationship.PRINCIPAL,
    @Column(name = "county")
    var county: String? = null,
    @Column(name = "payment_mode", nullable = true)
    @Enumerated(EnumType.STRING)
    var paymentMode: DataCollectionPaymentMode? = null,
    @Column(name = "no_of_adults")
    var noOfAdults: Long? = null,
    @Column(name = "adult_premium_amount")
    var adultPremiumAmount: BigDecimal? = null,
    @Column(name = "total_adult_premium_amount")
    var totalAdultPremiumAmount: BigDecimal? = null,
    @Column(name = "no_of_children")
    var noOfChildren: Long? = null,
    @Column(name = "child_premium_amount")
    var childPremiumAmount: BigDecimal? = null,
    @Column(name = "total_child_premium_amount")
    var totalChildPremiumAmount: BigDecimal? = null,
    @Column(name = "total_premium_amount")
    var totalPremiumAmount: BigDecimal? = null,
    @Column(name = "next_of_kin")
    var nextOfKin: String? = null,
    @Column(name = "next_of_kin_dob")
    var nextOfKinDob: String? = null,
    @Column(name = "next_of_kin_id_number")
    var nextOfKinIdNumber: String? = null,
    @Column(name = "staff_logged_in")
    var staffLoggedIn: String? = null,
    @ManyToOne
    @JoinColumn(name = "principal_id", nullable = true)
    var principal: DataCollectionBeneficiary? = null,
    @JsonIgnore
    @OneToMany(mappedBy = "principal")
    var dependants: List<DataCollectionBeneficiary> = mutableListOf(),
    @OneToMany(mappedBy = "beneficiaryId")
    var documents: List<DataCollectionDocument> = mutableListOf()
) {
    @Override
    override fun toString(): String {
        return this::class.simpleName + "(id = $id , employeeNumber = $employeeNumber , employeeName = $employeeName )"
    }
}


@Entity
@Table(
    name = "data_collection_document"
)
data class DataCollectionDocument(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    val id: Long = 0,

    @CreatedDate
    @Column(name = "created_date", nullable = false)
    var createdAt: LocalDate,

    @LastModifiedDate
    @Column(name = "modified_at")
    var modifiedAt: LocalDateTime? = null,

    @Column(name = "fileUrl")
    var fileUrl: String? = null,

    @Column(name = "type")
    var type: String? = null,

    @Column(name = "beneficiary_id")
    var beneficiaryId: Long? = null
)


@Entity
@Table(
    name = "onboard_principal"
)
data class OnboardPrincipal(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    val id: Long = 0,

    @Column(name = "grower_number", unique = true)
    val growerNumber: String,

    @Column(name = "first_name")
    val firstName: String,

    @Column(name = "middle_name")
    val middleName: String? = null,

    @Column(name = "surname")
    val surname: String,

    @Column(name = "date_of_birth")
    val dateOfBirth: LocalDate,

    @Column(name = "id_number", unique = true)
    val idNumber: String,

    @Column(name = "gender")
    @Enumerated(EnumType.STRING)
    val gender: Gender,

    @Column(name = "marital_status")
    @Enumerated(EnumType.STRING)
    val maritalStatus: MaritalStatus,

    @Column(name = "mobile_number")
    val mobileNumber: String,

    @Column(name = "town")
    val town: String? = null,

    @Column(name = "county")
    val county: String,

    @Column(name = "hospital")
    val hospital: String,

    @Column(name = "inpatient_premium", nullable = false)
    var inpatientPremium: BigDecimal,

    @Column(name = "outpatient_premium")
    var outpatientPremium: BigDecimal,

    @Column(name = "category")
    @Enumerated(EnumType.STRING)
    val category: OnboardPrincipalCategory,

    @Column(name = "payment_mode")
    @Enumerated(EnumType.STRING)
    val paymentMode: OnboardPaymentMode,

    @Column(name = "beneficiary_type")
    @Enumerated(EnumType.STRING)
    val beneficiaryType: BeneficiaryType = BeneficiaryType.PRINCIPAL,

    @OneToMany(mappedBy = "principal", cascade = [CascadeType.ALL], orphanRemoval = true)
    val dependants: MutableList<OnboardDependant> = mutableListOf(),

    @OneToMany(mappedBy = "principal", cascade = [CascadeType.ALL], orphanRemoval = true)
    val beneficiaries: MutableList<OnboardBeneficiary> = mutableListOf(),

    @OneToMany(mappedBy = "principal", cascade = [CascadeType.ALL], orphanRemoval = true)
    val documents: MutableList<OnboardDocument> = mutableListOf(),

    @OneToMany(mappedBy = "principal", cascade = [CascadeType.ALL], orphanRemoval = true)
    val benefits: MutableList<OnboardBenefit> = mutableListOf(),

    @OneToMany(mappedBy = "principal", cascade = [CascadeType.ALL], orphanRemoval = true)
    val applicationFormContextIds: MutableList<OnboardPrincipalApplicationFormContextId> = mutableListOf(),

    @OneToMany(mappedBy = "principal", cascade = [CascadeType.ALL], orphanRemoval = true)
    val applicationFormInfoIds: MutableList<OnboardPrincipalApplicationFormInfoId> = mutableListOf(),

    @OneToMany(mappedBy = "principal", cascade = [CascadeType.ALL], orphanRemoval = true)
    val hospitalFormContextIds: MutableList<OnboardPrincipalHospitalFormContextId> = mutableListOf(),

    @OneToMany(mappedBy = "principal", cascade = [CascadeType.ALL], orphanRemoval = true)
    val hospitalFormInfoIds: MutableList<OnboardPrincipalHospitalFormInfoId> = mutableListOf(),

    @OneToMany(mappedBy = "principal", cascade = [CascadeType.ALL], orphanRemoval = true)
    val studentIdContextIds: MutableList<OnboardPrincipalStudentIdContextId> = mutableListOf(),

    @OneToMany(mappedBy = "principal", cascade = [CascadeType.ALL], orphanRemoval = true)
    val studentIdInfoIds: MutableList<OnboardPrincipalStudentIdInfoId> = mutableListOf(),

    @ManyToOne(cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
    @JoinColumn(name = "agent_id")
    val agent: OnboardAgent,

    @Column(name = "created_by_name")
    val createdByName: String,

    @Column(name = "created_by_username")
    val createdByUsername: String,

    @Column(name = "application_date")
    val applicationDate: String,

    @Column(name = "premium_payer_grower_number")
    val premiumPayerGrowerNumber: String? = null,

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    var createdAt: LocalDate? = LocalDate.now(),
)


@Entity
@Table(
    name = "onboard_dependant"
)
data class OnboardDependant(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    val id: Long = 0,

    @Column(name = "first_name")
    val firstName: String,

    @Column(name = "middle_name")
    val middleName: String? = null,

    @Column(name = "surname")
    val surname: String,

    @Column(name = "date_of_birth")
    val dateOfBirth: LocalDate,

    @Column(name = "id_number")
    val idNumber: String? = null,

    @Column(name = "gender")
    @Enumerated(EnumType.STRING)
    val gender: Gender,

    @Column(name = "beneficiary_type")
    @Enumerated(EnumType.STRING)
    val beneficiaryType: OnboardDependantBeneficiaryType,

    @Column(name = "member_number")
    val memberNumber: String? = null,

    @Column(name = "mobile_number")
    val mobileNumber: String? = null,

    @Column(name = "hospital")
    val hospital: String? = null,

    @Column(name = "inpatient_premium")
    var inpatientPremium: BigDecimal,

    @Column(name = "outpatient_premium")
    var outpatientPremium: BigDecimal,

    @ManyToOne
    @JoinColumn(name = "principal_id", nullable = false)
    val principal: OnboardPrincipal,

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    var createdAt: LocalDate? = LocalDate.now(),
)


@Entity
@Table(
    name = "onboard_beneficiary"
)
data class OnboardBeneficiary(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    val id: Long = 0,

    @Column(name = "first_name")
    val firstName: String,

    @Column(name = "middle_name")
    val middleName: String? = null,

    @Column(name = "surname")
    val surname: String,

    @Column(name = "date_of_birth")
    val dateOfBirth: LocalDate,

    @Column(name = "id_number")
    val idNumber: String,

    @Column(name = "gender")
    @Enumerated(EnumType.STRING)
    val gender: Gender,

    @Column(name = "mobile_number")
    val mobileNumber: String,

    @ManyToOne
    @JoinColumn(name = "principal_id", nullable = false)
    val principal: OnboardPrincipal,

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    var createdAt: LocalDate? = LocalDate.now(),
)


@Entity
@Table(
    name = "onboard_document"
)
data class OnboardDocument(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    val id: Long = 0,

    @Column(name = "url")
    val url: String,

    @Column(name = "type")
    @Enumerated(EnumType.STRING)
    val type: OnboardDocumentType,

    @ManyToOne
    @JoinColumn(name = "principal_id", nullable = true)
    val principal: OnboardPrincipal,

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    val createdAt: LocalDate? = LocalDate.now(),
)

@Entity
@Table(
    name = "onboard_benefit"
)
data class OnboardBenefit(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    val id: Long = 0,

    @Column(name = "benefit_type")
    @Enumerated(EnumType.STRING)
    val benefitType: OnboardBenefitType,

    @Column(name = "benefit_limit")
    val benefitLimit: BigDecimal,

    @Column(name = "benefit_monthly_amount")
    val benefitMonthlyAmount: BigDecimal? = BigDecimal(0),

    @Column(name = "benefit_utilization")
    val benefitUtilization: BigDecimal? = BigDecimal(0),

    @ManyToOne
    @JoinColumn(name = "principal_id", nullable = true)
    val principal: OnboardPrincipal,

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    var createdAt: LocalDate? = LocalDate.now(),
)

@Entity
@Table(name = "onboard_agent")
data class OnboardAgent(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    val id: Long = 0,

    @Column(name = "name")
    val name: String,

    @Column(name = "agent_number", unique = true)
    val agentNumber: String,

    @Column(name = "id_number")
    val idNumber: String? = null,

    @Column(name = "factory")
    val factory: String,

    @Column(name = "email_address")
    val emailAddress: String? = null,

    @Column(name = "date_of_birth")
    val dateOfBirth: String? = null,

    @Column(name = "registration_date")
    val registrationDate: String? = null,

    @Column(name = "zone")
    val zone: String,

    @Column(name = "mobile_number")
    val mobileNumber: String,

    @OneToMany(cascade = [CascadeType.ALL], mappedBy = "agent", fetch = FetchType.LAZY)
    val principals: List<OnboardPrincipal> = mutableListOf(),

    @Column(name = "created_at", nullable = false, updatable = false)
    var createdAt: LocalDate? = LocalDate.now(),
)

@Entity
@Table(name = "user_roles_audit_logs")
data class UserRolesAuditLog(
    @Id @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: Long = 0,

    @Column(name = "user_id")
    val userId: String,

    @Column(name = "actioned_by")
    val actionedBy: String,

    @Column(name = "event_timestamp", nullable = false)
    val eventTimestamp: ZonedDateTime = ZonedDateTime.now(ZoneId.of("Africa/Nairobi")),

    @ElementCollection
    @CollectionTable(
        name = "user_roles_audit_added",
        joinColumns = [JoinColumn(name = "audit_log_id")]
    )
    @Column(name = "role")
    val addedRoles: MutableList<String> = mutableListOf(),

    @ElementCollection
    @CollectionTable(
        name = "user_roles_audit_removed",
        joinColumns = [JoinColumn(name = "audit_log_id")]
    )
    @Column(name = "role")
    val removedRoles: MutableList<String> = mutableListOf(),

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    val createdAt: ZonedDateTime = ZonedDateTime.now(ZoneId.of("Africa/Nairobi")),
)

@Entity
@Table(name = "onboard_principal_application_form_context_id")
data class OnboardPrincipalApplicationFormContextId(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    val id: Long = 0,

    @Column(name = "value", nullable = false)
    val value: String,

    @ManyToOne
    @JoinColumn(name = "principal_id", nullable = false)
    val principal: OnboardPrincipal,

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    val createdAt: LocalDate = LocalDate.now()
)

@Entity
@Table(name = "onboard_principal_application_form_info_id")
data class OnboardPrincipalApplicationFormInfoId(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    val id: Long = 0,

    @Column(name = "value", nullable = false)
    val value: String,

    @ManyToOne
    @JoinColumn(name = "principal_id", nullable = false)
    val principal: OnboardPrincipal,

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    val createdAt: LocalDate = LocalDate.now()
)

@Entity
@Table(name = "onboard_principal_hospital_form_context_id")
data class OnboardPrincipalHospitalFormContextId(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    val id: Long = 0,

    @Column(name = "value", nullable = false)
    val value: String,

    @ManyToOne
    @JoinColumn(name = "principal_id", nullable = false)
    val principal: OnboardPrincipal,

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    val createdAt: LocalDate = LocalDate.now()
)

@Entity
@Table(name = "onboard_principal_hospital_form_info_id")
data class OnboardPrincipalHospitalFormInfoId(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    val id: Long = 0,

    @Column(name = "value", nullable = false)
    val value: String,

    @ManyToOne
    @JoinColumn(name = "principal_id", nullable = false)
    val principal: OnboardPrincipal,

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    val createdAt: LocalDate = LocalDate.now()
)

@Entity
@Table(name = "onboard_principal_student_id_context_id")
data class OnboardPrincipalStudentIdContextId(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    val id: Long = 0,

    @Column(name = "value", nullable = false)
    val value: String,

    @ManyToOne
    @JoinColumn(name = "principal_id", nullable = false)
    val principal: OnboardPrincipal,

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    val createdAt: LocalDate = LocalDate.now()
)

@Entity
@Table(name = "onboard_principal_student_id_info_id")
data class OnboardPrincipalStudentIdInfoId(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    val id: Long = 0,

    @Column(name = "value", nullable = false)
    val value: String,

    @ManyToOne
    @JoinColumn(name = "principal_id", nullable = false)
    val principal: OnboardPrincipal,

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    val createdAt: LocalDate = LocalDate.now()
)
