package net.lctafrica.membership.api.domain

import java.time.LocalDateTime
import javax.persistence.*

@Entity
@Table(name = "custom_roles")
data class CustomRole(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: Long = 0,

    @Column(name = "name", nullable = false)
    val name: String,

    @Column(name = "description", length = 550)
    val description: String,

    @Column(name = "is_predefined")
    val isPredefined: Boolean = false,

    @Column(name = "payer_id")
    val payerId: Long,

    @ElementCollection(fetch = FetchType.EAGER)
    @CollectionTable(
        name = "custom_role_permissions",
        joinColumns = [Join<PERSON><PERSON>umn(name = "custom_role_id")]
    )
    @Column(name = "permission")
    val permissions: MutableList<String> = mutableListOf(),

    @Column(name = "created_by", nullable = false)
    val createdBy: String,

    @Column(name = "created_at", nullable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @Column(name = "updated_at", nullable = true)
    var updatedAt: LocalDateTime? = null
)


@Entity
@Table(name = "user_groups")
data class UserGroup(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: Long = 0,

    @Column(name = "keycloak_group_id", nullable = false)
    var keycloakGroupId: String,

    @Column(name = "name", nullable = false)
    val name: String,

    @Column(name = "description", length = 550)
    val description: String,

    @Column(name = "payer_id", nullable = false)
    val payerId: Long,

    @ManyToMany
    @JoinTable(
        name = "user_group_custom_roles",
        joinColumns = [JoinColumn(name = "user_group_id")],
        inverseJoinColumns = [JoinColumn(name = "custom_role_id")]
    )
    val customRoles: MutableList<CustomRole> = mutableListOf(),

    @ElementCollection(fetch = FetchType.EAGER)
    @CollectionTable(
        name = "user_group_predefined_roles",
        joinColumns = [JoinColumn(name = "user_group_id")]
    )
    @Column(name = "predefined_role")
    val predefinedRoles: MutableList<String> = mutableListOf(),

    @Column(name = "created_by", nullable = false)
    val createdBy: String,

    @Column(name = "created_at", nullable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @Column(name = "updated_at", nullable = true)
    var updatedAt: LocalDateTime? = null
)


@Entity
@Table(name = "user_group_members")
data class UserGroupMember(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: Long = 0,

    @ManyToOne
    @JoinColumn(name = "user_group_id", nullable = false)
    val userGroup: UserGroup,

    @Column(name = "user_id", nullable = false)
    val userId: String,

    @Column(name = "added_by", nullable = false)
    val addedBy: String,

    @Column(name = "added_at", nullable = false)
    val addedAt: LocalDateTime = LocalDateTime.now()
)


@Entity
@Table(name = "deny_policies")
data class DenyPolicy(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: Long = 0,

    @Column(name = "name", nullable = false)
    val name: String,

    @Column(name = "reason", nullable = true, length = 550)
    val reason: String? = null,

    @Column(name = "payer_id")
    val payerId: Long,

    @ElementCollection(fetch = FetchType.EAGER)
    @CollectionTable(
        name = "deny_policy_users",
        joinColumns = [JoinColumn(name = "deny_policy_id")]
    )
    @Column(name = "user_id")
    val users: MutableList<String> = mutableListOf(),

    @ElementCollection
    @CollectionTable(
        name = "deny_policy_permissions",
        joinColumns = [JoinColumn(name = "deny_policy_id")]
    )
    @Column(name = "denied_permission")
    val deniedPermissions: MutableList<String> = mutableListOf(),

    @Column(name = "created_by", nullable = false)
    val createdBy: String,

    @Column(name = "created_at", nullable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @Column(name = "updated_at", nullable = true)
    var updatedAt: LocalDateTime? = null,

    @Column(name = "expires_at", nullable = true)
    val expiresAt: LocalDateTime? = null
)


@Entity
@Table(
    name = "user_custom_roles",
    uniqueConstraints = [
        UniqueConstraint(columnNames = ["user_id", "custom_role_id"], name = "user_custom_role_UNQ")
    ]
)
data class UserCustomRole(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: Long = 0,

    @Column(name = "user_id", nullable = false)
    val userId: String,

    @ManyToOne
    @JoinColumn(name = "custom_role_id", nullable = false)
    val customRole: CustomRole,

    @Column(name = "assigned_by", nullable = false)
    val assignedBy: String,

    @Column(name = "assigned_at", nullable = false)
    val assignedAt: LocalDateTime = LocalDateTime.now()
)
