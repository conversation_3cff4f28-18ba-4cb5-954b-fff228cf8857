package net.lctafrica.membership.api.domain

import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import java.util.*

interface CustomRoleRepository : JpaRepository<CustomRole, Long> {
    fun findByPayerId(payerId: Long, pageable: Pageable): Page<CustomRole>
    fun existsByNameAndPayerId(name: String, payerId: Long): Boolean

    @Query("SELECT cr FROM CustomRole cr WHERE cr.payerId = :payerId AND LOWER(cr.name) LIKE LOWER(CONCAT('%', :name, '%'))")
    fun findByNameContainingIgnoreCaseAndPayerId(
        @Param("name") name: String,
        @Param("payerId") payerId: Long,
        pageable: Pageable
    ): Page<CustomRole>
}

interface UserGroupRepository : JpaRepository<UserGroup, Long> {
    fun findByPayerId(payerId: Long, pageable: Pageable): Page<UserGroup>
    fun existsByNameAndPayerId(name: String, payerId: Long): Boolean

    @Query("SELECT ug FROM UserGroup ug WHERE ug.payerId = :payerId AND LOWER(ug.name) LIKE LOWER(CONCAT('%', :name, '%'))")
    fun findByNameContainingIgnoreCaseAndPayerId(
        @Param("name") name: String,
        @Param("payerId") payerId: Long,
        pageable: Pageable
    ): Page<UserGroup>
}

interface UserGroupMemberRepository : JpaRepository<UserGroupMember, Long> {
    fun findByUserId(userId: String): List<UserGroupMember>

    @Query("SELECT ugm FROM UserGroupMember ugm WHERE ugm.userGroup.id = :groupId")
    fun findByGroupId(@Param("groupId") groupId: Long): List<UserGroupMember>

    @Query("SELECT ugm FROM UserGroupMember ugm WHERE ugm.userGroup.id = :groupId AND ugm.userId = :userId")
    fun findByGroupIdAndUserId(
        @Param("groupId") groupId: Long,
        @Param("userId") userId: String
    ): Optional<UserGroupMember>

    @Query("SELECT CASE WHEN COUNT(ugm) > 0 THEN true ELSE false END FROM UserGroupMember ugm WHERE ugm.userGroup.id = :groupId AND ugm.userId = :userId")
    fun existsByGroupIdAndUserId(@Param("groupId") groupId: Long, @Param("userId") userId: String): Boolean
}


interface UserCustomRoleRepository : JpaRepository<UserCustomRole, Long> {
    fun findByUserId(userId: String): List<UserCustomRole>

    @Query("SELECT CASE WHEN COUNT(ucr) > 0 THEN true ELSE false END FROM UserCustomRole ucr WHERE ucr.userId = :userId AND ucr.customRole.id = :roleId")
    fun existsByUserIdAndRoleId(@Param("userId") userId: String, @Param("roleId") roleId: Long): Boolean

    @Query("SELECT ucr FROM UserCustomRole ucr WHERE ucr.customRole.id = :roleId")
    fun findByRoleId(@Param("roleId") roleId: Long): List<UserCustomRole>

    @Query("SELECT ucr FROM UserCustomRole ucr WHERE ucr.customRole.id = :roleId")
    fun findByRoleIdPaginated(@Param("roleId") roleId: Long, pageable: Pageable): Page<UserCustomRole>
}

interface DenyPolicyRepository : JpaRepository<DenyPolicy, Long> {
    @Query("SELECT dp FROM DenyPolicy dp JOIN dp.users u WHERE u = :userId")
    fun findByUserId(@Param("userId") userId: String): List<DenyPolicy>

    fun findByPayerId(@Param("payerId") payerId: Long, pageable: Pageable): Page<DenyPolicy>

    @Query("SELECT dp FROM DenyPolicy dp JOIN dp.users u WHERE u = :userId AND dp.payerId = :payerId")
    fun findByUserIdAndPayerId(
        @Param("payerId") payerId: Long,
        @Param("userId") userId: String,
        pageable: Pageable
    ): Page<DenyPolicy>

    @Query("SELECT dp FROM DenyPolicy dp JOIN dp.users u" +
            " WHERE u = :userId " +
            "AND (dp.expiresAt IS NULL OR dp.expiresAt > CURRENT_TIMESTAMP)")
    fun findActiveByUserId(@Param("userId") userId: String): List<DenyPolicy>

    @Query(value = """
        SELECT dpu.user_id
        FROM deny_policy_users dpu
        INNER JOIN deny_policies dp ON dpu.deny_policy_id = dp.id
        WHERE dp.id = :policyId AND dp.payer_id = :payerId
        ORDER BY dpu.user_id
    """,
    countQuery = """
        SELECT COUNT(dpu.user_id)
        FROM deny_policy_users dpu
        INNER JOIN deny_policies dp ON dpu.deny_policy_id = dp.id
        WHERE dp.id = :policyId AND dp.payer_id = :payerId
    """,
    nativeQuery = true)
    fun findUserIdsByPolicyIdAndPayerId(
        @Param("policyId") policyId: Long,
        @Param("payerId") payerId: Long,
        pageable: Pageable
    ): Page<String>

    @Query("SELECT COUNT(dp) > 0 FROM DenyPolicy dp WHERE dp.id = :policyId AND dp.payerId = :payerId")
    fun existsByIdAndPayerId(@Param("policyId") policyId: Long, @Param("payerId") payerId: Long): Boolean

    @Query("SELECT CASE WHEN COUNT(dp) > 0 THEN true ELSE false END FROM DenyPolicy dp JOIN dp.users u" +
            " WHERE u = :userId")
    fun existsByUserId(@Param("userId") userId: String): Boolean
}
