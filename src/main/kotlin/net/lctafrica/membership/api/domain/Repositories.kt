package net.lctafrica.membership.api.domain

import java.time.LocalDate
import java.time.LocalDateTime
import java.util.Optional
import net.lctafrica.membership.api.dtos.BeneficiaryProviderMappingProjectionDTO
import net.lctafrica.membership.api.dtos.BenefitAdminPlansProjectionDTO
import net.lctafrica.membership.api.dtos.BenefitPayerMappingDTO
import net.lctafrica.membership.api.dtos.BenefitProviderMappingProjectionDTO
import net.lctafrica.membership.api.dtos.BranchesDTO
import net.lctafrica.membership.api.dtos.FindBenefitsByServiceGroupsProjectionDTO
import net.lctafrica.membership.api.dtos.FingerPrintProjectionDTO
import net.lctafrica.membership.api.dtos.GetPlanFromBenefitProjectionDTO
import net.lctafrica.membership.api.dtos.JubileePayerProviderCodeMappingDTO
import net.lctafrica.membership.api.dtos.KeycloakUserResolve
import net.lctafrica.membership.api.dtos.NearbyProvidersDTO
import net.lctafrica.membership.api.dtos.PayerBenefitCodeMappingDTO
import net.lctafrica.membership.api.dtos.PayerProviderMappingProjectionWithRestrictionCheckDTO
import net.lctafrica.membership.api.dtos.PayerSearchDTO
import net.lctafrica.membership.api.dtos.ProviderRestrictionSetProjectionDTO
import net.lctafrica.membership.api.dtos.RequestServiceProjectionDto
import net.lctafrica.membership.api.dtos.RequestTypeProjectionDto
import net.lctafrica.membership.api.dtos.ServiceDto
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param


interface PayerRepository : JpaRepository<Payer, Long> {
	fun findByNameLike(name: String): MutableList<Payer>
	fun findByType(type: PayerType): MutableList<Payer>
	fun findByType(type: PayerType, pageable: Pageable): Page<Payer>
	fun findByNameIgnoreCase(name: String): Optional<Payer>

	@Query(value = "SELECT p FROM Payer p WHERE p.type IN(:types)")
	fun findAdmins(@Param("types") types: List<PayerType>): MutableList<Payer>

	@Query("SELECT p FROM Payer p WHERE p.id IN (:ids)")
	fun findByIds(
		@Param("ids") ids: List<Long>
	):List<Payer>

	@Query(
		"SELECT p FROM Beneficiary b INNER JOIN b.category c INNER JOIN c.benefits bf INNER JOIN bf.payer p " +
				"WHERE b.id = :beneficiaryId GROUP BY b.id, p.id"
	)
	fun findPayerByBeneficiaryId(
		@Param("beneficiaryId") beneficiaryId: Long
	): MutableList<Payer>

	@Query("SELECT p FROM Benefit b INNER JOIN b.payer p WHERE b.category.id = :categoryId GROUP BY b.category.id, b.payer.id")
	fun getPayersByCategoryId(@Param("categoryId") categoryId: Long) : MutableList<Payer>

}

interface PayerConfigRepository : JpaRepository<PayerConfig, Long> {
	fun findByPayerAndConfig(payer:Payer, config: ConfigCatalog) : Optional<PayerConfig>
}

interface PlanRepository : JpaRepository<Plan, Long> {
	@Query(
		"select * from plan where plan_id in ( select plan_id from policy where policy_id in ( " +
				"select DISTINCT policy_id from category where category_id in ( select DISTINCT " +
				"category_id FROM benefit where payer_id = ?1 ) ) )", nativeQuery = true
	)
	fun findByPayer(@Param("payerId") payerId: Long): MutableList<Plan>

	@Query(
			"select p.* from plan p where p.plan_id in (select po.plan_id from policy po where po.end_date > ?1)", nativeQuery = true
	)
	fun findActiveSchemes(endDate:LocalDateTime): MutableList<Plan>

	fun findByNameIgnoreCase(name: String): Optional<Plan>
}

interface PolicyRepository : JpaRepository<Policy, Long> {
	fun findByPlan(plan: Plan): MutableList<Policy>

	@Query("select p from Policy p inner join p.categories c inner join c.benefits b where b.payer =:payer group by p.id order by p.id desc")
	fun findByPayer(payer: Payer): MutableList<Policy>
	fun findByPolicyNumber(policyNumber: String): Optional<Policy>
	@Query("select p from Policy p inner join p.categories c where c.id in (:categoryIds)")
	fun findPoliciesByCategoryIds(@Param("categoryIds") categoryIds: Set<Long>): MutableList<Policy>
}

interface CategoryRepository : JpaRepository<Category, Long> {
	@Query(value = "SELECT * FROM category WHERE category_id = ?1", nativeQuery = true)
	fun findByCategoryId(categoryId: Long): Optional<Category>
	fun findByPolicy(policy: Policy): MutableList<Category>
	@Query("select c from Category c where c.policy.id in (:ids) order by c.id desc")
	fun findByPolicyIds(ids: Set<Long>): MutableList<Category>

	@Query("select c from Category c where c.policy.plan.id in (:ids) order by c.id desc")
	fun findByPlanIds(ids: Set<Long>): MutableList<Category>

	@Query(value = "SELECT c FROM Category c JOIN FETCH c.policy p JOIN FETCH p.plan l WHERE c.id = :categoryId")
	fun fetchWithPolicy(@Param("categoryId") categoryId: Long): Category?

	@Query(value = "SELECT c FROM Category c WHERE c.policy = :policy AND UPPER(c.name)=UPPER(:name)")
	fun searchByPolicyAndName(
		@Param("policy") policy: Policy,
		@Param("name") name: String
	): Category?

	@Query(value = "SELECT c FROM Category c WHERE c.policy.id = :policyId AND UPPER(c.name)=UPPER(:categoryName)")
	fun searchByPolicyIdAndCategoryName(
		@Param("policyId") policyId: Long,
		@Param("categoryName") categoryName: String
	): Category?

	@Query(value = "SELECT c FROM Category c WHERE c.policy.id = :policyId AND UPPER(c.name) IN (:catNames)")
	fun findByPolicyIdAndNameIn(
		@Param("policyId") policyId: Long,
		@Param("catNames") catNames: MutableList<String>
	): MutableList<Category>

	@Query(value = "SELECT c FROM Category c WHERE c.id in (:categoryIds)")
	fun findByCategoryIds(
		@Param("categoryIds") categoryIds: List<Long>
	): MutableList<Category>

}

interface ConfigCatalogRepository : JpaRepository<ConfigCatalog, Long> {

}
interface CategoryConfigRepository : JpaRepository<CategoryConfig, Long> {
	@Query("select c from CategoryConfig c inner join c.config cnf where cnf.name = :cnfName and cnf.cnfGroup = :cnfGroup")
	fun findByConfigNameAndGroup(
		@Param("cnfName") cnfName: String,
		@Param("cnfGroup") cnfGroup: ConfigGroup
	):MutableList<CategoryConfig>
}


interface BenefitRepository : JpaRepository<Benefit, Long> {
	fun findByCategory(category: Category): MutableList<Benefit>
	fun findByCategory(category: Category, request: Pageable): Page<Benefit>
	fun findByCategoryAndNameIgnoreCase(category: Category, name: String): Optional<Benefit>

	@Query(
		value = "SELECT b FROM Benefit b WHERE b.category = :category AND UPPER(b" +
				".name) LIKE" +
				" " +
				"concat('%',:name,'%')"
	)
	fun findByCategoryAndBenefitName(
		@Param("category") category: Category, @Param("name") name:
		String, request: Pageable
	): Page<Benefit>

	@Query(value = "SELECT b FROM Benefit b WHERE b.parentBenefit IS NULL AND b.category = :category")
	fun findMainBenefitsByCategoryShallow(@Param("category") category: Category): MutableList<Benefit>

	@Query(value = "SELECT DISTINCT b FROM Benefit b LEFT JOIN b.subBenefits s WHERE b.parentBenefit IS NULL AND b.category = :category")
	fun findMainBenefitsByCategory(@Param("category") category: Category): MutableList<Benefit>

	@Query(value = "SELECT DISTINCT b FROM Benefit b  LEFT JOIN b.subBenefits s WHERE b.parentBenefit IS NULL AND b.category = :category AND b.processed = FALSE")
	fun findUnprocessedMainBenefitsByCategory(@Param("category") category: Category): MutableList<Benefit>

	@Query(value = "SELECT b FROM Benefit b WHERE b.category = :category AND b.benefitRef = :ref")
	fun findByCategoryAndRef(
		@Param("category") category: Category,
		@Param("ref") ref: BenefitCatalog
	): Optional<Benefit>

	@Query(value = "SELECT b FROM Benefit b WHERE b.payer.id = :payerId AND b.benefitRef.id = :catalogId")
	fun findByPayerAndCatalog(
		@Param("payerId") payerId: Long,
		@Param("catalogId") catalogId: Long
	): MutableList<Benefit>

	@Query(value = "SELECT * FROM benefit WHERE benefit_id = ?1", nativeQuery = true)
	fun findByBenefitId(@Param("benefit_id") benefit_id: String): Optional<Benefit>

	@Query(value = "SELECT b FROM Benefit b WHERE b.id IN (:ids)")
	fun findPayersByBenefitIds(@Param("ids") benefitIds: Collection<Long>): MutableList<BenefitPayerMappingDTO>

	@Query(value = "SELECT b FROM Benefit b WHERE b.payer.id = :payerId")
	fun findByPayerId(@Param("payerId") payerId: Long) : MutableList<Benefit>
	@Query(value = "SELECT b FROM Benefit b WHERE b.payer = :payer AND b.id IN (:benefitIds)")
	fun findByPayerAndBenefitIds(
		@Param("payer") payer: Payer,
		@Param("benefitIds") benefitIds: Set<Long>): MutableList<Benefit>

//	@Query(value = "SELECT b FROM Benefit b WHERE b.payer = :payer AND b.mappingCode = :mappingCode")
//	fun findByMappingCode(
//		@Param("payer") payer: Payer,
//		@Param("mappingCode") mappingCode: String): MutableList<Benefit>

	@Query("SELECT aggregate_id as aggregateId FROM claims.benefit_beneficiary WHERE category_id " +
			"=" +
			" ?1 AND benefit_id = ?2 " +
			"AND beneficiary_id = ?3", nativeQuery = true)
	fun findMainBenefitAggregateId(@Param("categoryId") categoryId: Long,@Param("benefitId")
	benefitId: Long,@Param("beneficiaryId") beneficiaryId: Long):Optional<ClaimsBenefit>

	@Query("select b from Benefit b where b.id in (:ids)")
	fun findPlanByBenefitIds(@Param("ids") benefitIds: Collection<Long>) :MutableList<GetPlanFromBenefitProjectionDTO>

	@Query("select b from Benefit b where b.benefitRef.serviceGroup in (:serviceGroups)")
	fun findByServiceGroups(@Param("serviceGroups") serviceGroups: Collection<ServiceGroup>) :MutableList<FindBenefitsByServiceGroupsProjectionDTO>

	@Query("select bf from Benefit bf inner join bf.category c inner join c.policy p where p.policyAdmin.id = :policyAdminId")
	fun findByPolicyAdminId(@Param("policyAdminId") policyAdminId: Long): MutableList<Benefit>
}

interface BeneficiaryRepository : JpaRepository<Beneficiary, Long> {
	fun findByCategory(category: Category, request: Pageable): Page<Beneficiary>

	fun findByCategory(category: Category): MutableList<Beneficiary>

	@Query("SELECT b FROM Beneficiary b WHERE b.otherNumber = :otherNumber AND b.id != :excludeBeneficiaryId")
	fun findByOtherNumberAndIdNot(
		@Param("otherNumber") otherNumber: String,
		@Param("excludeBeneficiaryId") excludeBeneficiaryId: Long
	): MutableList<Beneficiary>


	@Query("select b from Beneficiary b where b.category = :category and b.beneficiaryType = :beneficiaryType and b.dob < :aboveDob and b.status = :memberStatus and (b.exemptedFromCoverAgeLimit is null or b.exemptedFromCoverAgeLimit = false)")
	fun findBeneficiariesToExemptFromCover(
		@Param("category") category: Category,
		@Param("beneficiaryType") beneficiaryType: BeneficiaryType,
		@Param("aboveDob") aboveDob: LocalDate,
		@Param("memberStatus") memberStatus: MemberStatus
	): MutableList<Beneficiary>

	fun findByCategoryAndProcessed(
		category: Category,
		processed: Boolean
	): MutableList<Beneficiary>
	fun findByCategoryAndBeneficiaryTypeAndProcessed(
		category: Category,
		type: BeneficiaryType,
		processed: Boolean
	): MutableList<Beneficiary>

	fun findByPrincipal(principal:Beneficiary): MutableList<Beneficiary>

	@Query(value = "SELECT DISTINCT b FROM Beneficiary b LEFT JOIN b.dependants d WHERE b.principal IS NULL AND b.category = :category")
	fun findFamiliesByCategory(@Param("category") category: Category): MutableList<Beneficiary>

	@Query(
		value = "SELECT DISTINCT b FROM Beneficiary b LEFT JOIN b.dependants d WHERE b.principal IS NULL AND b.category = :category AND b.memberNumber = :memberNumber"
	)
	fun findFamilyByCategoryAndMemberNumber(
		@Param("category") category: Category,
		@Param("memberNumber") memberNumber: String
	): MutableList<Beneficiary>

	@Query(
		value = "SELECT DISTINCT b FROM Beneficiary b LEFT JOIN b.dependants d WHERE b.principal IS NULL AND b.category = :category AND b.memberNumber like concat(:familyNumber,'%')"
	)
	fun findPrincipalByCategoryAndFamilyNumberLike(
		@Param("category") category: Category,
		@Param("familyNumber") familyNumber: String
	): MutableList<Beneficiary>

	@Query(
		value = "SELECT DISTINCT b FROM Beneficiary b LEFT JOIN b.dependants d WHERE b.category = :category AND b.memberNumber = :memberNumber"
	)
	fun findBeneficiaryByCategoryAndMemberNumber(
		@Param("category") category: Category,
		@Param("memberNumber") memberNumber: String
	): MutableList<Beneficiary>

	@Query(
		value = "SELECT DISTINCT b FROM Beneficiary b LEFT JOIN b.dependants d WHERE b.category.name = :categoryName AND b.memberNumber = :memberNumber"
	)
	fun findBeneficiaryByCategoryNameAndMemberNumber(
		@Param("categoryName") categoryName: String,
		@Param("memberNumber") memberNumber: String
	): MutableList<Beneficiary>

	@Query(value = "SELECT DISTINCT b FROM Beneficiary b LEFT JOIN b.dependants d WHERE b.principal IS NULL AND b.processed = FALSE AND b.category = :category")
	fun findUnprocessedFamiliesByCategory(@Param("category") category: Category): MutableList<Beneficiary>

	@Query(
		value = """
        SELECT DISTINCT b FROM Beneficiary b LEFT JOIN b.principal p WHERE b.principal IS NOT NULL AND b.processed = FALSE
            AND p.processed = TRUE AND b.category = :category
    """
	)
	fun findUnprocessedDependentsByCategory(category: Category): MutableList<Beneficiary>

	@Query(value ="select * from beneficiary where member_number = ?1 and status = 'ACTIVE'", nativeQuery = true)
	fun findByMemberNumberAndActive(@Param(value = "memberNumber") memberNumber: String):
			MutableList<Beneficiary>


	@Query(value ="SELECT b.* from beneficiary b inner join category c on b.category_id =c.category_id inner join policy p \n" +
			"on c.policy_id =p.policy_id INNER JOIN plan s on p.plan_id = s.plan_id inner join benefit bt on c.category_id =bt.category_id \n" +
			"where (b.member_number like CONCAT(:search,'%') or b.beneficiary_name like CONCAT('%',:search,'%') " +
			"or b.other_number like CONCAT(:search,'%') \n" +
			"or b.nhif_number like CONCAT(:search,'%') or b.phone_number like CONCAT(:search,'%')) \n" +
			"and ((s.`type`='SCHEME' and CURDATE() BETWEEN p.start_date and p.end_date) or \n" +
			"(s.`type`='RETAIL' and CURDATE() BETWEEN b.join_date and DATE_ADD(DATE_ADD(b.join_date, INTERVAL 1 YEAR),INTERVAL -1 DAY))) \n" +
			"and b.migrated = false group by b.beneficiary_id ORDER by b.beneficiary_id desc limit 20", nativeQuery = true)
	fun searchByNameOrMemberNumberAndActive(@Param(value = "search") search: String):
			MutableList<Beneficiary>

	@Query(
			value = "select * from beneficiary where member_number = ?1 and dob=?2 and status = 'ACTIVE'", nativeQuery = true
	)
	fun findByMemberNumberAndDateOfBirthAndActive(memberNumber: String, dateOfBirth: String):
			MutableList<Beneficiary>

	@Query(
			value = "SELECT * FROM membership.beneficiary b where b.member_number=?1 and b.status = 'ACTIVE' and b.category_id in (select c.category_id from category c where c.policy_id in (select p.policy_id from policy p where p.plan_id = ?2 and p.end_date >= ?3))", nativeQuery = true
	)
	fun findBySchemeIdAndActive(memberNumber: String, schemeId: Int, endDate:LocalDateTime):
			MutableList<Beneficiary>


	@Query(
			value = "select * from beneficiary where beneficiary_id = ?1 and status = 'ACTIVE'", nativeQuery = true
	)
	fun findByBeneficiaryIdAndActive(beneficiaryId: Long):
			MutableList<Beneficiary>

	@Query(value = "SELECT b FROM Beneficiary b where b.id = :beneficiaryId and b.status = 'ACTIVE'")
	fun searchByBeneficiaryIdAndStatus(@Param(value = "beneficiaryId") beneficiaryId: Long): Optional<Beneficiary>
	@Query("""
		select b from Beneficiary b inner join b.category c INNER JOIN c.benefits bf INNER JOIN bf.payer p 
		where p.id = :payerId and (b.memberNumber like CONCAT(:search,'%') or b.name like CONCAT('%',:search,'%') 
		or b.nhifNumber like CONCAT(:search,'%') or b.otherNumber like CONCAT(:search,'%') or b.phoneNumber like CONCAT(:search,'%') or b.jicEntityId 
		like CONCAT(:search,'%') or b.apaEntityId like CONCAT(:search,'%') or b.idNumber like CONCAT(:search,'%')) 
		group by b.id order by b.id desc
	""")
	fun searchByPayerIdNameOrMemberNumber(
		@Param(value = "payerId") payerId: Long,
		@Param(value = "search") search: String
	): MutableList<Beneficiary>

	@Query(
		value = "select*from beneficiary where category_id IN ( select DISTINCT category_id from category where category_id in (" +
				"select DISTINCT category_id FROM benefit where payer_id = ?1 ) ) AND ((beneficiary" +
				".beneficiary_name LIKE concat('%',?2,'%') and " +
				"LENGTH(?2) >=4)OR(beneficiary.member_number LIKE concat('%',?2,'%') " +
				"and LENGTH(?2) >=4) or (b.other_number like concat('%',?2,'%') and LENGTH(?2) >=4)) and status = 'ACTIVE'",
		nativeQuery = true
	)
	fun searchByPayerIdNameOrMemberNumberFuzzySearch(
		@Param(value = "payerId") payerId: Long,
		@Param(value = "search") search: String
	): MutableList<Beneficiary>


	@Query(
		value = "select b.beneficiary_id as id,b.dob,b.gender,pn.plan_name as scheme, b" +
				".category_id as categoryId, b" +
				".member_number as memberNumber, b" +
				".other_number as otherNumber, b" +
				".beneficiary_name as name, b.phone_number as phoneNumber, " +
				"b.status, b.beneficiary_type as beneficiaryType from beneficiary b " +
				"inner join membership.category c on c.category_id = b.category_id " +
				"inner join membership.policy p on p.policy_id = c.policy_id " +
				"inner join membership.plan pn on pn.plan_id = p.plan_id " +
				"where b.category_id IN ( select DISTINCT category_id from category where category_id " +
				"in ( select DISTINCT b.category_id FROM benefit where payer_id = ?1 ) ) " +
				"AND ( ( b.beneficiary_name LIKE concat('%', ?2,'%') and  LENGTH(?2) > 4 ) OR( b" +
				".member_number LIKE concat('%', ?2,'%') and LENGTH(?2) > 4 ) " +
				"OR ( b.other_number LIKE concat('%', ?2,'%') and LENGTH(?2) > 4 ) ) and b.status ='ACTIVE' and pn.plan_id = ?3",
		nativeQuery = true
	)
	fun searchByPayerIdPlanIdNameOrMemberNumber(
		@Param(value = "payerId") payerId: Long,
		@Param(value = "search") search: String,
		@Param(value = "planId") planId: Long
	): MutableList<PayerSearchDTO>


	@Query(value = "SELECT b FROM Beneficiary b WHERE b.category = :category AND b.memberNumber= :memberNumber")
	fun findByCategoryAndBeneficiary(
		@Param(value = "category") category: Category,
		@Param(value = "memberNumber") memberNumber: String
	): Optional<Beneficiary>

	@Query(value = "SELECT b FROM Beneficiary b WHERE b.category.policy.plan.id = :planId AND b.memberNumber = :memberNumber order by b.id desc")
	fun findByPlanIdAndMemberNumber(
		@Param(value = "planId") planId: Long,
		@Param(value = "memberNumber") memberNumber: String
	): MutableList<Beneficiary>

	@Query(
		value = "SELECT b FROM Beneficiary b WHERE b.category = :category AND UPPER(b" +
				".name) LIKE" +
				" " +
				"concat('%',:beneficiaryName,'%')"
	)
	fun findByCategoryAndBeneficiaryName(
		@Param("category") category: Category,
		@Param("beneficiaryName") beneficiaryName: String,
		request: Pageable
	): Page<Beneficiary>

	@Query(value = "SELECT DISTINCT b FROM Beneficiary b WHERE b.category = :category AND b.principal IS NULL")
	fun findPrincipalsByCategory(@Param(value = "category") category: Category): MutableList<Beneficiary>

	@Query(
		"SELECT b FROM Beneficiary b INNER JOIN b.category c INNER JOIN c.benefits bf INNER JOIN bf.payer p " +
				"WHERE SUBSTRING(b.phoneNumber, -9) = SUBSTRING(:phoneNumber,-9) and b.beneficiaryType = :beneficiaryType " +
				"GROUP BY b.id"
	)
	fun findPrincipalCoversByPhoneNumber(
		@Param("phoneNumber") phoneNumber: String,
		@Param("beneficiaryType") beneficiaryType: BeneficiaryType,
		request: Pageable
	): Page<Beneficiary>


	@Query(
		"SELECT b FROM Beneficiary b INNER JOIN BeneficiaryLink bl on (b.id = bl.beneficiary.id) " +
				"WHERE SUBSTRING(bl.phoneNumber, -9) = SUBSTRING(:phoneNumber,-9) AND bl.status is TRUE"
	)
	fun findLinkedDependantCoversByPhoneNumber(
		@Param("phoneNumber") phoneNumber: String, request: Pageable
	): Page<Beneficiary>

	@Query(
		"""
        SELECT * FROM(SELECT b.* FROM beneficiary b INNER JOIN category c on b.category_id = c.category_id 
        INNER JOIN benefit bf on c.category_id = bf.category_id INNER JOIN payer p on p.payer_id = bf.payer_id 
        INNER JOIN policy po on c.policy_id = po.policy_id WHERE curdate() BETWEEN po.start_date AND po.end_date AND 
		SUBSTRING(b.phone_number, -9) = SUBSTRING(:phoneNumber,-9) 
		and b.beneficiary_type = 'PRINCIPAL' AND b.status = 'ACTIVE' GROUP BY b.beneficiary_id 
		UNION SELECT b.* FROM beneficiary b INNER JOIN beneficiary_link bl on (b.beneficiary_id  = bl.beneficiary_id) 
		INNER JOIN category c on b.category_id = c.category_id INNER JOIN policy po on c.policy_id = po.policy_id 
		WHERE curdate() BETWEEN po.start_date AND po.end_date AND SUBSTRING(bl.phone_number, -9) = SUBSTRING(:phoneNumber,-9) 
        AND b.status = 'ACTIVE' AND bl.status is TRUE) as tb LIMIT :page,:size
        """,
		nativeQuery = true
	)
	fun findCoversByPhoneNumber(
		@Param("phoneNumber") phoneNumber: String,
		@Param("page") page: Int,
		@Param("size") size: Int,
	): List<Beneficiary>

	@Query(
		"""
		select b from Beneficiary b INNER JOIN b.category.benefits bf 
		where (:payer is null or bf.payer = :payer)
		and (COALESCE(:policyIds) is null or b.category.policy.id in (:policyIds)) 
		and (COALESCE(:idNumbers) is null or b.nhifNumber in (:idNumbers)) 
		and (COALESCE(:phoneNumbers) is null or b.phoneNumber in (:phoneNumbers)) 
		and (COALESCE(:planIds) is null or  b.category.policy.plan.id in (:planIds)) 
		and (COALESCE(:categoryIds) is null or  b.category.id in (:categoryIds)) 
		and (COALESCE(:statuses) is null or b.status in (:statuses)) 
		and (COALESCE(:beneficiaryTypes) is null or b.beneficiaryType in (:beneficiaryTypes)) 
		and (:verificationMode is null or b.canUseBiometrics = :verificationMode) 
		and (:fromDate is null or b.joinDate >= :fromDate) 
		and (:toDate is null or b.joinDate <= :toDate) 
		group by b.id order by b.id desc
		"""
	)
	fun filterBeneficiaries(
		@Param("payer")  payer: Payer?,
		@Param("policyIds")  policyIds: Set<Long>?,
		@Param("idNumbers")  idNumbers: Set<String>?,
		@Param("phoneNumbers")  phoneNumbers: Set<String>?,
		@Param("planIds")  planIds: Set<Long>?,
		@Param("categoryIds")  categoryIds: Set<Long>?,
		@Param("statuses") statuses: Set<MemberStatus>?,
		@Param("beneficiaryTypes") beneficiaryTypes: Set<BeneficiaryType>?,
		@Param("verificationMode") verificationMode: Boolean?,
		@Param("fromDate") fromDate: LocalDate?,
		@Param("toDate") toDate: LocalDate?,
		pageable: Pageable
	): Page<Beneficiary>

@Query(
	"""
		select b from Beneficiary b INNER JOIN b.category.benefits bf INNER JOIN b.changeLog cl 
		where (:payer is null or bf.payer = :payer)
		and (COALESCE(:policyIds) is null or b.category.policy.id in (:policyIds)) 
		and (COALESCE(:idNumbers) is null or b.nhifNumber in (:idNumbers)) 
		and (COALESCE(:phoneNumbers) is null or b.phoneNumber in (:phoneNumbers)) 
		and (COALESCE(:planIds) is null or  b.category.policy.plan.id in (:planIds)) 
		and (COALESCE(:categoryIds) is null or  b.category.id in (:categoryIds)) 
		and (COALESCE(:statuses) is null or b.status in (:statuses)) 
		and (COALESCE(:beneficiaryTypes) is null or b.beneficiaryType in (:beneficiaryTypes)) 
		and (:verificationMode is null or b.canUseBiometrics = :verificationMode) 
		and (:changeLogType is null or cl.changeLogType = :changeLogType) 
		and (:fromDate is null or b.joinDate >= :fromDate) 
		and (:toDate is null or b.joinDate <= :toDate) 
		group by b.id order by b.id desc
		"""
)
	fun filterBeneficiaries(
		@Param("payer")  payer: Payer?,
		@Param("policyIds")  policyIds: Set<Long>?,
		@Param("idNumbers")  idNumbers: Set<String>?,
		@Param("phoneNumbers")  phoneNumbers: Set<String>?,
		@Param("planIds")  planIds: Set<Long>?,
		@Param("categoryIds")  categoryIds: Set<Long>?,
		@Param("statuses") statuses: Set<MemberStatus>?,
		@Param("beneficiaryTypes") beneficiaryTypes: Set<BeneficiaryType>?,
		@Param("verificationMode") verificationMode: Boolean?,
		@Param("changeLogType") changeLogType: ChangeLogType?,
		@Param("fromDate") fromDate: LocalDate?,
		@Param("toDate") toDate: LocalDate?,
		pageable: Pageable
	): Page<Beneficiary>

	@Query("select b from Beneficiary b where b.category.policy.plan.id = :planId and b.memberNumber = :memberNumber")
	fun findBeneficiaryCoverPeriods(
		@Param("planId") planId: Long,
		@Param("memberNumber") memberNumber: String,
	): MutableList<Beneficiary>
}

interface BeneficiaryUploadErrorRepository : JpaRepository<BeneficiaryUploadError, Long>
interface BeneficiaryStagingRepository : JpaRepository<BeneficiaryStaging, Long>{
	@Query("select t from BeneficiaryStaging t where t.processed = false and t.memberType = :memberType and t.status = :status and t.error is null")
	fun findByUnprocessedAndByBeneficiaryType(@Param("memberType") memberType:BeneficiaryType, @Param("status") status:StagingStatus, pageable: Pageable): Page<BeneficiaryStaging>
	@Query("select t from BeneficiaryStaging t where t.processed = false and t.memberType <> :memberType and t.status = :status and t.error is null")
	fun findByUnprocessedAndNotBeneficiaryType(@Param("memberType") memberType:BeneficiaryType, @Param("status") status:StagingStatus, pageable: Pageable): Page<BeneficiaryStaging>
	@Query("select t from BeneficiaryStaging t where t.processed = false and t.familyNumber = :familyNumber and t.category = :category and t.memberType <> :memberType")
	fun findByUnprocessedAndFamilyNumberAndCategoryNotPrincipal(
		@Param("familyNumber") familyNumber:String, @Param("category") category:String, @Param("memberType") memberType:BeneficiaryType
	): MutableList<BeneficiaryStaging>

	@Query("select b from BeneficiaryStaging b where b.policyId = :policyId")
	fun filterStagedBeneficiaries(
		@Param("policyId") policyId:Long,
		pageable: Pageable):Page<BeneficiaryStaging>

	@Query("select b from BeneficiaryStaging b where b.memberNumber = :memberNo and b.category = :categoryName and b.policyId = :policyId")
	fun findByMemberNoAndCategoryNameAndPolicyId(
		@Param("memberNo") memberNo:String,
		@Param("categoryName") categoryName:String,
		@Param("policyId") policyId:Long,
	):Optional<BeneficiaryStaging>
}

interface SharedBenefitTrackerRepository : JpaRepository<SharedBenefitTracker, Long> {
	fun findByBeneficiaryAndBenefit(parentBen: Beneficiary, benefit: Benefit): SharedBenefitTracker?
}

interface BenefitCatalogRepository : JpaRepository<BenefitCatalog, Long> {
	fun findByNameLike(search: String, request: Pageable): Page<BenefitCatalog>
	fun findByServiceGroup(serviceGroup: ServiceGroup, request: Pageable): Page<BenefitCatalog>

	@Query(value = "SELECT b FROM BenefitCatalog b WHERE b.id IN (:ids)")
	fun findByIds(@Param("ids") ids: List<Long>): MutableList<BenefitCatalog>

	@Query(
		value = "select b.benefit_ref as serviceId, bc.service_group as serviceGroup from benefit b inner join benefit_catalog bc on bc.service_id = b.benefit_ref where b.benefit_id = ?1",
		nativeQuery = true
	)
	fun findByBenefitId(@Param("benefitId") benefitId: Long): ServiceDto

	@Query("select c from BenefitCatalog c inner join c.benefits b where b.payer.id = :payerId and b.parentBenefit is null group by c.id")
	fun findByPayerId(@Param("payerId") payerId: Long):List<BenefitCatalog>
}

interface CountryRepository : JpaRepository<Country, Long> {
	fun findByNameIgnoreCase(name: String): Country?
}

interface RegionRepository : JpaRepository<Region, Long> {
	fun findByNameIgnoreCase(name: String): Region?
	fun findByCountryAndNameIgnoreCase(country: Country, name: String): Region?
	fun findByCountry(country: Country, request: Pageable): Page<Region>
}

interface ProviderRepository : JpaRepository<Provider, Long> {

	@Query(
		value = "select provider_id as providerId, provider_name as providerName from provider " +
				"where " +
				"main_facility_id = ?1",
		nativeQuery = true
	)
	fun findProviderBranches(@Param("mainFacility") mainFacility: Long): MutableList<BranchesDTO?>

	@Query("SELECT p FROM Provider p WHERE p.id IN (:ids)")
	fun findByIds(
		@Param("ids") ids: List<Long>
	):List<Provider>

	@Query("SELECT p FROM Provider p WHERE p.mainFacility.id IN (:ids)")
	fun findBranchesByProviderIds(
		@Param("ids") ids: Set<Long>
	):List<Provider>
	@Query(
		value = "select * from provider where provider_name LIKE concat('%',?1,'%')",
		nativeQuery = true
	)
	fun searchByName(@Param(value = "search") search: String, request: Pageable):
			Page<Provider>

	fun findByTier(tier: ProviderTier, request: Pageable): Page<Provider>
	fun findByRegion(region: Region, pageable: Pageable): Page<Provider>

	@Query(
		value = "SELECT * FROM (SELECT p.provider_id as providerId, provider_name as providerName, p.latitude , p.longitude , " +
				"created_on as createdOn, r.region_name as regionName, " +
				"111.045 * DEGREES(ACOS(COS(RADIANS(:latitude)) * COS(RADIANS(p.latitude)) * COS(RADIANS(p.longitude) - " +
				"RADIANS(:longitude)) + SIN(RADIANS(:latitude)) * SIN(RADIANS(p.latitude)))) AS distanceInKm \n" +
				"FROM provider p inner join payer_provider_mapping ppm on p.provider_id = ppm .provider_id  \n " +
				"INNER JOIN region r on p.region_id = r.region_id " +
				"WHERE (COALESCE(:providersInIds) is null or p.provider_id in (:providersInIds)) " +
				"and (COALESCE(:providersNotInIds) is null or p.provider_id not in (:providersNotInIds)) " +
				"and p.latitude is not null AND p.longitude is not null AND ppm.payer_id IN (:ids) AND ppm.status='ACTIVE' \n" +
				"GROUP BY p.provider_id ORDER BY distanceInKm ASC) AS t WHERE t.distanceInKm <= :radius LIMIT :page, :size",
		nativeQuery = true
	)
	fun findNearbyProviders(
		@Param("latitude") latitude: Double,
		@Param("longitude") longitude: Double,
		@Param("radius") radius: Double,
		@Param("page") page: Int,
		@Param("size") size: Int,
		@Param("ids") ids: List<Long>,
		@Param("providersInIds") providersInIds: Set<Long>?,
		@Param("providersNotInIds") providersNotInIds: Set<Long>?,
	): List<NearbyProvidersDTO>


	@Query("select p from Provider p where (:providerName is null or p.name like concat('%',:providerName,'%'))")
	fun searchAllProviders(
		@Param("providerName") providerName: String?,
		pageable: Pageable):Page<Provider>

	@Query("select p from Provider p where (:providerName is null or p.name like concat('%',:providerName,'%')) and p.mainFacility is null")
	fun searchMainProviders(
		@Param("providerName") providerName: String?,
		pageable: Pageable):Page<Provider>

	@Query("""
		select p from Provider p where (:providerName is null or p.name like concat('%',:providerName,'%')) 
		and ((:mainFacilityId is null and p.mainFacility is not null) or p.mainFacility.id = :mainFacilityId)
	""")
	fun searchBranchProviders(
		@Param("providerName") providerName: String?,
		@Param("mainFacilityId") mainFacilityId: Long?,
		pageable: Pageable):Page<Provider>

	@Query("""
		select p from Provider p where 
		exists (select 1 from PayerProviderMapping m where m.payer.id = :payerId)
		and (:query is null or p.name like concat('%',:query,'%')) 
		and (:mainFacilityId is null or p.mainFacility.id = :mainFacilityId)
		and (:regionId is null or p.region.id = :regionId)
		and (:countryId is null or p.region.country.id = :countryId)
		and (:tier is null or p.tier = :tier)
		and (:providerType is null
			or (:#{#providerType != null ? #providerType.name : ''} = 'MAIN' and p.mainFacility is null) 
			or (:#{#providerType != null ? #providerType.name : ''} = 'BRANCH' and p.mainFacility is not null) 
		)
		order by p.id asc
	""")
	fun searchPayerProviders(
			@Param("payerId") payerId: Long,
			@Param("query") query: String?,
			@Param("countryId") countryId: Long?,
			@Param("regionId") regionId: Long?,
			@Param("providerType") providerType: ProviderType?,
			@Param("tier") tier: ProviderTier?,
			@Param("mainFacilityId") mainFacilityId: Long?,
			pageable: Pageable
	): Page<Provider>
}

interface ProviderExclusionRepository : JpaRepository<ProviderExclusion, Long> {
	fun findByCategory(category: Category): MutableList<ProviderExclusion>
	fun findByCategory(category: Category, request: Pageable): Page<ProviderExclusion>
	fun findByProvider(provider: Provider): MutableList<ProviderExclusion>
	fun findByCategoryAndProvider(
		category: Category, provider: Provider
	): Optional<ProviderExclusion>
}

interface CopaySetupRepository : JpaRepository<Copayment, Long> {
	fun findByCategory(category: Category): MutableList<Copayment>
	fun findByProvider(provider: Provider): MutableList<Copayment>
	fun findByCategoryAndProvider(
		category: Category, provider: Provider
	): Optional<Copayment>
}

interface WhiteListRepository : JpaRepository<Whitelist, Long> {
	fun findByProvider(provider: Provider, pageable: Pageable): Page<Whitelist>
	fun findByBenefit(benefit: BenefitCatalog, pageable: Pageable): Page<Whitelist>
}

interface PayerBenefitMappingRepository : JpaRepository<PayerBenefitMapping, Long> {
	fun findByPayer(payer: Payer): MutableList<PayerBenefitMapping>

	@Query(
		value = "SELECT * from payer_benefit_mapping where payer_id = ?1 AND code is not null AND" +
				" code != ''",
		countQuery = "SELECT * from payer_benefit_mapping where payer_id = ?1 AND code is not " +
				"null AND code != ''",
		nativeQuery = true
	)
	fun findByPayerId(payer: Long,pageable: Pageable): Page<PayerBenefitMapping>

	fun findByPayerIdAndBenefitId(
		payerId: Long,
		benefitCatalogId: Long
	): PayerBenefitCodeMappingDTO?

	fun findByBenefit(benefit: BenefitCatalog): MutableList<PayerBenefitMapping>
}

interface PayerProviderMappingRepository : JpaRepository<PayerProviderMapping, Long> {
	@Query(
		value = "SELECT * from payer_provider_mapping where payer_id = ?1 AND code is not null",
		countQuery = "SELECT * from payer_provider_mapping where payer_id = ?1 AND code is not " +
				"null",
		nativeQuery = true
	)
	fun findByPayerId(payer: Long, pageable: Pageable): Page<PayerProviderMapping>

	@Query(
		value = "SELECT * from payer_provider_mapping where provider_id = ?1 AND code is not null",
		countQuery = "SELECT * from payer_provider_mapping where provider_id = ?1 AND code is not " +
				"null",
		nativeQuery = true
	)
	fun findByProviderId(providerId: Long, pageable: Pageable): Page<PayerProviderMapping>

	fun findByPayer(payer: Payer, pageable: Pageable): Page<PayerProviderMapping>

	fun findByPayerAndCodeNotNull(payer: Payer, pageable: Pageable): Page<PayerProviderMappingProjectionWithRestrictionCheckDTO>
	fun findByPayerAndProviderAndStatus(
		payer: Payer,
		provider: Provider,
		status: PayerProviderMapping.PayerProviderMappingStatus
	): Optional<PayerProviderMapping>

	fun findByProvider(provider: Provider): MutableList<PayerProviderMapping>

	@Query(
		value = "SELECT * from payer_provider_mapping where provider_id = ?1 AND code is not null",
		nativeQuery = true
	)
	fun findByProviderId(providerId: Long): MutableList<PayerProviderMapping>

	fun findByPayerIdAndProviderId(payerId: Long, providerId: Long): Optional<PayerProviderMapping>
	fun findByPayerRegionId(payerRegionId: Long): MutableList<PayerProviderMapping>

	fun findByProviderIdAndPayerId(
		providerId: Long,
		payerId: Long
	): Optional<JubileePayerProviderCodeMappingDTO>

	@Query("""
		select p from PayerProviderMapping p inner join p.payer py where p.provider.id = :providerId 
		and (:query is null or py.name like concat('%',:query,'%')) 
		and (p.status is null or p.status = :status) 
		order by py.id asc
	""")
	fun filterProviderPayers(
		@Param("providerId") providerId: Long,
		@Param("status") status: PayerProviderMapping.PayerProviderMappingStatus?,
		@Param("query") query: String?,
		pageable: Pageable
	): Page<PayerProviderMapping>


	fun findByPayerAndProviderIn(
		payer:Payer,
		provider: List<Provider>,
		pageable: Pageable): Page<PayerProviderMapping>

	fun findByPayerAndProviderNotIn(
		payer:Payer,
		provider: List<Provider>,
		pageable: Pageable): Page<PayerProviderMapping>

	fun findByPayerAndProvider_region(payer: Payer, region: Region, pageable: Pageable): Page<PayerProviderMapping>
	fun findByPayerAndProviderInAndProvider_region(
		payer:Payer,
		provider: List<Provider>,
		region: Region,
		pageable: Pageable): Page<PayerProviderMapping>
	fun findByPayerAndProviderNotInAndProvider_region(
		payer:Payer,
		provider: List<Provider>,
		region: Region,
		pageable: Pageable): Page<PayerProviderMapping>

	@Query("select p from PayerProviderMapping p where p.payer.id = :payerId and p.provider.id in (:providerIds)")
	fun findByPayerIdAndProviderIds(
		@Param("payerId") payerId: Long,
		@Param("providerIds") providerIds: Set<Long>): MutableList<PayerProviderMapping>
	@Query("""
		select p from PayerProviderMapping p inner join p.provider pr where p.payer.id = :payerId and (:query is null or pr.name like concat('%',:query,'%')) 
		and (p.status is null or p.status = :status) 
		and (:mainFacilityId is null or pr.mainFacility.id = :mainFacilityId) and (:regionId is null or pr.region.id = :regionId) 
		and (:countryId is null or pr.region.country.id = :countryId) and (:tier is null or pr.tier = :tier) 
        and (:providerType is null
			or (:#{#providerType != null ? #providerType.name : ''} = 'MAIN' and pr.mainFacility is null)
			or (:#{#providerType != null ? #providerType.name : ''} = 'BRANCH' and pr.mainFacility is not null)
		) order by pr.id asc
	""")
	fun filterPayerProviders(
		@Param("payerId") payerId: Long,
		@Param("status") status: PayerProviderMapping.PayerProviderMappingStatus?,
		@Param("query") query: String?,
		@Param("countryId") countryId: Long?,
		@Param("regionId") regionId: Long?,
		@Param("providerType") providerType: ProviderType?,
		@Param("tier") tier: ProviderTier?,
		@Param("mainFacilityId") mainFacilityId: Long?,
		pageable: Pageable
	): Page<PayerProviderMapping>
}

interface CardBatchRepository : JpaRepository<CardBatch, Long> {
	fun findByPolicy(policy: Policy): List<CardBatch>
	fun findByType(type: CardRequestType, pageable: Pageable): Page<CardBatch>

	@Query(value = "SELECT c FROM CardBatch c LEFT JOIN c.beneficiaryCards x LEFT JOIN x.beneficiary y WHERE y.memberNumber = :memberNumber ")
	fun searchByMemberNumber(@Param("memberNumber") memberNumber: String): List<CardBatch>
}


interface BeneficiaryLinkRepository : JpaRepository<BeneficiaryLink, Long> {
	fun findByPhoneNumberAndBeneficiary_Id(phoneNumber: String, id: Long): Optional<BeneficiaryLink>
	fun findByBeneficiary_IdAndStatus(beneficiaryId: Long, status: Boolean): List<BeneficiaryLink>
}


interface AuditLogRepo : JpaRepository<AuditLog, Long> {

	@Query("""
		select a from AuditLog a where (:beneficiaryId is null or a.beneficiary.id = :beneficiaryId)
	""")
	fun filterAuditLog(
		@Param("beneficiaryId") beneficiaryId: Long?,
		pageable: Pageable
	): Page<AuditLog>

}

interface DeviceModelRepo:JpaRepository<DeviceModel,Long>{
	fun findByModel(model:String):Optional<DeviceModel>
}

interface DeviceCatalogRepo : JpaRepository<DeviceCatalog, Long> {

	@Query("select d from DeviceCatalog d order by d.id desc")
	fun findAllDeviceCatalog(pageable: Pageable) : Page<DeviceCatalog>
	fun findByDeviceId(deviceId: String): Optional<DeviceCatalog>

	@Query("""
		select c from DeviceCatalog c where c.id not in (:ids)
	""")
	fun findUnallocatedDevices(@Param("ids") ids: Set<Long>,pageable: Pageable):Page<DeviceCatalog>

	@Query("select c from DeviceCatalog c left join c.allocations a where a.status is null or a.status <> :allocationStatus order by c.id desc")
	fun getUnallocatedDevices(
		@Param("allocationStatus") allocationStatus: AllocationStatus,pageable: Pageable
	):Page<DeviceCatalog>

	@Query("""
		select d from DeviceCatalog d left join d.imei i where (:deviceId is null or d.deviceId like concat('%',:deviceId,'%')) 
		and (:imei is null or i.imei like concat('%',:imei,'%')) 
		and (:deviceStatus is null or d.status =:deviceStatus) group by d.id
	""")
	fun searchCatalog(@Param("deviceId") deviceId: String?,
		              @Param("deviceStatus") deviceStatus: DeviceStatus?,
				      @Param("imei") imei: String?,
					  pageable: Pageable):Page<DeviceCatalog>
}

interface DeviceImeiRepo:JpaRepository<DeviceImei,Long>{
	fun findByImei(imei:String):Optional<DeviceImei>
}

interface DeviceSimRepo:JpaRepository<DeviceSim,Long>{
	@Query("""
		select d from DeviceSim d where (:simNumber is null or d.simNumber like concat('%',:simNumber,'%')) 
		and (:simStatus is null or d.status =:simStatus) group by d.id
	""")
	fun searchSIM(
		@Param("simNumber") simNumber: String?,
		@Param("simStatus") simStatus: SimStatus?,
		pageable: Pageable):Page<DeviceSim>

	fun findBySimNumber(simNumber:String):Optional<DeviceSim>
}

interface DeviceAccessoryRepo:JpaRepository<DeviceAccessory,Long>{

}

interface SimUploadErrorRepo:JpaRepository<SimUploadError,Long>{

}

interface DeviceDocumentRepo:JpaRepository<DeviceDocument,Long>{

}

interface DeviceManagementAuditLogRepo:JpaRepository<DeviceManagementAuditLog,Long>{

}

interface DeviceAllocationRepo : JpaRepository<DeviceAllocation, Long> {
	fun findByDeviceCatalogAndStatus(
		deviceCatalog: DeviceCatalog,
		status: AllocationStatus
	): Optional<DeviceAllocation>

	@Query("select d from DeviceAllocation d where d.status = :status")
	fun findByAllocationStatus(@Param("status") status: AllocationStatus,pageable: Pageable):Page<DeviceAllocation>

	@Query("select d from DeviceAllocation d where d.status = :status")
	fun findByAllocationStatus(@Param("status") status: AllocationStatus): List<DeviceAllocation>

	@Query("select d from DeviceAllocation d where d.provider.id = :providerId and d.checkerStatus = :checkerStatus order by d.id desc")
	fun findProviderAllocatedDevices(
		@Param("providerId") providerId: Long,
		@Param("checkerStatus") checkerStatus: CheckerStatus,
		pageable: Pageable
	): Page<DeviceAllocation>

	@Query("select d from DeviceAllocation d order by d.id desc")
	fun findAllocations(
		pageable: Pageable
	): Page<DeviceAllocation>
}


interface DeviceUploadErrorRepo:JpaRepository<DeviceUploadError,Long>{

}

interface BenefitProviderMappingRepo: JpaRepository<BenefitProviderMapping,Long>{
	fun findByBenefitAndProvider(
		 benefit: Benefit,
		 provider: Provider,
	):Optional<BenefitProviderMapping>

	@Query(value = "SELECT b FROM BenefitProviderMapping " +
			"b WHERE b.benefit.id = :benefitId AND b.mappingEnabled = true")
	fun getAllPayerBenefitMappings(@Param("benefitId") benefitId: Long): MutableList<BenefitProviderMapping>

	@Query(value = "SELECT b FROM BenefitProviderMapping " +
			"b WHERE b.benefit.id = :benefitId AND b.mappingEnabled = true")
	fun getPayerBenefitMappingsByPayerIdAndBenefitId(
		@Param("benefitId") benefitId: Long,
		pageable: Pageable): Page<BenefitProviderMapping>

	@Query(value = "SELECT b FROM BenefitProviderMapping b WHERE b.benefit.id = :benefitId " +
			"AND b.mappingEnabled = true AND b.provider IN (:includedProviders)")
	fun getProvidersByBenefitIdInclusive(
		@Param("benefitId") benefitId: Long,
		@Param("includedProviders") includedProviders: List<Provider>,
		pageable: Pageable): Page<BenefitProviderMapping>

	@Query(value = "SELECT b FROM BenefitProviderMapping b WHERE b.benefit.id = :benefitId " +
			"AND b.mappingEnabled = true AND b.provider IN (:includedProviders)")
	fun getAllProvidersByBenefitIdInclusive(
		@Param("benefitId") benefitId: Long,
		@Param("includedProviders") includedProviders: List<Provider>): MutableList<BenefitProviderMapping>


	@Query(value = "SELECT b FROM BenefitProviderMapping b WHERE b.benefit.id = :benefitId " +
			"AND b.mappingEnabled = true AND b.provider NOT IN (:excludedProviders)")
	fun getProvidersByBenefitIdExclusive(
		@Param("benefitId") benefitId: Long,
		@Param("excludedProviders") excludedProviders: List<Provider>,
		pageable: Pageable): Page<BenefitProviderMapping>

	@Query(value = "SELECT b FROM BenefitProviderMapping b WHERE b.benefit.id = :benefitId " +
			"AND b.mappingEnabled = true AND b.provider NOT IN (:excludedProviders)")
	fun getAllProvidersByBenefitIdExclusive(
		@Param("benefitId") benefitId: Long,
		@Param("excludedProviders") excludedProviders: List<Provider>): MutableList<BenefitProviderMapping>

	@Query(value = "SELECT b FROM BenefitProviderMapping b WHERE b.mappingEnabled = true AND b.benefit = :benefit " +
			"AND b.provider.region = :region AND lower(b.provider.name) like lower(concat('%', :facilityName, '%'))")
	fun searchByBenefitAndProviderNameAndRegion(
		@Param("benefit") benefit: Benefit,
		@Param("region") region: Region,
		@Param("facilityName") facilityName:String,
		pageable: Pageable) : Page<BenefitProviderMappingProjectionDTO>

	@Modifying
	@Query("UPDATE BenefitProviderMapping b SET b.mappingEnabled = false WHERE b.benefit = :benefit")
	fun disableAllBenefitProviderMappings(@Param("benefit") benefit: Benefit)

	@Modifying
	@Query("UPDATE BenefitProviderMapping b SET b.mappingEnabled = false WHERE b.benefit = :benefit AND b.provider.id IN (:providers)")
	fun disableSelectedBenefitProviderMappings(
		@Param("benefit") benefit: Benefit,
		@Param("providers") providers: List<Long>)
}

interface BeneficiaryBenefitProviderMappingRepo: JpaRepository<BeneficiaryProviderMapping,Long>{
	fun findByBeneficiaryAndBenefitAndMappingEnabled(
		beneficiary: Beneficiary,
		benefit: Benefit,
		mappingEnabled: Boolean): MutableList<BeneficiaryProviderMappingProjectionDTO>

	fun findByBeneficiaryAndBenefitAndProvider(
		beneficiary: Beneficiary,benefit:Benefit, provider: Provider): Optional<BeneficiaryProviderMapping>

	@Modifying
	@Query("UPDATE BeneficiaryProviderMapping b SET b.mappingEnabled = false WHERE b.benefit = :benefit " +
			"AND b.beneficiary = :beneficiary")
	fun disableAllBeneficiaryProviderMappingForABenefit(
		@Param("benefit") benefit: Benefit,
		@Param("beneficiary") beneficiary: Beneficiary)

	fun findByBenefitAndBeneficiaryAndMappingEnabled(benefit: Benefit,beneficiary: Beneficiary, mappingEnabled:Boolean):MutableList<BeneficiaryProviderMapping>
}

interface CategoryRestrictionMappingRepo: JpaRepository<CategoryRestriction,Long>{
	@Query(value = "SELECT b FROM CategoryRestriction b WHERE b.category = :category AND b.restrictionStatus = 'ACTIVE'")
	fun getProviderRestrictionsForACategory(category:Category): MutableList<CategoryRestriction>

	fun findByCategoryAndProvider(
		category: Category, provider: Provider
	): Optional<CategoryRestriction>

	fun findByCategoryAndProviderAndRestrictionStatus(
		category: Category, provider: Provider,status: RestrictionStatus
	): Optional<CategoryRestriction>


}


interface BenefitRestrictionRepo: JpaRepository<BenefitRestriction,Long>{
	fun findByPayerAndMigrated(payer: Payer, migrated:Boolean):MutableList<BenefitRestriction>
	fun findByIdAndMigrated(id: Long, migrated:Boolean):Optional<BenefitRestriction>
	fun findByPayerAndNameAndMigrated(payer: Payer, name:String, migrated:Boolean):Optional<BenefitRestriction>
}

interface BenefitRestrictionProviderSetRepo: JpaRepository<BenefitRestrictionProviderSet,Long>{
	@Query("select b from BenefitRestrictionProviderSet b where b.restriction.id = :restrictionId and b.active = true")
	fun findByRestriction(restrictionId: Long):MutableList<BenefitRestrictionProviderSet>

	fun findByRestrictionAndActive(restriction: BenefitRestriction,
								   active:Boolean,
								   pageable: Pageable):Page<ProviderRestrictionSetProjectionDTO>

	fun findByRestrictionAndProviderAndActive(restriction: BenefitRestriction,
											  provider: Provider?,
											  active:Boolean):Optional<BenefitRestrictionProviderSet>

	fun findByRestrictionAndProvider(restriction: BenefitRestriction,
									 provider: Provider):Optional<BenefitRestrictionProviderSet>
}

interface BenefitRestrictionMappingRepo: JpaRepository<BenefitRestrictionMapping, Long>{
	@Query("select b from BenefitRestrictionMapping b inner join b.restriction r " +
			"inner join r.benefitRestrictionProviderSet s where b.benefit.id = :benefitId " +
			"and s.provider.id = :providerId")
	fun findByBenefitIdAndProviderId(
		benefitId:Long,
		providerId:Long,
	) : MutableList<BenefitRestrictionMapping>

	@Query("select b from BenefitRestrictionMapping b inner join b.restriction r " +
			"inner join r.benefitRestrictionProviderSet s where b.benefit.id = :benefitId " +
			"and s.provider.id = :providerId and b.mappingEnabled = true and s.active = true " +
			"order by b.id asc")
	fun findByBenefitIdAndProviderIdAndEnabled(
		benefitId:Long,
		providerId:Long,
	) : MutableList<BenefitRestrictionMapping>

	@Query("select b from BenefitRestrictionMapping b where b.benefit.id = :benefitId and b.mappingEnabled = true")
	fun findByBenefitAndEnabled(benefitId:Long):MutableList<BenefitRestrictionMapping>
}

interface ServiceCatalogRepository: JpaRepository<ServiceCatalog,Long>{

	fun findByIsEnabled(isEnabled:Boolean, pageable: Pageable):Page<ServiceCatalog>
	fun findByName(name:String): Optional<ServiceCatalog>
}

interface ServiceBenefitMappingRepository: JpaRepository<ServiceBenefitMapping,Long>{
	fun findByServiceCatalogAndBenefitCatalog(
		serviceCatalog:ServiceCatalog,
		benefitCatalog:BenefitCatalog
	): Optional<ServiceBenefitMapping>

	fun findByServiceCatalogAndMappingEnabled(serviceCatalog:ServiceCatalog,
											  mappingEnabled: Boolean = true,
											  pageable: Pageable) : Page<ServiceBenefitMapping>
	@Modifying
	@Query("UPDATE ServiceBenefitMapping b SET b.mappingEnabled = false WHERE b.serviceCatalog = :serviceCatalog AND b.benefitCatalog.id IN (:benefitCatalogIds)")
	fun disableMappedServiceBenefits(
		@Param("serviceCatalog") serviceCatalog: ServiceCatalog,
		@Param("benefitCatalogIds") benefitCatalogIds: List<Long>)
}

interface ServiceProviderMappingRepository: JpaRepository<ServiceProviderMapping,Long>{
	fun findByServiceCatalogAndProvider(
		serviceCatalog:ServiceCatalog,
		provider: Provider
	): Optional<ServiceProviderMapping>

	fun findByServiceCatalogAndMappingEnabled(serviceCatalog:ServiceCatalog,
											  mappingEnabled: Boolean = true,
											  pageable: Pageable) : Page<ServiceProviderMapping>
	@Modifying
	@Query("UPDATE ServiceProviderMapping b SET b.mappingEnabled = false WHERE b.serviceCatalog = :serviceCatalog AND b.provider.id IN (:ids)")
	fun disableMappedServiceProviders(
		@Param("serviceCatalog") serviceCatalog: ServiceCatalog,
		@Param("ids") providerIds: List<Long>)

	@Query(value = """
		 SELECT spm.* FROM service_provider_mapping spm inner join service_catalog sc on 
		 (spm.service_catalog_id = sc.service_id) INNER JOIN service_benefit_mapping sbm on 
		 (sc.service_id = sbm.service_catalog_id) WHERE spm.provider_id = :providerId and 
		 sbm.benefit_catalog_id = :benefitCatalogId and spm.mapping_enabled = true and 
		 sc.is_enabled = true and sbm.mapping_enabled = true 
		 """,nativeQuery = true)
	fun getProviderServiceMappingByProviderAndBenefitCatalog(
		@Param("providerId") providerId: Long,
		@Param("benefitCatalogId") benefitCatalogId: Long
	): MutableList<ServiceProviderMapping>
}

interface RequestTypeRepository: JpaRepository<RequestType,Long>{
	@Query(value = "select r from RequestType r")
	fun getAll():List<RequestTypeProjectionDto>
	fun findByName(name:String):Optional<RequestType>
}

interface RequestServiceRepository: JpaRepository<RequestService,Long>{
	fun findByRequestType(requestType: RequestType):List<RequestServiceProjectionDto>
	fun findByRequestTypeAndName(requestType: RequestType, name: String):Optional<RequestService>
}

interface PayerRegionRepo:JpaRepository<PayerRegion,Long>{
	fun findByPayerId(id: Long):MutableList<PayerRegion>
}

interface ResolveDataRepository:JpaRepository<Beneficiary, Long>{

	@Query(value = """
        select fp.biometric_id as biometricId,fp.beneficiary_id as beneficiaryId,fp.member_number as memberNumber, 
        captured_date as captureDate,biometric_status as biometricStatus from profile.finger_print fp WHERE fp.biometric_id in (
	select MAX(fp.biometric_id) from profile.finger_print fp WHERE fp.beneficiary_id 
    in (:beneficiaryIds) or fp.member_number in (:memberNumbers) group by fp.member_number)
    """, nativeQuery = true)
	fun findBiometricsForBeneficiaries(
		@Param("beneficiaryIds") beneficiaryIds: List<Long>,
		@Param("memberNumbers") memberNumbers: List<String>): List<FingerPrintProjectionDTO>

	@Query("SELECT ue.ID as id,ue.EMAIL as email,ue.FIRST_NAME as firstName,ue.LAST_NAME as lastName," +
			"ue.USERNAME as userName,ua.NAME as name,ua.VALUE as value from keycloakDb.USER_ENTITY ue " +
			"inner join keycloakDb.USER_ATTRIBUTE ua on ue.ID = ua.USER_ID " +
			"WHERE ua.NAME = :name and ua.VALUE = :value", nativeQuery = true)
	fun findKeycloakUsers(@Param("name") name: String, @Param("value") value: String, pageable: Pageable) : Page<KeycloakUserResolve>

}

interface DataCollectionBeneficiaryRepository : JpaRepository<DataCollectionBeneficiary, Long> {
	@Query(value ="SELECT b.* from data_collection_beneficiary b where b.employee_number like CONCAT(:search,'%')  ORDER by b.id", nativeQuery = true)
	fun search(@Param(value = "search") search: String): MutableList<DataCollectionBeneficiary>

	@Query(
		value = "SELECT * FROM data_collection_beneficiary p" +
				" WHERE (LOWER(p.employee_name) LIKE LOWER(CONCAT(:searchTerm, '%'))" +
				" OR LOWER(p.employee_number) LIKE LOWER(CONCAT(:searchTerm, '%')))" +
				" AND p.principal_id IS NULL" +
				" ORDER BY p.id",
		nativeQuery = true
	)
	fun findPrincipalsByFullNameOrGrowerNumber(
		@Param("searchTerm") searchTerm: String
	): List<DataCollectionBeneficiary>

	fun findByEmployeeNumberIgnoreCase(name: String): MutableList<DataCollectionBeneficiary>

	@Query(value ="SELECT b.* from data_collection_beneficiary b where principal_id=:principalId  and relationship='SPOUSE'", nativeQuery = true)
	fun findSpouse(@Param("principalId") principalId: Long?): Optional<DataCollectionBeneficiary>

	@Query(value ="""SELECT b from DataCollectionBeneficiary b where b.principal IS NULL and (:fromDate is null or b.createdAt >= :fromDate) and (:toDate is null or b.createdAt <= :toDate) """)
	fun findPrincipals(@Param("fromDate") fromDate: LocalDate?,
					   @Param("toDate") toDate: LocalDate?,
					   pageable: Pageable): Page<DataCollectionBeneficiary>

	@Query(value ="SELECT COUNT(*) FROM DataCollectionBeneficiary b where b.principal IS  NULL")
	fun findPrincipalsCount(): Optional<Long>

	@Query(value ="""SELECT b from DataCollectionBeneficiary b where b.principal IS NOT NULL and (:fromDate is null or b.createdAt >= :fromDate) and (:toDate is null or b.createdAt <= :toDate) """)
	fun findDependants(@Param("fromDate") fromDate: LocalDate?,
					   @Param("toDate") toDate: LocalDate?,
					   pageable: Pageable): Page<DataCollectionBeneficiary>

	@Query(value ="SELECT COUNT(*) from DataCollectionBeneficiary b")
	fun findAllCount(): Optional<Long>

	@Query(value ="SELECT b.* from data_collection_beneficiary b where b.principal_id is null and created_date >=:fromDate and created_date <=:toDate   ORDER by b.id", nativeQuery = true)
	fun findPrincipalReport(@Param("fromDate") fromDate: LocalDate?,
								   @Param("toDate") toDate: LocalDate?): MutableList<DataCollectionBeneficiary>

	@Query(value ="SELECT b.* from data_collection_beneficiary b where b.principal_id is not null  and created_date >=:fromDate and created_date <=:toDate  ORDER by b.id", nativeQuery = true)
	fun findDependantReport(@Param("fromDate") fromDate: LocalDate?,
								   @Param("toDate") toDate: LocalDate?): MutableList<DataCollectionBeneficiary>

	@Query(value ="SELECT b.* from data_collection_beneficiary b where created_date >=:fromDate and created_date <=:toDate  ORDER by b.id", nativeQuery = true)
	fun findAllBeneficiariesReport(@Param("fromDate") fromDate: LocalDate?,
							@Param("toDate") toDate: LocalDate?): MutableList<DataCollectionBeneficiary>
}
interface DataCollectionDocumentRepository : JpaRepository<DataCollectionDocument, Long> {
	////fun findByBenefitNameIgnoreCase(name: String): Optional<DataCollectionDocument>
}

interface ProviderBankAccountRepository: JpaRepository<ProviderBankAccount, Long>{
	fun findByProvider(provider:Provider) : MutableList<ProviderBankAccount>

	@Query("select p from ProviderBankAccount p inner join p.providerBankAccountMapping pbam where pbam.payer.id = :payerId")
	fun findByPayerId(@Param("payerId") payerId: Long) : MutableList<ProviderBankAccount>
}
interface PayerProviderBankAccountMappingRepository: JpaRepository<PayerProviderBankAccountMapping, Long> {
	fun findByPayer(payer: Payer) : MutableList<PayerProviderBankAccountMapping>
}

interface PayerPolicyMappingRepository: JpaRepository<PayerPolicyMapping, Long> {

	@Query("select m from PayerPolicyMapping m inner join m.policy p inner join p.plan pln where m.payer.id = :payerId and pln.id = :planId and m.mappingEnabled = true")
	fun findMappedPoliciesByPayerIdAndPlanId(@Param("payerId") payerId: Long,
									@Param("planId") planId: Long) : MutableList<PayerPolicyMapping>

	fun findByPayerAndPolicy(payer: Payer, policy: Policy) : Optional<PayerPolicyMapping>

	fun findByPayerAndPolicyAndMappingEnabled(payer: Payer, policy:Policy, mappingEnabled:Boolean) : MutableList<PayerPolicyMapping>

}

interface OnboardPrincipalRepository : JpaRepository<OnboardPrincipal, Long> {
	@Query(
		"SELECT p FROM OnboardPrincipal p" +
				" WHERE LOWER(CONCAT(p.firstName, ' ', COALESCE(p.middleName, '') , ' ', p.surname))" +
				" LIKE LOWER( CONCAT(:searchTerm, '%'))" +
				" OR LOWER(p.surname) LIKE LOWER(CONCAT('%', :searchTerm, '%'))" +
				" OR LOWER(p.middleName) LIKE LOWER(CONCAT('%', :searchTerm, '%'))" +
				" OR LOWER(p.growerNumber) LIKE LOWER(CONCAT(:searchTerm, '%'))" +
				" ORDER BY p.id"
	)
	fun findByFullNameOrGrowerNumber(
		@Param("searchTerm") searchTerm: String
	): List<OnboardPrincipal?>

	@Query(
		"SELECT p FROM OnboardPrincipal p" +
				" WHERE  (:fromDate IS NULL OR p.createdAt >=:fromDate)" +
				" AND (:toDate IS NULL OR p.createdAt <=:toDate)" +
				"  ORDER BY p.id"
	)
	fun filterPrincipals(
		@Param("fromDate") fromDate: LocalDate? = null,
		@Param("toDate") toDate: LocalDate? = null,
		pageable: Pageable? = null,
	): Page<OnboardPrincipal>

	fun findByAgentId(agentId: Long): List<OnboardPrincipal>
}

interface OnboardDependantRepository : JpaRepository<OnboardDependant, Long> {
	fun findByPrincipalId(principalId: Long): List<OnboardDependant>

	@Query("SELECT d FROM OnboardDependant d" +
			" WHERE  (:fromDate IS NULL OR d.createdAt >= :fromDate)" +
			" AND (:toDate IS NULL OR d.createdAt <= :toDate)" +
			"  ORDER BY d.id")
	fun filterDependants(
		@Param("fromDate") fromDate: LocalDate? = null,
		@Param("toDate") toDate: LocalDate? = null,
		pageable: Pageable? = null,
	): Page<OnboardDependant>
}

interface OnboardBeneficiaryRepository : JpaRepository<OnboardBeneficiary, Long> {
	fun findByPrincipalId(principalId: Long): List<OnboardBeneficiary>
}

interface OnboardDocumentRepository: JpaRepository<OnboardDocument, Long> {
	fun findByPrincipalId(principalId: Long): List<OnboardDocument>
}

interface OnboardBenefitRepository: JpaRepository<OnboardBenefit, Long> {
	fun findByPrincipalId(principalId: Long): List<OnboardBenefit>
}

interface OnboardAgentRepository: JpaRepository<OnboardAgent, Long> {
	fun findByFactory(factory: String, pageable: Pageable?): Page<OnboardAgent>
	override fun findAll(pageable: Pageable): Page<OnboardAgent>

	@Query(
		"SELECT a FROM OnboardAgent a" +
				" WHERE LOWER(a.name)" +
				" LIKE LOWER( CONCAT(:searchTerm, '%'))" +
				" OR LOWER(a.agentNumber) LIKE LOWER(CONCAT(:searchTerm, '%'))" +
				" ORDER BY a.id"
	)
	fun findByFullNameOrAgentNumber(
		@Param("searchTerm") searchTerm: String
	): List<OnboardAgent?>
}

interface OnboardPrincipalApplicationFormContextIdRepository: JpaRepository<OnboardPrincipalApplicationFormContextId, Long> {
	fun findByPrincipalId(principalId: Long): List<OnboardPrincipalApplicationFormContextId>
}

interface OnboardPrincipalApplicationFormInfoIdRepository: JpaRepository<OnboardPrincipalApplicationFormInfoId, Long> {
	fun findByPrincipalId(principalId: Long): List<OnboardPrincipalApplicationFormInfoId>
}

interface OnboardPrincipalHospitalFormContextIdRepository: JpaRepository<OnboardPrincipalHospitalFormContextId, Long> {
	fun findByPrincipalId(principalId: Long): List<OnboardPrincipalHospitalFormContextId>
}

interface OnboardPrincipalHospitalFormInfoIdRepository: JpaRepository<OnboardPrincipalHospitalFormInfoId, Long> {
	fun findByPrincipalId(principalId: Long): List<OnboardPrincipalHospitalFormInfoId>
}

interface OnboardPrincipalStudentIdContextIdRepository: JpaRepository<OnboardPrincipalStudentIdContextId, Long> {
	fun findByPrincipalId(principalId: Long): List<OnboardPrincipalStudentIdContextId>
}

interface OnboardPrincipalStudentIdInfoIdRepository: JpaRepository<OnboardPrincipalStudentIdInfoId, Long> {
	fun findByPrincipalId(principalId: Long): List<OnboardPrincipalStudentIdInfoId>
}

interface PayerProviderUserMappingRepository: JpaRepository<PayerProviderUserMapping, Long> {

}

interface BenefitPayerAdminRepository: JpaRepository<BenefitPayerAdmin, Long> {

	fun findByPayerAndBenefit(payer: Payer, benefit: Benefit): Optional<BenefitPayerAdmin>

//	@Query("select pln FROM BenefitPayerAdmin bpa inner join PayerPolicyMapping ppm on (bpa.payer.id = ppm.payer.id) " +
//			"inner join ppm.policy p inner join p.plan pln where bpa.mappingEnabled = true and ppm.mappingEnabled = true " +
//			"and bpa.payer.id = :payerId group by pln.id order by pln.id desc")
//	fun findSchemesAdministeredByPayerAndMappingEnabled(payerId: Long): MutableList<BenefitAdminPlansProjectionDTO>

	@Query("select pln FROM Benefit b left join BenefitPayerAdmin bpa on (b.id = bpa.benefit.id) inner join PayerPolicyMapping ppm on (bpa.payer.id = ppm.payer.id) " +
			"inner join ppm.policy p inner join p.plan pln where bpa.mappingEnabled = true and ppm.mappingEnabled = true " +
			"and (b.payer.id = :payerId or bpa.payer.id = :payerId) group by pln.id order by pln.id desc")
	fun findSchemesAdministeredByPayerAndMappingEnabled(payerId: Long): MutableList<BenefitAdminPlansProjectionDTO>
}


interface UserRolesAuditLogRepository: JpaRepository<UserRolesAuditLog, Long> {
	fun findByUserId(userId: String, pageable: Pageable): Page<UserRolesAuditLog>
	fun findByActionedBy(actionedBy: String, pageable: Pageable): Page<UserRolesAuditLog>
}
