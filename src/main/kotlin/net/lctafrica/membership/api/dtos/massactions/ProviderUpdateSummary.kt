package net.lctafrica.membership.api.dtos.massactions


data class ProviderUpdateSummary(
    val totalRows: Int,
    val successfulUpdates: Int,
    val codeUpdates: Int,
    val nameUpdates: Int,
    val skippedRows: Int,
    val errors: List<ProviderUpdateError>,
    // enhanced metrics for mapping operations
    val automaticMappings: Int = 0,
    val processingTimeMs: Long = 0,
    val recordsPerSecond: Double = 0.0,
    val memoryUsageMB: Long = 0,
    val concurrencyLevel: Int = 1
)


data class ProviderUpdateError(
    val rowNumber: Int,
    val providerId: Long?,
    val errorMessage: String,
    val errorType: ProviderUpdateErrorType
)

enum class ProviderUpdateErrorType {
    PROVIDER_NOT_FOUND,
    PROVIDER_NOT_MAPPED_TO_PAYER,
    INVALID_DATA,
    UPDATE_FAILED,
    DATABASE_ERROR,
    TIMEOUT_ERROR,
    VALIDATION_ERROR,
    MAPPING_CREATION_FAILED,
    CONCURRENT_MODIFICATION,
    INSUFFICIENT_PERMISSIONS,
    SYSTEM_ERROR
}