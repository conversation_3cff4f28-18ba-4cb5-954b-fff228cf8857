package net.lctafrica.membership.api.dtos

import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import com.sksamuel.avro4k.ScalePrecision
import com.sksamuel.avro4k.serializer.BigDecimalSerializer
import com.sksamuel.avro4k.serializer.LocalDateSerializer
import kotlinx.serialization.Serializable
import net.lctafrica.membership.api.domain.*
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.rest.core.config.Projection
import org.springframework.format.annotation.DateTimeFormat
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import javax.validation.Valid
import javax.validation.constraints.NotBlank
import javax.validation.constraints.NotNull
import javax.validation.constraints.Past

data class PayerDTO(
    @field:NotBlank(message = "Payer name cannot be blank")
    val name: String,
    @field:NotBlank(message = "Payer contact cannot be blank")
    val contact: String,
    var email: String? = null,
    var website: String? = null,
    var streetAddress: String? = null,
    var postalAddress: String? = null,
    var logo: String? = null,
    val type: PayerType,
    val mainPayerId: Long? = null
)

data class PayerConfigDto(
    val payerId: Long,
    val configId: Long,
    val value: String,
    var description: String? = null,
)

data class PayerBenefitMappingDTO(
    val payerId: Long,
    val benefitCatalogId: Long,
    @field:NotBlank(message = "Benefit code cannot be blank")
    val code: String
)

data class PayerProviderMappingDTO(
    val payerId: Long,
    val providerId: Long,
    @field:NotBlank(message = "Provider code cannot be blank")
    val code: String
)

data class PayerProviderMappingUpdateDTO(
    val mappingId: Long,
    val status: PayerProviderMapping.PayerProviderMappingStatus?,
    val code: String?
)

data class PayerProviderMap(
    val payerId: Long,
    val providerId: Long,
    val code: String
)

data class PlanDTO(
    //val payerId: Long,
    @field:NotBlank(message = "Name of plan cannot be blank")
    val name: String,
    val type: PlanType,
    val benefitAccessMode: BenefitAccessMode
)

data class PolicyDTO(
    val planId: Long,
    val startDate: LocalDate,
    val endDate: LocalDate,
    @field:NotBlank(message = "Policy number cannot be blank")
    val policyNumber: String,
    val policyAdmin: Long? = null
)


data class CategoryDTO(
    val policyId: Long,
    val categories: MutableList<CatDTO>
)

data class CatDTO(
    @field:NotBlank(message = "Category name cannot be blank")
    val name: String,
    val description: String,
    val jicSchemeCode: Int?,
    val apaSchemeCode: Int?,
    val policyPayerCode: Long?,
    val restrictionType: RestrictionType? = null,
) {
}

data class BenefitPayerDto(
    var id: Long = 0
)

data class BenefitDTO(
    val categoryId: Long,
    @field:NotBlank(message = "Benefit name cannot be blank")
    val name: String,
    val applicableGender: ApplicableGender,
    val applicableMember: ApplicableMember,
    val limit: BigDecimal,
    val suspensionThreshold: BigDecimal,
    val preAuthThreshold: BigDecimal? = null,
    val preAuthType: PreAuthType,
    val sharing: BenefitDistribution,
    val coPaymentRequired: Boolean,
    val coPaymentAmount: BigDecimal?,
    val waitingPeriod: WaitingPeriod,
    val parentBenefitId: Long?,
    val payer: BenefitPayerDto,
    val catalogRefId: Long,
    val payerCode: Long?,
    val benefitType: BenefitType? = null,
    val capitationType: CapitationType? = null,
    val capitationPeriod: CapitationPeriod? = null,
    val visitCountPeriod: VisitCountPeriod? = null,
    val capitationMaxVisitCount: Int = 0,
    val capitationFacilitiesCount: Int = 0,
    val requireBeneficiaryToSelectProvider: Boolean? = false,
    val visitLimit: BigDecimal? = null,
    val daysOfAdmissionLimit: Int? = 0,
    val amountPerDayLimit: BigDecimal = BigDecimal(0.00),
    var applicableMinAge: BigDecimal? = null,
    var applicableMaxAge: BigDecimal? = null,
    var transferable: Boolean? = false,
    var billable: Boolean? = null
)

data class BeneficiaryDTO(
    val categoryId: Long,
    @field:NotBlank(message = "Beneficiary name cannot be blank")
    val name: String,
    @field:NotBlank(message = "Member number cannot be blank")
    val memberNumber: String,
    val nhifNumber: String?,
    val dob: LocalDate,
    val gender: Gender,
    val phoneNumber: String?,
    val email: String?,
    val beneficiaryType: BeneficiaryType,
    val principalId: Long?,
    val jicEntityId: Int?,
    val apaEntityId: Int?,
    val joinDate: LocalDate?

)

data class BeneficiaryStagingDTO(
    val categoryId: Long,
    @field:NotBlank(message = "Beneficiary name cannot be blank")
    val name: String,
    @field:NotBlank(message = "Member number cannot be blank")
    val memberNumber: String,
    val nhifNumber: String?,
    val idNumber: String?,
    val dob: LocalDate,
    val gender: Gender,
    val phoneNumber: String?,
    val email: String?,
    val beneficiaryType: BeneficiaryType,
    val jicEntityId: Int?,
    val apaEntityId: Int?,
    val joinDate: LocalDate?,
    val status: StagingStatus = StagingStatus.ACTIVATE,
    val processBenefits: Boolean? = false,
)

data class AddyStagedBeneficiaryDTO(
    val categoryId: Long,
    @field:NotBlank(message = "Beneficiary name cannot be blank")
    val name: String,
    @field:NotBlank(message = "Member number cannot be blank")
    val memberNumber: String,
    val nhifNumber: String?,
    val idNumber: String?,
    val dob: LocalDate,
    val gender: Gender,
    val phoneNumber: String?,
    val email: String?,
    val beneficiaryType: BeneficiaryType,
    val jicEntityId: Int?,
    val apaEntityId: Int?,
    val joinDate: LocalDate?,
    val processBenefits: Boolean? = false,
)

data class NestedBenefitDTO(
    val parent: BenefitDTO,
    val children: List<BenefitDTO>
)

data class BenefitCatalogDTO(
    @field:NotBlank(message = "Benefit code cannot be blank")
    val code: String,
    @field:NotBlank(message = "Benefit name cannot be blank")
    val name: String,
    val serviceGroup: ServiceGroup
)

interface ServiceDto {
    val serviceId: Long
    val serviceGroup: String
}

data class NestedBenefitCatalogs(
    val benefits: List<BenefitCatalogDTO>
)

data class ProviderDTO(
    @field:NotBlank(message = "Provider name cannot be blank")
    val name: String,
    val email: String? = null,
    val country: String,
    val region: String,
    val latitude: Double,
    val longitude: Double,
    val billsOnHmis: Boolean? = false,
    var billsOnPoral: Boolean? = false,
    val billsOnDevice: Boolean? = false,
    val canUseOtpVerificationFailOver: Boolean? = false,
    val tier: ProviderTier,
    val mainFacilityId: Long,
    val providerId: Long? = null,
    val verificationType: VerificationType? = VerificationType.OTP
)

data class CountryDTO(
    @field:NotBlank(message = "Country name cannot be blank")
    val name: String
)

data class RegionDTO(
    @field:NotBlank(message = "Region name cannot be blank")
    val name: String,
    val countryId: Long
)

data class WhiteListDTO(
    val benefitId: Long,
    val providerId: Long
)

data class MultipleWhiteListDTO(
    val benefitIds: List<Long>
)

data class ExclusionDTO(
    val categoryId: Long,
    val providerId: Long
)

data class RestrictionDTO(
    val categoryId: Long,
    val payerProviderId: Long,
    val restrictionType: RestrictionType?
)

data class RemoveRestrictionDTO(
    val restrictionId: Long
)

data class CopayDTO(
    val amount: BigDecimal,
    val categoryId: Long,
    val providerId: Long
)

data class CardDTO(
    @field:NotNull(message = "Policy ID must be provided")
    val policyId: Long,
    @field:NotNull(message = "Request type must be provided")
    val type: CardRequestType,
    val paymentRef: String?
)

@Serializable
data class CreateBenefitDTO(
    var aggregateId: String?,
    val benefitName: String,
    val subBenefits: Set<SubBenefitDTO>,
    val beneficiaries: Set<CreateBeneficiaryDTO>,
    @Serializable(with = LocalDateSerializer::class)
    val startDate: LocalDate,
    @Serializable(with = LocalDateSerializer::class)
    val endDate: LocalDate,
    val status: String = "ACTIVE",
    @Serializable(with = BigDecimalSerializer::class)
    @ScalePrecision(2, 10)
    val suspensionThreshold: BigDecimal,
    @Serializable(with = BigDecimalSerializer::class)
    @ScalePrecision(2, 10)
    val preAuthThreshold: BigDecimal? = null,
    @Serializable(with = BigDecimalSerializer::class)
    @ScalePrecision(2, 10)
    val balance: BigDecimal,
    val policyNumber: String,
    val payer: SchemePayerDTO,
    val categoryId: Long?,
    val benefitId: Long?,
    val catalogId: Long?,
    val manualSubBenefitParentId: Long?,
    var waitingPeriod: WaitingPeriod? = WaitingPeriod.ZERO_DAYS,
    var benefitType: BenefitType? = null,
    var capitationType: CapitationType? = null,
    var capitationPeriod: CapitationPeriod? = null,
    var visitCountPeriod: VisitCountPeriod? = null,
    var capitationMaxVisitCount: Int? = null,
    var capitationFacilitiesCount: Int? = null,
    val requireBeneficiaryToSelectProvider: Boolean? = null,
    val daysOfAdmissionLimit: Int? = null,
    @Serializable(with = BigDecimalSerializer::class)
    @ScalePrecision(2, 10)
    val amountPerDayLimit: BigDecimal? = null,
    @Serializable(with = BigDecimalSerializer::class)
    @ScalePrecision(2, 10)
    var applicableMinAge: BigDecimal? = null,
    @Serializable(with = BigDecimalSerializer::class)
    @ScalePrecision(2, 10)
    var applicableMaxAge: BigDecimal? = null,
    var transferable: Boolean? = false,
    var planType: PlanType? = null,
    var planDate: PlanDate? = null
)

data class CreateBenefitDTOAvro(
    var aggregateId: String?,
    val benefitName: String,
    val subBenefits: HashMap<String, List<String>>,
    val beneficiaries: HashMap<CharSequence, CharSequence>,
    val startDate: LocalDate,
    val endDate: LocalDate,
    val status: String = "ACTIVE",
    val suspensionThreshold: BigDecimal,
    val balance: BigDecimal,
    val policyNumber: String,
    val payer: SchemePayerDTO,
    val categoryId: Long
)

@Serializable
data class SubBenefitDTO(
    var name: String,
    val status: String = "ACTIVE",
    @Serializable(with = BigDecimalSerializer::class)
    @ScalePrecision(2, 10)
    val balance: BigDecimal,
    @Serializable(with = LocalDateSerializer::class)

    val startDate: LocalDate,
    @Serializable(with = LocalDateSerializer::class)
    val endDate: LocalDate,
    @Serializable(with = BigDecimalSerializer::class)
    @ScalePrecision(2, 10)
    val suspensionThreshold: BigDecimal,
    @Serializable(with = BigDecimalSerializer::class)
    @ScalePrecision(2, 10)
    val preAuthThreshold: BigDecimal? = null,
    val benefitId: Long,
    val gender: ApplicableGender,
    val memberType: ApplicableMember,
    val catalogId: Long,
    @Serializable(with = BigDecimalSerializer::class)
    @ScalePrecision(2, 10)
    val applicableMinAge: BigDecimal? = null,
    @Serializable(with = BigDecimalSerializer::class)
    @ScalePrecision(2, 10)
    val applicableMaxAge: BigDecimal? = null,
    var waitingPeriod: WaitingPeriod? = WaitingPeriod.ZERO_DAYS
)

@Serializable
data class CreateBeneficiaryDTO(
    val id: Long,
    val name: String,
    val memberNumber: String,
    val beneficiaryType: BeneficiaryType,
    val email: String?,
    val phoneNumber: String?,
    val gender: Gender,
    val type: BeneficiaryType,
    val jicEntityId: Int?,
    val apaEntityId: Int?,
    @Serializable(with = LocalDateSerializer::class)
    val joinDate: LocalDate?,
    @Serializable(with = LocalDateSerializer::class)
    var dob: LocalDate
)

@Serializable
data class SchemePayerDTO(
    val payerId: Long,
    val payerName: String
)

@Serializable
data class PayerBenefitCodeMappingDTO(
    val id: Long = 0,
    val code: String
)

@Serializable
data class LinkCardDto(
    val beneficiaryId: Long,
    val phoneNumber: String,
    val link: Boolean,
)

data class BeneficiaryQueryDto(
    var beneficiaryId: Long?,
    var name: String?,
    var memberNumber: String?,
    var nhifNumber: String?,
    var dob: LocalDate?,
    var gender: Gender?,
    var phoneNumber: String?,
    var email: String?,
    var beneficiaryType: BeneficiaryType?,
    var category: Category?,
    var principal: Beneficiary?,
    var processed: Boolean?,
    var processedTime: LocalDateTime?,
    var payer: List<Payer>?
)

fun Beneficiary.toBeneficiaryDto(_payer: List<Payer>?) = BeneficiaryQueryDto(
    beneficiaryId = id,
    name = name,
    memberNumber = memberNumber,
    nhifNumber = nhifNumber,
    dob = dob,
    gender = gender,
    phoneNumber = phoneNumber,
    email = email,
    beneficiaryType = beneficiaryType,
    category = category,
    principal = principal,
    processed = processed,
    processedTime = processedTime,
    payer = _payer
)

data class JubileeCategoryDTO(
    @field:NotNull(message = "Category ID must be provided")
    val categoryId: Long,
    @field:NotNull(message = "MemberNumber must be provided")
    val memberNumber: String,
)

data class JubileeProviderCodeDTO(
    @field:NotNull(message = "Provider ID must be provided")
    val providerId: Long,
    @field:NotNull(message = "Payer ID must be provided")
    val payerId: Long
)

data class JubileeBenefitTO(
    @field:NotNull(message = "Benefit ID must be provided")
    val benefitId: Long,
    @field:NotNull(message = "Policy ID must be provided")
    val policyId: Long
)

@Serializable
data class JubileePayerProviderCodeMappingDTO(
    val id: Long = 0,
    val code: String? = null,
    val providerName: String?
)

@Serializable
data class JubileePayerBenefitNameMappingDTO(
    val id: Long = 0,
    val benefitName: String
)

@Serializable
data class JubileeResponseBenefitNameDTO(
    val benefit: String?,
    val subBenefit: String?
)


@Serializable
data class JubileeResponseMemberDTO(
    val policyPayerCode: Long?,
    val policyEffectiveDate: String?,
    val memberActisureId: Int?,
    val schemeName: String?,
    val memberFullName: String?
)

@Serializable
data class CashierUserDTO(
    val username: String,
    val email: String? = null,
    val password: String? = null,
    val providerId: Long,
    val providerName: String? = null,
    val firstName: String? = null,
    val lastName: String? = null,
    val creditcontrol: Boolean? = null
)

@Serializable
@JsonIgnoreProperties(ignoreUnknown = true)
data class PayerUserDTO(
    val username: String,
    val email: String,
    val password: String,
    val payerId: Long,
    val firstName: String? = null,
    val lastName: String? = null,
)

@Serializable
data class AdminUserDTO(
    val username: String,
    val email: String,
    val password: String,
    val payerId: Long,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class ChangeCategoryDTO(
    val oldCategoryId: Long,
    val newCategoryId: Long,
    val memberNumber: String,
)

data class UpdateCategoryDTO(
    val allowOtpVerificationFailOver: Boolean? = null,
)

data class DeactivateBenefitDTO(
    val beneficiaryId: Long,
    val categoryId: Long
)


data class UpdateMemberDTO(
    val id: Long,
    val name: String?,
    val phoneNumber: String?,
    val email: String?,
    val dob: LocalDate?,
    val canUseBiometrics: Boolean?,
)

data class AuditLoggingDTO(
    var action: String,
    var user: String,
    var data: String? = null,
    var organisation: String? = null,
    var reason: String? = null,
    var narration: String? = null,
    val type: ChangeLogType?,
    val beneficiary: Beneficiary? = null,
    val bankAccount: ProviderBankAccount? = null,
    val payerProviderUserMapping: PayerProviderUserMapping? = null,
    val benefitPayerAdmin: BenefitPayerAdmin? = null,
)

data class AuditLogDTO(

    var action: String,
    var user: String,
    val time: LocalDate?,
    var data: String?,
    var organisation: String?,
    var reason: String,
    val beneficiaryId: Long? = null,
    val memberNumber: String,
    val type: ChangeLogType?
)

data class MembershipAuditLogDTO(
    var action: String,
    var user: String,
    val time: LocalDateTime,
    var data: String?,
    val narration: String? = null,
    var organisation: String? = null,
    var reason: String,
    val beneficiaryId: Long? = null,
    val categoryId: Long? = null,
    val benefitId: Long? = null,
    val memberNumber: String? = null,
    val type: ChangeLogType?
)

data class AuditLogFilter(
    val beneficiaryId: Long? = null,
)

interface BenefitPayerMappingDTO {
    val id: Long
    val name: String
    val payer: Payer?
}

data class AddDeviceModel(
    @field:NotNull(message = "Device model must be provided")
    val model: String,
    val description: String?,
    val addedBy: String,
)

data class AddSIM(
    val simNumber: String,
    val registeredByUser: String,
)

data class SimBatchInput(
    val simNumber: String,
    val registeredByUser: String
)

data class DeviceRegistrationRequestDTO(
    @field:NotNull(message = "Device ID must be provided")
    val deviceId: String,
    val imei: Set<String>?,
    val description: String?,
    val registeredByUser: String,
    val modelId: Long,
    val accessories: Set<Accessory>?,
)

data class DeviceRegistrationBatchInput(
    val deviceId: String,
    val imei: Set<String>?,
    val description: String?,
    val registeredByUser: String,
    val model: String,
    val accessories: Set<Accessory>?,
)

data class AllocateProviderDeviceSimAndAccessory(
    val providerId: Long,
    val deviceCatalogId: Long,
    val simId: Long,
    var enableGeofence: Boolean = false,
    var radius: Long?,
    val accessories: Set<Accessory>?,
    val createdBy: String,
)

data class AllocateDeviceRequestDTO(
    @field:NotNull(message = "Provider ID must be provided")
    val providerId: Long,
    val createdBy: String,
    val deviceAndAccessories: Set<DeviceAndAccessory>
)

data class DeviceAndAccessory(
    val deviceCatalogId: Long,
    val simId: Long,
    var enableGeofence: Boolean = false,
    var radius: Long?,
    val accessories: Set<Accessory>?
)

data class AllocateDeviceAndSIMRequestDTO(
    val deviceCatalogId: Long,
    val simId: Long,
    val accessories: Set<Accessory>?,
    val createdBy: String,
)

data class Accessory(
    val accessory: String,
    val note: String? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class BenefitsResponse(
    @JsonProperty("data")
    val data: MutableList<BeneficiaryBenefit>,

    @JsonProperty("success")
    val success: Boolean,

    @JsonProperty("msg")
    val msg: String?
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class BeneficiaryBenefit(
    val id: Long? = 0,
    val aggregateId: String? = null,
    var status: String? = null,
    val categoryId: Long? = null,
    val beneficiaryId: Long? = null,
)

interface NearbyProvidersDTO {
    val providerId: Long
    val providerName: String
    val latitude: Double
    val longitude: Double
    val distanceInKm: Double
    val createdOn: String
    val regionName: String
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class PayerMappings(
    val payerId: Long,
    val payerName: String,
    val benefitCode: String? = null,
    val serviceId: Long,
    val serviceGroup: String,
    val providerCode: String?,
    val providerName: String?,
    val schemeName: String?,
    val policyStartDate: LocalDate,
    val policyEndDate: LocalDate
)

data class ProviderUpdateDTO(
    val latitude: Double? = null,
    val longitude: Double? = null,
    val canUseOtpVerificationFailOver: Boolean? = null
)

data class ProvidersUpdateDTO(
    val providerId: Long? = null,
    val latitude: Double? = null,
    val longitude: Double? = null,
    val canUseOtpVerificationFailOver: Boolean? = null
)

data class MultiProviderUpdateDTO(
    val providers: List<ProvidersUpdateDTO>
)

data class BeneficiaryUpdateDTO(
    val canUseBiometrics: Boolean? = null,
    val nhifNumber: String? = null,
    val dob: LocalDate? = null,
    val gender: Gender? = null,
    val phoneNumber: String? = null,
    val email: String? = null,
    val status: MemberStatus? = null,
    val joinDate: LocalDate? = null,
    val name: String? = null,
    val privilege: MemberPrivilege? = null,
    val updateType: UpdateType? = UpdateType.INDIVIDUAL,
    val migrated: Boolean? = null,
    val reason: String? = null,
    val updateBy: String,
    val otherNumber: String? = null,
)

data class BeneficiaryUpdateStatementDateDTO(
    val statementDate: LocalDateTime
)


data class BeneficiaryStagedUpdateDto(
    val familyNumber: String?,
    val name: String?,
    val dob: LocalDate?,
    val gender: Gender?,
    val memberType: BeneficiaryType?,
    val memberNumber: String?,
    val email: String?,
    val phone: String?,
    val nhifNumber: String?,
    val idNumber: String?,
    var joinDate: LocalDate?,
    var category: String?,
    var stagingStatus: StagingStatus?,
    var processBenefits: Boolean? = false,
)

data class BeneficiaryStagedMultiUpdateDto(
    val stagedId: Long,
    val familyNumber: String?,
    val name: String?,
    val dob: LocalDate?,
    val gender: Gender?,
    val memberType: BeneficiaryType?,
    val memberNumber: String?,
    val email: String?,
    val phone: String?,
    val nhifNumber: String?,
    val idNumber: String?,
    var joinDate: LocalDate?,
    var category: String?,
    var stagingStatus: StagingStatus?,
    var processBenefits: Boolean? = false,
)

data class BatchBeneficiaryUpdateDTO(
    val memberNumber: String,
    val beneficiaryId: Long,
    val name: String? = null,
    val phoneNumber: String? = null,
    val email: String? = null,
    val nhifNumber: String? = null,
    val dob: LocalDate? = null,
    val gender: Gender? = null,
    val status: MemberStatus? = null,
    val joinDate: LocalDate? = null,
    val canUseBiometrics: Boolean? = null,
    val reason: String? = null,
    val otherNumber: String? = null,
)

interface PayerSearchDTO {
    val scheme: String
    val id: Long?
    val dob: String
    val gender: String
    val categoryId: Long
    val name: String
    val memberNumber: String
    val otherNumber: String?
    val phoneNumber: String
    val status: String
    val beneficiaryType: String
}

interface BranchesDTO {
    val providerId: Long
    val providerName: String?
}

data class BenefitProviderMappingDto(
    val benefitId: Long,
    val providerId: Long,
)

data class BatchBenefitPayerProviderMappingDto(
    val payerId: Long,
    val benefitId: Long,
    val providerIds: List<Long>,
)

data class BatchBenefitProviderUnmappingDto(
    val providerIds: List<Long>,
)

data class BeneficiaryBenefitProviderMappingDto(
    val providerId: Long,
    val benefitId: Long,
    val beneficiaryId: Long
)

data class PayerProviderMappingProjectionDTO(
    val id: Long,
    val code: String?,
    val providerId: Long?,
    val providerName: String?,
    val tier: String?,
    val region: String?,
    val country: String?,
    var mapped: Boolean
)

@Projection(
    name = "benefitProviderMappingProjectionDTO",
    types = [BenefitProviderMapping::class]
)
interface BenefitProviderMappingProjectionDTO {
    val id: Long

    @get:Value("#{target.provider}")
    val provider: Provider
}

data class CustomPaging<T>(
    var content: List<T>,
    val empty: Boolean,
    val first: Boolean,
    val last: Boolean,
    val number: Int,
    val numberOfElements: Int,
    val pageable: Pageable,
    val size: Int,
    val sort: Sort,
    val totalElements: Long,
    val totalPages: Int
)

@Projection(
    name = "beneficiaryProviderMappingProjectionDTO",
    types = [BeneficiaryProviderMapping::class]
)
interface BeneficiaryProviderMappingProjectionDTO {
    val id: Long
    val provider: Provider
    var benefit: Benefit
}

data class CreateRestrictionDto(
    @field:NotNull(message = "Name should not be null")
    @field:NotBlank(message = "Name should not be blank")
    val name: String,
    @field:NotNull(message = "Payer Id should not be null")
    val payerId: Long,
    @field:NotNull(message = "Restriction Type should not be null")
    val restrictionType: RestrictionType
)

data class BatchProviderRestrictionDto(
    val restrictionId: Long,
    val providerIds: List<Long>,
)

data class BatchProvidersRestrictionDto(
    val providerIds: List<Long>,
)

@Projection(
    name = "providerRestrictionSetProjectionDTO",
    types = [BenefitRestrictionProviderSet::class]
)
interface ProviderRestrictionSetProjectionDTO {
    val id: Long
    val provider: Provider
    val createDate: LocalDateTime
}

@Projection(
    name = "payerProviderMappingProjectionWithRestrictionCheckDTO",
    types = [PayerProviderMapping::class]
)
interface PayerProviderMappingProjectionWithRestrictionCheckDTO {
    val id: Long
    val code: String?

    @get:Value("#{target.provider.id}")
    val providerId: Long?

    @get:Value("#{target.provider.name}")
    val providerName: String?

    @get:Value("#{target.provider.tier}")
    val tier: String?

    @get:Value("#{target.provider.region.name}")
    val region: String?

    @get:Value("#{target.provider.region.country.name}")
    val country: String?
}

data class PayerProviderWithRestrictionStatusDTO(
    val id: Long,
    val code: String?,
    val providerId: Long?,
    val providerName: String?,
    val tier: String?,
    val region: String?,
    val country: String?,
    val inRestriction: Boolean,
    val dateAddedToRestriction: LocalDateTime?
)

data class BenefitProviderRestrictionCheckDto(
    val beneficiaryId: Long,
    val providerId: Long,
    val benefitIds: List<Long>
)

data class BenefitProviderRestrictionResponseDto(
    val beneficiaryId: Long,
    val providerId: Long,
    val benefitId: Long,
    var restricted: Boolean = false,
    var message: String? = null
)

data class RestrictionCheckDto(
    val restricted: Boolean,
    var message: String? = null
)

data class AllowedResponseDto(
    val allowed: Boolean
)

data class CreateServiceCatalogDto(
    var name: String,
    var description: String?
)


data class MapServiceAndBenefitCatalogBatchDto(
    var serviceCatalogId: Long,
    var benefitCatalogIds: List<Long>
)

data class UnmapServiceAndBenefitCatalogBatchDto(
    var benefitCatalogIds: List<Long>
)

data class BatchMapServiceProvidersDto(
    var serviceCatalogId: Long,
    var providerIds: List<Long>
)

data class IdsListDto(
    @JsonAlias("benefitIds")
    val ids: List<Long>
)

data class IdsSetDto(
    val ids: Set<Long>
)

data class ServiceGroupListDto(
    val serviceGroups: Collection<ServiceGroup>
)

@Projection(
    name = "getPlanFromBenefitProjectionDTO",
    types = [Benefit::class]
)
interface GetPlanFromBenefitProjectionDTO {
    @get:Value("#{target.id}")
    val benefitId: Long

    @get:Value("#{target.name}")
    var benefitName: String

    @get:Value("#{target.category.policy.plan}")
    val plan: Plan
}

@Projection(
    name = "findBenefitsByServiceGroupsProjectionDTO",
    types = [Benefit::class]
)
interface FindBenefitsByServiceGroupsProjectionDTO {
    @get:Value("#{target.id}")
    val benefitId: Long

    @get:Value("#{target.name}")
    var benefitName: String

    @get:Value("#{target.benefitRef.id}")
    val benefitRefId: Long

    @get:Value("#{target.benefitRef.name}")
    val serviceName: Long

    @get:Value("#{target.benefitRef.serviceGroup}")
    var serviceGroup: ServiceGroup
}

@Projection(
    name = "requestTypeProjectionDto",
    types = [RequestType::class]
)
interface RequestTypeProjectionDto {
    var name: String
}

@Projection(
    name = "requestServiceProjectionDto",
    types = [RequestService::class]
)
interface RequestServiceProjectionDto {
    var name: String
}

data class AddRequestTypeServiceDto(
    val requestName: String,
    val serviceName: List<String>
)

data class FilterBeneficiariesDto(
    val payerId: Long? = null,
    val policyIds: Set<Long>? = null,
    val planIds: Set<Long>? = null,
    val categoryIds: Set<Long>? = null,
    val idNumbers: Set<String>? = null,
    val phoneNumbers: Set<String>? = null,
    val statuses: Set<MemberStatus>? = null,
    val beneficiaryTypes: Set<BeneficiaryType>? = null,
    val verificationMode: VerificationMode? = null,
    val changeLogType: ChangeLogType? = null,
    val reportType: ReportType? = null,
    val fromDate: String? = null,
    val toDate: String? = null
)


data class BeneficiaryResponseDto(
    var id: Long = 0,
    var name: String,
    var memberNumber: String,
    var nhifNumber: String?,
    var dob: LocalDate,
    var gender: Gender,
    var phoneNumber: String?,
    var email: String?,
    var beneficiaryType: BeneficiaryType,
    var category: Category,
    var canUseBiometrics: Boolean? = null,
    var changeLog: List<AuditLog>? = null,
    var processedTime: LocalDateTime?,
    var status: MemberStatus?,
    var joinDate: LocalDate? = null,
    var familySize: Int? = null,
    var biometricCaptureDate: LocalDateTime? = null,
    var biometricStatus: BiometricStatus? = null
)

data class eHealtheneficiaryResponseDto(
    var id: Long?,
    var name: String?,
    var memberNumber: String?,
    var gender: Gender?,
    var beneficiaryType: BeneficiaryType?,
    var phoneNumber: String?,
    var status: MemberStatus?,
    var dob: LocalDate?
)

data class eHealtheneficiaryData(
    var id: Long?,
    var memberNumber: String?,
    var memberName: String?,
    var gender: Gender?,
    var beneficiaryType: BeneficiaryType?,
    var phoneNumber: String?,
    var status: MemberStatus?,
    val categoryId: Long?,
)


data class BeneficiariesDto(
    val beneficiaryIds: List<Long>,
    val memberNumbers: List<String>
)

data class FingerPrintDto(
    val biometricId: Long,
    val beneficiaryId: Long? = null,
    val memberNumber: String?,
    val captureDate: LocalDateTime?,
    val biometricStatus: BiometricStatus?
)

enum class SearchType {
    ALL, MAIN, BRANCH
}

enum class BenefitSearchType {
    ALL, MAIN, SUB_LIMIT
}

enum class ReportType {
    MEMBERSHIP_REPORT, MEMBERSHIP_STATUS, MEMBERSHIP_EDITS, BIOMETRIC_CAPTURE, CATEGORY_CHANGE
}

data class SupportingDocumentRequest(
    var allocationId: Long,
    var uploadedBy: String,
    val supportingDocuments: Array<String>
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as SupportingDocumentRequest

        if (allocationId != other.allocationId) return false
        if (uploadedBy != other.uploadedBy) return false
        return supportingDocuments.contentEquals(other.supportingDocuments)
    }

    override fun hashCode(): Int {
        var result = allocationId.hashCode()
        result = 31 * result + uploadedBy.hashCode()
        result = 31 * result + supportingDocuments.contentHashCode()
        return result
    }
}

data class DeviceStatusUpdate(
    val deviceStatus: DeviceStatus,
    val updatedBy: String,
    val reason: String?
)

data class SIMStatusUpdate(
    val simStatus: SimStatus,
    val updatedBy: String,
    val reason: String?
)

data class AllocationStatusUpdate(
    val allocationStatus: AllocationStatus,
    val updatedBy: String,
    val reason: String?
)

data class ProviderDeviceAllocation(
    val providerId: Long,
    val allocatedBy: String,
    var enableGeofence: Boolean = false,
    var radius: Long?,
    val allocationIds: Set<Long>
)

data class SmsDTO(
    val phone: String,
    val msg: String
)

class DeviceCatalogResponseDto(
    val id: Long,
    val deviceId: String,
    val status: DeviceStatus,
    val description: String?,
    val deviceModel: DeviceModel,
    val registeredOn: LocalDateTime,
    val imei: List<DeviceImei>,
    var allocations: List<DeviceAllocationResponseDto>,
    val accessories: List<DeviceAccessory>,
    val logs: List<DeviceManagementAuditLog>
)

class DeviceAllocationResponseDto(
    var id: Long = 0,
    var provider: Provider? = null,
    var deviceSim: DeviceSim? = null,
    var enableGeofence: Boolean = false,
    var radius: Long? = null,
    var status: AllocationStatus,
    var createdBy: String,
    var allocatedBy: String? = null,
    var checkedBy: String? = null,
    var checkerStatus: CheckerStatus? = null,
    var note: String? = null,
    var dateCreated: LocalDateTime,
    var accessories: List<DeviceAccessory>,
    var documents: List<DeviceDocument>,
    var logs: List<DeviceManagementAuditLog>
)

data class CategoryStatusUpdateDTO(
    val updateBy: String,
    val reason: String,
    val categoryStatus: CategoryStatus,
    val categoryIds: Set<Long>
)

data class BenefitStatusUpdateDTO(
    val updateBy: String,
    val reason: String,
    val benefitStatus: BenefitStatus,
    val benefitIds: Set<Long>
)

enum class UpdateType {
    INDIVIDUAL,
    FAMILY
}

data class BeneficiaryStatusUpdateDTO(
    val updateBy: String,
    val reason: String,
    val status: MemberStatus,
    val updateType: UpdateType,
    val beneficiaryIds: Set<Long>
)

data class CategoryChangeDto(
    val categoryId: Long,
    val updateBy: String,
    val reason: String,
    val transferUtilization: Boolean = true
)

data class BeneficiariesCategoryChangeDto(
    val beneficiaryIds: Set<Long>,
    val categoryId: Long,
    val updateBy: String,
    val reason: String,
    val transferUtilization: Boolean = true
)

enum class AccessModeType {
    CREDIT_BASIS, REIMBURSEMENT_BASIS, PRE_AUTH_BASIS
}

data class BeneficiaryBenefitsDto(
    val aggregateId: String,
    val benefitId: Long,
    val parentBenefitId: Long? = null,
    val parentBeneficiaryId: Long? = null,
    val beneficiaryId: Long,
    val memberName: String,
    val memberNumber: String,
    val benefitName: String,
    val accessMode: AccessModeType,
    val suspensionThreshold: BigDecimal,
    val preAuthThreshold: BigDecimal? = null,
    val thresholdAction: ThresholdAction?,
    val sharing: BenefitDistribution,
    val initialLimit: BigDecimal,
    val categoryId: Long,
    val payerId: Long,
    val policyId: Long? = null,
    val startDate: LocalDate,
    val endDate: LocalDate,
    val joinDate: LocalDate? = null,
    val gender: Gender,
    val memberType: BeneficiaryType,
    val catalogId: Long,
    val jicEntityId: Int?,
    val apaEntityId: Int?,
    val benefitType: BenefitType?,
    val capitationType: CapitationType? = null,
    val capitationPeriod: CapitationPeriod? = null,
    val visitCountPeriod: VisitCountPeriod? = null,
    val capitationMaxVisitCount: Int? = null,
    val capitationFacilitiesCount: Int? = null,
    val requireBeneficiaryToSelectProvider: Boolean? = null,
    val visitLimit: BigDecimal? = null,
    val daysOfAdmissionLimit: Int? = null,
    val amountPerDayLimit: BigDecimal? = null,
    val applicableMinAge: BigDecimal? = null,
    val applicableMaxAge: BigDecimal? = null,
    val transferable: Boolean? = false,
    val billable: Boolean? = null,
    val dob: LocalDate? = null,
    val migratedFromCategoryId: Long? = null,
    val subBenefits: Set<BeneficiaryBenefitsDto>,
    val transferUtilization: Boolean? = null
)

data class MigrateBenefitsDto(
    val beneficiaryIds: Set<Long>,
    val categoryId: Long
)

data class BeneficiaryBenefitChangeResponseDto(
    val id: Long,
    val aggregateId: String,
)

enum class TemplateType {
    MEMBER_ACTIVATION_OR_DEACTIVATION,
    MEMBER_EDIT,
    DEVICE_UPLOAD,
    SIM_UPLOAD,
    MEMBER_ADDITION,
    PROVIDER_BULK_UPDATE,
    PROVIDER_BULK_MAPPING
}

enum class BeneficiaryBenefitProcess {
    ALL, ONLY_PROCESSED_BENEFICIARIES, ONLY_UNPROCESSED_BENEFICIARIES, ONLY_SELECTED_BENEFICIARIES
}

data class ProcessCategoryDto(
    val categoryId: Long,
    val processBeneficiaries: BeneficiaryBenefitProcess = BeneficiaryBenefitProcess.ALL,
)

data class ProcessSelectBenefitsDto(
    val benefitIds: Set<Long>,
    val beneficiaryIds: Set<Long>? = null,
    val processSubBenefits: Boolean = true,
    val processBeneficiaries: BeneficiaryBenefitProcess = BeneficiaryBenefitProcess.ALL,
)

data class FilterPayerProvidersDto(
    val query: String?,
    val countryId: Long?,
    val regionId: Long?,
    val providerType: ProviderType?,
    val tier: ProviderTier?,
    val mainFacilityId: Long?,
)

interface FingerPrintProjectionDTO {
    val biometricId: Long
    val beneficiaryId: Long?
    val memberNumber: String?
    val captureDate: LocalDateTime?
    val biometricStatus: BiometricStatus?
}

data class BeneficiaryInfoToEdit(
    val memberName: String? = null,
    val dob: LocalDate? = null,
    val joinDate: LocalDate? = null,
    val gender: Gender? = null
)

data class EditBeneficiaryInfoDto(
    val beneficiaryId: Long,
    val categoryId: Long,
    val memberName: String? = null,
    val dob: LocalDate? = null,
    val joinDate: LocalDate? = null,
    val gender: Gender? = null
)

data class UpdateProfileDto(
    val oldPhoneNumber: String,
    val newPhoneNumber: String
)

data class DataCollectionBeneficiaryDTO(
    var employeeNumber: String?,
    var policyNumber: String?,
    var employeeName: String?,
    var employeeKrapin: String?,
    var name: String?,
    var employeeIdNumber: String?,
    var employeeDateOfBirth: LocalDate?,
    var employeeMobileNumber: String?,
    var age: String?,
    var employeePostalCode: String?,
    var employeeGender: String? = null,
    var employeeRelationship: String?,
    var employeePobox: String?,
    var employeeMaritalStatus: String?,
    var employeeTown: String?,
    var employeeMembertype: String?,
    var employeeBenefitType: String?,
    var employeeCategory: String?,
    var county: String?,
    var category: String?,
    var dependantName: String?,
    var dependantRelationship: String?,
    var dependantDateOfBirth: LocalDate?,
    var dependantGender: String?,
    var dependantIdorBirthCertificateNo: String?,
    var dependantBenefitType: String?,
    var dependantPhoneNumber: String?,
    var dependants: Set<DataCollectionBeneficiaryDTO>?,
    var documents: Set<DataCollectionDocumentDTO>?,
    var paymentMode: String?,
    var benefits: Set<DataCollectionBenefitDTO>?,
    var provider: String?,
    var staff: String?,

    )

enum class DataCollectionPaymentMode {
    MONTHLY, BONUS, ANNUAL, PREMIUMFINANCING
}

enum class DataCollectionRelationship {
    PRINCIPAL,
    SPOUSE,
    CHILD
}

enum class DataCollectionBenefitType(val description: String) {
    OUTPATIENT("OUTPATIENT"), INPATIENT("INPATIENT")
}


data class DataCollectionBenefitDTO(
    val benefitName: String?,
    val paymentMode: String?,
    val category: String?,
    val spouseCategory: String?,
    val noOfAdults: Long?,
    val noOfChildren: Long?,
    val adultPremiumAmount: BigDecimal?,
    val spousePremiumAmount: BigDecimal?,
    val totalAdultPremiumAmount: BigDecimal?,
    val totalSpousePremiumAmount: BigDecimal?,
    val childPremiumAmount: BigDecimal?,
    val totalChildPremiumAmount: BigDecimal?,
    val totalPremiumAmount: BigDecimal?,
    var nextOfkinName: String?,
    var nextOfKinDob: String?,
    var nextOfKinIdNumber: String?,
)

enum class DataCollectionReportType {
    PRINCIPALS, PRINCIPALS_AND_DEPENDANTS, DEPENDANTS
}

data class FilterDataCollectionBeneficiariesDto(
    val payerId: Long? = null,
    val reportType: DataCollectionReportType? = null,
    val fromDate: String? = null,
    val toDate: String? = null
)

data class DataCollectionDocumentDTO(
    var fileUrl: String?,
    var type: String?,
)

data class FileDownloadResponse(
    @JsonProperty("data")
    val data: ByteArray,
    @JsonProperty("msg")
    val msg: String?,
    @JsonProperty("success")
    val success: Boolean
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as FileDownloadResponse

        if (!data.contentEquals(other.data)) return false
        if (msg != other.msg) return false
        if (success != other.success) return false

        return true
    }

    override fun hashCode(): Int {
        var result = data.contentHashCode()
        result = 31 * result + (msg?.hashCode() ?: 0)
        result = 31 * result + success.hashCode()
        return result
    }
}


@Serializable
data class UpdatedRolesDTO(
    val username: String,
    val enabledRoles: Set<String>? = null,
    val disabledRoles: Set<String>? = null,
)

interface KeycloakUserResolve {
    val id: String
    val email: String?
    val firstName: String?
    val lastName: String?
    val userName: String
    val name: String?
    val value: String?
}

data class CreateProviderAccountDto(
    val providerId: Long,
    val bankName: String,
    val bankBranch: String?,
    val branchCode: String?,
    val bankCode: String?,
    val swiftCode: String?,
    val accountName: String,
    val accountNumber: String,
    val actionedBy: String,
)

data class CreateMappedProviderAccountDto(
    val payerId: Long,
    val providerId: Long,
    val bankName: String,
    val bankBranch: String?,
    val branchCode: String?,
    val bankCode: String?,
    val swiftCode: String?,
    val accountName: String,
    val accountNumber: String,
    val actionedBy: String,
)

data class UpdateProviderAccountDto(
    val bankName: String?,
    val bankBranch: String?,
    val branchCode: String?,
    val bankCode: String?,
    val swiftCode: String?,
    val accountName: String?,
    val accountNumber: String?,
    val actionedBy: String,
)

data class MapProviderAccountToPayerDto(
    val payerId: Long,
    val accountId: Long,
    val actionedBy: String,
)

data class AddProviderContactDto(
    val providerId: Long,
    var phoneNumber: String?,
    var email: String?,
    var contactPerson: ContactPerson?,
    val actionedBy: String,
)

data class MapProviderUserToPayerDto(
    val payerId: Long,
    val providerId: Long,
    var userId: String,
    var contactPerson: ContactPerson,
    val actionedBy: String,
)

data class AddPayerPolicyMappingDto(
    val payerId: Long,
    val policyId: Long,
    val actionedBy: String,
)

data class UpdatePayerPolicyMappingDto(
    val mappingEnabled: Boolean? = null,
    val actionedBy: String,
)

data class AddBenefitPayerAdminDto(
    val payerId: Long,
    val benefitId: Long,
    val mappingEnabled: Boolean? = null,
    val actionedBy: String,
)

data class OnboardPrincipalDTO(
    @field:NotBlank(message = "Grower number is required.") val growerNumber: String,
    @field:NotBlank(message = "First name is required.") val firstName: String,
    val middleName: String? = null,
    @field:NotBlank(message = "Surname is required.") val surname: String,
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    @field:NotNull(message = "Date of birth is required.")
    @field:Past(message = "Date of birth must be in the past") val dateOfBirth: LocalDate,
    @field:NotBlank(message = "ID number is required.") val idNumber: String,
    @field:NotNull(message = "Gender is required.") val gender: Gender,
    @field:NotNull(message = "Marital status is required.") val maritalStatus: MaritalStatus,
    @field:NotBlank(message = "Mobile number is required.") val mobileNumber: String,
    @field:NotBlank(message = "Application date is required.") val applicationDate: String,
    val town: String? = null,
    @field:NotBlank(message = "County is required.") val county: String,
    @field:NotBlank(message = "Hospital is required.") val hospital: String,
    @field:NotNull(message = "Inpatient premium is required.") val inpatientPremium: BigDecimal,
    @field:NotNull(message = "Outpatient premium is required.") val outpatientPremium: BigDecimal,
    @field:NotNull(message = "Payment mode is required.") val paymentMode: OnboardPaymentMode,
    @field:NotNull(message = "Beneficiary type is required.") val beneficiaryType: BeneficiaryType = BeneficiaryType.PRINCIPAL,
    @field:NotNull(message = "Agent information is required.") val agent: OnboardAgentDTO,
    @field:NotBlank(message = "Staff name is required.") val createdByName: String,
    @field:NotBlank(message = "Staff username is required.") val createdByUsername: String,
    val premiumPayerGrowerNumber: String? = null,
    val applicationFormContextIds: List<String>? = null,
    val applicationFormInfoIds: List<String>? = null,
    val hospitalFormContextIds: List<String>? = null,
    val hospitalFormInfoIds: List<String>? = null,
    val studentIdContextIds: List<String>? = null,
    val studentIdInfoIds: List<String>? = null
)

data class OnboardPrincipalResponseDTO(
    val id: Long,
    val name: String,
    val growerNumber: String,
    val memberNumber: String,
    val dateOfBirth: LocalDate,
    val applicationDate: String,
    val age: Int,
    val idNumber: String,
    val gender: Gender,
    val maritalStatus: MaritalStatus,
    val category: OnboardPrincipalCategory,
    val mobileNumber: String,
    val town: String,
    val county: String,
    val hospital: String,
    val inpatientPremium: BigDecimal,
    val outpatientPremium: BigDecimal,
    val inpatientCategory: String,
    val outpatientCategory: String,
    val familySize: String,
    val paymentMode: OnboardPaymentMode,
    val beneficiaryType: BeneficiaryType,
    val agentName: String,
    val agentMobileNumber: String,
    val agentNumber: String,
    val agentFactory: String,
    val agentZone: String,
    val beneficiaryName: String,
    val beneficiaryIdNumber: String,
    val beneficiaryMobileNumber: String,
    val createdByName: String,
    val createdByUsername: String,
    val premiumPayerGrowerNumber: String? = null,
    val applicationFormContextIds: List<String>? = null,
    val applicationFormInfoIds: List<String>? = null,
    val hospitalFormContextIds: List<String>? = null,
    val hospitalFormInfoIds: List<String>? = null,
    val studentIdContextIds: List<String>? = null,
    val studentIdInfoIds: List<String>? = null,
    val createdAt: LocalDate,
)

data class OnboardDependantDTO(
    @field:NotBlank(message = "Fist name is required.") val firstName: String,
    val middleName: String? = null,
    @field:NotBlank(message = "Surname is required.") val surname: String,
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    @field:NotNull(message = "Date of birth is required.")
    @field:Past(message = "Date of birth must be in the past") val dateOfBirth: LocalDate,
    val idNumber: String? = null,
    @field:NotNull(message = "Gender is required.") val gender: Gender,
    val mobileNumber: String? = null,
    @field:NotBlank(message = "Hospital is required.") val hospital: String,
    @field:NotNull(message = "Outpatient premium is required.") val outpatientPremium: BigDecimal,
    @field:NotNull(message = "Outpatient premium is required.") val inpatientPremium: BigDecimal,
    @field:NotNull(message = "Beneficiary type is required.") val beneficiaryType: OnboardDependantBeneficiaryType
)

data class OnboardDependantResponseDTO(
    val id: Long,
    val name: String,
    val principalGrowerNumber: String,
    val memberNumber: String,
    val familySize: String,
    val dateOfBirth: LocalDate,
    val applicationDate: String,
    val age: Int,
    val idNumber: String?,
    val gender: Gender,
    val mobileNumber: String,
    val maritalStatus: String,
    val town: String,
    val county: String,
    val hospital: String,
    val inpatientPremium: BigDecimal,
    val outpatientPremium: BigDecimal,
    val inpatientCategory: String,
    val outpatientCategory: String,
    val beneficiaryType: OnboardDependantBeneficiaryType,
    val agentName: String,
    val agentMobileNumber: String,
    val agentNumber: String,
    val agentFactory: String,
    val agentZone: String,
    val createdByName: String,
    val createdByUsername: String,
    val createdAt: LocalDate,
)

data class OnboardBeneficiaryDTO(
    @field:NotBlank(message = "First name is required.") val firstName: String,
    val middleName: String? = null,
    @field:NotBlank(message = "Surname is required.") val surname: String,
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    @field:NotNull(message = "Date of birth is required.")
    @field:Past(message = "Date of birth must be in the past") val dateOfBirth: LocalDate,
    @field:NotBlank(message = "ID number is required.") val idNumber: String,
    @field:NotNull(message = "Gender is required.") val gender: Gender,
    @field:NotBlank(message = "Mobile number is required.") val mobileNumber: String,
)

data class OnboardBeneficiaryResponseDTO(
    val id: Long,
    val name: String,
    val dateOfBirth: LocalDate,
    val age: Int,
    val idNumber: String,
    val gender: Gender,
    val mobileNumber: String,
    val createdAt: LocalDate,
)

data class OnboardDocumentDTO(
    @field:NotNull(message = "Document type is required.") val type: OnboardDocumentType,
    @field:NotBlank(message = "Document url is required.") val url: String
)

data class OnboardDocumentResponseDTO(
    val id: Long,
    val type: OnboardDocumentType,
    val url: String,
)

data class OnboardBenefitDTO(
    @field:NotNull(message = "Benefit limit is required.") val benefitLimit: BigDecimal,
    @field:NotNull(message = "Benefit type is required.") val benefitType: OnboardBenefitType,
    val benefitMonthlyAmount: BigDecimal? = BigDecimal(0),
)

data class OnboardBenefitResponseDTO(
    val id: Long,
    val benefitType: OnboardBenefitType,
    val benefitLimit: BigDecimal,
    val benefitUtilization: BigDecimal,
    val benefitMonthlyAmount: BigDecimal,
    val balance: BigDecimal,
    val createdAt: LocalDate
)

data class OnboardAgentDTO(
    val id: Long? = null,
    @field:NotBlank(message = "Name is required.") val name: String,
    @field:NotBlank(message = "Mobile number is required.") val mobileNumber: String,
    @field:NotBlank(message = "Agent number is required.") val agentNumber: String,
    val idNumber: String? = null,
    @field:NotBlank(message = "Factory is required.") val factory: String,
    val emailAddress: String? = null,
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    @field:NotNull(message = "Date of birth is required.") val dateOfBirth: String,
    val registrationDate: String? = null,
    @field:NotBlank(message = "Agent zone is required.") val zone: String,
)

data class OnboardAgentResponseDTO(
    val id: Long,
    val name: String,
    val agentNumber: String,
    val idNumber: String,
    val mobileNumber: String,
    val factory: String,
    val emailAddress: String?,
    val dateOfBirth: String,
    val registrationDate: String?,
    val zone: String,
    val createdAt: LocalDate
)

data class OnboardMemberRequestDTO(
    @field:Valid val principal: OnboardPrincipalDTO,
    @field:Valid val dependants: List<OnboardDependantDTO>?,
    @field:Valid val beneficiaries: List<OnboardBeneficiaryDTO>,
    @field:Valid val documents: List<OnboardDocumentDTO>?,
)

data class AddPrincipalDependantsRequestDTO(
    @field:Valid val dependants: List<OnboardDependantDTO>,
    @field:Valid val documents: List<OnboardDocumentDTO>,
    val applicationFormContextIds: List<String>? = null,
    val applicationFormInfoIds: List<String>? = null,
    val hospitalFormContextIds: List<String>? = null,
    val hospitalFormInfoIds: List<String>? = null,
    val studentIdContextIds: List<String>? = null,
    val studentIdInfoIds: List<String>? = null
)

data class UserRoleUpdateDTO(
    val addedRoles: Set<String>?,
    val removedRoles: Set<String>?,
    val actionedBy: String,
)

data class BatchUserRoleUpdateDTO(
    val userIds: List<String>,
    val addedRoles: Set<String>?,
    val removedRoles: Set<String>?,
    val actionedBy: String,
)

data class AddKeycloakGroupDto(
    val payerId: Long? = null,
    val providerId: Long? = null,
    val payerAdminId: Long? = null,
    val groupName: String,
    val roles: Set<String>? = null
)

data class QueryBenefitCatalog(
    var id: Long?,
    var code: String,
    var name: String,
    var serviceGroup: ServiceGroup,
    var benefitName: String,
)

@Projection(
    name = "benefitAdminPlansProjectionDTO",
    types = [Plan::class]
)
interface BenefitAdminPlansProjectionDTO {
    var id: Long
    var name: String
    var type: PlanType
    var planDate: PlanDate?
    var accessMode: BenefitAccessMode
}

data class UserRolesAuditLogResponse(
    val id: Long,
    val userId: String,
    val actionedBy: String,
    val eventTimestamp: String,
    val addedRoles: Set<String>?,
    val removedRoles: Set<String>?,
    val createdAt: String,
)
