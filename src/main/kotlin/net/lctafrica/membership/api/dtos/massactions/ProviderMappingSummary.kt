package net.lctafrica.membership.api.dtos.massactions


data class ProviderMappingSummary(
    val totalRows: Int,
    val successfulMappings: Int,
    val skippedMappings: Int,
    val errors: List<ProviderMappingError>,
    val processingTimeMs: Long = 0,
    val recordsPerSecond: Double = 0.0
)


data class ProviderMappingError(
    val rowNumber: Int,
    val providerId: Long?,
    val payerId: Long?,
    val errorMessage: String,
    val errorType: ProviderMappingErrorType
)


enum class ProviderMappingErrorType {
    PROVIDER_NOT_FOUND,
    PAYER_NOT_FOUND,
    ALREADY_MAPPED,
    MAPPING_FAILED,
    INVALID_DATA,
    DATABASE_ERROR,
    TIMEOUT_ERROR,
    VALIDATION_ERROR,
    DUPLICATE_MAPPING,
    CONCURRENT_MODIFICATION,
    INSUFFICIENT_PERMISSIONS,
    SYSTEM_ERROR
}