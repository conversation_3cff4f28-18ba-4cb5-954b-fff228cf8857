package net.lctafrica.membership.api.dtos

import java.time.LocalDateTime
import javax.validation.constraints.Email
import javax.validation.constraints.NotBlank
import javax.validation.constraints.NotNull


data class PayerUserUpdateDTO(
    @field:NotBlank(message = "User ID cannot be blank")
    val userId: String,

    val firstName: String? = null,

    val lastName: String? = null,

    @field:Email(message = "Email should be valid")
    val email: String? = null,

    @field:NotBlank(message = "Reason for update cannot be blank")
    val reason: String,

    @field:NotBlank(message = "Updated by username cannot be blank")
    val updatedBy: String
)


data class UserStatusUpdateDTO(
    @field:NotBlank(message = "User ID cannot be blank")
    val userId: String,

    @field:NotNull(message = "Enabled status cannot be null")
    val enabled: Boolean,

    @field:NotBlank(message = "Reason for status change cannot be blank")
    val reason: String,

    @field:NotBlank(message = "Updated by username cannot be blank")
    val updatedBy: String
)


data class PasswordResetRequestDTO(
    @field:NotBlank(message = "Username cannot be blank")
    val username: String
)


data class PasswordResetResponseDTO(
    val userId: String,
    val username: String,
    val email: String,
    val payerId: Long,
    val payerName: String,
    val temporaryPassword: String,
    val expiryTime: LocalDateTime
)
