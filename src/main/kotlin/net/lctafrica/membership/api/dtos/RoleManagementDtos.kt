package net.lctafrica.membership.api.dtos

import java.time.LocalDateTime
import javax.validation.constraints.NotBlank
import javax.validation.constraints.NotNull


data class CreateCustomRoleDto(
    @field:NotBlank(message = "Role name is required")
    val name: String,

    @field:NotBlank(message = "Role description is required")
    val description: String,

    @field:NotNull(message = "At least one permission is required")
    val permissions: Set<String>,

    @field:NotBlank(message = "Created by is required")
    val createdBy: String,

    @field:NotNull(message = "Payer ID is required")
    val payerId: Long
)


data class UpdateCustomRoleDto(
    val name: String? = null,

    val description: String? = null,

    val permissionsToAdd: Set<String>? = null,

    val permissionsToRemove: Set<String>? = null,

    val usersToAdd: Set<String>? = null,

    val usersToRemove: Set<String>? = null,

    @field:NotBlank(message = "Updated by is required")
    val updatedBy: String
)


data class CustomRoleDto(
    val id: Long,
    val name: String,
    val description: String,
    val isPredefined: Boolean,
    val permissions: List<PermissionDto>,
    val createdBy: String,
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime?,
    val payerId: Long,
)


data class CreateUserGroupDto(
    @field:NotBlank(message = "Group name is required")
    val name: String,

    @field:NotBlank(message = "Group description is required")
    val description: String,

    val customRoleIds: Set<Long>? = null,

    val predefinedRoles: Set<String>? = null,

    val userIds: Set<String>? = null,

    @field:NotBlank(message = "Created by is required")
    val createdBy: String,

    @field:NotNull(message = "Payer ID is required")
    val payerId: Long
)


data class UserGroupDto(
    val id: Long,
    val keycloakGroupId: String,
    val name: String,
    val description: String,
    val customRoles: List<CustomRoleDto>,
    val predefinedRoles: List<String>,
    val createdBy: String,
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime?,
    val payerId: Long
)


data class AddUsersToGroupDto(
    @field:NotNull(message = "User IDs are required")
    val userIds: Set<String>,

    @field:NotBlank(message = "Added by is required")
    val addedBy: String
)


data class UserGroupMemberDto(
    val id: Long,
    val userId: String,
    val userName: String?,
    val userEmail: String?,
    val name: String?,
    val addedBy: String,
    val addedAt: LocalDateTime
)


data class CreateDenyPolicyDto(
    @field:NotBlank(message = "Policy name is required")
    val name: String,

    @field:NotNull(message = "User IDs are required")
    val userIds: Set<String>,

    @field:NotNull(message = "Denied permissions are required")
    val deniedPermissions: Set<String>,

    @field:NotBlank(message = "Reason is required")
    val reason: String,

    @field:NotNull(message = "Payer id is required")
    val payerId: Long,

    val expiresAt: LocalDateTime? = null,

    @field:NotBlank(message = "Created by is required")
    val createdBy: String
)


data class DenyPolicyDto(
    val id: Long,
    val name: String,
    val payerId: Long,
    val users: List<UserBasicDto>,
    val deniedPermissions: List<String>,
    val reason: String?,
    val createdBy: String,
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime?,
    val expiresAt: LocalDateTime?
) {
    fun toUserDenyPolicyDto(): UserDenyPolicyDto {
        return UserDenyPolicyDto(
            id = this.id,
            name = this.name,
            payerId = this.payerId,
            deniedPermissions = this.deniedPermissions,
            reason = this.reason,
            createdBy = this.createdBy,
            createdAt = this.createdAt,
            updatedAt = this.updatedAt,
            expiresAt = this.expiresAt
        )
    }
}


data class UserDenyPolicyDto(
    val id: Long,
    val name: String,
    val payerId: Long,
    val deniedPermissions: List<String>,
    val reason: String?,
    val createdBy: String,
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime?,
    val expiresAt: LocalDateTime?
)


data class UserBasicDto(
    val userId: String,
    val userName: String?,
    val userEmail: String?,
    val name: String?
)


data class UserEffectiveRolesDto(
    val userId: String,
    val userName: String?,
    val userEmail: String?,
    val directRoles: List<PermissionDto>,
    val inheritedRoles: Map<String, List<PermissionDto>>, // Group name -> List of roles
    val deniedPermissions: List<PermissionDto>,
    val effectivePermissions: List<PermissionDto>
)

data class UserDirectRolesDto(
    val userId: String,
    val userName: String?,
    val userEmail: String?,
    val directRoles: List<PermissionDto>
)

data class UpdateDenyPolicyDto(
    val name: String? = null,
    val reason: String? = null,
    val expiresAt: LocalDateTime? = null,
    val addUsers: Set<String>? = null,
    val removeUsers: Set<String>? = null,
    val addPermissions: Set<String>? = null,
    val removePermissions: Set<String>? = null,

    @field:NotBlank(message = "Updated by is required")
    val updatedBy: String
)

data class PermissionDto(
    val name: String,
    val description: String?
)

data class RemoveUsersFromGroupDto(
    @field:NotNull(message = "User IDs are required")
    val userIds: Set<String>,

    @field:NotBlank(message = "Actioned by is required")
    val actionedBy: String
)

data class UpdateGroupRolesDto(
    val customRoleIds: Set<Long>? = null,

    val predefinedRoles: Set<String>? = null,

    @field:NotBlank(message = "Updated by is required")
    val updatedBy: String
)

data class UpdateUserGroupDto(
    val name: String? = null,

    val description: String? = null,

    val customRolesToAdd: Set<Long>? = null,

    val customRolesToRemove: Set<Long>? = null,

    val predefinedRolesToAdd: Set<String>? = null,

    val predefinedRolesToRemove: Set<String>? = null,

    val usersToAdd: Set<String>? = null,

    val usersToRemove: Set<String>? = null,

    @field:NotBlank(message = "Updated by is required")
    val updatedBy: String
)

data class UserStandaloneRolesDto(
    val userId: String,
    val userName: String?,
    val userEmail: String?,
    val customRoles: List<CustomRoleDto>,
    val standaloneRoles: List<PermissionDto>
)


data class CreatePayerUserDto(
    @field:NotBlank(message = "Username is required")
    val username: String,

    @field:NotBlank(message = "Email is required")
    val email: String,

    @field:NotBlank(message = "Password is required")
    val password: String,

    @field:NotNull(message = "Payer ID is required")
    val payerId: Long,

    @field:NotBlank(message = "First name is required")
    val firstName: String,

    @field:NotBlank(message = "Last name is required")
    val lastName: String,

    val groupIds: Set<Long>? = null,

    val customRoleIds: Set<Long>? = null,

    val predefinedRoles: Set<String>? = null,

    @field:NotBlank(message = "Created by is required")
    val createdBy: String
)
