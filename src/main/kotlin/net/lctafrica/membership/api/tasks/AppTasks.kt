package net.lctafrica.membership.api.tasks

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import net.lctafrica.membership.api.service.IConfigService
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
class AppTasks(
    private val configService: IConfigService
) {

    @Scheduled(cron = "0 0 0/1 * * ?")
    @SchedulerLock(name = "AppTaskScheduler_everyHour", lockAtLeastFor = "PT20M", lockAtMostFor = "PT30M")
    fun everyHour() {
        CoroutineScope(Dispatchers.IO).launch {
            configService.removePrincipalFromCoverIfAboveAge()
        }

        CoroutineScope(Dispatchers.IO).launch {
            configService.removeChildFromCoverIfAboveAge()
        }
    }
}