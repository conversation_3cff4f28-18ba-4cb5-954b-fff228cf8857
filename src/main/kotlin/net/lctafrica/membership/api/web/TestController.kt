package net.lctafrica.membership.api.web

import java.io.File
import java.util.UUID
import net.lctafrica.membership.api.util.AppConstants
import net.lctafrica.membership.api.util.Result
import net.lctafrica.membership.api.util.ResultFactory
import org.springframework.batch.core.Job
import org.springframework.batch.core.JobParametersBuilder
import org.springframework.batch.core.launch.JobLauncher
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.multipart.MultipartFile

@RestController
@RequestMapping("/test")
class TestController {

    @Autowired
    @Qualifier("batchJobLauncher")
    lateinit var batchJobLauncher: JobLauncher

    @Autowired
    @Qualifier("beneficiaryUploadJob")
    lateinit var beneficiaryUploadJob: Job

    @PostMapping("/upload/excel")
    fun uploadTransactionsFromExcel(
        @RequestParam("file") multipartFile: MultipartFile,
        @RequestParam("policyId", defaultValue = "0") policyId:Long,
    ): Result<Boolean> {
        try {
            val location = System.getProperty("java.io.tmpdir")
            val origFileName = multipartFile.originalFilename
            var fileFullPath = ""
            origFileName?.let {
                val fileName = it.substring(0,it.lastIndexOf("."))
                val extension = it.substring(it.lastIndexOf("."))
                fileFullPath = "$location${fileName}_${System.currentTimeMillis()}$extension"
            }
            val file = File(fileFullPath)
            multipartFile.transferTo(file)
            val params = JobParametersBuilder()
            params.addString("batchNo", UUID.randomUUID().toString())
            params.addString("fileFullPath",fileFullPath)
            params.addLong("transactionsUploadTime",System.currentTimeMillis())
            params.addLong("policyId",policyId)

            val execution = batchJobLauncher.run(beneficiaryUploadJob, params.toJobParameters())
            println("Job execution status : " + execution.status)
            return ResultFactory.getSuccessResult(msg = AppConstants.UPLOADED_SUCCESSFULLY_P)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return ResultFactory.getFailResult("Processing failed")
    }
}