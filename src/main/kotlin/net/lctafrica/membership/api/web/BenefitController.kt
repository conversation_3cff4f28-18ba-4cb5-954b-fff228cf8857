package net.lctafrica.membership.api.web

import io.swagger.v3.oas.annotations.Operation
import javax.validation.Valid
import net.lctafrica.membership.api.dtos.AddBenefitPayerAdminDto
import net.lctafrica.membership.api.dtos.BenefitSearchType
import net.lctafrica.membership.api.service.IBenefitService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/v1/membership/benefit")
class BenefitController : AbstractController() {

    @Autowired
    lateinit var benefitService: IBenefitService

    @PutMapping(value = ["/admin"], produces = ["application/json"])
    @Operation(summary = "Add/Update (enable/disable) Benefit Payer Admin")
    suspend fun addBenefitPayerAdmin(@Valid @RequestBody dto: AddBenefitPayerAdminDto) = benefitService.addBenefitPayerAdmin(dto)

    @GetMapping(value = ["/payer/{payerId}/schemes"], produces = ["application/json"])
    @Operation(summary = "Get Schemes Administered by Payer or Payer Admin")
    suspend fun getSchemesAdministeredByPayerOrPayerAdmin(@PathVariable("payerId") payerId:Long) = benefitService.getSchemesAdministeredByPayerOrPayerAdmin(payerId)

    @GetMapping(value = ["/administered"], produces = ["application/json"])
    @Operation(summary = "Get Benefits Administered by Payer or Payer Admin")
    suspend fun getBenefitsAdministeredByPayerOrPayerAdmin(
        @RequestParam("payerId") payerId:Long,
        @RequestParam("searchType") searchType: BenefitSearchType? = null,
    ) = benefitService.getSchemesAdministeredByPayerOrPayerAdmin(payerId)


}