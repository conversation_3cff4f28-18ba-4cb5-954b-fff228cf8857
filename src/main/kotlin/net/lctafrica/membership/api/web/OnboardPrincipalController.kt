package net.lctafrica.membership.api.web

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import net.lctafrica.membership.api.dtos.AddPrincipalDependantsRequestDTO
import net.lctafrica.membership.api.dtos.OnboardBeneficiaryDTO
import net.lctafrica.membership.api.dtos.OnboardPrincipalDTO
import net.lctafrica.membership.api.dtos.OnboardPrincipalResponseDTO
import net.lctafrica.membership.api.service.OnboardPrincipalService
import net.lctafrica.membership.api.util.toResponse
import org.springframework.data.domain.Page
import org.springframework.data.domain.Sort
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import java.time.LocalDate
import javax.validation.Valid

@RestController
@RequestMapping("/api/v1/membership/onboard/principals")
@Validated
@Tag(name = "Onboard Principal", description = "Operations related to the Onboard Portal principal.")
class OnboardPrincipalController(private val onboardPrincipalService: OnboardPrincipalService) {
    @PostMapping
    @Operation(description = "Create a new principal without dependants and beneficiaries.")
    fun createPrincipal(
        @Valid @RequestBody request: OnboardPrincipalDTO,
    ): ResponseEntity<Map<String, String>> {
        onboardPrincipalService.createPrincipalWithoutDependantsAndBeneficiaries(request)
        return mapOf("message" to "Principal created successfully.").toResponse(
            HttpStatus.CREATED
        )
    }

    @Operation(description = "Fetch a principal by their id.")
    @GetMapping("/{id}")
    fun getPrincipalById(@PathVariable id: Long): ResponseEntity<OnboardPrincipalResponseDTO> {
        return ResponseEntity.ok(onboardPrincipalService.getPrincipalById(id))
    }

    @Operation(description = "Fetch all principals in the system.")
    @GetMapping
    fun getAllPrincipals(
        @RequestParam(required = false) fromDate: LocalDate? = null,
        @RequestParam(required = false) toDate: LocalDate? = null,
        @RequestParam(defaultValue = "0") page: Int,
        @RequestParam(defaultValue = "10") size: Int,
        @RequestParam(defaultValue = "createdAt") sort: String,
        @RequestParam(defaultValue = "DESC") direction: String,
    ): ResponseEntity<Page<OnboardPrincipalResponseDTO>> {
        val sortDirection = Sort.Direction.valueOf(direction.uppercase())
        val principals = onboardPrincipalService.filterPrincipals(fromDate, toDate, page, size, sort, sortDirection)
        return ResponseEntity.ok(principals)
    }

    @Operation(description = "Add a new dependant to a principal with the provided id.")
    @PostMapping("/{principalId}/addDependants")
    @ResponseStatus(HttpStatus.CREATED)
    fun addDependants(
        @PathVariable principalId: Long,
        @Valid @RequestBody request: AddPrincipalDependantsRequestDTO
    ): ResponseEntity<Map<String, String>> {
        onboardPrincipalService.addNewDependants(principalId, request)
        return mapOf("message" to "Dependants added successfully.").toResponse(HttpStatus.CREATED)
    }

    @Operation(description = "Add a new beneficiary to a principal with the provided id.")
    @PostMapping("/{principalId}/addBeneficiary")
    @ResponseStatus(HttpStatus.CREATED)
    fun addBeneficiary(
        @PathVariable principalId: Long,
        @Valid @RequestBody request: OnboardBeneficiaryDTO
    ): ResponseEntity<String> {
        onboardPrincipalService.addNewBeneficiary(principalId, request)
        return "Beneficiary added successfully.".toResponse(HttpStatus.CREATED)
    }

    @Operation(description = "Get the number off all principals in the system.")
    @GetMapping("/count")
    fun getPrincipalsCount(): ResponseEntity<Long> {
        val count = onboardPrincipalService.countAllPrincipals()
        return ResponseEntity.ok(count)
    }

    @Operation(description = "Search for a principal by either their full name or grower number.")
    @GetMapping("/search")
    fun searchPrincipal(
        @RequestParam searchTerm: String
    ): ResponseEntity<List<OnboardPrincipalResponseDTO?>> {
        val principals = onboardPrincipalService.getPrincipalBySearchTerm(searchTerm)
        return if (principals.isNotEmpty()) ResponseEntity.ok(principals) else ResponseEntity.notFound().build()
    }
}
