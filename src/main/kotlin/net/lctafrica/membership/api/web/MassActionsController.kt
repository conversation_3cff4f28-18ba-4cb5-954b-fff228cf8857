package net.lctafrica.membership.api.web

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import net.lctafrica.membership.api.config.BadRequestException
import net.lctafrica.membership.api.dtos.TemplateType
import net.lctafrica.membership.api.dtos.massactions.ProviderMappingSummary
import net.lctafrica.membership.api.dtos.massactions.ProviderUpdateSummary
import net.lctafrica.membership.api.service.TemplateService
import net.lctafrica.membership.api.service.massactions.IProviderBulkUpdateService
import net.lctafrica.membership.api.util.Result
import org.springframework.core.io.InputStreamResource
import org.springframework.core.io.Resource
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.IOException

@Tag(name = "Mass Actions APIs", description = "Centralized bulk operations for system administration")
@RestController
@RequestMapping("/api/v1/mass-actions")
class MassActionsController(
    private val templateService: TemplateService,
    private val providerBulkUpdateService: IProviderBulkUpdateService
) : AbstractController() {

    @GetMapping("/template/download")
    @Operation(
        summary = "Download Excel templates for mass operations",
        description = "Downloads Excel templates with proper formatting and sample data for bulk operations"
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Template downloaded successfully",
                content = [Content(mediaType = "application/octet-stream")]
            ),
            ApiResponse(
                responseCode = "400",
                description = "Invalid template type",
                content = [Content(mediaType = "application/json")]
            )
        ]
    )
    @Throws(IOException::class)
    fun templateDownload(
        @Parameter(
            description = "Type of template to download",
            required = true,
            schema = Schema(implementation = TemplateType::class)
        )
        @RequestParam("templateType") templateType: TemplateType
    ): ResponseEntity<Resource> {

        val (workbook, templateName) = when (templateType) {
            TemplateType.MEMBER_ADDITION ->
                templateService.createMemberAdditionTemplate() to "Member_Addition_Template.xlsx"

            TemplateType.MEMBER_EDIT ->
                templateService.createMemberEditTemplate() to "Member_Edit_Template.xlsx"

            TemplateType.MEMBER_ACTIVATION_OR_DEACTIVATION ->
                templateService.createMemberActivationDeactivationTemplate() to "Member_Activation_Deactivation_Template.xlsx"

            TemplateType.DEVICE_UPLOAD ->
                templateService.createDeviceUploadTemplate() to "Upload_Devices_Template.xlsx"

            TemplateType.SIM_UPLOAD ->
                templateService.createSimUploadTemplate() to "Upload_SIMs_Template.xlsx"

            TemplateType.PROVIDER_BULK_UPDATE ->
                templateService.createProviderBulkUpdateTemplate() to "Provider_Bulk_Update_Template.xlsx"

            TemplateType.PROVIDER_BULK_MAPPING ->
                templateService.createProviderBulkMappingTemplate() to "Provider_Bulk_Mapping_Template.xlsx"
        }

        val byteArrayOutputStream = ByteArrayOutputStream()
        workbook.write(byteArrayOutputStream)
        val inputStreamResource = InputStreamResource(ByteArrayInputStream(byteArrayOutputStream.toByteArray()))

        return ResponseEntity.ok()
            .headers(HttpHeaders().apply {
                add("Content-Disposition", "attachment; filename=$templateName")
            })
            .contentType(MediaType.APPLICATION_OCTET_STREAM)
            .body(inputStreamResource)
    }

    @PostMapping("/provider/bulk-update/{payerId}", consumes = [MediaType.MULTIPART_FORM_DATA_VALUE])
    @Operation(
        summary = "Bulk update provider codes and names from Excel file",
        description = "Upload an Excel file to bulk update provider codes in payer mappings and provider names. " +
                "The Excel file should contain columns: Provider ID (required), Code (optional), Provider Name (optional). " +
                "Updates are scoped to the specified payer. Use the template download endpoint to get the correct format."
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Bulk update completed successfully",
                content = [Content(
                    mediaType = "application/json",
                    schema = Schema(implementation = Result::class)
                )]
            ),
            ApiResponse(
                responseCode = "400",
                description = "Invalid request parameters or file format",
                content = [Content(mediaType = "application/json")]
            ),
            ApiResponse(
                responseCode = "404",
                description = "Payer not found",
                content = [Content(mediaType = "application/json")]
            )
        ]
    )
    suspend fun bulkUpdateProviders(
        @Parameter(
            description = "ID of the payer to scope provider updates to",
            required = true,
            example = "1"
        )
        @PathVariable("payerId") payerId: Long,

        @RequestParam("file") file: MultipartFile
    ): Result<ProviderUpdateSummary> {
        if (file.isEmpty) {
            return Result(
                success = false,
                msg = "File is required and cannot be empty",
                data = null
            )
        }

        // validate file size (10MB limit)
        val maxFileSize = 10 * 1024 * 1024
        if (file.size > maxFileSize) {
            return Result(
                success = false,
                msg = "File size exceeds maximum limit of 10MB",
                data = null
            )
        }

        return try {
            val summary = providerBulkUpdateService.processProviderUpdates(payerId, file)

            val message = when {
                summary.errors.isEmpty() && summary.successfulUpdates > 0 ->
                    "Bulk update completed successfully. Updated ${summary.successfulUpdates} providers."

                summary.errors.isNotEmpty() && summary.successfulUpdates > 0 ->
                    "Bulk update completed with partial success. Updated ${summary.successfulUpdates} providers, ${summary.errors.size} failed."

                summary.errors.isNotEmpty() ->
                    "Bulk update failed. All ${summary.totalRows} providers failed processing."

                else ->
                    "No valid provider updates found in the uploaded file."
            }

            Result(
                success = summary.successfulUpdates > 0,
                msg = message,
                data = summary
            )
        } catch (e: IllegalArgumentException) {
            throw BadRequestException(e.message ?: "Invalid request parameters")
        } catch (e: Exception) {
            throw RuntimeException("An error occurred while processing the bulk update: ${e.message}", e)
        }
    }

    @PostMapping("/provider/bulk-mapping/{payerId}", consumes = [MediaType.MULTIPART_FORM_DATA_VALUE])
    @Operation(
        summary = "Bulk create provider mappings from Excel file",
        description = "Upload an Excel file to bulk create initial mappings between providers and the specified payer. " +
                "The Excel file should contain columns: Provider ID (required), Code (required). " +
                "The payer ID is specified in the URL path to restrict mappings to a single payer. " +
                "This endpoint is for creating new mappings only - existing mappings will be skipped. " +
                "Use the template download endpoint to get the correct format."
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Bulk mapping completed successfully",
                content = [Content(
                    mediaType = "application/json",
                    schema = Schema(implementation = Result::class)
                )]
            ),
            ApiResponse(
                responseCode = "400",
                description = "Invalid request parameters or file format",
                content = [Content(mediaType = "application/json")]
            ),
            ApiResponse(
                responseCode = "404",
                description = "Payer not found",
                content = [Content(mediaType = "application/json")]
            )
        ]
    )
    suspend fun bulkCreateProviderMappings(
        @Parameter(
            description = "ID of the payer to create provider mappings for",
            required = true,
            example = "1"
        )
        @PathVariable("payerId") payerId: Long,

        @RequestParam("file") file: MultipartFile
    ): Result<ProviderMappingSummary> {
        if (file.isEmpty) {
            return Result(
                success = false,
                msg = "File is required and cannot be empty",
                data = null
            )
        }

        // validate file size (10MB limit)
        val maxFileSize = 10 * 1024 * 1024
        if (file.size > maxFileSize) {
            return Result(
                success = false,
                msg = "File size exceeds maximum limit of 10MB",
                data = null
            )
        }

        return try {
            val summary = providerBulkUpdateService.processProviderMappings(payerId, file)

            val message = when {
                summary.errors.isEmpty() && summary.successfulMappings > 0 ->
                    "Bulk mapping completed successfully. Created ${summary.successfulMappings} provider mappings."

                summary.errors.isNotEmpty() && summary.successfulMappings > 0 ->
                    "Bulk mapping completed with partial success. Created ${summary.successfulMappings} mappings, " +
                            "${summary.skippedMappings} skipped, ${summary.errors.size} failed."

                summary.errors.isNotEmpty() && summary.successfulMappings == 0 ->
                    "Bulk mapping failed. All ${summary.totalRows} provider mappings failed processing."

                summary.skippedMappings > 0 && summary.successfulMappings == 0 ->
                    "No new mappings created. All ${summary.skippedMappings} providers were already mapped."

                else ->
                    "No valid provider mappings found in the uploaded file."
            }

            Result(
                success = summary.successfulMappings > 0 || (summary.errors.isEmpty() && summary.skippedMappings > 0),
                msg = message,
                data = summary
            )
        } catch (e: IllegalArgumentException) {
            throw BadRequestException(e.message ?: "Invalid request parameters")
        } catch (e: Exception) {
            throw RuntimeException("An error occurred while processing the bulk mapping: ${e.message}", e)
        }
    }
}