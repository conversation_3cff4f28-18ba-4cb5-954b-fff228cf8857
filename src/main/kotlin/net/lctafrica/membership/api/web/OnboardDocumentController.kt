package net.lctafrica.membership.api.web

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import net.lctafrica.membership.api.dtos.OnboardDocumentResponseDTO
import net.lctafrica.membership.api.service.OnboardDocumentService
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RequestMapping("/api/v1/membership/onboard/documents")
@Tag(name = "Onboard Document", description = "Operations related to the Onboard Portal document.")
@RestController
class OnboardDocumentController(private val onboardDocumentService: OnboardDocumentService) : AbstractController() {
    @GetMapping("principal/{principalId}")
    @Operation(description = "Get all the documents related to the principal with the provided id.")
    fun getAllPrincipalDocuments(@PathVariable principalId: Long): ResponseEntity<List<OnboardDocumentResponseDTO>> {
        val documents = onboardDocumentService.getAllPrincipalDocuments(principalId)
        return ResponseEntity.ok(documents)
    }
}
