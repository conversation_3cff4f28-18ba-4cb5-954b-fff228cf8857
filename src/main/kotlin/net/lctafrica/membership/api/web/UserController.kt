package net.lctafrica.membership.api.web

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import net.lctafrica.membership.api.dtos.*
import net.lctafrica.membership.api.service.IKeycloakService
import net.lctafrica.membership.api.service.UserManagementService
import net.lctafrica.membership.api.service.UserRolesAuditLogService
import net.lctafrica.membership.api.util.paginateList
import org.keycloak.representations.idm.RoleRepresentation
import org.keycloak.representations.idm.UserRepresentation
import org.springframework.data.domain.Page
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import javax.validation.Valid

@RestController
@Tag(name = "User Management", description = "Operations related to managing user accounts and roles.")
@RequestMapping("/api/v1/membership")
class UserController(
    private val userManagementService: UserManagementService,
    private val keycloakService: IKeycloakService,
    private val userRolesAuditLogService: UserRolesAuditLogService,
) {

    @PostMapping(value = ["/user/addAdmin"], produces = ["application/json"])
    fun addAdmin(@Valid @RequestBody dto: AdminUserDTO) = userManagementService.addAdminUser(dto)

    @PostMapping(value = ["/user/addPayerAdmin"], produces = ["application/json"])
    fun addPayer(@Valid @RequestBody dto: PayerUserDTO) = userManagementService.addPayerAdmin(dto)

    @PostMapping(value = ["/user/addCashier"], produces = ["application/json"])
    fun addCashier(@Valid @RequestBody dto: CashierUserDTO) = userManagementService.addCashier(dto)

    @PostMapping(value = ["/user/addCreditControl"], produces = ["application/json"])
    fun addCreditControl(@Valid @RequestBody dto: CashierUserDTO) = userManagementService.addCreditControlCashier(dto)

    @GetMapping(value = ["/user/provider/{providerId}"], produces = ["application/json"])
    fun getProviderUsers(@PathVariable(value = "providerId") providerId: Long) =
        userManagementService.getProviderUsers(providerId)

    @GetMapping(value = ["/user/payer/{payerId}"], produces = ["application/json"])
    fun getPayerUsers(@PathVariable(value = "payerId") payerId: Long) =
        userManagementService.getPayerUsers(payerId)

    @PostMapping(value = ["/user/resetPassword/{username}"], produces = ["application/json"])
    fun resetPassword(@PathVariable(value = "username") username: String) =
        userManagementService.resetPassword(username)

    @PostMapping(value = ["/user/addCreditControlRole/{username}"], produces = ["application/json"])
    fun addCreditControlRole(@PathVariable(value = "username") username: String) =
        userManagementService.addCreditControlRole(username)

    @PostMapping(value = ["/user/savePayerAdminRole"], produces = ["application/json"])
    fun savePayerAdminRole(@RequestBody dto: UpdatedRolesDTO) = userManagementService.savePayerAdminRole(dto)

    @GetMapping(value = ["/user/getRole"], produces = ["application/json"])
    fun getUserRoles(@RequestParam(value = "username") username: String) =
        userManagementService.getUserRoles(username)

    @PostMapping(value = ["/group"], produces = ["application/json"])
    fun addGroup(@Valid @RequestBody dto: AddKeycloakGroupDto) =
        userManagementService.addGroup(dto)

    @GetMapping(value = ["/system/users/{payerId}"], produces = ["application/json"])
    @Operation(summary = "Get Payer system users")
    fun getPayerKeycloakUsers(
        @PathVariable(value = "payerId") payerId: Long,
        @RequestParam(value = "page", defaultValue = "1") page: Int,
        @RequestParam(value = "size", defaultValue = "50") size: Int,
    ) = keycloakService.getPayerKeycloakUsers(payerId, page, size)

    @GetMapping(value = ["/provider/users/{providerId}"], produces = ["application/json"])
    @Operation(summary = "Get Provider system users")
    fun getProviderKeycloakUsers(
        @PathVariable(value = "providerId") providerId: Long,
        @RequestParam(value = "page", defaultValue = "1") page: Int,
        @RequestParam(value = "size", defaultValue = "50") size: Int,
    ) = keycloakService.getProviderKeycloakUsers(providerId, page, size)

    @PostMapping("/user/updateRoles")
    fun updateUserRoles(
        @RequestParam userId: String,
        @RequestBody updatedRoles: UserRoleUpdateDTO
    ): ResponseEntity<Map<String, String>> {
        val res = userManagementService.updateUserRoles(updatedRoles, userId)
        return ResponseEntity.ok(res)
    }

    @PostMapping("/users/updateRoles/batch")
    @Operation(
        summary = "Update roles for multiple users",
        description = "This endpoint updates roles for multiple users at once"
    )
    fun updateMultipleUserRoles(
        @RequestBody batchUpdatedRoles: BatchUserRoleUpdateDTO
    ): ResponseEntity<Map<String, Any>> {
        val res = userManagementService.updateMultipleUserRoles(batchUpdatedRoles)
        return ResponseEntity.ok(res)
    }

    @GetMapping("/user/{userId}")
    fun findUserById(@PathVariable userId: String): ResponseEntity<UserRepresentation> {
        return ResponseEntity.ok(userManagementService.findUserById(userId))
    }

    @GetMapping("/user/roles/realm/{userId}")
    @Operation(summary = "Get user realm roles", description = "This lists all the realm roles assigned to a user")
    fun findUserRolesById(@PathVariable userId: String): ResponseEntity<List<RoleRepresentation>> {
        return ResponseEntity.ok(userManagementService.findEffectiveUserRealmRoles(userId))
    }

    @GetMapping("/users/roles/realm")
    @Operation(summary = "Get all realm roles", description = "This lists all the available realm roles")
    fun getAllSystemRealmRoles(): ResponseEntity<List<RoleRepresentation>> {
        return ResponseEntity.ok(userManagementService.getAllSystemRealmRoles())
    }

    @GetMapping("/users/roles/auditLogs/user/{userId}")
    fun getAuditLogsByUserId(
        @PathVariable userId: String,
        @RequestParam(value = "page", defaultValue = "1") page: Int,
        @RequestParam(value = "size", defaultValue = "100") size: Int,
    ): ResponseEntity<Page<UserRolesAuditLogResponse>> {
        return ResponseEntity.ok(userRolesAuditLogService.getAuditLogsByUserId(userId, page, size))
    }

    @GetMapping("/users/roles/auditLogs/actionedBy/{username}")
    fun getAuditLogsByActionedBy(
        @PathVariable username: String,
        @RequestParam(value = "page", defaultValue = "1") page: Int,
        @RequestParam(value = "size", defaultValue = "100") size: Int,
    ): ResponseEntity<Page<UserRolesAuditLogResponse>> {
        return ResponseEntity.ok(userRolesAuditLogService.getAuditLogsByActionedBy(username, page, size))
    }

    @GetMapping("/users/payer/paginated/{payerId}")
    @Operation(
        summary = "Get paginated payer users with search capability",
        description = "Retrieves users for a specific payer with optional search functionality. Search can be performed on full name (first name + last name), email address, and username."
    )
    fun getPayerUsersPaginated(
        @PathVariable(value = "payerId") payerId: Long,
        @RequestParam(value = "page", defaultValue = "1") page: Int,
        @RequestParam(value = "size", defaultValue = "100") size: Int,
        @RequestParam(value = "search", required = false) searchTerm: String?,
    ): ResponseEntity<Page<UserRepresentation>> {
        val payerUsers = userManagementService.findPayerUsers(payerId, searchTerm)
        return ResponseEntity.ok(
            paginateList(
                payerUsers,
                page,
                size
            )
        )
    }

    @PostMapping("/users/assignAllModulesRoles")
    @Operation(
        summary = "Assign all payer roles to all payer users",
        description = "This endpoint finds all users with payerId attribute (excluding KTDA) and assigns them the umbrella roles for all the modules."
    )
    fun assignAllPayerAccountsAllModulesRole(
        @RequestParam actionedBy: String
    ): ResponseEntity<Map<String, Any>> {
        val result = userManagementService.assignAllPayerAccountsAllModulesRoles(actionedBy)
        return ResponseEntity.ok(result)
    }

    @PostMapping("/users/{userId}/makePayerSuperAdmin")
    @Operation(
        summary = "Make a user a payer superadmin",
        description = "This endpoint adds the PAYER_SUPER_ADMIN role and all module roles (ALL_CARE_ROLE, ALL_CLAIMS_ROLE, etc.) to the specified user, enabling them to handle role management for their payer account."
    )
    fun makeUserPayerSuperAdmin(
        @PathVariable userId: String,
        @RequestParam actionedBy: String
    ): ResponseEntity<Map<String, String>> {
        val result = userManagementService.makeUserPayerSuperAdmin(userId, actionedBy)
        return ResponseEntity.ok(result)
    }

    @PutMapping("/users/payer/update")
    @Operation(
        summary = "Update payer user basic information",
        description = "Updates a payer user's first name, last name, and email address, providing a reason for the update."
    )
    fun updatePayerUserInfo(
        @Valid @RequestBody dto: PayerUserUpdateDTO
    ): ResponseEntity<Any> {
        val result = userManagementService.updatePayerUserInfo(dto)
        return if (result.success) {
            ResponseEntity.ok(result.data)
        } else {
            ResponseEntity.badRequest().body(mapOf("error" to (result.msg ?: "Unknown error")))
        }
    }

    @PutMapping("/users/payer/status")
    @Operation(
        summary = "Activate or deactivate a user",
        description = "Activates or deactivates a user, providing a reason for the operation."
    )
    fun updateUserStatus(
        @Valid @RequestBody dto: UserStatusUpdateDTO
    ): ResponseEntity<Any> {
        val result = userManagementService.updateUserStatus(dto)
        return if (result.success) {
            ResponseEntity.ok(result.data)
        } else {
            ResponseEntity.badRequest().body(mapOf("error" to (result.msg ?: "Unknown error")))
        }
    }

    @PostMapping("/users/password/reset")
    @Operation(
        summary = "Request password reset",
        description = "Requests a password reset for a user by username. Generates a secure temporary password that expires after 15 minutes."
    )
    fun requestPasswordReset(
        @Valid @RequestBody dto: PasswordResetRequestDTO
    ): ResponseEntity<Any> {
        val result = userManagementService.requestPasswordReset(dto)
        return if (result.success) {
            ResponseEntity.ok(result.data)
        } else {
            ResponseEntity.badRequest().body(mapOf("error" to (result.msg ?: "Unknown error")))
        }
    }
}
