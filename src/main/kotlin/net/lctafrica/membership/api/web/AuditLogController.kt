package net.lctafrica.membership.api.web

import io.swagger.v3.oas.annotations.Operation
import javax.validation.Valid
import net.lctafrica.membership.api.dtos.AuditLogDTO
import net.lctafrica.membership.api.dtos.AuditLogFilter
import net.lctafrica.membership.api.service.AuditLogService
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/v1/audit")
class AuditLogController(
	val auditLogService: AuditLogService
) : AbstractController() {

	@PostMapping(value = ["/save"], produces = ["application/json"])
	fun saveLog( @RequestBody dto: AuditLogDTO) = auditLogService.saveLog(dto)

	@GetMapping(value = ["/logs"], produces = ["application/json"])
	@Operation(summary = "Get Audit Logs")
	suspend fun getAuditLogs(
		@Valid @RequestParam("beneficiaryId") beneficiaryId: Long?,
		@Valid @RequestParam("page", defaultValue = "1") page: Int,
		@Valid @RequestParam("size", defaultValue = "50") size: Int,
	) = auditLogService.getAuditLogs(page,size,AuditLogFilter(beneficiaryId = beneficiaryId))
}