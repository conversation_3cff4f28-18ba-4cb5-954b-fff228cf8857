package net.lctafrica.membership.api.web

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import javax.validation.Valid
import net.lctafrica.membership.api.dtos.BatchProviderRestrictionDto
import net.lctafrica.membership.api.dtos.BatchProvidersRestrictionDto
import net.lctafrica.membership.api.dtos.BenefitProviderRestrictionCheckDto
import net.lctafrica.membership.api.dtos.CreateRestrictionDto
import net.lctafrica.membership.api.service.IRestrictionService
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@Tag(name = "Payer Restrictions", description = "Restriction APIs")
@RestController
@RequestMapping("/api/v1/restriction")
class RestrictionController(
    val restrictionService: IRestrictionService
) : AbstractController() {

    @GetMapping(value = [""])
    @Operation(summary = "Get all active payer restrictions")
    fun getRestrictions(@RequestParam(value = "payerId") payerId: Long) = restrictionService.getRestrictions(payerId)

    @PostMapping(value = ["/create"], produces = ["application/json"])
    @Operation(summary = "Create a restriction")
    fun createRestriction(
        @Valid @RequestBody dto: CreateRestrictionDto
    ) = restrictionService.createRestriction(dto)

    @PutMapping(value = ["/remove/{restrictionId}"], produces = ["application/json"])
    @Operation(summary = "Remove a restriction")
    fun removeRestriction(@PathVariable("restrictionId") restrictionId: Long) =
        restrictionService.removeRestriction(restrictionId)

    @GetMapping(value = ["/providers"])
    @Operation(summary = "Get all active providers in a restriction")
    fun getActiveProvidersInARestriction(
        @RequestParam(value = "restrictionId") restrictionId: Long,
        @RequestParam(value = "page") page: Int,
        @RequestParam(value = "size") size: Int
    ) = restrictionService.getActiveProvidersInARestriction(restrictionId, page, size)

    @GetMapping(value = ["/providers/all"])
    @Operation(summary = "Get all payer providers with a restriction status")
    fun getPayerProvidersWithRestrictionCheck(
        @RequestParam(value = "restrictionId") restrictionId: Long,
        @RequestParam(value = "page") page: Int,
        @RequestParam(value = "size") size: Int
    ) = restrictionService.getPayerProvidersWithRestrictionCheck(restrictionId, page, size)

    @PostMapping(value = ["/add/providers"], produces = ["application/json"])
    @Operation(summary = "Add providers to a restriction")
    fun addProvidersToARestriction(
        @Valid @RequestBody dto: BatchProviderRestrictionDto
    ) = restrictionService.addProvidersToARestriction(dto)

    @PutMapping(value = ["/remove/{restrictionId}/providers"], produces = ["application/json"])
    @Operation(summary = "Remove providers from a restriction")
    fun removeProvidersFromARestriction(
        @PathVariable("restrictionId") restrictionId: Long,
        @Valid @RequestBody dto: BatchProvidersRestrictionDto
    ) = restrictionService.removeProvidersFromARestriction(restrictionId, dto)

    @PostMapping(value = ["/check"], produces = ["application/json"])
    @Operation(summary = "Checks Provider restriction")
    fun checkProviderRestriction(
        @Valid @RequestBody dto: BenefitProviderRestrictionCheckDto
    ) = restrictionService.checkProviderRestriction(dto)

    @GetMapping(value = ["/check/provider/{providerId}/benefit/{benefitId}"])
    @Operation(summary = "Check if Provider is allowed to offer service for a Benefit - Returns True or False")
    fun checkIfProviderIsAllowedToOfferServiceForBenefit(
        @PathVariable(value = "providerId") providerId: Long,
        @PathVariable(value = "benefitId") benefitId: Long
    ) = restrictionService.checkIfProviderIsAllowedToOfferServiceForBenefit(benefitId, providerId)

}