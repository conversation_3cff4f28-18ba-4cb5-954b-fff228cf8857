package net.lctafrica.membership.api.web

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import net.lctafrica.membership.api.dtos.OnboardDependantResponseDTO
import net.lctafrica.membership.api.service.OnboardDependantService
import org.springframework.data.domain.Page
import org.springframework.data.domain.Sort
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import java.time.LocalDate


@RestController
@RequestMapping("/api/v1/membership/onboard/dependants")
@Tag(name = "Onboard Dependant", description = "Operations related to the Onboard Portal dependant.")
class OnboardDependantController(private val onboardDependantService: OnboardDependantService) : AbstractController() {
    @GetMapping
    @Operation(description = "Fetch all dependants in the system.")
    fun finAllDependants(
        @RequestParam(required = false) fromDate: LocalDate? = null,
        @RequestParam(required = false) toDate: LocalDate? = null,
        @RequestParam(defaultValue = "0") page: Int,
        @RequestParam(defaultValue = "10") size: Int,
        @RequestParam(defaultValue = "createdAt") sort: String,
        @RequestParam(defaultValue = "DESC") direction: String,
    ): ResponseEntity<Page<OnboardDependantResponseDTO>> {
        val sortDirection = Sort.Direction.valueOf(direction.uppercase()) // Convert to Sort.Direction
        val dependants = onboardDependantService.filterDependants(fromDate, toDate, page, size, sort, sortDirection)
        return ResponseEntity.ok(dependants)
    }

    @Operation(description = "Fetch a dependant by their id.")
    @GetMapping("/{id}")
    fun findDependantById(@PathVariable id: Long): OnboardDependantResponseDTO {
        return onboardDependantService.getDependantById(id)
    }

    @Operation(description = "Fetch all the dependants related to a principal with the given id.")
    @GetMapping("/principal/{principalId}")
    fun findAllDependantsByPrincipalId(@PathVariable principalId: Long): List<OnboardDependantResponseDTO> {
        return onboardDependantService.getDependantsByPrincipalId(principalId)
    }

    @Operation(description = "Get the number off all dependants in the system.")
    @GetMapping("/count")
    fun getDependantsCount(): ResponseEntity<Long> {
        val count = onboardDependantService.countAllDependants()
        return ResponseEntity.ok(count)
    }
}
