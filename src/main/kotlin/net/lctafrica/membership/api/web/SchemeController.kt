package net.lctafrica.membership.api.web

import io.swagger.v3.oas.annotations.Operation
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.IOException
import javax.validation.Valid
import javax.validation.constraints.Size
import net.lctafrica.membership.api.config.BadRequestException
import net.lctafrica.membership.api.domain.*
import net.lctafrica.membership.api.dtos.*
import net.lctafrica.membership.api.service.*
import org.springframework.core.io.InputStreamResource
import org.springframework.core.io.Resource
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile


@RestController
@RequestMapping("/api/v1/membership")
class SchemeController(
	val payerService: IPayerService,
	val planService: IPlanService,
	val policyService: IPolicyService,
	val categoryService: ICategoryService,
	val benefitService: IBenefitService,
	val beneficiaryService: IBeneficiaryService,
	val providerService: ProviderService,
	val jubileeService: JubileeService,
	val batchUploadService: IBatchUploadService,
	val payerRegionService: PayerRegionService,
	private val templateService: TemplateService
) : AbstractController() {
	@GetMapping(value = ["/administrators"], produces = ["application/json"])
	fun getAdministrators() = payerService.findAdministrators()

	@GetMapping(value = ["/payers"], produces = ["application/json"])
	fun getAllPayers() = payerService.findAll()

	@GetMapping(value = ["/payers/type/{type}"], produces = ["application/json"])
	fun getPayersByType(@PathVariable("type") type: PayerType) = payerService.findByType(type)

	@GetMapping(value = ["/payers/{payerId}/payer"], produces = ["application/json"])
	fun getPayerById(@PathVariable("payerId") payerId: Long) = payerService.findById(payerId)

	@PostMapping(value = ["/findPayersByIds"], produces = ["application/json"])
	@Operation(summary = "Get Payers by Ids")
	fun findPayersByIds(@Valid @RequestBody dto: IdsListDto) = payerService.findPayersByIds(dto)

	@GetMapping(value = ["/payers/findByPolicyAdminId"], produces = ["application/json"])
	@Operation(summary = "Get Payers by Policy Admin Id")
	fun findByPolicyAdminId(@RequestParam("policyAdminId") policyAdminId: Long) = payerService.findByPolicyAdminId(policyAdminId)

	@PostMapping(value = ["/payers"], produces = ["application/json"])
	fun addPayer(@Valid @RequestBody dto: PayerDTO) = payerService.addPayer(dto)

	@PostMapping(value = ["/payer/config"], produces = ["application/json"])
	fun addPayerConfig(@Valid @RequestBody dto: PayerConfigDto) = payerService.addPayerConfig(dto)

	@PostMapping(value = ["/payers/mapping/benefits"], produces = ["application/json"])
	fun addBenefitMapping(@Valid @RequestBody dto: PayerBenefitMappingDTO) =
		payerService.addBenefitMapping(dto)

	@PostMapping(value = ["/payers/mapping/providers"], produces = ["application/json"])
	fun addProviderMapping(@Valid @RequestBody dto: PayerProviderMappingDTO) =
		payerService.addProviderMapping(dto)

	@PutMapping(value = ["/payers/mapping/update/batch"], produces = ["application/json"])
	@Operation(summary = "Batch update payer provider mapping")
	fun updatePayerProviderMappingBatch(
		@Valid @RequestBody dto: List<PayerProviderMappingUpdateDTO>
	) = payerService.updatePayerProviderMappingBatch(dto)

	@GetMapping(value = ["/payer/category/provider/mapping/{payerId}/{categoryId}/{page}/{size}"], produces = ["application/json"])
	@Operation(summary = "Get Provider mappings by Payer and Category")
	fun getProvidersForCategoryFilteredByRestriction(
		@PathVariable(value = "payerId", required = true) payerId: Long,
		@PathVariable(value = "categoryId", required = true) categoryId: Long,
		@PathVariable(value = "page", required = true) @Size(min = 1) page: Int,
		@PathVariable(value = "size", required = true) @Size(min = 10, max = 100) size: Int
	) = providerService.getProvidersForCategoryFilteredByRestriction(payerId,categoryId,page,size)

	@GetMapping(value = ["/payer/benefit/{benefitId}/provider/mapping/{page}/{size}"], produces = ["application/json"])
	@Operation(summary = "Get Provider mappings by Benefit - Returns a list of providers mapped to the benefit plus list of unmapped providers from the Payer Providers")
	fun getProvidersForBenefitFilteredByRestriction(
		@PathVariable(value = "benefitId", required = true) benefitId: Long,
		@PathVariable(value = "page", required = true) @Size(min = 1) page: Int,
		@PathVariable(value = "size", required = true) @Size(min = 10, max = 100) size: Int
	) = providerService.getProvidersForBenefitFilteredByRestriction(benefitId,page,size)

	@GetMapping(value = ["/payer/benefit/{benefitId}/provider/region/{regionId}/mapping/{page}/{size}"], produces = ["application/json"])
	@Operation(summary = "Get Provider mappings by Benefit and Region- Returns a list of regional providers mapped to the benefit plus list of unmapped providers from the Payer Providers")
	fun getProvidersForBenefitFilteredByRestrictionAndRegion(
		@PathVariable(value = "benefitId", required = true) benefitId: Long,
		@PathVariable(value = "regionId", required = true) regionId: Long,
		@PathVariable(value = "page", required = true) @Size(min = 1) page: Int,
		@PathVariable(value = "size", required = true) @Size(min = 10, max = 100) size: Int
	) = providerService.getProvidersForBenefitFilteredByRestrictionAndRegion(benefitId,regionId,page,size)

	@GetMapping(value = ["/payer/benefit/{benefitId}/provider/region/{regionId}"], produces = ["application/json"])
	@Operation(summary = "Search Mapped Benefit Providers by Benefit Id, Region Id and Facility Name")
	fun searchMappedProvidersForABenefit(
		@PathVariable(value = "benefitId", required = true) benefitId: Long,
		@PathVariable(value = "regionId", required = true) regionId: Long,
		@RequestParam(value = "facilityName", defaultValue = "") facilityName: String,
		@RequestParam(value = "page", defaultValue = "1") page: Int,
		@RequestParam(value = "size", defaultValue = "10") size: Int
	) = providerService.searchMappedProvidersForABenefit(benefitId,regionId,facilityName,page,size)


	@PostMapping(value = ["/benefit/mapping/provider"], produces = ["application/json"])
	@Operation(summary = "Map a Benefit to a Provider/Facility")
	fun benefitProviderMapping(
		@Valid @RequestBody dto: BenefitProviderMappingDto
	) = benefitService.benefitProviderMapping(dto)

	@PostMapping(value = ["/benefit/mapping/provider/batch"], produces = ["application/json"])
	@Operation(summary = "Maps a Benefit to a batch of Providers/Facilities")
	fun batchBenefitPayerProviderMapping(
		@Valid @RequestBody dto: BatchBenefitPayerProviderMappingDto
	) = benefitService.batchBenefitProviderMapping(dto)

	@PutMapping(value = ["/benefit/{benefitId}/unmap/provider/batch"], produces = ["application/json"])
	@Operation(summary = "Unmaps a batch of Providers/Facilities from a Benefit")
	fun batchBenefitPayerProviderUnMapping(
		@PathVariable("benefitId") benefitId:Long,
		@Valid @RequestBody dto: BatchBenefitProviderUnmappingDto
	) = benefitService.batchBenefitProviderUnmapping(benefitId,dto)

	@GetMapping(value = ["/benefit/provider/mapping/{benefitId}/{page}/{size}"], produces = ["application/json"])
	@Operation(summary = "Get Provider mappings by Benefit")
	fun getPayerBenefitProviderMappings(
		@PathVariable(value = "benefitId", required = true) benefitId: Long,
		@PathVariable(value = "page", required = true) @Size(min = 1) page: Int,
		@PathVariable(value = "size", required = true) @Size(min = 1, max = 100) size: Int
	) = benefitService.getPayerBenefitProviderMappings(benefitId,page,size)


	@GetMapping(value = ["/beneficiary/benefit/mapping/provider"], produces = ["application/json"])
	@Operation(summary = "Get Mapped Providers by Beneficiary and Benefit")
	fun getBeneficiaryBenefitProviderMapping(
		@RequestParam(value = "beneficiaryId", required = true) beneficiaryId: Long,
		@RequestParam(value = "benefitId", required = true) benefitId: Long
	) = benefitService.getBeneficiaryBenefitProviderMapping(beneficiaryId,benefitId)

	@PostMapping(value = ["/beneficiary/mapping/provider"], produces = ["application/json"])
	@Operation(summary = "Maps Beneficiary to a service Provider")
	fun beneficiaryBenefitProviderMapping(
		@Valid @RequestBody dto: BeneficiaryBenefitProviderMappingDto
	) = benefitService.beneficiaryBenefitProviderMapping(dto)

	@GetMapping(value = ["/payers/{payerId}/mapping/benefits"], produces = ["application/json"])
	fun getBenefitMappingsByPayer(@PathVariable("payerId") payerId: Long) =
		payerService.findBenefitMapping(payerId)

	@GetMapping(
		value = ["/payers/{payerId}/benefitRef/{benefitRef}/code"],
		produces = ["application/json"]
	)
	fun getBenefitCodeByPayerAndBenefitRef(
		@PathVariable("payerId") payerId: String, @PathVariable
			("benefitRef") benefitRef: String
	) = payerService.findBenefitCode(payerId, benefitRef)

	@GetMapping(
		value = ["/payers/{payerId}/{page}/{size}/mapping/providers"], produces =
		["application/json"]
	)
	fun getProviderMappingsByPayer(
		@PathVariable("payerId") payerId: Long,
		@PathVariable("page") page: Int = 1,
		@PathVariable("size") size: Int = 10
	) = payerService.findProviderMapping(payerId, page, size)

	@GetMapping(
		value = ["/payers/{payerId}/providers"], produces = ["application/json"]
	)
	fun getPayerProviders(
		@Valid @PathVariable("payerId") payerId: Long,
		@Valid @RequestParam("query") query: String?,
		@Valid @RequestParam("countryId") countryId: Long?,
		@Valid @RequestParam("regionId") regionId: Long?,
		@Valid @RequestParam("mainFacilityId") mainFacilityId: Long?,
		@Valid @RequestParam("tier") tier: ProviderTier?,
		@Valid @RequestParam("providerType") providerType: ProviderType?,
		@Valid @RequestParam("page", defaultValue = "1") page: Int,
		@Valid @RequestParam("size", defaultValue = "50") size: Int,
	) = payerService.findPayerProviders(
			payerId=payerId,
			filters = FilterPayerProvidersDto(
					query = query,
					providerType = providerType,
					countryId = countryId,
					regionId = regionId,
					tier = tier,
					mainFacilityId = mainFacilityId
			),
			page=page,
			size=size,
	)

	@GetMapping(
		value = ["/payers/{payerId}/providers/export"], produces = ["application/json"]
	)
	fun exportPayerProviders(
		@Valid @PathVariable("payerId") payerId: Long,
		@Valid @RequestParam("query") query: String?,
		@Valid @RequestParam("countryId") countryId: Long?,
		@Valid @RequestParam("regionId") regionId: Long?,
		@Valid @RequestParam("mainFacilityId") mainFacilityId: Long?,
		@Valid @RequestParam("tier") tier: ProviderTier?,
		@Valid @RequestParam("providerType") providerType: ProviderType?,
		@Valid @RequestParam("fileType") fileType: ExportFileType,
	) =  when  {
		fileType == ExportFileType.PDF -> payerService.exportPayerProvidersPDF(payerId=payerId,
			filters = FilterPayerProvidersDto(
				query = query,
				providerType = providerType,
				countryId = countryId,
				regionId = regionId,
				tier = tier,
				mainFacilityId = mainFacilityId
			))
		(fileType == ExportFileType.XLSX) || (fileType == ExportFileType.XLS) || (fileType == ExportFileType.CSV) -> payerService.exportPayerProvidersXLSX(payerId=payerId,
			filters = FilterPayerProvidersDto(
				query = query,
				providerType = providerType,
				countryId = countryId,
				regionId = regionId,
				tier = tier,
				mainFacilityId = mainFacilityId
			))
		else -> throw BadRequestException("Unknown file type")
	}

	@GetMapping(
		value = ["/payers/{providerId}/{page}/{size}/mapping/payers"], produces =
		["application/json"]
	)
	fun getPayerMappingsByProvider(
		@PathVariable("providerId") providerId: Long,
		@PathVariable("page") page: Int = 1,
		@PathVariable("size") size: Int = 10
	) = payerService.findPayerMapping(providerId, null, page, size)


	@GetMapping(
		value = ["/payers/{payerId}/{page}/{size}/mapping/benefits"], produces =
		["application/json"]
	)
	fun getBenefitMappingsByPayer(
		@PathVariable("payerId") payerId: Long,
		@PathVariable("page") page: Int = 1,
		@PathVariable("size") size: Int = 10
	) = payerService.findBenefitMapping(payerId, page, size)

	@PostMapping(value = ["/plans"], produces = ["application/json"])
	fun addPlan(@Valid @RequestBody dto: PlanDTO) = planService.addPlan(dto)

	@GetMapping(value = ["/plans"], produces = ["application/json"])
	fun findPlans(page: Int = 0, size: Int = 10) = planService.findAll(page, size)

	@GetMapping(value = ["/eHealth/schemes"], produces = ["application/json"])
	fun findSchemes() = planService.findSchemes()

	@GetMapping(value = ["/payer/{payerId}/plans"], produces = ["application/json"])
	fun findPlansByPayer(@PathVariable("payerId") payerId: Long) =
		planService.findByPayerId(payerId)

	@PostMapping(value = ["/policies"], produces = ["application/json"])
	fun addPolicy(@Valid @RequestBody dto: PolicyDTO) = policyService.addPolicy(dto)

	@GetMapping(value = ["/policies"], produces = ["application/json"])
	fun findAllPolicies() = policyService.findAll()

	@GetMapping(value = ["/policies/{policyId}/policy"], produces = ["application/json"])
	fun findPolicyById(@PathVariable("policyId") policyId: Long) = policyService.findById(policyId)

	@GetMapping(value = ["/plan/{planId}/policies"])
	fun findPoliciesByPlan(@PathVariable("planId") planId: Long) = policyService.findByPlan(planId)

	@GetMapping(value = ["/payer/{payerId}/plan/{planId}/policies"])
	@Operation(summary = "Find Policies by Payer And Plan")
	fun findPoliciesByPayerAndPlan(@PathVariable("payerId") payerId: Long,
								   @PathVariable("planId") planId: Long) = policyService.findPoliciesByPayerAndPlan(payerId,planId)

	@GetMapping(value = ["/payer/{payerId}/policies"])
	@Operation(summary = "Get policies by Payer Id")
	fun findByPayerId(@PathVariable("payerId") payerId: Long) = policyService.findByPayerId(payerId)

	@PostMapping(value = ["/categories"], produces = ["application/json"])
	fun addCategory(@Valid @RequestBody dto: CategoryDTO) = categoryService.addCategory(dto)

	@GetMapping(value = ["/policy/{policyId}/categories"], produces = ["application/json"])
	fun findCategoriesByPolicy(@PathVariable("policyId") policyId: Long) =
		categoryService.findByPolicy(policyId)

	@PostMapping(value = ["/policy/categories/findByPolicyIds"], produces = ["application/json"])
	@Operation(summary = "Get categories by Policy Ids")
	suspend fun findCategoriesByPolicyIds(@Valid @RequestBody dto: IdsSetDto) =
		categoryService.findByPolicyIds(dto)

	@PostMapping(value = ["/policy/categories/findByPlanIds"], produces = ["application/json"])
	@Operation(summary = "Get categories by Plan Ids")
	suspend fun findCategoriesByPlanIds(@Valid @RequestBody dto: IdsSetDto) =
		categoryService.findByPlanIds(dto)


	@PostMapping(value = ["/benefits"], produces = ["application/json"])
	fun addBenefit(@Valid @RequestBody dto: BenefitDTO) = benefitService.addBenefit(dto)

	@GetMapping(value = ["/benefit/{benefitId}"], produces = ["application/json"])
	fun findBenefit(@PathVariable("benefitId") benefitId: Long) =
		benefitService.findBenefit(benefitId)

	@GetMapping(value = ["/benefit"], produces = ["application/json"])
	@Operation(summary = "Get benefits by ids")
	fun findBenefits(
		@RequestParam("benefitId") benefitIds: Set<Long>
	) = benefitService.findBenefits(benefitIds)

	@GetMapping(value = ["/category/{categoryId}/benefits"], produces = ["application/json"])
	fun findBenefitsByCategory(
		@PathVariable("categoryId") categoryId: Long,
		page: Int = 1,
		size: Int = 10
	) =
		benefitService.findByCategory(categoryId, page, size)

	@GetMapping(value = ["/payer/{payerId}/benefits"], produces = ["application/json"])
	@Operation(summary = "Get Payer Benefits by Payer Id")
	fun findBenefitsByPayerId(@PathVariable("payerId") payerId: Long) =
		benefitService.findBenefitsByPayerId(payerId)

	@GetMapping(value = ["/payer/{payerId}/benefits/catalog"], produces = ["application/json"])
	@Operation(summary = "Get Payer Benefit Catalog by Payer Id")
	fun findBenefitsCatalogByPayerId(@PathVariable("payerId") payerId: Long) =
		categoryService.findBenefitsCatalogByPayerId(payerId)

	@GetMapping(value = ["/payer/{payerId}/providers/accounts"], produces = ["application/json"])
	@Operation(summary = "Get Providers Accounts by Payer Id")
	fun findProviderAccountsByPayerId(@PathVariable("payerId") payerId: Long) =
		benefitService.findProviderAccountsByPayerId(payerId)

	@GetMapping(
		value = ["/category/benefits"], produces =
		["application/json"]
	)
	fun findBenefitsByCategoryAndName(
		@RequestParam(value = "categoryId") categoryId: Long,
		@RequestParam(value = "benefitName") benefitName: String, page: Int = 1,
		size: Int = 10
	) = benefitService.findByCategoryAndName(categoryId, benefitName, page, size)

	@GetMapping(value = ["/category/{categoryId}/principals"], produces = ["application/json"])
	fun findPrincipalsByCategory(@PathVariable("categoryId") categoryId: Long) =
		beneficiaryService.findPrincipalsByCategory(categoryId)

	@GetMapping(value = ["/category/{categoryId}/benefits/main"], produces = ["application/json"])
	fun findMainBenefitsByCategory(@PathVariable("categoryId") categoryId: Long) =
		benefitService.findMainBenefitsByCategory(categoryId)

	@GetMapping(value = ["/category/{categoryId}/benefits/all"], produces = ["application/json"])
	fun findAllBenefitsByCategory(@PathVariable("categoryId") categoryId: Long) =
		benefitService.findByCategory(categoryId)

	@PostMapping(
		value = ["/categories/{categoryId}/benefits/process"],
		produces = ["application/json"]
	)
	suspend fun processCategoryBenefits(@PathVariable("categoryId") categoryId: Long) =
		benefitService.processBenefitsInCategory(ProcessCategoryDto(categoryId = categoryId,
			processBeneficiaries = BeneficiaryBenefitProcess.ALL))


	@PostMapping(
		value = ["/categories/{benefitId}/manualSubBenefits/process"],
		produces = ["application/json"]
	)
	suspend fun processSubBenefitManual(@PathVariable("benefitId") benefitId: Long) =
		benefitService.processSelectBenefitsInCategory(
			ProcessSelectBenefitsDto(
			benefitIds = mutableSetOf(benefitId)
		))

	@PostMapping(value = ["/beneficiaries"], produces = ["application/json"])
	fun addBeneficiary(@RequestBody dto: BeneficiaryDTO) =
		beneficiaryService.addBeneficiary(dto)

	@PostMapping(value = ["/staged/beneficiary"], produces = ["application/json"])
	@Operation(summary = "Add Beneficiaries with STAGED or ACTIVATE status")
	fun addStagedBeneficiary(@RequestBody dtoList: List<BeneficiaryStagingDTO>) =
		beneficiaryService.addStagedBeneficiary(dtoList)

	@PostMapping(value = ["/staged/beneficiary/addition"], produces = ["application/json"])
	@Operation(summary = "Add Beneficiary with STAGED or ACTIVATE status")
	fun addAStagedBeneficiary(@RequestBody dtoList: BeneficiaryStagingDTO) =
		beneficiaryService.addAStagedBeneficiary(dtoList)

	@PostMapping(value = ["/addStagedBeneficiary"], produces = ["application/json"])
	@Operation(summary = "Add Beneficiaries with STAGED status")
	fun addStagedBeneficiaryV2(@RequestBody dtoList: List<AddyStagedBeneficiaryDTO>) =
		beneficiaryService.addStagedBeneficiaryV2(dtoList)


	@GetMapping(value = ["/beneficiaries/UtilizationSms"], produces = ["application/json"])
	fun findByNameOrMemberNumberActiveUtilizationSms(@RequestParam(value = "search") search: String) =
		beneficiaryService.findByNameOrMemberNumberActiveUtilizationSms(search)

	@GetMapping(value = ["/beneficiary/active"], produces = ["application/json"])
	fun getBeneficiaryByBeneficiaryId(@RequestParam(value = "beneficiaryId") beneficiaryId: Long) =
		beneficiaryService.getBeneficiaryByBeneficiaryId(beneficiaryId)


	@GetMapping(value = ["/beneficiaries"], produces = ["application/json"])
	fun findByNameOrMemberNumberActive(@RequestParam(value = "search") search: String,
	                                   @RequestParam(value = "providerId") providerId: Long) =
		beneficiaryService.findByNameOrMemberNumberActive(search,providerId)

	@GetMapping(value = ["/beneficiaries/v2"], produces = ["application/json"])
	fun findByNameOrMemberNumberActiveV2(@RequestParam(value = "search") search: String,
	                                   @RequestParam(value = "providerId") providerId: Long) =
		beneficiaryService.findByNameOrMemberNumberActiveV2(search,providerId)


	@GetMapping(value = ["/beneficiary/getBeneficiaryData"], produces = ["application/json"])
	fun findByMemberNumberAndDateOfBirth(@RequestParam(value = "memberNumber") memberNumber: String,
										 @RequestParam(value = "schemeId") schemeId: Int,
												  @RequestParam(value = "dateOfBirth") dateOfBirth: String) =
			beneficiaryService.findByMemberNumberAndDateOfBirthAndActive(memberNumber,dateOfBirth,schemeId)

	@GetMapping(value = ["/beneficiary/beneficiaryData/{beneficiaryId}"], produces = ["application/json"])
	fun findByBeneficiaryId(@PathVariable(value = "beneficiaryId") beneficiaryId: Long) =
			beneficiaryService.findByBeneficiaryIdAndActive(beneficiaryId)

	@GetMapping(value = ["/beneficiaries/noStatus"], produces = ["application/json"])
	fun findBeneficiaries(@RequestParam(value = "search") search: String) =
		beneficiaryService.searchByNameOrMemberNumber(search)

	@GetMapping(value = ["/find/beneficiary"], produces = ["application/json"])
	fun findBeneficiary(@RequestParam(value = "memberNumber") memberNumber: String) =
		beneficiaryService.findByMemberNumberActive(memberNumber)

	@GetMapping(value = ["/beneficiariesByPayer"], produces = ["application/json"])
	fun findBeneficiariesByPayer(
		@RequestParam(value = "payerId") payerId: Long,
		@RequestParam(value = "search") search: String,
	) =
		beneficiaryService.findByPayerIdNameOrMemberNumber(payerId, search)

	@GetMapping(value = ["/memberSearchPayerInquiry"], produces = ["application/json"])
	fun findMemberSearchPayerInquiry(
		@RequestParam(value = "payerId") payerId: Long,
		@RequestParam(value = "search") search: String,
	) =
		beneficiaryService.findByPayerIdNameOrMemberNumber(payerId, search)


	@GetMapping(value = ["/beneficiariesByPayerAndPlan"], produces = ["application/json"])
	fun findBeneficiariesByPayerAndPlan(
		@RequestParam(value = "payerId") payerId: Long,
		@RequestParam(value = "search") search: String,
		@RequestParam(value = "planId") planId:Long,
	) =
		beneficiaryService.findByPayerIdPlanIdNameOrMemberNumber(payerId, search, planId)

	@GetMapping(value = ["/category/{categoryId}/beneficiaries"], produces = ["application/json"])
	fun getBeneficiariesByCategory(
		@PathVariable("categoryId") categoryId: Long,
		page: Int = 1,
		size: Int = 10
	) =
		beneficiaryService.findByCategory(categoryId, page, size)

	@GetMapping(
		value = ["/category/beneficiaries"], produces =
		["application/json"]
	)
	fun getBeneficiariesByCategoryAndName(
		@RequestParam(value = "categoryId") categoryId: Long,
		@RequestParam(value = "beneficiaryName") beneficiaryName: String, page: Int = 1,
		size: Int = 10
	) = beneficiaryService.findByCategoryAndName(categoryId, beneficiaryName, page, size)


	@GetMapping(value = ["/findByPlanIdAndMemberNumber"], produces = ["application/json"])
	fun findByPlanIdAndMemberNumber(
		@RequestParam(value = "planId") planId: Long,
		@RequestParam(value = "memberNumber") memberNumber: String) = beneficiaryService.findByPlanIdAndMemberNumber(planId, memberNumber)

	@PostMapping(
		value = ["/category/massupload"],
		produces = ["application/json"],
		consumes = ["multipart/form-data"]
	)
	fun batchUpload(
		@RequestParam(value = "policyId") policyId: Long,
		@RequestParam(value = "file") file: MultipartFile
	) = categoryService.batchUpload(policyId, file)

	@PostMapping(value = ["/category/dependantsUpload"],
	produces = ["application/json"],
	consumes = ["multipart/form-data"]
	)
	fun dependantsUpload(
		@RequestParam(value = "policyId") policyId: Long,
		@RequestParam(value = "file") file: MultipartFile
	) = categoryService.dependantsUpload(policyId, file)

	@PostMapping(value = ["/uploadBeneficiaries"],
		produces = ["application/json"],
		consumes = ["multipart/form-data"]
	)
	@Operation(summary = "Upload Beneficiaries")
	suspend fun uploadBeneficiaries(
		@RequestParam(value = "policyId") policyId: Long,
		@RequestParam(value = "status", defaultValue = "ACTIVATE") status: StagingStatus?,
		@RequestParam(value = "processBenefits", defaultValue = "false") processBenefits: Boolean?,
		@RequestParam(value = "file") file: MultipartFile
	) = batchUploadService.uploadBeneficiaries(policyId,status,file, processBenefits)

	@GetMapping(value = ["/staged/beneficiaries"], produces = ["application/json"])
	fun getStagedBeneficiaries(
		@RequestParam(value = "policyId") policyId: Long,
		@RequestParam(value = "page") page: Int,
		@RequestParam(value = "size") size: Int,
	) = beneficiaryService.getStagedBeneficiaries(policyId, page, size)

	@PutMapping(value = ["/staged/update/{stagedId}"], produces = ["application/json"])
	suspend fun updateStagedBeneficiary(@PathVariable("stagedId") stagedId:Long,
								  @Valid @RequestBody dto: BeneficiaryStagedUpdateDto) =
		beneficiaryService.updateStagedBeneficiary(stagedId, dto)

	@PostMapping(value = ["/staged/multiUpdate"], produces = ["application/json"])
	suspend fun multiUpdateStagedBeneficiary(@Valid @RequestBody dto: List<BeneficiaryStagedMultiUpdateDto>) =
		beneficiaryService.multiUpdateStagedBeneficiary(dto)

	@GetMapping(value = ["/mapping"], produces = ["application/json"])
	fun getPayerProviderMapping(
		@RequestParam(value = "payerId") payerId: Long,
		@RequestParam(value = "providerId") providerId: Long
	) =
		payerService.findPayerProviderMapping(payerId, providerId)

	@GetMapping(value = ["/mapping/payer/provider/region/{payerRegionId}"], produces = ["application/json"])
	fun getPayerProviderRegionMapping(
		@PathVariable(value = "payerRegionId") payerRegionId: Long
	) = payerService.findPayerProviderMappingByPayerRegion(payerRegionId)

	@GetMapping(value = ["/exclusion"], produces = ["application/json"])
	fun getProviderCategoryExclusion(
		@PathVariable(value = "providerId") providerId: Long,
		@PathVariable(value = "categoryId") categoryId: Long
	) =
		categoryService.findCategoryProviderExclusion(categoryId, providerId)

	@GetMapping(
		value = ["/covers/{beneficiaryType}/{phoneNumber}/{page}/{size}"],
		produces = ["application/json"]
	)
	fun findBeneficiaries(
		@PathVariable("beneficiaryType") beneficiaryType: String,
		@PathVariable("phoneNumber") phoneNumber: String,
		@PathVariable("page") page: Int = 1,
		@PathVariable("size") size: Int = 20
	) =
		beneficiaryService.findBeneficiaries(phoneNumber, beneficiaryType, page, size)

	@GetMapping(value = ["/covers/{phoneNumber}/{page}/{size}"], produces = ["application/json"])
	fun findCoversByPhoneNumber(
		@PathVariable("phoneNumber") phoneNumber: String,
		@PathVariable("page") page: Int = 1,
		@PathVariable("size") size: Int = 20
	) =
		beneficiaryService.findCoversByPhoneNumber(phoneNumber, page, size)

	@GetMapping(value = ["/beneficiaries/{beneficiaryId}"], produces = ["application/json"])
	suspend fun findCoverBeneficiaries(@PathVariable("beneficiaryId") beneficiaryId: Long) =
		beneficiaryService.findCoverBeneficiaries(beneficiaryId)

	@GetMapping(value = ["/beneficiary/findByCategoryAndMemberNumber"], produces = ["application/json"])
	@Operation(summary = "Get By Category and Member Number")
	suspend fun findByCategoryAndMemberNumber(
		@RequestParam ("categoryId") categoryId:Long,
		@RequestParam ("memberNumber") memberNumber:String,
	) = beneficiaryService.findByCategoryAndMemberNumber(categoryId,memberNumber)

	@GetMapping(value = ["/beneficiaries/byIds"], produces = ["application/json"])
	@Operation(summary = "Get beneficiaries by ids")
	fun findBeneficiariesByIds(
		@Valid @RequestParam ("id") ids: Set<Long>
	) = beneficiaryService.findBeneficiaries(ids)

	@GetMapping(value = ["/family"], produces = ["application/json"])
	@Operation(description = "Find Family by beneficiaryId")
	suspend fun findFamily(@RequestParam("beneficiaryId") beneficiaryId: Long) =
		beneficiaryService.findFamily(beneficiaryId)

	@PutMapping(value = ["/beneficiary/update/{beneficiaryId}"], produces = ["application/json"])
	suspend fun updateBeneficiary(@PathVariable("beneficiaryId") beneficiaryId:Long,
					   @Valid @RequestBody dto: BeneficiaryUpdateDTO) =
		beneficiaryService.updateBeneficiary(beneficiaryId, dto)

	@PutMapping(value = ["/beneficiary/{beneficiaryId}/update/statementDate"], produces = ["application/json"])
	suspend fun updateBeneficiaryStatementDate(@PathVariable("beneficiaryId") beneficiaryId:Long,
								  @Valid @RequestBody dto: BeneficiaryUpdateStatementDateDTO) =
		beneficiaryService.updateBeneficiaryStatementDate(beneficiaryId, dto)

	@PostMapping(value = ["/beneficiaries/status/updateByTemplate"],
		produces = ["application/json"],
		consumes = ["multipart/form-data"])
	@Operation(summary = "Beneficiaries Activation or Deactivation Template Format: ", description = "MEMBER NUMBER (Required), REASON")
	suspend fun beneficiariesActivationOrDeactivation(
		@RequestParam(value = "policyId") policyId: Long,
		@RequestParam(value = "file") file: MultipartFile,
		@RequestParam("status") status: MemberStatus,
		@RequestParam("updateBy") updateBy: String,
		@RequestParam("reason") reason: String?,
		@RequestParam("emailCallBack") emailCallBack: String?,
	) = beneficiaryService.beneficiariesStatusUpdate(policyId, file, updateBy, emailCallBack, reason,status)


	@PostMapping(value = ["/beneficiaries/edit"],
		produces = ["application/json"],
		consumes = ["multipart/form-data"])
	@Operation(summary = "Member Batch Editing Template Format: ", description = "MEMBER NUMBER (Required), MEMBER NAME, PHONE NUMBER, EMAIL, " +
			"NHIF NUMBER, JOIN DATE, REASON")
	suspend fun beneficiariesEdit(
		@RequestParam(value = "policyId") policyId: Long,
		@RequestParam(value = "file") file: MultipartFile,
		@RequestParam("updateType", defaultValue = "INDIVIDUAL")  updateType: UpdateType?,
		@RequestParam("updateBy") updateBy: String,
		@RequestParam("reason") reason: String?,
		@RequestParam("emailCallBack") emailCallBack: String?,
	) = beneficiaryService.updateBeneficiaryBatch(policyId, file, updateType, updateBy, reason, emailCallBack)

	@PostMapping(value = ["/linkCover"], produces = ["application/json"])
	fun linkCover(@Valid @RequestBody dto: LinkCardDto) =
		beneficiaryService.linkCover(dto)

	@GetMapping(value = ["/coverLinks/{beneficiaryId}"], produces = ["application/json"])
	fun getBeneficiaryLinkedCovers(@PathVariable("beneficiaryId") beneficiaryId: Long) =
		beneficiaryService.getBeneficiaryLinkedCovers(beneficiaryId)

	@GetMapping(value = ["/category/{categoryId}/plan"], produces = ["application/json"])
	fun findPlanByCategory(@PathVariable("categoryId") categoryId: Long) =
		planService.findByCategory(categoryId)

	@PostMapping(
		value = ["/saveProvidersFromFile"],
		produces = ["application/json"],
		consumes = ["multipart/form-data"]
	)
	@Operation(summary = "Save ICD10 codes")
	fun saveProvidersFromFile(@RequestParam("file") file: MultipartFile) =
		providerService.saveProvidersFromFile(file)

	@GetMapping(value = ["/category/{categoryId}/category"], produces = ["application/json"])
	fun getCategory(@PathVariable("categoryId") categoryId: Long) =
		categoryService.findById(categoryId)

	@PostMapping(
		value = ["/category/findByCategoryIds"],
		consumes = ["application/json"],
		produces = ["application/json"]
	)
	fun findByCategoryIds(@Valid @RequestBody dto: IdsListDto) =
		categoryService.findByCategoryIds(dto)

	////
	@GetMapping(value = ["/jubilee/getPolicyAndDate"], produces = ["application/json"])
	fun getPolicyIdAndDate(
		@RequestParam(value = "categoryId") categoryId: Long,
		@RequestParam(value = "memberNumber") memberNumber: String
	) = jubileeService.findPolicyIdAndDate(categoryId, memberNumber)

	@GetMapping(value = ["/jubilee/getProviderCode"], produces = ["application/json"])
	fun getPolicyIdAndDate(
		@RequestParam(value = "providerId") providerId: Long,
		@RequestParam(value = "payerId") payerId: Long
	) = jubileeService.findProviderCode(providerId, payerId)

	@GetMapping(value = ["/jubilee/getBenefit"], produces = ["application/json"])
	fun getBenefit(
		@RequestParam(value = "benefitId") benefitId: Long,
		@RequestParam(value = "policyId") policyId: Long
	) = jubileeService.findBenefit(benefitId, policyId)

	@GetMapping(value = ["/benefit/getBenefit"], produces = ["application/json"])
	fun getBenefit(
		@RequestParam(value = "benefitId") benefitId: Long
	) = jubileeService.findBenefit(benefitId)

	@PostMapping(value = ["/changeCategory"], produces = ["application/json"])
	fun changeCategory(@Valid @RequestBody dto: ChangeCategoryDTO) =
		benefitService.changeCategory(dto)

	@PutMapping(value = ["/updateCategory/{categoryId}"], produces = ["application/json"])
	fun updateCategory(@PathVariable("categoryId") categoryId:Long,
					   @Valid @RequestBody dto: UpdateCategoryDTO) =
		benefitService.updateCategory(categoryId, dto)
	@PostMapping(value = ["/updateMember"], produces = ["application/json"])
	fun updateMember(@RequestBody dto: UpdateMemberDTO) = beneficiaryService.updateMember(dto)

	@PostMapping(value = ["/activateBeneficiary/{beneficiaryId}"], produces = ["application/json"])
	fun activateBeneficiary(@PathVariable("beneficiaryId") beneficiaryId: Long) =
		beneficiaryService.activateBeneficiary(beneficiaryId)

//	@PostMapping(
//		value = ["/deactivateBeneficiary/{beneficiaryId}"],
//		produces = ["application/json"]
//	)
//	fun deactivateBeneficiary(@PathVariable("beneficiaryId") beneficiaryId: Long) =
//		beneficiaryService.deactivateBeneficiary(beneficiaryId)

	@PostMapping(
		value = ["/getPayersByBenefitIds"],
		consumes = ["application/json"],
		produces = ["application/json"]
	)
	fun findPayersByBenefitIds(@Valid @RequestBody dto: IdsListDto) =
		benefitService.findPayersByBenefitIds(dto.ids)

	@PostMapping(
		value = ["/getPlansByBenefitIds"],
		consumes = ["application/json"],
		produces = ["application/json"]
	)
	fun findPlanByBenefitIds(@Valid @RequestBody dto: IdsListDto) =
		benefitService.findPlanByBenefitIds(dto.ids)

	@PostMapping(
		value = ["/findByServiceGroups"],
		consumes = ["application/json"],
		produces = ["application/json"]
	)
	fun findByServiceGroups(@Valid @RequestBody dto: ServiceGroupListDto) =
		benefitService.findByServiceGroups(dto.serviceGroups)

	@GetMapping(value = ["/payer/mappings"], produces = ["application/json"])
	fun getPayerMappings(
		@RequestParam(value = "providerId") providerId: Long,
		@RequestParam(value = "payerId") payerId: Long,
		@RequestParam(value = "benefitId") benefitId: Long,
		@RequestParam(value = "categoryId") categoryId: Long,
	) = payerService.findPayerProviderAndBenefitMappings(providerId, payerId, benefitId, categoryId)

	@PostMapping(
		value = ["/categories/status/update"],
		consumes = ["application/json"],
		produces = ["application/json"]
	)
	@Operation(summary = "Categories status update")
	suspend fun updateCategoriesStatus(@Valid @RequestBody dto: CategoryStatusUpdateDTO) =
		benefitService.updateCategoriesStatus(dto)

	@PostMapping(
		value = ["/benefits/status/update"],
		consumes = ["application/json"],
		produces = ["application/json"]
	)
	@Operation(summary = "Benefits status update")
	suspend fun updateBenefitsStatus(@Valid @RequestBody dto: BenefitStatusUpdateDTO) =
		benefitService.updateBenefitsStatus(dto)

	@PostMapping(
		value = ["/beneficiaries/status/update"],
		consumes = ["application/json"],
		produces = ["application/json"]
	)
	@Operation(summary = "Beneficiaries status update")
	suspend fun updateBeneficiariesStatus(@Valid @RequestBody dto: BeneficiaryStatusUpdateDTO) =
		benefitService.updateBeneficiariesStatus(dto)

	@PutMapping(
		value = ["/category/change/{beneficiaryId}"],
		consumes = ["application/json"],
		produces = ["application/json"]
	)
	@Operation(summary = "Beneficiary category change")
	suspend fun categoryChange(
		@PathVariable("beneficiaryId") beneficiaryId:Long,
		@Valid @RequestBody dto: CategoryChangeDto
	) =
		benefitService.categoryChange(beneficiaryId,dto)

	@PostMapping(
		value = ["/category/change"],
		consumes = ["application/json"],
		produces = ["application/json"]
	)
	@Operation(summary = "Beneficiaries category change")
	suspend fun beneficiariesCategoryChange(
		@Valid @RequestBody dto: BeneficiariesCategoryChangeDto
	) = benefitService.beneficiariesCategoryChange(dto)

	@RequestMapping(path = ["/template/download"], method = [RequestMethod.GET])
	@Throws(IOException::class)
	fun templateDownload(templateType: TemplateType): ResponseEntity<Resource> {
		val (workbook, templateName) = when(templateType) {
			TemplateType.MEMBER_ADDITION -> 
				templateService.createMemberAdditionTemplate() to "Member_Addition_Template.xlsx"
			TemplateType.MEMBER_EDIT -> 
				templateService.createMemberEditTemplate() to "Member_Edit_Template.xlsx"
			TemplateType.MEMBER_ACTIVATION_OR_DEACTIVATION -> 
				templateService.createMemberActivationDeactivationTemplate() to "Member_Activation_Deactivation_Template.xlsx"
			TemplateType.DEVICE_UPLOAD ->
				templateService.createDeviceUploadTemplate() to "Upload_Devices_Template.xlsx"
			TemplateType.SIM_UPLOAD ->
				templateService.createSimUploadTemplate() to "Upload_SIMs_Template.xlsx"
			TemplateType.PROVIDER_BULK_UPDATE ->
				templateService.createProviderBulkUpdateTemplate() to "Provider_Bulk_Update_Template.xlsx"
		}

		val byteArrayOutputStream = ByteArrayOutputStream()
		workbook.write(byteArrayOutputStream)
		val inputStreamResource = InputStreamResource(ByteArrayInputStream(byteArrayOutputStream.toByteArray()))

		return ResponseEntity.ok()
			.headers(HttpHeaders().apply {
				add("Content-Disposition", "attachment; filename=$templateName")
			})
			.contentType(MediaType.APPLICATION_OCTET_STREAM)
			.body(inputStreamResource)
	}

	@PostMapping(
		value = ["/category/process"],
		produces = ["application/json"]
	)
	@Operation(summary = "Process a Category")
	suspend fun processBenefitsInCategory(@RequestBody dto: ProcessCategoryDto) =
		benefitService.processBenefitsInCategory(dto)


	@PostMapping(
		value = ["/category/benefits/process"],
		produces = ["application/json"]
	)
	@Operation(summary = "Process Selected Benefits")
	suspend fun processSelectBenefitsInCategory(@Valid @RequestBody dto: ProcessSelectBenefitsDto) =
		benefitService.processSelectBenefitsInCategory(dto)

	@GetMapping(value = ["/payer/regions/{payerId}"], produces = ["application/json"])
	fun findPayerRegionsByPayerId(
		@PathVariable("payerId") payerId: Long
	) =
		payerRegionService.findByPayerId(payerId)

 @GetMapping(value = ["/provider/{providerId}/payers"], produces = ["application/json"])
 @Operation(
 	summary = "Get payers mapped to a provider",
 	description = "Returns payers mapped to a provider. The search query parameter filters payers by name."
 )
 fun getPayersByProviderId(
 	@PathVariable("providerId") providerId: Long,
 	@RequestParam(value = "query", required = false) query: String?,
 	@RequestParam(value = "page", defaultValue = "1") page: Int,
 	@RequestParam(value = "size", defaultValue = "10") size: Int
 ) = payerService.findPayerMapping(providerId, query, page, size)

	@PostMapping(value = ["/policy/findPoliciesByCategoryIds"], produces = ["application/json"])
	@Operation(summary = "Get policies by category ids")
	fun findPoliciesByCategoryIds(@Valid @RequestBody dto: IdsSetDto) = categoryService.findPoliciesByCategoryIds(dto)

	@PostMapping(value = ["/payer/{payerId}/mapping/findByPayerIdAndProviderIds"], produces = ["application/json"])
	@Operation(summary = "Get Payer Provider mappings by Payer and Provider ids")
	fun findByPayerIdAndProviderIds(@PathVariable("payerId") payerId: Long,
									@Valid @RequestBody dto: IdsSetDto) = categoryService.findByPayerIdAndProviderIds(payerId,dto)

	@GetMapping(value = ["/cover/periods"], produces = ["application/json"])
	fun getBeneficiaryCoverPeriods(
		@RequestParam("planId") planId: Long,
		@RequestParam("memberNo") memberNo: String,
	) =
		beneficiaryService.getBeneficiaryCoverPeriods(planId,memberNo)

	@PostMapping(value = ["/payer/policy/mapping"], produces = ["application/json"])
	@Operation(summary = "Add Payer Policy Mapping")
	fun addPayerPolicyMapping(@Valid @RequestBody dto: AddPayerPolicyMappingDto) = policyService.addPayerPolicyMapping(dto)


	@PutMapping(value = ["/payer/policy/mapping/{mappingId}"], produces = ["application/json"])
	@Operation(summary = "Update Payer Policy Mapping")
	fun updatePayerPolicyMapping(@PathVariable("mappingId") mappingId: Long,
								 @Valid @RequestBody dto: UpdatePayerPolicyMappingDto) = policyService.updatePayerPolicyMapping(mappingId, dto)


}
