package net.lctafrica.membership.api.web

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import net.lctafrica.membership.api.domain.OnboardReportType
import net.lctafrica.membership.api.dtos.OnboardAgentResponseDTO
import net.lctafrica.membership.api.dtos.OnboardMemberRequestDTO
import net.lctafrica.membership.api.service.OnboardAgentService
import net.lctafrica.membership.api.service.OnboardService
import net.lctafrica.membership.api.util.toResponse
import org.springframework.core.io.Resource
import org.springframework.data.domain.Page
import org.springframework.data.domain.Sort
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import java.time.LocalDate
import javax.validation.Valid

@RestController
@RequestMapping("/api/v1/membership/onboard")
@Validated
@Tag(name = "Onboard A New Member", description = "Operations related to onboarding a new member.")
class OnboardController(
    private val onboardService: OnboardService,
    private val agentService: OnboardAgentService
) {
    @PostMapping
    @Operation(description = "Onboard a new member, their dependants and beneficiaries.")
    fun onboardPrincipal(
        @RequestBody @Valid onboardMemberRequest: OnboardMemberRequestDTO,
    ): ResponseEntity<Map<String, String>> {
        onboardService.onboardPrincipal(onboardMemberRequest)
        return mapOf("message" to "Principal onboarded successfully!").toResponse(HttpStatus.CREATED)
    }

    @GetMapping("/data/export")
    @Operation(description = "Export members data as Excel.")
    fun exportMembersData(
        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
        @RequestParam("fromDate")
        fromDate: LocalDate?,
        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
        @RequestParam("toDate")
        toDate: LocalDate?,
        @RequestParam("reportType") reportType: OnboardReportType?
    ): ResponseEntity<Resource>? {
        return onboardService.exportMembersData(fromDate, toDate, reportType)
    }

    @Operation(description = "Fetch all members in the system with the results paginated.")
    @GetMapping("/members/paginated")
    fun getAllMembers(
        @RequestParam(required = false)
        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
        fromDate: LocalDate?,
        @RequestParam(required = false)
        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
        toDate: LocalDate?,
        @RequestParam(defaultValue = "0") page: Int,
        @RequestParam(defaultValue = "10") size: Int,
        @RequestParam(
            required = false
        ) beneficiaryType: OnboardReportType? = OnboardReportType.PRINCIPALS_AND_DEPENDANTS,
    ): ResponseEntity<Page<Any>> {
        val paginatedMembers = onboardService.filterAllMembers(fromDate, toDate, page, size, beneficiaryType)
        return ResponseEntity.ok(paginatedMembers)
    }

    @Operation(description = "Fetch all agents in the system.")
    @GetMapping("/agents")
    fun findAllAgents(
        @RequestParam(defaultValue = "0") page: Int,
        @RequestParam(defaultValue = "10") size: Int,
        @RequestParam(defaultValue = "createdAt") sort: String,
        @RequestParam(
            required = false,
            defaultValue = "DESC"
        ) direction: Sort.Direction? = Sort.Direction.DESC,
    ): ResponseEntity<Page<OnboardAgentResponseDTO>> {
        val agents = agentService.findAllAgents(page, size, sort, direction)
        return ResponseEntity.ok(agents)
    }

    @Operation(description = "Get all agents by factory")
    @GetMapping("/agents/filter/{factory}")
    fun getAgentsByEmployerNumber(
        @PathVariable factory: String,
        @RequestParam(defaultValue = "0") page: Int,
        @RequestParam(defaultValue = "10") size: Int,
        @RequestParam(defaultValue = "createdAt") sort: String,
        @RequestParam(
            required = false,
            defaultValue = "DESC"
        ) direction: Sort.Direction? = Sort.Direction.DESC,
    ): ResponseEntity<Page<OnboardAgentResponseDTO>> {
        val agents = agentService.getAgentsByFactory(factory, page, size, sort, direction)
        return ResponseEntity.ok(agents)
    }

    @Operation(description = "Search for an agent by either their full name or agent number.")
    @GetMapping("/agent/search")
    fun searchAgent(
        @RequestParam searchTerm: String
    ): ResponseEntity<List<OnboardAgentResponseDTO?>> {
        val agents = agentService.getAgentBySearchTerm(searchTerm)
        return if (agents.isNotEmpty()) ResponseEntity.ok(agents) else ResponseEntity.notFound().build()
    }
}
