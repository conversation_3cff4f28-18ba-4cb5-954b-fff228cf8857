package net.lctafrica.membership.api.web

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import net.lctafrica.membership.api.dtos.OnboardBenefitResponseDTO
import net.lctafrica.membership.api.service.OnboardBenefitService
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RequestMapping("/api/v1/membership/onboard/benefits")
@Tag(name = "Onboard Benefits", description = "Operations related to the Onboard Portal benefits.")
@RestController
class OnboardBenefitController(private val onboardBenefitService: OnboardBenefitService) : AbstractController() {
    @GetMapping("principal/{principalId}")
    @Operation(description = "Get all the benefits related to the principal with the provided id.")
    fun getAllPrincipalBenefits(@PathVariable principalId: Long): ResponseEntity<List<OnboardBenefitResponseDTO>> {
        val benefits = onboardBenefitService.getAllPrincipalBenefits(principalId)
        return ResponseEntity.ok(benefits)
    }
}
