package net.lctafrica.membership.api.web

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import net.lctafrica.membership.api.dtos.OnboardBeneficiaryResponseDTO
import net.lctafrica.membership.api.service.OnboardBeneficiaryService
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/v1/membership/onboard/beneficiaries")
@Tag(name = "Onboard Beneficiary", description = "Operations related to the Onboard Portal beneficiary.")
class OnboardBeneficiaryController(private val onboardBeneficiaryService: OnboardBeneficiaryService) {
    @Operation(description = "Fetch all beneficiaries in the system.")
    @GetMapping
    fun findAllBeneficiaries(): List<OnboardBeneficiaryResponseDTO> {
        return onboardBeneficiaryService.getAllBeneficiaries()
    }

    @Operation(description = "Fetch a beneficiary by their id.")
    @GetMapping("/{id}")
    fun findBeneficiaryById(@PathVariable id: Long): OnboardBeneficiaryResponseDTO {
        return onboardBeneficiaryService.getBeneficiaryById(id)
    }

    @Operation(description = "Fetch all the beneficiaries related to a principal with the given id.")
    @GetMapping("/principal/{principalId}")
    fun findBeneficiariesByPrincipalId(@PathVariable principalId: Long): List<OnboardBeneficiaryResponseDTO> {
        return onboardBeneficiaryService.getBeneficiariesByPrincipalId(principalId)
    }
}
