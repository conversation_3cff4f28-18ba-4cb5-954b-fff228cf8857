package net.lctafrica.membership.api.web

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import javax.validation.Valid
import net.lctafrica.membership.api.dtos.AddRequestTypeServiceDto
import net.lctafrica.membership.api.dtos.BatchMapServiceProvidersDto
import net.lctafrica.membership.api.dtos.CreateServiceCatalogDto
import net.lctafrica.membership.api.dtos.IdsListDto
import net.lctafrica.membership.api.dtos.MapServiceAndBenefitCatalogBatchDto
import net.lctafrica.membership.api.dtos.UnmapServiceAndBenefitCatalogBatchDto
import net.lctafrica.membership.api.service.IManageProviderBenefitService
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@Tag(
    name = "Services",
    description = "Service APIs - Create and manage services, provider to services mapping and service to benefit mapping"
)
@RestController
@RequestMapping("/api/v1/services")
class ServicesController(
    private val manageProviderBenefitService: IManageProviderBenefitService
) {
    @GetMapping(value = ["/"], produces = ["application/json"])
    @Operation(summary = "Get created active services")
    fun getServiceCatalog(
        @RequestParam(value = "page", defaultValue = "1") page: Int,
        @RequestParam(value = "size", defaultValue = "10") size: Int
    ) =
        manageProviderBenefitService.getServiceCatalog(page, size)

    @PostMapping(value = ["/"], produces = ["application/json"])
    @Operation(summary = "Create a service")
    fun addToServiceCatalog(@Valid @RequestBody dto: CreateServiceCatalogDto) =
        manageProviderBenefitService.addToServiceCatalog(dto)

    @DeleteMapping(value = ["/{catalogId}"], produces = ["application/json"])
    @Operation(summary = "Delete/Remove from Service Catalog")
    fun removeFromServiceCatalog(@PathVariable("catalogId") catalogId: Long) =
        manageProviderBenefitService.removeFromServiceCatalog(catalogId)

    @PostMapping(value = ["/map/benefits"], produces = ["application/json"])
    @Operation(summary = "Map service benefits")
    fun batchMapServiceAndBenefitCatalog(@Valid @RequestBody dto: MapServiceAndBenefitCatalogBatchDto) =
        manageProviderBenefitService.batchMapServiceAndBenefitCatalog(dto)

    @DeleteMapping(value = ["/unmap/benefits/{serviceCatalogId}"], produces = ["application/json"])
    @Operation(summary = "Delete/Remove from Service and Benefit mapping")
    fun batchUnmapServiceBenefits(
        @PathVariable("serviceCatalogId") serviceCatalogId: Long,
        @Valid @RequestBody dto: UnmapServiceAndBenefitCatalogBatchDto
    ) = manageProviderBenefitService.batchUnmapServiceBenefits(serviceCatalogId, dto)

    @GetMapping(value = ["/mapped/benefits"], produces = ["application/json"])
    @Operation(summary = "Get mapped service benefits")
    fun getMappedBenefits(
        @RequestParam(value = "serviceCatalogId") serviceCatalogId: Long,
        @RequestParam(value = "page", defaultValue = "1") page: Int,
        @RequestParam(value = "size", defaultValue = "10") size: Int
    ) =
        manageProviderBenefitService.getMappedBenefits(serviceCatalogId, page, size)

    @PostMapping(value = ["/map/providers"], produces = ["application/json"])
    @Operation(summary = "Map service providers")
    fun batchMapServiceProviders(@Valid @RequestBody dto: BatchMapServiceProvidersDto) =
        manageProviderBenefitService.batchMapServiceProviders(dto)

    @DeleteMapping(value = ["/unmap/providers/{serviceCatalogId}"], produces = ["application/json"])
    @Operation(summary = "Delete/Remove from service providers")
    fun batchUnmapServiceProviders(
        @PathVariable("serviceCatalogId") serviceCatalogId: Long,
        @Valid @RequestBody dto: IdsListDto
    ) = manageProviderBenefitService.batchUnmapServiceProviders(serviceCatalogId, dto)

    @GetMapping(value = ["/mapped/providers"], produces = ["application/json"])
    @Operation(summary = "Get mapped service providers")
    fun getMappedProviders(
        @RequestParam(value = "serviceCatalogId") serviceCatalogId: Long,
        @RequestParam(value = "page", defaultValue = "1") page: Int,
        @RequestParam(value = "size", defaultValue = "10") size: Int
    ) =
        manageProviderBenefitService.getMappedProviders(serviceCatalogId, page, size)

    @GetMapping(value = ["/requestTypes"], produces = ["application/json"])
    @Operation(summary = "Get Request Types")
    fun getRequestTypes(
        @RequestParam(value = "providerId") providerId: Long
    ) = manageProviderBenefitService.getRequestTypes(providerId)

    @GetMapping(value = ["/requestServices"], produces = ["application/json"])
    @Operation(summary = "Get Request Services")
    fun getRequestService(
        @RequestParam(value = "providerId") providerId: Long,
        @RequestParam(value = "requestName") requestName: String
    ) = manageProviderBenefitService.getRequestService(providerId,requestName)

    @PostMapping(value = ["/requestTypesAndServices"], produces = ["application/json"])
    @Operation(summary = "Add Request Types And Services")
    fun addRequestTypesAndServices(@Valid @RequestBody dto: AddRequestTypeServiceDto) =
        manageProviderBenefitService.addRequestTypesAndServices(dto)
}