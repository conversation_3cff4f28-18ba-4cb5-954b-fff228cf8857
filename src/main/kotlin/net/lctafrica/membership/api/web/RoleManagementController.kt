package net.lctafrica.membership.api.web

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import net.lctafrica.membership.api.domain.DenyPolicy
import net.lctafrica.membership.api.dtos.*
import net.lctafrica.membership.api.service.RoleManagementService
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import javax.validation.Valid

@RestController
@Tag(name = "Role Management", description = "Operations related to managing custom roles, groups, and permissions.")
@RequestMapping("/api/v1/membership/roles")
class RoleManagementController(
    private val roleManagementService: RoleManagementService
) {
    @PostMapping("/custom")
    @Operation(
        summary = "Create a custom role",
        description = "Creates a new custom role with the specified permissions."
    )
    fun createCustomRole(
        @Valid @RequestBody dto: CreateCustomRoleDto,
    ): ResponseEntity<Any> {
        val result = roleManagementService.createCustomRole(dto)
        return if (result.success) {
            ResponseEntity.ok(result.data)
        } else {
            ResponseEntity.badRequest().body(mapOf("error" to (result.msg ?: "Unknown error")))
        }
    }

    @PutMapping("/custom/{id}")
    @Operation(
        summary = "Update a custom role",
        description = "Updates an existing custom role. Permissions can be added or removed separately. Users can be added or removed from the role."
    )
    fun updateCustomRole(
        @PathVariable id: Long,
        @Valid @RequestBody dto: UpdateCustomRoleDto,
        @RequestParam payerId: Long
    ): ResponseEntity<Any> {
        val result = roleManagementService.updateCustomRole(id, dto, payerId)
        return if (result.success) {
            ResponseEntity.ok(result.data!!)
        } else {
            ResponseEntity.badRequest().body(mapOf("error" to (result.msg ?: "Unknown error")))
        }
    }

    @GetMapping("/custom/{id}")
    @Operation(
        summary = "Get a custom role",
        description = "Retrieves a custom role by ID."
    )
    fun getCustomRole(
        @PathVariable id: Long,
        @RequestParam payerId: Long
    ): ResponseEntity<Any> {
        val result = roleManagementService.getCustomRoleById(id, payerId)
        return if (result.success) {
            ResponseEntity.ok(result.data)
        } else {
            if (result.msg?.contains("Access denied") == true) {
                ResponseEntity.status(403).body(mapOf("error" to (result.msg)))
            } else {
                ResponseEntity.notFound().build()
            }
        }
    }

    @GetMapping("/custom/{id}/users")
    @Operation(
        summary = "Get users with a custom role",
        description = "Retrieves all users who are directly assigned to a specific custom role with pagination."
    )
    fun getCustomRoleUsers(
        @PathVariable id: Long,
        @RequestParam payerId: Long,
        @RequestParam(defaultValue = "1") page: Int,
        @RequestParam(defaultValue = "10") size: Int
    ): ResponseEntity<Page<UserBasicDto>> {
        val pageable = PageRequest.of(if (page > 0) page - 1 else 0, size)
        val result = roleManagementService.getCustomRoleUsers(id, payerId, pageable)
        return if (result.success) {
            ResponseEntity.ok(result.data!!)
        } else {
            if (result.msg?.contains("Access denied") == true) {
                ResponseEntity.status(403).build()
            } else {
                ResponseEntity.badRequest().build()
            }
        }
    }

    @GetMapping("/custom")
    @Operation(
        summary = "Get all custom roles",
        description = "Retrieves all custom roles with pagination. Optionally filter by name."
    )
    fun getAllCustomRoles(
        @RequestParam(defaultValue = "1") page: Int,
        @RequestParam(defaultValue = "10") size: Int,
        @RequestParam payerId: Long,
        @RequestParam(required = false) name: String?
    ): ResponseEntity<Page<CustomRoleDto>> {
        val roles = roleManagementService.getAllCustomRoles(page, size, payerId, name)
        return ResponseEntity.ok(roles)
    }


    @DeleteMapping("/custom/{id}")
    @Operation(
        summary = "Delete a custom role",
        description = "Deletes a custom role by ID. If the role has invalid user assignments, they will be cleaned up automatically."
    )
    fun deleteCustomRole(
        @PathVariable id: Long,
        @RequestParam deletedBy: String,
        @RequestParam payerId: Long
    ): ResponseEntity<DeleteOperationResponseDto> {
        val result = roleManagementService.deleteCustomRole(id, deletedBy, payerId)
        return if (result.success) {
            ResponseEntity.ok(DeleteOperationResponseDto(true, "Custom role deleted successfully"))
        } else {
            if (result.msg?.contains("Access denied") == true) {
                ResponseEntity.status(403).body(DeleteOperationResponseDto(false, result.msg))
            } else if (result.msg?.startsWith("You can't delete this role because it's still assigned to") == true) {
                // specific error about role assignments
                ResponseEntity.badRequest().body(DeleteOperationResponseDto(false, result.msg))
            } else {
                ResponseEntity.badRequest().body(DeleteOperationResponseDto(false, result.msg ?: "Unknown error"))
            }
        }
    }


    @PostMapping("/groups")
    @Operation(summary = "Create a user group", description = "Creates a new user group with the specified roles.")
    fun createUserGroup(@Valid @RequestBody dto: CreateUserGroupDto): ResponseEntity<Any> {
        val result = roleManagementService.createUserGroup(dto)
        return if (result.success) {
            ResponseEntity.ok(result.data!!)
        } else {
            ResponseEntity.badRequest().body(mapOf("error" to (result.msg ?: "Failed to create user group")))
        }
    }

    @GetMapping("/groups")
    @Operation(
        summary = "Get all user groups",
        description = "Retrieves all user groups with pagination for a specific payer. Optionally filter by name."
    )
    fun getUserGroups(
        @RequestParam(defaultValue = "1") page: Int,
        @RequestParam(defaultValue = "10") size: Int,
        @RequestParam payerId: Long,
        @RequestParam(required = false) name: String?
    ): ResponseEntity<Page<UserGroupDto>> {
        val groups = roleManagementService.getUserGroups(page, size, payerId, name)
        return ResponseEntity.ok(groups)
    }

    @GetMapping("/groups/{id}")
    @Operation(summary = "Get a user group", description = "Retrieves a user group by ID.")
    fun getUserGroup(@PathVariable id: Long, @RequestParam payerId: Long): ResponseEntity<Any> {
        val result = roleManagementService.getUserGroupById(id, payerId)
        return if (result.success) {
            ResponseEntity.ok(result.data!!)
        } else {
            if (result.msg?.contains("not found") == true) {
                ResponseEntity.notFound().build()
            } else if (result.msg?.contains("Access denied") == true) {
                ResponseEntity.status(403).body(mapOf("error" to result.msg))
            } else {
                ResponseEntity.badRequest().body(mapOf("error" to (result.msg ?: "Failed to retrieve user group")))
            }
        }
    }

    @PutMapping("/groups/{id}/roles")
    @Operation(
        summary = "Update group roles",
        description = "Updates a group's custom and predefined roles."
    )
    fun updateGroupRoles(
        @PathVariable id: Long,
        @Valid @RequestBody dto: UpdateGroupRolesDto,
        @RequestParam payerId: Long
    ): ResponseEntity<Any> {
        val result = roleManagementService.updateGroupRoles(id, dto, payerId)
        return if (result.success) {
            ResponseEntity.ok(result.data!!)
        } else {
            ResponseEntity.badRequest().body(mapOf("error" to (result.msg ?: "Failed to update group roles")))
        }
    }

    @PutMapping("/groups/{id}")
    @Operation(
        summary = "Update user group",
        description = "Updates a user group's name, description, roles, and members. Allows adding and removing specific roles and users."
    )
    fun updateUserGroup(
        @PathVariable id: Long,
        @Valid @RequestBody dto: UpdateUserGroupDto,
        @RequestParam payerId: Long
    ): ResponseEntity<Any> {
        val result = roleManagementService.updateUserGroup(id, dto, payerId)
        return if (result.success) {
            ResponseEntity.ok(result.data!!)
        } else {
            if (result.msg?.contains("Access denied") == true) {
                ResponseEntity.status(403).body(mapOf("error" to result.msg))
            } else {
                ResponseEntity.badRequest().body(mapOf("error" to (result.msg ?: "Failed to update user group")))
            }
        }
    }

    @DeleteMapping("/groups/{id}")
    @Operation(
        summary = "Delete a user group",
        description = "Deletes a user group by ID and removes all members from the group. Requires payerId to ensure payers can only delete their own groups."
    )
    fun deleteUserGroup(
        @PathVariable id: Long,
        @RequestParam deletedBy: String,
        @RequestParam payerId: Long
    ): ResponseEntity<DeleteOperationResponseDto> {
        val result = roleManagementService.deleteUserGroup(id, deletedBy, payerId)
        return if (result.success) {
            ResponseEntity.ok(DeleteOperationResponseDto(true, "User group deleted successfully"))
        } else {
            if (result.msg?.contains("Access denied") == true) {
                ResponseEntity.status(403).body(DeleteOperationResponseDto(false, result.msg))
            } else {
                ResponseEntity.badRequest().body(DeleteOperationResponseDto(false, result.msg ?: "Failed to delete user group"))
            }
        }
    }


    @PostMapping("/groups/{groupId}/members")
    @Operation(
        summary = "Add users to a group",
        description = "Adds multiple users to a group, allowing them to inherit the group's roles."
    )
    fun addUsersToGroup(
        @PathVariable groupId: Long,
        @Valid @RequestBody dto: AddUsersToGroupDto
    ): ResponseEntity<Any> {
        val result = roleManagementService.addUsersToGroup(groupId, dto)
        return if (result.success) {
            ResponseEntity.ok(mapOf("message" to result.data))
        } else {
            ResponseEntity.badRequest().body(mapOf("error" to (result.msg ?: "Failed to add users to group")))
        }
    }


    @DeleteMapping("/groups/{groupId}/members")
    @Operation(
        summary = "Remove users from a group",
        description = "Removes multiple users from a group, revoking their inherited roles."
    )
    fun removeUsersFromGroup(
        @PathVariable groupId: Long,
        @Valid @RequestBody dto: RemoveUsersFromGroupDto
    ): ResponseEntity<Any> {
        val result = roleManagementService.removeUsersFromGroup(groupId, dto)
        return if (result.success) {
            ResponseEntity.ok(mapOf("message" to result.data))
        } else {
            ResponseEntity.badRequest().body(mapOf("error" to (result.msg ?: "Failed to remove users from group")))
        }
    }

    @GetMapping("/groups/{groupId}/members")
    @Operation(summary = "Get group members", description = "Retrieves all members of a group with pagination.")
    fun getGroupMembers(
        @PathVariable groupId: Long,
        @RequestParam(defaultValue = "1") page: Int,
        @RequestParam(defaultValue = "10") size: Int
    ): ResponseEntity<Page<UserGroupMemberDto>> {
        val pageable = PageRequest.of(if (page > 0) page - 1 else 0, size)
        val result = roleManagementService.getGroupMembers(groupId, pageable)
        return if (result.success) {
            ResponseEntity.ok(result.data!!)
        } else {
            ResponseEntity.badRequest().build()
        }
    }


    @PostMapping("/deny-policies")
    @Operation(
        summary = "Create deny policy",
        description = "Creates a single deny policy for multiple users and multiple permissions, which take precedence over allowed roles."
    )
    fun createDenyPolicy(@Valid @RequestBody dto: CreateDenyPolicyDto): ResponseEntity<Any> {
        val result = roleManagementService.createDenyPolicy(dto)
        return if (result.success) {
            ResponseEntity.ok(result.data!!)
        } else {
            ResponseEntity.badRequest().body(mapOf("error" to (result.msg ?: "Failed to create deny policy")))
        }
    }


    @GetMapping("/deny-policies")
    @Operation(
        summary = "Get deny policies",
        description = "Gets all the deny policies related to a payer"
    )
    fun getPayerDenyPolicies(
        @RequestParam(defaultValue = "1") page: Int,
        @RequestParam(defaultValue = "10") size: Int,
        @RequestParam payerId: Long
    ): ResponseEntity<Page<DenyPolicy>> {
        val payerDenyPolicies = roleManagementService.getPayerDenyPolicies(page, size, payerId)
        return ResponseEntity.ok(payerDenyPolicies)
    }

    @GetMapping("/deny-policies/user/{userId}")
    @Operation(
        summary = "Get user deny policies",
        description = "Retrieves all deny policies for a user."
    )
    fun getUserDenyPolicies(@PathVariable userId: String): ResponseEntity<List<DenyPolicyDto>> {
        val result = roleManagementService.getUserDenyPolicies(userId)
        return if (result.success) {
            ResponseEntity.ok(result.data!!)
        } else {
            ResponseEntity.badRequest().build()
        }
    }

    @GetMapping("/deny-policies/{id}/users")
    @Operation(
        summary = "Get users in a deny policy",
        description = "Retrieves all users associated with a specific deny policy with pagination. Requires payerId to ensure payers can only access their own policies."
    )
    fun getDenyPolicyUsers(
        @PathVariable id: Long,
        @RequestParam payerId: Long,
        @RequestParam(defaultValue = "1") page: Int,
        @RequestParam(defaultValue = "10") size: Int
    ): ResponseEntity<Page<UserBasicDto>> {
        try {
            val pageable = PageRequest.of(if (page > 0) page - 1 else 0, size)
            val result = roleManagementService.getDenyPolicyUsers(id, payerId, pageable)
            return if (result.success) {
                ResponseEntity.ok(result.data!!)
            } else {
                if (result.msg?.contains("Access denied") == true) {
                    ResponseEntity.status(403).build()
                } else {
                    ResponseEntity.badRequest().build()
                }
            }
        } catch (e: Exception) {
            return ResponseEntity.status(500).build()
        }
    }

    @GetMapping("/deny-policies/{id}")
    @Operation(
        summary = "Get deny policy by ID",
        description = "Retrieves a deny policy by ID including users in the policy data. Requires payerId to ensure payers can only access their own policies."
    )
    fun getDenyPolicyById(
        @PathVariable id: Long,
        @RequestParam payerId: Long
    ): ResponseEntity<DenyPolicyDto> {
        val result = roleManagementService.getDenyPolicyById(id, payerId)
        return if (result.success) {
            ResponseEntity.ok(result.data!!)
        } else {
            if (result.msg?.contains("Access denied") == true) {
                ResponseEntity.status(403).build()
            } else {
                ResponseEntity.notFound().build()
            }
        }
    }


    @PutMapping("/deny-policies/{id}")
    @Operation(
        summary = "Update a deny policy",
        description = "Updates an existing deny policy with new name, reason, expiration date, users, or permissions."
    )
    fun updateDenyPolicyV2(
        @PathVariable id: Long,
        @Valid @RequestBody dto: UpdateDenyPolicyDto
    ): ResponseEntity<DenyPolicyDto> {
        val result = roleManagementService.updateDenyPolicy(id, dto)
        return if (result.success) {
            ResponseEntity.ok(result.data!!)
        } else {
            ResponseEntity.badRequest().build()
        }
    }


    @DeleteMapping("/deny-policies/{id}")
    @Operation(
        summary = "Delete a deny policy",
        description = "Deletes a deny policy by ID. Requires payerId to ensure payers can only delete their own policies."
    )
    fun deleteDenyPolicyV2(
        @PathVariable id: Long,
        @RequestParam payerId: Long
    ): ResponseEntity<DeleteOperationResponseDto> {
        val result = roleManagementService.deleteDenyPolicy(id, payerId)
        return if (result.success) {
            ResponseEntity.ok(DeleteOperationResponseDto(true, "Deny policy deleted successfully"))
        } else {
            if (result.msg?.contains("Access denied") == true) {
                ResponseEntity.status(403).body(DeleteOperationResponseDto(false, result.msg))
            } else {
                ResponseEntity.badRequest().body(DeleteOperationResponseDto(false, result.msg ?: "Failed to delete deny policy"))
            }
        }
    }

    @GetMapping("/users/{userId}/effective-roles")
    @Operation(
        summary = "Get a user's effective roles",
        description = "Retrieves a user's effective roles, including direct roles, inherited roles, and denied permissions."
    )
    fun getUserEffectiveRoles(@PathVariable userId: String): ResponseEntity<UserEffectiveRolesDto> {
        val result = roleManagementService.getUserEffectiveRoles(userId)
        return if (result.success) {
            ResponseEntity.ok(result.data!!)
        } else {
            ResponseEntity.badRequest().build()
        }
    }

    @GetMapping("/users/{userId}/direct-roles")
    @Operation(
        summary = "Get a user's direct roles",
        description = "Retrieves roles directly assigned to a user (not inherited from groups or custom roles)."
    )
    fun getUserDirectRoles(@PathVariable userId: String): ResponseEntity<UserDirectRolesDto> {
        val result = roleManagementService.getUserDirectRoles(userId)
        return if (result.success) {
            ResponseEntity.ok(result.data!!)
        } else {
            ResponseEntity.badRequest().build()
        }
    }


    @GetMapping("/permissions")
    @Operation(
        summary = "Get all available permissions",
        description = "Retrieves all available permissions (realm roles) with their descriptions."
    )
    fun getAllPermissions(): ResponseEntity<List<PermissionDto>> {
        val result = roleManagementService.getAllPermissions()
        return if (result.success) {
            ResponseEntity.ok(result.data!!)
        } else {
            ResponseEntity.badRequest().build()
        }
    }

    @GetMapping("/users/{userId}/groups")
    @Operation(
        summary = "Get user groups",
        description = "Retrieves all groups a user belongs to."
    )
    fun getUserGroups(@PathVariable userId: String): ResponseEntity<List<UserGroupDto>> {
        val result = roleManagementService.getUserGroups(userId)
        return if (result.success) {
            ResponseEntity.ok(result.data!!)
        } else {
            ResponseEntity.badRequest().body(emptyList())
        }
    }

    @GetMapping("/users/{userId}/custom-roles")
    @Operation(
        summary = "Get user custom roles",
        description = "Retrieves all custom roles directly assigned to a user."
    )
    fun getUserCustomRoles(@PathVariable userId: String): ResponseEntity<List<CustomRoleDto>> {
        val result = roleManagementService.getUserCustomRoles(userId)
        return if (result.success) {
            ResponseEntity.ok(result.data!!)
        } else {
            ResponseEntity.badRequest().body(emptyList())
        }
    }

    @GetMapping("/users/{userId}/standalone-roles")
    @Operation(
        summary = "Get user standalone roles",
        description = "Retrieves all standalone roles (custom and predefined) directly assigned to a user."
    )
    fun getUserStandaloneRoles(@PathVariable userId: String): ResponseEntity<UserStandaloneRolesDto> {
        val result = roleManagementService.getUserStandaloneRoles(userId)
        return if (result.success) {
            ResponseEntity.ok(result.data!!)
        } else {
            ResponseEntity.badRequest().build()
        }
    }

    @PostMapping("/users/{userId}/custom-roles/{roleId}")
    @Operation(
        summary = "Assign custom role to user",
        description = "Assigns a custom role directly to a user."
    )
    fun assignCustomRoleToUser(
        @PathVariable userId: String,
        @PathVariable roleId: Long,
        @RequestParam assignedBy: String
    ): ResponseEntity<Any> {
        val result = roleManagementService.assignCustomRoleToUser(userId, roleId, assignedBy)
        return if (result.success) {
            ResponseEntity.ok(mapOf("message" to result.data))
        } else {
            ResponseEntity.badRequest().body(mapOf("error" to (result.msg ?: "Failed to assign custom role to user")))
        }
    }

    @DeleteMapping("/users/{userId}/custom-roles/{roleId}")
    @Operation(
        summary = "Remove custom role from user",
        description = "Removes a custom role from a user."
    )
    fun removeCustomRoleFromUser(
        @PathVariable userId: String,
        @PathVariable roleId: Long,
        @RequestParam actionedBy: String
    ): ResponseEntity<DeleteOperationResponseDto> {
        val result = roleManagementService.removeCustomRoleFromUser(userId, roleId, actionedBy)
        return if (result.success) {
            ResponseEntity.ok(DeleteOperationResponseDto(true, "Custom role removed from user successfully"))
        } else {
            ResponseEntity.badRequest().body(DeleteOperationResponseDto(false, result.msg ?: "Failed to remove custom role from user"))
        }
    }

    @PostMapping("/users/payer")
    @Operation(
        summary = "Create a payer user",
        description = "Creates a new payer user with optional group and role assignments."
    )
    fun createPayerUser(@Valid @RequestBody dto: CreatePayerUserDto): ResponseEntity<Any> {
        val result = roleManagementService.createPayerUser(dto)
        return if (result.success) {
            ResponseEntity.ok(result.data)
        } else {
            ResponseEntity.badRequest().body(mapOf("error" to (result.msg ?: "Failed to create payer user")))
        }
    }
}
