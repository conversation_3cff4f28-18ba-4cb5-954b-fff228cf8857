package net.lctafrica.membership.api.web

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import net.lctafrica.membership.api.dtos.OnboardAgentDTO
import net.lctafrica.membership.api.dtos.OnboardAgentResponseDTO
import net.lctafrica.membership.api.service.OnboardAgentService
import net.lctafrica.membership.api.util.toResponse
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import javax.validation.Valid

@RestController
@RequestMapping("/api/v1/membership/onboard/agents")
@Tag(name = "Onboard Agents", description = "Operations related to Onboard Portal agents.")
class OnboardAgentController(private val onboardAgentService: OnboardAgentService) {
    @Operation(description = "Create a new agent.")
    @PostMapping("/create")
    fun createAgent(@Valid @RequestBody request: OnboardAgentDTO): ResponseEntity<String> {
        val res = onboardAgentService.createAgent(request)
        return if (res.keys.contains(true)) {
            "Agent created successfully!".toResponse(HttpStatus.CREATED)
        } else {
            "Error creating agent.".toResponse(
                HttpStatus.BAD_REQUEST
            )
        }
    }

    @GetMapping("/count")
    fun countAllAgents(): ResponseEntity<Long> = ResponseEntity.ok(onboardAgentService.countAllAgents())

    @GetMapping("/{id}")
    fun getAgentById(@PathVariable id: Long): ResponseEntity<OnboardAgentResponseDTO> {
        return ResponseEntity.ok(onboardAgentService.findAgentById(id))
    }
}
