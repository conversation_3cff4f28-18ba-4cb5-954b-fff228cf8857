package net.lctafrica.membership.api.web


import io.swagger.v3.oas.annotations.Operation
import net.lctafrica.membership.api.dtos.DataCollectionBeneficiaryDTO
import net.lctafrica.membership.api.dtos.DataCollectionReportType
import net.lctafrica.membership.api.service.DataCollectionService
import org.springframework.core.io.Resource
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import javax.validation.Valid

@RestController
@RequestMapping("/api/v1/membership/data/collection")
class DataCollectionController(
    val dataCollectionService: DataCollectionService
) : AbstractController() {
    @PostMapping(value = ["/saveBeneficiary"], produces = ["application/json"])
    @Operation(description = "Add Beneficiary and Dependants")
    fun addBeneficiary(@Valid @RequestBody dto: DataCollectionBeneficiaryDTO) =
        dataCollectionService.addBeneficiary(dto)

    @GetMapping(value = ["/search"], produces = ["application/json"])
    @Operation(description = "Search beneficiaries by the beneficiary name")
    fun findBeneficiary(@RequestParam(value = "search") search: String) = dataCollectionService.search(search)

    @GetMapping("/principals/search")
    @Operation(description = "Search principal by name or employee number")
    fun findPrincipals(@RequestParam(value = "search") search: String) =
        dataCollectionService.searchForPrincipals(search)

    @GetMapping(value = ["/family"], produces = ["application/json"])
    @Operation(description = "Find Family by beneficiaryId")
    suspend fun findFamily(@RequestParam("beneficiaryId") beneficiaryId: Long) =
        dataCollectionService.findFamily(beneficiaryId)

    @GetMapping(value = ["/dependants"], produces = ["application/json"])
    @Operation(description = "Find Dependants by beneficiary Id")
    fun findDependants(@RequestParam("beneficiaryId") beneficiaryId: Long) =
        dataCollectionService.findDependants(beneficiaryId)

    @GetMapping(value = ["/beneficiaries"], produces = ["application/json"])
    @Operation(
        summary = "Filter Beneficiaries"
    )
    fun filterBeneficiaries(
        @RequestParam(value = "payerId") payerId: Long,
        @RequestParam(value = "fromDate") fromDate: String,
        @RequestParam(value = "toDate") toDate: String,
        @RequestParam(value = "reportType") reportType: String,
        @RequestParam(value = "page") page: Int,
        @RequestParam(value = "size") size: Int,
    ) = dataCollectionService.filterBeneficiaries(payerId, fromDate, toDate, reportType, page, size)

    @GetMapping(value = ["/count/principals"], produces = ["application/json"])
    @Operation(description = "Find number of principals")
    fun findPrincipalsCount() = dataCollectionService.findPrincipalsCount()

    @GetMapping(value = ["/count/all"], produces = ["application/json"])
    @Operation(description = "Find total number of beneficiaries")
    fun findAllBeneficiariesCount() = dataCollectionService.findAllBeneficiariesCount()

    @GetMapping("/export")
    @Operation(summary = "Export beneficiaries data as Excel")
    fun exportBeneficiaries(
        @RequestParam(value = "payerId") payerId: Long,
        @RequestParam(value = "fromDate") fromDate: String,
        @RequestParam(value = "toDate") toDate: String,
        @RequestParam(value = "reportType") reportType: DataCollectionReportType,
        @RequestParam(value = "page") page: Int,
        @RequestParam(value = "size") size: Long,
    ): ResponseEntity<Resource>? {
        return dataCollectionService.exportBeneficiaries(payerId, fromDate, toDate, reportType, page, size)
    }

    @GetMapping(value = ["/documentDownload"], produces = ["application/json"])
    @Operation(summary = "Download Document given the document id")
    suspend fun getDocumentDownloadByDocumentId(
        @RequestParam(name = "id") id: Long
    ) = dataCollectionService.getDocumentDownloadByDocumentId(id);

    @PutMapping(value = ["/update"], produces = ["application/json"])
    @Operation(summary = "Update beneficiary benefit type")
    fun updateBeneficiaryBenefit(
        @RequestParam("beneficiaryId") beneficiaryId: Long,
        @RequestParam("benefitName") benefitName: String
    ) =
        dataCollectionService.updateBeneficiaryBenefit(beneficiaryId, benefitName);
}