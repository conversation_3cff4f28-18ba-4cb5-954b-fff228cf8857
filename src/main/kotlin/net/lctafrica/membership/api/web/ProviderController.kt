package net.lctafrica.membership.api.web

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import javax.validation.Valid
import net.lctafrica.membership.api.domain.Category
import net.lctafrica.membership.api.domain.Copayment
import net.lctafrica.membership.api.domain.ProviderTier
import net.lctafrica.membership.api.dtos.CopayDTO
import net.lctafrica.membership.api.dtos.CreateMappedProviderAccountDto
import net.lctafrica.membership.api.dtos.CreateProviderAccountDto
import net.lctafrica.membership.api.dtos.ExclusionDTO
import net.lctafrica.membership.api.dtos.IdsListDto
import net.lctafrica.membership.api.dtos.IdsSetDto
import net.lctafrica.membership.api.dtos.MapProviderAccountToPayerDto
import net.lctafrica.membership.api.dtos.MapProviderUserToPayerDto
import net.lctafrica.membership.api.dtos.MultiProviderUpdateDTO
import net.lctafrica.membership.api.dtos.MultipleWhiteListDTO
import net.lctafrica.membership.api.dtos.ProviderDTO
import net.lctafrica.membership.api.dtos.ProviderUpdateDTO
import net.lctafrica.membership.api.dtos.RemoveRestrictionDTO
import net.lctafrica.membership.api.dtos.RestrictionDTO
import net.lctafrica.membership.api.dtos.SearchType
import net.lctafrica.membership.api.dtos.UpdateProviderAccountDto
import net.lctafrica.membership.api.dtos.WhiteListDTO
import net.lctafrica.membership.api.service.ICopaySetupService
import net.lctafrica.membership.api.service.IProviderRestrictionService
import net.lctafrica.membership.api.service.IProviderService
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.multipart.MultipartFile

@Tag(name = "Provider APIs", description = "Provider APIs")
@RestController
@RequestMapping("/api/v1/provider")
class ProviderController(
    val providerService: IProviderService,
    val restrictionService: IProviderRestrictionService,
    val copaySetupService: ICopaySetupService
) : AbstractController(){

    @GetMapping(produces = ["application/json"])
    fun findProviderById(@RequestParam("providerId") providerId: Long) =
        providerService.findById(providerId)

    @PostMapping(value = ["/findByIds"], produces = ["application/json"])
    @Operation(summary = "Get Providers by Ids")
    suspend fun findByIds(@Valid @RequestBody dto: IdsListDto) = providerService.findByIds(dto)

    @PostMapping(value = ["/findBranchesByProviderIds"], produces = ["application/json"])
    @Operation(summary = "Get Branches by Main Provider Ids")
    suspend fun findBranchesByProviderIds(@Valid @RequestBody dto: IdsSetDto) = providerService.findBranchesByProviderIds(dto)


    @GetMapping(value = ["/branches"],produces = ["application/json"])
    fun findProviderBranchesById(@RequestParam("providerId") providerId: Long) =
        providerService.findProviderBranchesById(providerId)
    @GetMapping(value = ["/search"],produces = ["application/json"])
    fun searchProviders(
        @RequestParam(value = "searchType", defaultValue = "ALL") searchType: SearchType,
        @RequestParam(value = "providerName") providerName: String?,
        @RequestParam(value = "mainFacilityId") mainFacilityId: Long?,
        @RequestParam(value = "page", defaultValue = "1") page: Int,
        @RequestParam(value = "size", defaultValue = "10") size: Int
    ) = providerService.searchProviders(searchType,providerName,mainFacilityId,page,size)

    @PostMapping(value = ["/"], produces = ["application/json"])
    fun addProvider(@Valid @RequestBody dto: ProviderDTO) = providerService.addProvider(dto)

    @GetMapping(value = ["/tier"], produces = ["application/json"])
    fun findByTier(@RequestParam("tier") tier: ProviderTier, page: Int = 1, size: Int = 10) =
        providerService.findByTier(tier, page, size)

    @GetMapping(value = ["/name"], produces = ["application/json"])
    fun findByTier(@RequestParam("name") name: String, page: Int = 1, size: Int = 10) =
        providerService.findByName(name, page, size)

    @GetMapping(value = ["/all"], produces = ["application/json"])
    fun findAllProviders(page: Int = 1, size: Int = 10) =
        providerService.findAllProviders(page, size)

    @PostMapping(value = ["/exclusion"], produces = ["application/json"])
    fun addExclusion(@Valid @RequestBody dto: ExclusionDTO) = restrictionService.addExclusion(dto)

    @PostMapping(value = ["/restriction"], produces = ["application/json"])
    fun addRestriction(@Valid @RequestBody dto: RestrictionDTO) = restrictionService.addRestriction(dto)

    @GetMapping(value = ["/category/{categoryId}/restrictions"], produces = ["application/json"])
    fun findRestrictionsByCategory(@PathVariable(value = "categoryId") categoryId: Long) =
        restrictionService.findRestrictionByCategory(categoryId)

    @PostMapping(value = ["/restriction/deactivate"], produces = ["application/json"])
    fun deactivate(@RequestBody restrictionDTO: RemoveRestrictionDTO) = restrictionService
        .deactivate(restrictionDTO)

    @PostMapping(value = ["/exclusions/process"], produces = ["application/json"])
    fun processExclusion(@Valid @RequestBody category: Category) = restrictionService.process(category)

    @GetMapping(value = ["/{providerId}/exclusions"], produces = ["application/json"])
    fun findExclusionsByProvider(@PathVariable(value = "providerId") providerId: Long) =
        restrictionService.findExclusionsByProvider(providerId)

    @GetMapping(value = ["/category/{categoryId}/exclusions"], produces = ["application/json"])
    fun findExclusionsByCategory(@PathVariable(value = "categoryId") categoryId: Long, page: Int = 1, size: Int = 10) =
        restrictionService.findExclusionsByCategory(categoryId, page, size)


    @GetMapping(value = ["/category/{categoryId}/provider/{providerId}/exclusions"], produces =
    ["application/json"])
    fun findExclusionsByCategoryAndProvider(@PathVariable(value = "categoryId") categoryId: Long,
                                            @PathVariable(value = "providerId") providerId: Long) =
        restrictionService.findExclusionsByCategoryAndProvider(categoryId, providerId)

    @PostMapping(value = ["/copay"], produces = ["application/json"])
    fun addCopay(@Valid @RequestBody dto: CopayDTO) = copaySetupService.addCopay(dto)

    @GetMapping(value = ["/copay"], produces = ["application/json"])
    fun findByProviderAndCategory(
        @RequestParam("providerId") providerId: Long,
        @RequestParam("categoryId") categoryId: Long
    ) = copaySetupService.findByCategoryAndProvider(categoryId, providerId)

    @PutMapping(value = ["/copay"], produces = ["application/json"])
    fun deactivateCopay(@RequestBody copayment: Copayment) = copaySetupService.deactivate(copayment)

    @PostMapping(value = ["/copays/process"], produces = ["application/json"])
    fun processCoPays(@Valid @RequestBody category: Category) = copaySetupService.process(category)

    @GetMapping(value = ["/{providerId}/copays"], produces = ["application/json"])
    fun findCoPaysByProvider(@PathVariable(value = "providerId") providerId: Long) =
        copaySetupService.findByProvider(providerId)

    @GetMapping(value = ["/category/{categoryId}/copays"], produces = ["application/json"])
    fun findCoPaysByCategory(@PathVariable(value = "categoryId") categoryId: Long) =
        copaySetupService.findByCategory(categoryId)

    @GetMapping(value = ["/{providerId}/whitelist"], produces = ["application/json"])
    fun findWhiteListByProvider(@PathVariable(value = "providerId") providerId: Long, page: Int = 1, size: Int = 10) =
        providerService.findWhiteListByProvider(providerId, page, size)

    @PostMapping(value = ["/whitelist"], produces = ["application/json"])
    fun addNewMapping(@Valid @RequestBody dto: WhiteListDTO) =
        providerService.addWhitelist(dto)

    @PostMapping(value = ["/{providerId}/whitelist/multiple"], produces = ["application/json"])
    fun addNewMappingMultiple(
        @PathVariable(value = "providerId") providerId: Long,
        @Valid @RequestBody dto: MultipleWhiteListDTO
    ) = providerService.addMultipleMappings(providerId, dto)

    @PostMapping(value = ["/massUpload/payerProviderMapping"], produces = ["application/json"], consumes = ["multipart/form-data"])
    @Operation(summary = "Save payer provider mapping from excel config file ")
    fun saveProviderMappingFromFile(@RequestParam("file") file: MultipartFile) = providerService.saveProviderMappingFromFile(file)


    @PostMapping(value = ["/massUpload/updatePayerProviderMapping"], produces = ["application/json"], consumes = ["multipart/form-data"])
    @Operation(summary = "Update payer provider mapping from excel config file ")
    fun updateProviderMappingFromFile(@RequestParam("file") file: MultipartFile) = providerService.updateProviderMappingFromFile(file)


    @GetMapping(
        value = ["/nearby"],
        produces = ["application/json"]
    )
    @Operation(summary = "Get Nearby Providers by beneficiaryId and radius (in Kilometres)")
    fun getNearbyProviders(
        @RequestParam("beneficiaryId") beneficiaryId: Long,
        @RequestParam("latitude") latitude: Double,
        @RequestParam("longitude") longitude: Double,
        @RequestParam(value = "radius", defaultValue = "100") radius:Double,
        @RequestParam(value = "page", defaultValue = "1") page: Int,
        @RequestParam(value = "size", defaultValue = "20") size: Int
    ) = providerService.getNearbyProviders(beneficiaryId, latitude, longitude,radius, page, size)

    @PutMapping(value = ["/update/{providerId}"], produces = ["application/json"])
    fun updateProvider(@PathVariable("providerId") providerId:Long,
                       @Valid @RequestBody dto: ProviderUpdateDTO) =
        providerService.updateProvider(providerId, dto)

    @PostMapping(value = ["/multiUpdate"], produces = ["application/json"])
    fun multiProviderUpdate(
        @Valid @RequestBody dto: MultiProviderUpdateDTO
    ) = providerService.multiProviderUpdate(dto)

    @GetMapping(value = ["/{providerId}/accounts"], produces = ["application/json"])
    @Operation(summary = "Get Provider accounts")
    fun getProviderAccounts(@PathVariable("providerId") providerId:Long) = providerService.getProviderAccounts(providerId)

    @GetMapping(value = ["/account/{accountId}"], produces = ["application/json"])
    @Operation(summary = "Get Provider account")
    fun getProviderAccount(@PathVariable("accountId") accountId:Long) = providerService.getProviderAccount(accountId)

    @PostMapping(value = ["/account"], produces = ["application/json"])
    @Operation(summary = "Create Provider account")
    suspend fun createProviderAccount(@Valid @RequestBody dto: CreateProviderAccountDto) = providerService.createProviderAccount(dto)

    @PostMapping(value = ["/account/mapped"], produces = ["application/json"])
    @Operation(summary = "Create Provider account and Map to Payer")
    suspend fun createPayerMappedProviderAccount(@Valid @RequestBody dto: CreateMappedProviderAccountDto) = providerService.createPayerMappedProviderAccount(dto)

    @PostMapping(value = ["/account/{accountId}"], produces = ["application/json"])
    @Operation(summary = "Update Provider account")
    suspend fun updateProviderAccount(@PathVariable("accountId") accountId:Long, @Valid @RequestBody dto: UpdateProviderAccountDto) = providerService.updateProviderAccount(accountId,dto)

    @PostMapping(value = ["/account/payer/mapping"], produces = ["application/json"])
    @Operation(summary = "Map Provider Account to Payer")
    suspend fun mapProviderAccountToPayer(@Valid @RequestBody dto: MapProviderAccountToPayerDto) = providerService.mapProviderAccountToPayer(dto)

    @PostMapping(value = ["/payer/users"], produces = ["application/json"])
    @Operation(summary = "Map Users to Payer e.g Credit Controller")
    suspend fun mapProviderUsersToPayer(@Valid @RequestBody dto: MapProviderUserToPayerDto) = providerService.mapProviderUsersToPayer(dto)

    @GetMapping(value = ["/payer/{payerId}"], produces = ["application/json"])
    @Operation(summary = "Get providers mapped to a payer with pagination and search by name")
    fun getProvidersByPayer(
        @PathVariable("payerId") payerId: Long,
        @RequestParam(value = "name", required = false) providerName: String?,
        @RequestParam(value = "page", defaultValue = "1") page: Int,
        @RequestParam(value = "size", defaultValue = "20") size: Int
    ) = providerService.getProvidersByPayer(payerId, providerName, page, size)
}
