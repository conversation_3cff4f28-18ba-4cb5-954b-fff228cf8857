package net.lctafrica.membership.api.web

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import javax.validation.Valid
import net.lctafrica.membership.api.domain.ExportFileType
import net.lctafrica.membership.api.dtos.FilterBeneficiariesDto
import net.lctafrica.membership.api.service.IMembershipReportService
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController


@Tag(name = "Membership Report APIs", description = "Membership Report APIs")
@RestController
@RequestMapping("/api/v1/membership/report")
class ReportController(
    val membershipReportService: IMembershipReportService
) : AbstractController() {

    @PostMapping(value = ["/beneficiaries/{page}/{size}"], produces = ["application/json"])
    @Operation(
        summary = "Filter Beneficiaries"
    )
    suspend fun filterBeneficiaries(
        @Valid @RequestBody dto: FilterBeneficiariesDto,
        @PathVariable(value = "page") page: Int,
        @PathVariable(value = "size") size: Int,
    ) = membershipReportService.filterBeneficiaries(dto, page, size)

    @PostMapping(value = ["/beneficiaries/{page}/{size}/export/{fileType}"], produces = ["application/json"])
    @Operation(
        summary = "Filter Beneficiaries and Export to the provided file format"
    )
    suspend fun exportFilteredBeneficiaries(
        @Valid @RequestBody dto: FilterBeneficiariesDto,
        @PathVariable(value = "fileType") fileType: ExportFileType,
        @PathVariable(value = "page") page: Int,
        @PathVariable(value = "size") size: Int
    ): Any = when (fileType) {
        ExportFileType.XLS -> {
            membershipReportService.exportFilteredBeneficiariesToExcel(dto, fileType, page, size)
        }

        ExportFileType.XLSX -> {
            membershipReportService.exportFilteredBeneficiariesToExcel(dto, fileType, page, size)
        }

        ExportFileType.CSV -> {
            membershipReportService.exportFilteredBeneficiariesToCSV(dto, fileType, page, size)
        }

        ExportFileType.PDF -> {
            membershipReportService.exportFilteredBeneficiariesToPDF(dto, fileType, page, size)
        }
    }

}