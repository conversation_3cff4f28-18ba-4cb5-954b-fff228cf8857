package net.lctafrica.membership.api.web

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import javax.validation.Valid
import net.lctafrica.membership.api.domain.DeviceStatus
import net.lctafrica.membership.api.domain.SimStatus
import net.lctafrica.membership.api.dtos.AddDeviceModel
import net.lctafrica.membership.api.dtos.AddSIM
import net.lctafrica.membership.api.dtos.AllocateDeviceAndSIMRequestDTO
import net.lctafrica.membership.api.dtos.AllocateDeviceRequestDTO
import net.lctafrica.membership.api.dtos.AllocateProviderDeviceSimAndAccessory
import net.lctafrica.membership.api.dtos.AllocationStatusUpdate
import net.lctafrica.membership.api.dtos.DeviceRegistrationRequestDTO
import net.lctafrica.membership.api.dtos.DeviceStatusUpdate
import net.lctafrica.membership.api.dtos.ProviderDeviceAllocation
import net.lctafrica.membership.api.dtos.SIMStatusUpdate
import net.lctafrica.membership.api.dtos.SupportingDocumentRequest
import net.lctafrica.membership.api.service.IDeviceManagementService
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile

@RestController
@RequestMapping("/api/v1/device")
@Tag(name = "Device Catalog Management APIs", description = "Device Registration, Allocation, etc")
class DeviceController(val service: IDeviceManagementService) : AbstractController() {

    @GetMapping(value = ["/search"], produces = ["application/json"])
    @Operation(description = "Search Device")
    suspend fun searchDevice(
        @RequestParam(name = "deviceId") deviceId: String?,
        @RequestParam(name = "imei") imei: String?,
        @RequestParam(name = "deviceStatus") deviceStatus: DeviceStatus?,
        @RequestParam(name = "page") page: Int,
        @RequestParam(name = "size") size: Int
    ) = service.searchDevice(deviceId, imei, deviceStatus, page, size)

    @GetMapping(value = ["/sim/search"], produces = ["application/json"])
    @Operation(description = "Search SIM")
    suspend fun searchSIM(
        @RequestParam(name = "simNumber") simNumber: String?,
        @RequestParam(name = "simStatus") simStatus: SimStatus?,
        @RequestParam(name = "page") page: Int,
        @RequestParam(name = "size") size: Int
    ) = service.searchSIM(simNumber, simStatus, page, size)
    @GetMapping(value = ["/models/all"], produces = ["application/json"])
    @Operation(description = "Get device models")
    suspend fun getDeviceModels(
        @RequestParam(name = "page") page: Int,
        @RequestParam(name = "size") size: Int
    ) = service.getDeviceModels(page,size)

    @GetMapping(value = ["/sim/all"], produces = ["application/json"])
    @Operation(description = "Get all SIM")
    suspend fun getAllSim(
        @RequestParam(name = "page") page: Int,
        @RequestParam(name = "size") size: Int
    ) = service.getAllSim(page,size)

    @PostMapping(value = ["/model"], produces = ["application/json"])
    @Operation(description = "Add device model")
    suspend fun addDeviceModel(@Valid @RequestBody dto: AddDeviceModel) = service.addDeviceModel(dto)

    @PostMapping(value = ["/sim"], produces = ["application/json"])
    @Operation(description = "Add SIM")
    suspend fun addSIM(@Valid @RequestBody dto: AddSIM) = service.addSIM(dto)

    @GetMapping(value = ["/catalog"], produces = ["application/json"])
    @Operation(description = "Get all devices in catalog")
    suspend fun getAllDevicesInCatalog(
        @RequestParam(name = "page") page: Int,
        @RequestParam(name = "size") size: Int
    ) = service.getAllDevicesInCatalog(page,size)

    @GetMapping(value = ["/unallocated"], produces = ["application/json"])
    @Operation(description = "Get unallocated devices in catalog")
    suspend fun getUnallocatedDevices(
        @RequestParam(name = "page") page: Int,
        @RequestParam(name = "size") size: Int
    ) = service.getUnallocatedDevices(page,size)

    @PostMapping(value = ["/register"], produces = ["application/json"])
    @Operation(description = "Add devices to catalog")
    suspend fun registerDevice(@Valid @RequestBody dto: DeviceRegistrationRequestDTO) = service.registerDevice(dto)

    @PostMapping(value = ["/allocate/batch"], produces = ["application/json"])
    @Operation(description = "Allocate devices to provider")
    suspend fun allocateBatch(@Valid @RequestBody dto: AllocateDeviceRequestDTO) = service.allocateBatch(dto)

    @PostMapping(value = ["/allocate"], produces = ["application/json"])
    @Operation(description = "Allocate device to provider")
    suspend fun allocate(@Valid @RequestBody dto: AllocateProviderDeviceSimAndAccessory) = service.allocateDevice(dto)

    @PostMapping(value = ["/sim/allocation"], produces = ["application/json"])
    @Operation(description = "Allocate device and SIM")
    suspend fun allocateDeviceAndSim(@Valid @RequestBody dto: AllocateDeviceAndSIMRequestDTO) = service.allocateDeviceAndSim(dto)

    @PostMapping(value = ["/allocation/uploadDocuments"], produces = ["application/json"])
    @Operation(description = "Upload Supporting Documents")
    suspend fun uploadDocuments(@Valid @RequestBody dto: SupportingDocumentRequest) = service.uploadDocuments(dto)

    @GetMapping(value = ["/provider/allocations"], produces = ["application/json"])
    @Operation(description = "Get devices allocated to a provider")
    suspend fun getDevicesAllocatedToAProvider(
        @RequestParam(name = "providerId") providerId: Long,
        @RequestParam(name = "page") page: Int,
        @RequestParam(name = "size") size: Int,
    ) = service.getDevicesAllocatedToAProvider(providerId, page, size)

    @GetMapping(value = ["/allocations"], produces = ["application/json"])
    @Operation(description = "Get devices allocations")
    suspend fun getDevicesAllocations(
        @RequestParam(name = "page") page: Int,
        @RequestParam(name = "size") size: Int,
    ) = service.getDevicesAllocations(page, size)

    @GetMapping(value = ["/allocation"], produces = ["application/json"])
    @Operation(description = "Get device allocation")
    suspend fun getAllocation(
        @RequestParam(name = "providerId") providerId: Long,
        @RequestParam(name = "deviceId") deviceId: String
    ) = service.getAllocation(providerId, deviceId)

    @PostMapping(value = ["/batchUpload"],
        produces = ["application/json"],
        consumes = ["multipart/form-data"]
    )
    @Operation(description = "Batch upload devices, format (DEVICE_ID - required, IMEI (Comma separated) - required, MODEL - required, REGISTERED_BY - required, DESCRIPTION - optional, ACCESSORIES (Comma separated) - optional)")
    suspend fun batchUploadDevice(
        @RequestParam(value = "file") file: MultipartFile,
        @RequestParam("emailCallBack") emailCallBack: String?,
    ) = service.batchUploadDevice(file,emailCallBack)

    @PostMapping(value = ["/sim/batchUpload"],
        produces = ["application/json"],
        consumes = ["multipart/form-data"]
    )
    @Operation(description = "Batch upload SIM, format (SIM_NUMBER - required, REGISTERED_BY - required)")
    suspend fun batchUploadSim(
        @RequestParam(value = "file") file: MultipartFile,
        @RequestParam("emailCallBack") emailCallBack: String?,
    ) = service.batchUploadSim(file,emailCallBack)

    @PatchMapping(value = ["/{deviceCatalogId}"], produces = ["application/json"])
    @Operation(description = "Change Device Status")
    suspend fun updateDeviceStatus(
        @PathVariable("deviceCatalogId") deviceCatalogId:Long,
        @Valid @RequestBody dto: DeviceStatusUpdate
    ) = service.updateDeviceStatus(deviceCatalogId,dto)

    @PatchMapping(value = ["/sim/{simCatalogId}"], produces = ["application/json"])
    @Operation(description = "Change SIM Status")
    suspend fun updateSIMStatus(
        @PathVariable("simCatalogId") simCatalogId:Long,
        @Valid @RequestBody dto: SIMStatusUpdate
    ) = service.updateSIMStatus(simCatalogId,dto)

    @PatchMapping(value = ["/allocation/{allocationId}"], produces = ["application/json"])
    @Operation(description = "Change Allocation Status")
    suspend fun updateAllocationStatus(
        @PathVariable("allocationId") allocationId:Long,
        @Valid @RequestBody dto: AllocationStatusUpdate
    ) = service.updateAllocationStatus(allocationId,dto)

    @PostMapping(value = ["/allocateToProvider"], produces = ["application/json"])
    @Operation(description = "Allocate device(s) to a Provider")
    suspend fun allocateDeviceToProvider(
        @Valid @RequestBody dto: ProviderDeviceAllocation
    ) = service.allocateDeviceToProvider(dto)

}