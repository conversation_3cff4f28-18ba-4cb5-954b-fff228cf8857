package net.lctafrica.membership.api.config.batch

import net.lctafrica.membership.api.util.UploadBeneficiaryDto
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.batch.item.ItemProcessor

class UploadBeneficiaryProcessor : ItemProcessor<UploadBeneficiaryDto, UploadBeneficiaryDto> {
    @Throws(Exception::class)
    override fun process(item: UploadBeneficiaryDto): UploadBeneficiaryDto {
        LOGGER.info("Processing information: {}", item)
        return item
    }

    companion object {
        private val LOGGER: Logger = LoggerFactory.getLogger(UploadBeneficiaryProcessor::class.java)
    }
}