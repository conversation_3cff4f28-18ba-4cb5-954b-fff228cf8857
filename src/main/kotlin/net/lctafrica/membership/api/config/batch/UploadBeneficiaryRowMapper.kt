package net.lctafrica.membership.api.config.batch

import net.lctafrica.membership.api.domain.BeneficiaryType
import net.lctafrica.membership.api.domain.Gender
import net.lctafrica.membership.api.domain.StagingStatus
import net.lctafrica.membership.api.util.UploadBeneficiaryDto
import net.lctafrica.membership.api.util.dbDateFormat
import org.springframework.batch.extensions.excel.RowMapper
import org.springframework.batch.extensions.excel.support.rowset.RowSet

class UploadBeneficiaryRowMapper(
    private val policyId: Long,
    private val batchNo: String,
    private val status: String,
    private val autoProcessBenefits: String
) : RowMapper<UploadBeneficiaryDto?> {

    @Throws(Exception::class)
    override fun mapRow(rowSet: RowSet): UploadBeneficiaryDto {
        val dob = dbDateFormat(readRow(rowSet,2),"dd/MM/yyyy")
        val error = if(dob == null){
            "Invalid Date of Birth ${readRow(rowSet,2)}"
        }else{
            null
        }
        return UploadBeneficiaryDto(
            batchNo = batchNo,
            familyNumber = readRow(rowSet,0)?:"",
            name = "${readRow(rowSet,1)}",
            dob = dob,
            gender = Gender.valueOf(readRow(rowSet,3)?:""),
            memberType = BeneficiaryType.valueOf(readRow(rowSet,4)?:""),
            memberNumber = readRow(rowSet,5)?:"",
            email = readRow(rowSet,6),
            phone = readRow(rowSet,7),
            nhifNumber = readRow(rowSet,8),
            idNumber = readRow(rowSet,9),
            joinDate = dbDateFormat(readRow(rowSet,10),"dd/MM/yyyy"),
            renewal = readRow(rowSet,11),
            policyId = policyId,
            category = rowSet.metaData.sheetName.trim(),
            status = StagingStatus.valueOf(status.uppercase()),
            autoProcessBenefits = autoProcessBenefits.toBoolean(),
            error = error
        )
    }

    private fun readRow(rowSet: RowSet, index:Int):String?{
        return try {
            rowSet.currentRow[index].trim()
        }catch (e:Exception){
            null
        }
    }
}