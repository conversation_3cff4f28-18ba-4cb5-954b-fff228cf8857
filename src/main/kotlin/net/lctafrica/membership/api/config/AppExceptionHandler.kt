package net.lctafrica.membership.api.config

import com.fasterxml.jackson.annotation.JsonFormat
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.transaction.UnexpectedRollbackException
import org.springframework.web.bind.MethodArgumentNotValidException
import org.springframework.web.bind.annotation.ControllerAdvice
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.ResponseBody
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.context.request.WebRequest
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler
import java.time.LocalDateTime
import javax.persistence.EntityNotFoundException

class BadRequestException(message: String) : RuntimeException() {
    override val message: String

    init {
        this.message = message
    }
}

class NotFoundRequestException(message: String) : RuntimeException() {
    override val message: String

    init {
        this.message = message
    }
}

class AppException(httpStatus: HttpStatus, message: String) : RuntimeException() {
    val httpStatus: HttpStatus
    override val message: String

    init {
        this.httpStatus = httpStatus
        this.message = message
    }
}

data class ErrorResponse(
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd hh:mm:ss")
    var timestamp: LocalDateTime? = null,
    var code: Int = 0,
    var error: String? = null
)

class InvalidExcelFormatException(message: String) : RuntimeException(message)

class InvalidMemberDataException(message: String) : RuntimeException(message)

class BatchProcessingException(message: String) : RuntimeException(message)

class InvalidPhoneNumberException(message: String) : RuntimeException(message)

class InvalidEmailException(message: String) : RuntimeException(message)

class InvalidDateException(message: String) : RuntimeException(message)

class KeycloakException(message: String, cause: Throwable? = null) : RuntimeException(message, cause)

@ControllerAdvice
class AppExceptionHandler : ResponseEntityExceptionHandler() {
    @ExceptionHandler(BadRequestException::class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    fun badRequestExceptionHandler(ex: BadRequestException, request: WebRequest): ErrorResponse {
        return ErrorResponse(LocalDateTime.now(), HttpStatus.BAD_REQUEST.value(), ex.message)
    }

    @ExceptionHandler(NotFoundRequestException::class)
    @ResponseBody
    @ResponseStatus(HttpStatus.NOT_FOUND)
    fun notFoundRequestExceptionHandler(ex: NotFoundRequestException, request: WebRequest): ErrorResponse {
        return ErrorResponse(LocalDateTime.now(), HttpStatus.NOT_FOUND.value(), ex.message)
    }

    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ResponseBody
    @ExceptionHandler(IllegalArgumentException::class)
    fun handleIllegalArgumentExceptions(ex: IllegalArgumentException): ErrorResponse {
        return ErrorResponse(LocalDateTime.now(), HttpStatus.BAD_REQUEST.value(), ex.message)
    }

    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ResponseBody
    @ExceptionHandler(UnexpectedRollbackException::class)
    fun handleUnexpectedRollbackExceptions(ex: UnexpectedRollbackException): ErrorResponse {
        return ErrorResponse(
            LocalDateTime.now(),
            HttpStatus.BAD_REQUEST.value(),
            "Failed to process the request. Transaction rolled back"
        )
    }

    @ExceptionHandler(AppException::class)
    fun appExceptionHandler(ex: AppException, request: WebRequest): ResponseEntity<Any> {
        val error = ErrorResponse(LocalDateTime.now(), ex.httpStatus.value(), ex.message)
        return ResponseEntity.status(ex.httpStatus).body(error)
    }

    @ExceptionHandler(EntityNotFoundException::class)
    @ResponseBody
    @ResponseStatus(HttpStatus.NOT_FOUND)
    fun entityNotFoundExceptionHandler(ex: EntityNotFoundException, request: WebRequest): ErrorResponse {
        return ErrorResponse(
            timestamp = LocalDateTime.now(),
            code = HttpStatus.NOT_FOUND.value(),
            error = "${ex.message}"
        )
    }

    override fun handleMethodArgumentNotValid(
        ex: MethodArgumentNotValidException,
        headers: HttpHeaders,
        status: HttpStatus,
        request: WebRequest
    ): ResponseEntity<Any> {
        val errors = ex.bindingResult.fieldErrors.associate { err ->
            err.field to (err.defaultMessage ?: "Invalid value")
        }

        val errorResponse = ErrorResponse(
            timestamp = LocalDateTime.now(),
            code = HttpStatus.BAD_REQUEST.value(),
            error = errors.toString()
        )
        return ResponseEntity(errorResponse, HttpStatus.BAD_REQUEST)
    }

    @ExceptionHandler(DataIntegrityViolationException::class)
    fun handleDataIntegrityViolationException(ex: DataIntegrityViolationException): ResponseEntity<ErrorResponse> {
        val errorMessage = ex.mostSpecificCause.message ?: "Error executing database query!"
        val errorResponse = ErrorResponse(
            timestamp = LocalDateTime.now(),
            code = HttpStatus.BAD_REQUEST.value(),
            error = errorMessage
        )
        return ResponseEntity(errorResponse, HttpStatus.BAD_REQUEST)
    }

    @ExceptionHandler(InvalidExcelFormatException::class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    fun handleInvalidExcelFormatException(ex: InvalidExcelFormatException): ErrorResponse {
        return ErrorResponse(LocalDateTime.now(), HttpStatus.BAD_REQUEST.value(), ex.message)
    }

    @ExceptionHandler(InvalidMemberDataException::class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    fun handleInvalidMemberDataException(ex: InvalidMemberDataException): ErrorResponse {
        return ErrorResponse(LocalDateTime.now(), HttpStatus.BAD_REQUEST.value(), ex.message)
    }

    @ExceptionHandler(BatchProcessingException::class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    fun handleBatchProcessingException(ex: BatchProcessingException): ErrorResponse {
        return ErrorResponse(LocalDateTime.now(), HttpStatus.BAD_REQUEST.value(), ex.message)
    }

    @ExceptionHandler(InvalidPhoneNumberException::class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    fun handleInvalidPhoneNumberException(ex: InvalidPhoneNumberException): ErrorResponse {
        return ErrorResponse(LocalDateTime.now(), HttpStatus.BAD_REQUEST.value(), ex.message)
    }

    @ExceptionHandler(InvalidEmailException::class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    fun handleInvalidEmailException(ex: InvalidEmailException): ErrorResponse {
        return ErrorResponse(LocalDateTime.now(), HttpStatus.BAD_REQUEST.value(), ex.message)
    }

    @ExceptionHandler(InvalidDateException::class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    fun handleInvalidDateException(ex: InvalidDateException): ErrorResponse {
        return ErrorResponse(LocalDateTime.now(), HttpStatus.BAD_REQUEST.value(), ex.message)
    }
}
