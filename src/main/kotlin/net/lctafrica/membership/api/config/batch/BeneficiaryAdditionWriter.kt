package net.lctafrica.membership.api.config.batch

import net.lctafrica.membership.api.domain.BeneficiaryStaging
import net.lctafrica.membership.api.service.IBeneficiaryService
import org.springframework.batch.item.ItemWriter

class BeneficiaryAdditionWriter(private val beneficiaryService: IBeneficiaryService) : ItemWriter<BeneficiaryStaging?> {

    override fun write(chunk: MutableList<out BeneficiaryStaging?>) {

        chunk.forEach { beneficiaryObj: BeneficiaryStaging? ->
            beneficiaryObj?.let { beneficiary ->
                beneficiaryService.processStagedBeneficiary(beneficiary)
            }
        }
    }

}