package net.lctafrica.membership.api.config.batch

import net.lctafrica.membership.api.domain.BeneficiaryStaging
import net.lctafrica.membership.api.domain.BeneficiaryStagingRepository
import net.lctafrica.membership.api.util.UploadBeneficiaryDto
import net.lctafrica.membership.api.util.emailValidation
import net.lctafrica.membership.api.util.phoneValidation
import net.lctafrica.membership.api.util.toRenew
import net.lctafrica.membership.api.util.validateDateNotAfter
import net.lctafrica.membership.api.util.validateFullName
import org.springframework.batch.item.ItemWriter

class UploadBeneficiaryWriter(private val beneficiaryStagingRepository: BeneficiaryStagingRepository) :
    ItemWriter<UploadBeneficiaryDto?> {
    override fun write(chunk: MutableList<out UploadBeneficiaryDto?>) {
        chunk.forEach { beneficiaryDto: UploadBeneficiaryDto? ->
            var error = beneficiaryDto?.error
            if(!validateFullName(beneficiaryDto?.name)) {
                error +="Invalid Full Name. Name should have a First and Last name"
            }

            beneficiaryDto?.joinDate?.let {
                if(!validateDateNotAfter(it)) {
                    error += "Join Date $it cannot be in the future\n"
                }
            }

            beneficiaryDto?.dob?.let {
                if(!validateDateNotAfter(it)) {
                    error += "Date of Birth $it cannot be in the future \n"
                }
            }

            if(beneficiaryDto?.dob == null){
                error += "Date of Birth is invalid \n"
            }

            beneficiaryDto?.email?.let {
                if(it.trim().isNotBlank() && !emailValidation(it)) {
                    error +="Invalid Email Address $it \n"
                }
            }
            beneficiaryDto?.phone?.let {
                if(it.trim().isNotBlank() && !phoneValidation(it)) {
                    error +="Invalid Phone Number $it \n"
                }
            }

            val addedOpt = beneficiaryStagingRepository.findByMemberNoAndCategoryNameAndPolicyId(memberNo = beneficiaryDto?.memberNumber ?: "",
                categoryName = beneficiaryDto?.category ?: "", policyId = beneficiaryDto?.policyId ?: 0)
            if(addedOpt.isPresent){
                val staged = addedOpt.get()
                beneficiaryDto?.let { beneficiary ->
                    staged.processed = false
                    staged.familyNumber = beneficiary.familyNumber
                    staged.name = beneficiary.name
                    staged.email = beneficiary.email
                    staged.phone = beneficiary.phone
                    staged.nhifNumber = beneficiary.nhifNumber
                    staged.idNumber = beneficiary.idNumber
                    staged.memberType = beneficiary.memberType
                    staged.dob = beneficiary.dob
                    staged.joinDate = beneficiary.joinDate
                    staged.gender = beneficiary.gender
                    staged.status = beneficiary.status
                    staged.toRenew = beneficiary.renewal.toRenew()
                    staged.error = error?.ifBlank { null }
                    staged.processBenefits = beneficiary.autoProcessBenefits
                    beneficiaryStagingRepository.save(staged)
                }
            }else {
                beneficiaryDto?.let { beneficiary ->
                    val temp = BeneficiaryStaging(
                        batchNo = beneficiary.batchNo ?: "",
                        familyNumber = beneficiary.familyNumber,
                        name = beneficiary.name,
                        dob = beneficiary.dob,
                        gender = beneficiary.gender,
                        memberType = beneficiary.memberType,
                        memberNumber = beneficiary.memberNumber,
                        email = beneficiary.email,
                        phone = beneficiary.phone,
                        nhifNumber = beneficiary.nhifNumber,
                        idNumber = beneficiary.idNumber,
                        joinDate = beneficiary.joinDate,
                        policyId = beneficiary.policyId ?: 0,
                        category = beneficiary.category ?: "",
                        status = beneficiary.status,
                        toRenew = beneficiary.renewal.toRenew(),
                        error = error?.ifBlank { null },
                        processBenefits = beneficiary.autoProcessBenefits
                    )
                    beneficiaryStagingRepository.save(temp)
                }
            }
        }
    }

}