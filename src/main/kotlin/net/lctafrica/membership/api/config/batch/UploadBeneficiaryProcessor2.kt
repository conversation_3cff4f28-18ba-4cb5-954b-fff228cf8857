package net.lctafrica.membership.api.config.batch

import net.lctafrica.membership.api.domain.Payer
import org.springframework.batch.item.ItemProcessor

class UploadBeneficiaryProcessor2 : ItemProcessor<Payer, Payer> {
    @Throws(Exception::class)
    override fun process(item: Payer): Payer {
        //LOGGER.info("UploadBeneficiaryProcessor2: Payer Processing information 2: {}", item)
        println("UploadBeneficiaryProcessor2: Payer Processing information 2: ${item.name}")
        return item
    }

//    companion object {
//        private val LOGGER: Logger = LoggerFactory.getLogger(UploadBeneficiaryProcessor2::class.java)
//    }
}