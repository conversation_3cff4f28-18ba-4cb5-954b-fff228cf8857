package net.lctafrica.membership.api.config

import io.netty.handler.ssl.SslContextBuilder
import io.netty.handler.ssl.util.InsecureTrustManagerFactory
import java.io.File
import net.lctafrica.membership.api.config.batch.BeneficiaryAdditionWriter
import net.lctafrica.membership.api.config.batch.UploadBeneficiaryProcessor
import net.lctafrica.membership.api.config.batch.UploadBeneficiaryProcessor2
import net.lctafrica.membership.api.config.batch.UploadBeneficiaryRowMapper
import net.lctafrica.membership.api.config.batch.UploadBeneficiaryWriter
import net.lctafrica.membership.api.domain.BeneficiaryStaging
import net.lctafrica.membership.api.domain.BeneficiaryStagingRepository
import net.lctafrica.membership.api.domain.BeneficiaryType
import net.lctafrica.membership.api.domain.Payer
import net.lctafrica.membership.api.domain.StagingStatus
import net.lctafrica.membership.api.service.IBeneficiaryService
import net.lctafrica.membership.api.util.UploadBeneficiaryDto
import net.lctafrica.membership.api.util.getClientHttpConnector
import org.springframework.batch.core.Job
import org.springframework.batch.core.Step
import org.springframework.batch.core.configuration.annotation.EnableBatchProcessing
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory
import org.springframework.batch.core.configuration.annotation.StepScope
import org.springframework.batch.core.launch.JobLauncher
import org.springframework.batch.core.launch.support.RunIdIncrementer
import org.springframework.batch.core.launch.support.SimpleJobLauncher
import org.springframework.batch.core.repository.JobRepository
import org.springframework.batch.extensions.excel.poi.PoiItemReader
import org.springframework.batch.item.ItemProcessor
import org.springframework.batch.item.ItemReader
import org.springframework.batch.item.ItemWriter
import org.springframework.batch.item.data.RepositoryItemReader
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.core.io.FileSystemResource
import org.springframework.core.task.SimpleAsyncTaskExecutor
import org.springframework.data.domain.Sort
import org.springframework.http.client.reactive.ReactorClientHttpConnector
import org.springframework.http.codec.ClientCodecConfigurer
import org.springframework.web.reactive.function.client.ExchangeStrategies
import org.springframework.web.reactive.function.client.WebClient
import reactor.netty.http.client.HttpClient


@Configuration
class WebClientConfig(
    @Value("\${lct-africa.benefit.command.base-url}")
    val claimBaseUrl: String,

    @Value("\${lct-africa.profile.base-url}")
    val profileUrl: String,

    @Value("\${lct-africa.notification.baseUrl}")
    val notificationBaseUrl: String,

    @Value("\${lct-africa.document.blobFile}")
    val blobFileUrl: String


) {
    @Bean
    @Qualifier("profileWebClientConfig")
    fun getWebClient(webClientBuilder: WebClient.Builder): WebClient? {
        return webClientBuilder
            .baseUrl(profileUrl)
            .build()
    }

    @Bean
    @Qualifier("notificationWebClientConfig")
    fun notificationWebClient(webClientBuilder: WebClient.Builder): WebClient? {
        return webClientBuilder
            .baseUrl(notificationBaseUrl)
            .clientConnector(getClientHttpConnector())
            .build()
    }

    @Bean
    @Qualifier("claimWebClientConfig")
    fun claimWebClient(webClientBuilder: WebClient.Builder): WebClient? {
        return webClientBuilder
            .baseUrl(claimBaseUrl)
            .build()
    }

    @Bean
    @Qualifier("postHereWebClientConfig")
    fun postHereWebClient(webClientBuilder: WebClient.Builder): WebClient? {
        return webClientBuilder
            .baseUrl("https://posthere.io")
            .clientConnector(getClientHttpConnector())
            .build()
    }

    @Bean
    @Qualifier("documentWebClientConfig")
    fun documentWebClient(webClientBuilder: WebClient.Builder): WebClient? {
        val size = 100 * 1024 * 1024 //100MB
        val strategies = ExchangeStrategies.builder()
            .codecs { codecs: ClientCodecConfigurer ->
                codecs.defaultCodecs().maxInMemorySize(size)
            }
            .build()
        return webClientBuilder
            .exchangeStrategies(strategies)
            .baseUrl(blobFileUrl)
            .build()
    }
}

@Configuration
@EnableBatchProcessing
class BatchConfig {
    ////////////////////Batch Upload Beneficiaries////////////////////////
    @Autowired
    private var jobBuilders: JobBuilderFactory? = null

    @Autowired
    private val stepBuilders: StepBuilderFactory? = null

    @Autowired
    lateinit var jobRepository: JobRepository

    @Autowired
    lateinit var beneficiaryStagingRepository: BeneficiaryStagingRepository

    @Autowired
    lateinit var beneficiaryService: IBeneficiaryService

    @Bean
    @StepScope
    fun beneficiaryUploadReader(
        @Value("#{jobParameters['batchNo']}") batchNo: String,
        @Value("#{jobParameters['fileFullPath']}") filePath: String,
        @Value("#{jobParameters['policyId']}") policyId: Long,
        @Value("#{jobParameters['status']}") status: String,
        @Value("#{jobParameters['autoProcessBenefits']}") autoProcessBenefits: String,
    ): PoiItemReader<UploadBeneficiaryDto> {
        val reader: PoiItemReader<UploadBeneficiaryDto> = PoiItemReader<UploadBeneficiaryDto>()
        reader.setLinesToSkip(1)
        reader.setResource(FileSystemResource(File(filePath)))
        reader.setRowMapper(memberExcelRowMapper(policyId, batchNo, status, autoProcessBenefits))
        return reader
    }
    @Bean
    @StepScope
    fun unprocessedPrincipalItemReader(): ItemReader<BeneficiaryStaging>? {
        val reader: RepositoryItemReader<BeneficiaryStaging> = RepositoryItemReader<BeneficiaryStaging>()
        reader.setRepository(beneficiaryStagingRepository)
        reader.setMethodName("findByUnprocessedAndByBeneficiaryType")
        val parameters = ArrayList<Any?>()
        parameters.add(BeneficiaryType.PRINCIPAL)
        parameters.add(StagingStatus.ACTIVATE)
        reader.setArguments(parameters)
        reader.setPageSize(500)
        val sorts: HashMap<String, Sort.Direction> = HashMap<String, Sort.Direction>()
        sorts["id"] = Sort.Direction.ASC
        reader.setSort(sorts)
        return reader
    }


    @Bean
    @StepScope
    fun unprocessedDependantItemReader(): ItemReader<BeneficiaryStaging>? {
        val reader: RepositoryItemReader<BeneficiaryStaging> = RepositoryItemReader<BeneficiaryStaging>()
        reader.setRepository(beneficiaryStagingRepository)
        reader.setMethodName("findByUnprocessedAndNotBeneficiaryType")
        val parameters = ArrayList<Any?>()
        parameters.add(BeneficiaryType.PRINCIPAL)
        parameters.add(StagingStatus.ACTIVATE)
        reader.setArguments(parameters)
        reader.setPageSize(500)
        val sorts: HashMap<String, Sort.Direction> = HashMap<String, Sort.Direction>()
        sorts["id"] = Sort.Direction.ASC
        reader.setSort(sorts)
        return reader
    }

    @Bean
    fun beneficiaryUploadChunkStep(
        beneficiaryUploadReader: PoiItemReader<UploadBeneficiaryDto>,
        beneficiaryUploadWriter: ItemWriter<UploadBeneficiaryDto>
    ): Step {
        return stepBuilders!!.get("beneficiaryUploadChunkStep")
            .chunk<UploadBeneficiaryDto, UploadBeneficiaryDto>(50)
            .reader(beneficiaryUploadReader)
            .writer(beneficiaryUploadWriter)
            .build()
    }

    @Bean
    fun processPrincipalStep(
        unprocessedPrincipalItemReader: ItemReader<BeneficiaryStaging>,
        beneficiaryAdditionWriter: ItemWriter<BeneficiaryStaging>
    ): Step {
        return stepBuilders!!.get("processPrincipalStep")
            .chunk<BeneficiaryStaging, BeneficiaryStaging>(500)
            .reader(unprocessedPrincipalItemReader)
            .writer(beneficiaryAdditionWriter)
            .build()
    }

    @Bean
    fun processDependantStep(
        unprocessedDependantItemReader: ItemReader<BeneficiaryStaging>,
        beneficiaryAdditionWriter: ItemWriter<BeneficiaryStaging>
    ): Step {
        return stepBuilders!!.get("processDependantStep")
            .chunk<BeneficiaryStaging, BeneficiaryStaging>(500)
            .reader(unprocessedDependantItemReader)
            .writer(beneficiaryAdditionWriter)
            .build()
    }
    @Bean(name = ["beneficiaryUploadJob"])
    fun beneficiaryUploadJob(
        @Qualifier("beneficiaryUploadReader") beneficiaryUploadReader: PoiItemReader<UploadBeneficiaryDto>,
        @Qualifier("beneficiaryUploadWriter") beneficiaryUploadWriter: ItemWriter<UploadBeneficiaryDto>,
        @Qualifier("unprocessedPrincipalItemReader") unprocessedPrincipalItemReader: ItemReader<BeneficiaryStaging>,
        @Qualifier("beneficiaryAdditionWriter") beneficiaryAdditionWriter: ItemWriter<BeneficiaryStaging>,
        @Qualifier("unprocessedDependantItemReader") unprocessedDependantItemReader: ItemReader<BeneficiaryStaging>,
    ): Job? {
        return jobBuilders!!.get("beneficiaryUploadJob")
            .start(beneficiaryUploadChunkStep(beneficiaryUploadReader, beneficiaryUploadWriter))
            .next(processPrincipalStep(unprocessedPrincipalItemReader, beneficiaryAdditionWriter))
            .next(processDependantStep(unprocessedDependantItemReader, beneficiaryAdditionWriter))
            .incrementer(RunIdIncrementer())
            .build()
    }

    fun memberExcelRowMapper(policyId: Long, batchNo: String, status: String, autoProcessBenefits: String): UploadBeneficiaryRowMapper {
        return UploadBeneficiaryRowMapper(policyId, batchNo, status, autoProcessBenefits)
    }
    @Bean
    fun beneficiaryUploadProcessor(): ItemProcessor<UploadBeneficiaryDto, UploadBeneficiaryDto> {
        return UploadBeneficiaryProcessor()
    }

    @Bean
    fun beneficiaryUploadProcessor2(): ItemProcessor<Payer, Payer> {
        return UploadBeneficiaryProcessor2()
    }
    @Bean
    fun beneficiaryUploadWriter(): ItemWriter<UploadBeneficiaryDto?>? {
        return UploadBeneficiaryWriter(beneficiaryStagingRepository)
    }

    @Bean
    fun beneficiaryAdditionWriter(): ItemWriter<BeneficiaryStaging?>? {
        return BeneficiaryAdditionWriter(beneficiaryService = beneficiaryService)
    }
    @Bean(name = ["batchJobLauncher"])
    @Throws(Exception::class)
    fun batchJobLauncher(): JobLauncher? {
            val jobLauncher = SimpleJobLauncher()
            jobLauncher.setJobRepository(jobRepository)
            jobLauncher.setTaskExecutor(SimpleAsyncTaskExecutor())
            jobLauncher.afterPropertiesSet()
            return jobLauncher
    }



}
