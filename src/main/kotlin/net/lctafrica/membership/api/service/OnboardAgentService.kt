package net.lctafrica.membership.api.service

import net.lctafrica.membership.api.domain.OnboardAgent
import net.lctafrica.membership.api.domain.OnboardAgentRepository
import net.lctafrica.membership.api.dtos.OnboardAgentDTO
import net.lctafrica.membership.api.dtos.OnboardAgentResponseDTO
import net.lctafrica.membership.api.util.toResponseDTO
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.stereotype.Service
import javax.persistence.EntityNotFoundException

@Service
class OnboardAgentService(private val onboardAgentRepository: OnboardAgentRepository) {
    fun createAgent(request: OnboardAgentDTO): Map<Boolean, OnboardAgent?> {
        return try {
            val agent = OnboardAgent(
                name = request.name,
                agentNumber = request.agentNumber,
                mobileNumber = request.mobileNumber,
                factory = request.factory,
                dateOfBirth = request.dateOfBirth,
                registrationDate = request.registrationDate,
                zone = request.zone,
                idNumber = request.idNumber,
                emailAddress = request.emailAddress,
            )
            val savedAgent = onboardAgentRepository.save(agent)
            mapOf(true to savedAgent)
        } catch (e: Exception) {
            mapOf(false to null)
        }
    }

    fun getAgentsByFactory(
        factory: String,
        page: Int? = 0,
        size: Int? = 10,
        sort: String? = "createdAt",
        direction: Sort.Direction? = Sort.Direction.DESC,
    ): Page<OnboardAgentResponseDTO> {
        val pageable = PageRequest.of(page ?: 0, size ?: 10, Sort.by(direction ?: Sort.Direction.DESC, sort))
        val agents = onboardAgentRepository.findByFactory(factory, pageable)
        return agents.map { it.toResponseDTO() }
    }

    fun findAgentById(id: Long): OnboardAgentResponseDTO = onboardAgentRepository.findById(id).orElseThrow {
        throw EntityNotFoundException("Agent not found!")
    }.toResponseDTO()

    fun countAllAgents(): Long = onboardAgentRepository.count()

    fun findOnboardAgentById(id: Long): OnboardAgent = onboardAgentRepository.findById(id).orElseThrow {
        throw EntityNotFoundException("Agent not found!")
    }

    fun findAllAgents(
        page: Int? = 0,
        size: Int? = 10,
        sort: String? = "createdAt",
        direction: Sort.Direction? = Sort.Direction.DESC
    ): Page<OnboardAgentResponseDTO> {
        val pageable = PageRequest.of(page ?: 0, size ?: 10, Sort.by(direction ?: Sort.Direction.DESC, sort))
        return onboardAgentRepository.findAll(pageable).map { it.toResponseDTO() }
    }

    fun getAgentBySearchTerm(searchTerm: String): List<OnboardAgentResponseDTO?> {
        return onboardAgentRepository.findByFullNameOrAgentNumber(searchTerm).map {
            it?.toResponseDTO()
        }
    }
}