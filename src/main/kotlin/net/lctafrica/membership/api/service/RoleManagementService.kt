package net.lctafrica.membership.api.service

import net.lctafrica.membership.api.config.KeycloakException
import net.lctafrica.membership.api.domain.*
import net.lctafrica.membership.api.dtos.*
import net.lctafrica.membership.api.util.KeycloakResponseUtil
import net.lctafrica.membership.api.util.Result
import net.lctafrica.membership.api.util.ResultFactory
import org.keycloak.OAuth2Constants
import org.keycloak.admin.client.Keycloak
import org.keycloak.admin.client.KeycloakBuilder
import org.keycloak.admin.client.resource.RealmResource
import org.keycloak.representations.idm.CredentialRepresentation
import org.keycloak.representations.idm.GroupRepresentation
import org.keycloak.representations.idm.RoleRepresentation
import org.keycloak.representations.idm.UserRepresentation
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime
import javax.annotation.PostConstruct
import javax.persistence.EntityNotFoundException
import javax.ws.rs.core.Response

@Service
class RoleManagementService(
    private val customRoleRepository: CustomRoleRepository,
    private val userGroupRepository: UserGroupRepository,
    private val userGroupMemberRepository: UserGroupMemberRepository,
    private val userCustomRoleRepository: UserCustomRoleRepository,
    private val denyPolicyRepository: DenyPolicyRepository,
    private val userManagementService: UserManagementService,
    private val userRolesAuditLogService: UserRolesAuditLogService,
    @Value("\${lct-africa.keycloak.serverUrl}") private val serverUrl: String,
    @Value("\${lct-africa.keycloak.keycloakRealm}") private val keycloakRealm: String,
    @Value("\${lct-africa.keycloak.realm}") private val realm: String,
    @Value("\${lct-africa.keycloak.keycloakClient}") private val keycloakClient: String,
    @Value("\${lct-africa.keycloak.clientSecret}") private val clientSecret: String
) {
    private val logger = LoggerFactory.getLogger(RoleManagementService::class.java)
    private lateinit var keycloak: Keycloak

    @PostConstruct
    fun initKeycloak() {
        keycloak = KeycloakBuilder.builder()
            .serverUrl(serverUrl)
            .realm(realm)
            .grantType(OAuth2Constants.CLIENT_CREDENTIALS)
            .clientId(keycloakClient)
            .clientSecret(clientSecret)
            .build()
    }


    @Transactional
    fun createCustomRole(dto: CreateCustomRoleDto): Result<CustomRoleDto> {
        if (customRoleRepository.existsByNameAndPayerId(dto.name, dto.payerId)) {
            return ResultFactory.getFailResult("A role with this name already exists!")
        }

        val allRealmRoles = userManagementService.getAllSystemRealmRoles()
        val allRealmRoleNames = allRealmRoles.map { it.name }.toSet()
        val invalidPermissions = dto.permissions.filter { !allRealmRoleNames.contains(it) }

        if (invalidPermissions.isNotEmpty()) {
            return ResultFactory.getFailResult(
                "The following permissions do not exist: ${
                    invalidPermissions.joinToString(
                        ", "
                    )
                }"
            )
        }

        val customRole = CustomRole(
            name = dto.name,
            description = dto.description,
            isPredefined = false,
            payerId = dto.payerId,
            permissions = dto.permissions.toMutableList(),
            createdBy = dto.createdBy
        )

        val savedRole = customRoleRepository.save(customRole)
        return ResultFactory.getSuccessResult(mapToCustomRoleDto(savedRole))
    }


    @Transactional
    fun updateCustomRole(id: Long, dto: UpdateCustomRoleDto, payerId: Long): Result<CustomRoleDto> {
        val customRole = customRoleRepository.findById(id)
            .orElseThrow { EntityNotFoundException("Custom role not found with id: $id") }

        if (customRole.isPredefined) {
            return ResultFactory.getFailResult("Predefined roles cannot be modified")
        }

        if (customRole.payerId != payerId) {
            return ResultFactory.getFailResult("Access denied: This role does not belong to your payer account")
        }

        if (dto.name != null && dto.name != customRole.name) {
            if (customRoleRepository.existsByNameAndPayerId(dto.name, payerId)) {
                return ResultFactory.getFailResult("A role with this name already exists for this payer")
            }
        }

        val updatedRole = customRole.copy(
            name = dto.name ?: customRole.name,
            description = dto.description ?: customRole.description,
            updatedAt = LocalDateTime.now()
        )

        if (dto.permissionsToAdd != null && dto.permissionsToAdd.isNotEmpty()) {
            val allRealmRoles = userManagementService.getAllSystemRealmRoles()
            val allRealmRoleNames = allRealmRoles.map { it.name }.toSet()
            val invalidPermissions = dto.permissionsToAdd.filter { !allRealmRoleNames.contains(it) }
            val validPermissions = dto.permissionsToAdd.filter { allRealmRoleNames.contains(it) }

            if (invalidPermissions.isNotEmpty()) {
                logger.warn(
                    "Invalid permissions to add for custom role {}: {}. These permissions will be ignored.",
                    customRole.name, invalidPermissions.joinToString(", ")
                )
            }

            if (validPermissions.isNotEmpty()) {
                updatedRole.permissions.addAll(validPermissions.filter { !updatedRole.permissions.contains(it) })
            }
        }

        if (dto.permissionsToRemove != null && dto.permissionsToRemove.isNotEmpty()) {
            val currentPermissions = updatedRole.permissions.toSet()
            val permissionsToRemoveFromRole = dto.permissionsToRemove.filter { currentPermissions.contains(it) }
            val permissionsNotInRole = dto.permissionsToRemove.filter { !currentPermissions.contains(it) }

            if (permissionsNotInRole.isNotEmpty()) {
                logger.warn(
                    "Attempted to remove permissions not in custom role {}: {}. These permissions will be ignored.",
                    customRole.name, permissionsNotInRole.joinToString(", ")
                )
            }

            if (permissionsToRemoveFromRole.isNotEmpty()) {
                val remainingPermissions = currentPermissions.minus(permissionsToRemoveFromRole)

                if (remainingPermissions.isEmpty()) {
                    return ResultFactory.getFailResult("Cannot remove all permissions. A custom role must retain at least one permission.")
                }

                updatedRole.permissions.removeAll(permissionsToRemoveFromRole)
            }
        }

        if (dto.usersToAdd != null && dto.usersToAdd.isNotEmpty()) {
            for (userId in dto.usersToAdd) {
                try {
                    val user = userManagementService.findUserById(userId)
                        ?: return ResultFactory.getFailResult("User not found with id: $userId")

                    if (!userCustomRoleRepository.existsByUserIdAndRoleId(userId, id)) {
                        val userCustomRole = UserCustomRole(
                            userId = userId,
                            customRole = customRole,
                            assignedBy = dto.updatedBy
                        )
                        userCustomRoleRepository.save(userCustomRole)

                        val kRealm = keycloak.realm(realm)
                        val groupName = "custom_role_payer_${customRole.payerId}_${customRole.id}_${
                            customRole.name.replace(
                                " ",
                                "_"
                            )
                        }"

                        val existingGroups = kRealm.groups().groups(groupName, 0, 1)
                        val groupId = if (existingGroups.isEmpty()) {
                            val groupRep = GroupRepresentation()
                            groupRep.name = groupName

                            val response = kRealm.groups().add(groupRep)
                            if (!HttpStatus.valueOf(response.status).is2xxSuccessful) {
                                return ResultFactory.getFailResult("Failed to create group for custom role in Keycloak")
                            }

                            val createdGroupId = response.location.path.substringAfterLast('/')
                            assignRolesToKeycloakGroup(kRealm, createdGroupId, customRole.permissions)
                            createdGroupId
                        } else {
                            existingGroups[0].id
                        }

                        kRealm.users().get(userId).joinGroup(groupId)

                        userRolesAuditLogService.logUserRoleChange(
                            userId,
                            dto.updatedBy,
                            customRole.permissions,
                            emptyList()
                        )
                    }
                } catch (e: Exception) {
                    // Log error but continue with other users
                    logger.warn("Error adding user {} to custom role: {}", userId, e.message)
                }
            }
        }

        if (dto.usersToRemove != null && dto.usersToRemove.isNotEmpty()) {
            for (userId in dto.usersToRemove) {
                try {
                    val userCustomRole = userCustomRoleRepository.findByUserId(userId)
                        .find { it.customRole.id == id }

                    if (userCustomRole != null) {
                        userCustomRoleRepository.delete(userCustomRole)

                        val kRealm = keycloak.realm(realm)
                        val groupName = "custom_role_payer_${customRole.payerId}_${customRole.id}_${
                            customRole.name.replace(
                                " ",
                                "_"
                            )
                        }"
                        val groups = kRealm.groups().groups(groupName, 0, 1)

                        if (groups.isNotEmpty()) {
                            try {
                                kRealm.users().get(userId).leaveGroup(groups[0].id)
                            } catch (e: Exception) {
                                // user might not be in the group anymore, just log and continue
                                logger.warn("Error removing user {} from Keycloak group: {}", userId, e.message)
                            }
                        }

                        userRolesAuditLogService.logUserRoleChange(
                            userId,
                            dto.updatedBy,
                            emptyList(),
                            customRole.permissions
                        )
                    }
                } catch (e: Exception) {
                    // log error but continue with other users
                    logger.warn("Error removing user {} from custom role: {}", userId, e.message)
                }
            }
        }

        if ((dto.permissionsToAdd != null && dto.permissionsToAdd.isNotEmpty()) ||
            (dto.permissionsToRemove != null && dto.permissionsToRemove.isNotEmpty())
        ) {

            val kRealm = keycloak.realm(realm)
            val groupName =
                "custom_role_payer_${customRole.payerId}_${customRole.id}_${customRole.name.replace(" ", "_")}"
            val existingGroups = kRealm.groups().groups(groupName, 0, 1)

            if (existingGroups.isNotEmpty()) {
                val groupId = existingGroups[0].id

                val existingRoles = kRealm.groups().group(groupId).roles().realmLevel().listAll()
                val existingRoleNames = existingRoles.map { it.name }.toSet()

                val newPermissions = updatedRole.permissions.toSet()

                val rolesToAdd = newPermissions.filter { !existingRoleNames.contains(it) }
                val rolesToRemove = existingRoleNames.filter { !newPermissions.contains(it) }

                if (rolesToRemove.isNotEmpty()) {
                    val rolesToRemoveReps = rolesToRemove.mapNotNull { roleName ->
                        try {
                            kRealm.roles().get(roleName).toRepresentation()
                        } catch (e: Exception) {
                            logger.warn("Role {} not found in Keycloak: {}", roleName, e.message)
                            null
                        }
                    }
                    if (rolesToRemoveReps.isNotEmpty()) {
                        kRealm.groups().group(groupId).roles().realmLevel().remove(rolesToRemoveReps)
                    }
                }

                if (rolesToAdd.isNotEmpty()) {
                    assignRolesToKeycloakGroup(kRealm, groupId, rolesToAdd)
                }

                val userCustomRoles = userCustomRoleRepository.findByRoleId(id)
                userCustomRoles.forEach { userCustomRole ->
                    try {
                        userRolesAuditLogService.logUserRoleChange(
                            userCustomRole.userId,
                            dto.updatedBy,
                            rolesToAdd,
                            rolesToRemove
                        )
                    } catch (e: Exception) {
                        logger.warn("Failed to log role changes for user {}: {}", userCustomRole.userId, e.message)
                    }
                }
            }
        }

        val savedRole = customRoleRepository.save(updatedRole)
        return ResultFactory.getSuccessResult(mapToCustomRoleDto(savedRole))
    }


    @Transactional(readOnly = true)
    fun getCustomRoleById(id: Long, payerId: Long): Result<CustomRoleDto> {
        val customRole = customRoleRepository.findById(id)
            .orElseThrow { EntityNotFoundException("Custom role not found with id: $id") }

        if (customRole.payerId != payerId) {
            return ResultFactory.getFailResult("Access denied: This role does not belong to your payer account")
        }

        return ResultFactory.getSuccessResult(mapToCustomRoleDto(customRole))
    }


    @Transactional(readOnly = true)
    fun getAllCustomRoles(
        page: Int,
        size: Int,
        payerId: Long,
        name: String? = null
    ): Page<CustomRoleDto> {
        val pageable = PageRequest.of(if (page > 0) page - 1 else 0, size)
        val roles = if (name.isNullOrBlank()) {
            customRoleRepository.findByPayerId(payerId, pageable)
        } else {
            customRoleRepository.findByNameContainingIgnoreCaseAndPayerId(name, payerId, pageable)
        }
        return roles.map { mapToCustomRoleDto(it) }
    }


    @Transactional
    fun createUserGroup(dto: CreateUserGroupDto): Result<UserGroupDto> {
        logger.info("Creating user group '{}' for payer {}", dto.name, dto.payerId)

        try {
            if (userGroupRepository.existsByNameAndPayerId(dto.name, dto.payerId)) {
                val message = "A group with the name '${dto.name}' already exists!"
                logger.warn("Group creation failed: {}", message)
                return ResultFactory.getFailResult(message)
            }

            if ((dto.customRoleIds == null || dto.customRoleIds.isEmpty()) &&
                (dto.predefinedRoles == null || dto.predefinedRoles.isEmpty())
            ) {
                val message = "At least one role (custom or predefined) must be assigned to the group"
                logger.warn("Group creation failed: {}", message)
                return ResultFactory.getFailResult(message)
            }

            val customRoles = mutableListOf<CustomRole>()
            if (dto.customRoleIds != null && dto.customRoleIds.isNotEmpty()) {
                for (roleId in dto.customRoleIds) {
                    try {
                        val role = customRoleRepository.findById(roleId)
                            .orElseThrow { EntityNotFoundException("Custom role not found with ID: $roleId") }

                        if (role.payerId != dto.payerId) {
                            val message = "Custom role '${role.name}' belongs to a different payer"
                            logger.warn("Invalid role assignment attempt: {}", message)
                            return ResultFactory.getFailResult(message)
                        }

                        customRoles.add(role)
                    } catch (e: EntityNotFoundException) {
                        val message = "Custom role with ID $roleId was not found"
                        logger.warn("Group creation failed: {}", message)
                        return ResultFactory.getFailResult(message)
                    }
                }
            }

            val kRealm = keycloak.realm(realm)
            val groupId = createKeycloakGroupWithRoles(
                kRealm,
                "payer_${dto.payerId}_${dto.name.replace(" ", "_")}",
                customRoles,
                dto.predefinedRoles?.toSet()
            )

            val userGroup = UserGroup(
                keycloakGroupId = groupId,
                name = dto.name,
                description = dto.description,
                payerId = dto.payerId,
                customRoles = customRoles.toMutableList(),
                predefinedRoles = dto.predefinedRoles?.toMutableList() ?: mutableListOf(),
                createdBy = dto.createdBy
            )

            val savedGroup = userGroupRepository.save(userGroup)
            logger.info(
                "Successfully created user group '{}' with ID {} for payer {}",
                dto.name, savedGroup.id, dto.payerId
            )

            if (dto.userIds != null && dto.userIds.isNotEmpty()) {
                logger.debug("Adding {} initial members to group '{}'", dto.userIds.size, dto.name)
                val addUsersDto = AddUsersToGroupDto(
                    userIds = dto.userIds,
                    addedBy = dto.createdBy
                )
                addUsersToGroup(savedGroup.id, addUsersDto)
            }

            val groupDto = UserGroupDto(
                id = savedGroup.id,
                keycloakGroupId = savedGroup.keycloakGroupId,
                name = savedGroup.name,
                description = savedGroup.description,
                customRoles = savedGroup.customRoles.map { mapToCustomRoleDto(it) },
                predefinedRoles = savedGroup.predefinedRoles,
                createdBy = savedGroup.createdBy,
                createdAt = savedGroup.createdAt,
                updatedAt = savedGroup.updatedAt,
                payerId = savedGroup.payerId
            )

            return ResultFactory.getSuccessResult(
                data = groupDto,
                msg = "Group '${dto.name}' created successfully"
            )

        } catch (e: Exception) {
            val message = "Unable to create group. Please try again later"
            logger.error(
                "Unexpected error creating user group '{}' for payer {}: {}",
                dto.name, dto.payerId, e.message, e
            )
            return ResultFactory.getFailResult(message)
        }
    }

    @Transactional
    fun addUsersToGroup(groupId: Long, dto: AddUsersToGroupDto): Result<String> {
        val userGroup = userGroupRepository.findById(groupId)
            .orElseThrow { EntityNotFoundException("User group not found with id: $groupId") }

        if (dto.userIds.isEmpty()) {
            return ResultFactory.getFailResult("At least one user must be provided")
        }

        val validUsers = mutableListOf<String>()
        val alreadyMembers = mutableListOf<String>()
        val invalidUsers = mutableListOf<String>()

        for (userId in dto.userIds) {
            if (userGroupMemberRepository.existsByGroupIdAndUserId(groupId, userId)) {
                alreadyMembers.add(userId)
                continue
            }

            try {
                val user = userManagementService.findUserById(userId)
                if (user != null) {
                    validUsers.add(userId)
                } else {
                    invalidUsers.add(userId)
                    logger.warn("User with ID {} not found in Keycloak: No user with the provided id exists!", userId)
                }
            } catch (e: Exception) {
                invalidUsers.add(userId)
                logger.warn("User with ID {} not found in Keycloak: {}", userId, e.message)
            }
        }

        val kRealm = keycloak.realm(realm)

        val kcGroupId = ensureKeycloakGroupExists(kRealm, userGroup)
        val actualKeycloakGroupId = kcGroupId

        var addedCount = 0
        val failedToAdd = mutableListOf<String>()

        for (userId in validUsers) {
            try {
                logger.debug(
                    "Attempting to add user {} to Keycloak group {} (database group {})",
                    userId, actualKeycloakGroupId, groupId
                )

                kRealm.users().get(userId).joinGroup(actualKeycloakGroupId)

                logger.debug("Successfully added user {} to Keycloak group {}", userId, actualKeycloakGroupId)

                val userGroupMember = UserGroupMember(
                    userGroup = userGroup,
                    userId = userId,
                    addedBy = dto.addedBy
                )
                userGroupMemberRepository.save(userGroupMember)
                addedCount++

                logger.debug("Successfully saved group membership for user {} in database", userId)
            } catch (e: Exception) {
                failedToAdd.add(userId)
                logger.warn(
                    "Failed to add user {} to group {} (Keycloak group {}): {}",
                    userId, groupId, actualKeycloakGroupId, e.message
                )
            }
        }

        val allRoles = mutableSetOf<String>()
        userGroup.customRoles.forEach { role -> allRoles.addAll(role.permissions) }
        allRoles.addAll(userGroup.predefinedRoles)

        val successfullyAddedUsers = validUsers.filter { !failedToAdd.contains(it) }
        successfullyAddedUsers.forEach { userId ->
            try {
                userRolesAuditLogService.logUserRoleChange(
                    userId,
                    dto.addedBy,
                    allRoles.toList(),
                    emptyList()
                )
            } catch (e: Exception) {
                logger.warn("Failed to log role changes for user {}: {}", userId, e.message)
            }
        }

        val messages = mutableListOf<String>()
        if (addedCount > 0) {
            messages.add("Successfully added $addedCount ${if (addedCount == 1) "user" else "users"} to group")
        }
        if (alreadyMembers.isNotEmpty()) {
            messages.add(
                "${alreadyMembers.size} ${if (alreadyMembers.size == 1) "user" else "users"} " +
                        "${if (alreadyMembers.size == 1) "was" else "were"} already " +
                        "${if (alreadyMembers.size == 1) "a member" else "members"} of the group"
            )
        }
        if (invalidUsers.isNotEmpty()) {
            messages.add("${invalidUsers.size} users not found in Keycloak")
        }
        if (failedToAdd.isNotEmpty()) {
            messages.add("${failedToAdd.size} users failed to be added")
        }

        val resultMessage = if (messages.isNotEmpty()) {
            messages.joinToString("; ")
        } else {
            "No users were processed"
        }

        return ResultFactory.getSuccessResult(resultMessage)
    }

    @Transactional
    fun removeUsersFromGroup(groupId: Long, dto: RemoveUsersFromGroupDto): Result<String> {
        val userGroup = userGroupRepository.findById(groupId)
            .orElseThrow { EntityNotFoundException("User group not found with id: $groupId") }

        if (dto.userIds.isEmpty()) {
            return ResultFactory.getFailResult("At least one user must be provided")
        }

        val kRealm = keycloak.realm(realm)

        try {
            kRealm.groups().group(userGroup.keycloakGroupId).toRepresentation()
        } catch (e: Exception) {
            logger.error(
                "Keycloak group with ID {} does not exist for group {}: {}",
                userGroup.keycloakGroupId,
                groupId,
                e.message
            )
            return ResultFactory.getFailResult("Group does not exist in Keycloak. Group ID: ${userGroup.keycloakGroupId}")
        }

        val allRoles = mutableSetOf<String>()

        userGroup.customRoles.forEach { role ->
            allRoles.addAll(role.permissions)
        }
        allRoles.addAll(userGroup.predefinedRoles)

        val successfulRemovals = mutableListOf<String>()
        val failedRemovals = mutableMapOf<String, String>() // userId to error message

        dto.userIds.forEach { userId ->
            val userGroupMemberOptional = userGroupMemberRepository.findByGroupIdAndUserId(groupId, userId)

            if (!userGroupMemberOptional.isPresent) {
                failedRemovals[userId] = "User is not a member of this group"
                return@forEach
            }

            val userGroupMember = userGroupMemberOptional.get()

            try {
                try {
                    kRealm.users().get(userId).leaveGroup(userGroup.keycloakGroupId)
                } catch (e: Exception) {
                    logger.warn(
                        "Failed to remove user {} from Keycloak group {}: {}",
                        userId,
                        userGroup.keycloakGroupId,
                        e.message
                    )
                }

                userGroupMemberRepository.delete(userGroupMember)
                successfulRemovals.add(userId)

                try {
                    userRolesAuditLogService.logUserRoleChange(
                        userId,
                        dto.actionedBy,
                        emptyList(),
                        allRoles.toList()
                    )
                } catch (e: Exception) {
                    logger.warn("Failed to log role changes for user {}: {}", userId, e.message)
                }
            } catch (e: Exception) {
                failedRemovals[userId] = "Failed to remove from group: ${e.message}"
            }
        }

        return when {
            failedRemovals.isEmpty() ->
                ResultFactory.getSuccessResult(data = "All users removed from group successfully")

            successfulRemovals.isEmpty() ->
                ResultFactory.getFailResult(
                    "Failed to remove any users from group. Errors: ${failedRemovals.entries.joinToString("; ") { "${it.key}: ${it.value}" }}"
                )

            else ->
                ResultFactory.getSuccessResult(
                    data = "Partially successful: Removed ${successfulRemovals.size} users, failed to remove ${failedRemovals.size} users. " +
                            "Errors: ${failedRemovals.entries.joinToString("; ") { "${it.key}: ${it.value}" }}"
                )
        }
    }


    @Transactional
    fun createDenyPolicy(dto: CreateDenyPolicyDto): Result<DenyPolicyDto> {
        if (dto.userIds.isEmpty()) {
            return ResultFactory.getFailResult("At least one user must be provided")
        }

        if (dto.deniedPermissions.isEmpty()) {
            return ResultFactory.getFailResult("At least one denied permission must be provided")
        }

        val allRealmRoles = userManagementService.getAllSystemRealmRoles()
        val allRealmRoleNames = allRealmRoles.map { it.name }.toSet()

        val invalidPermissions = dto.deniedPermissions.filter { !allRealmRoleNames.contains(it) }
        if (invalidPermissions.isNotEmpty()) {
            return ResultFactory.getFailResult(
                "The following permissions do not exist: ${
                    invalidPermissions.joinToString(
                        ", "
                    )
                }"
            )
        }

        val validUserIds = mutableListOf<String>()
        val failedUsers = mutableMapOf<String, String>() // userId to error message

        for (userId in dto.userIds) {
            val user = userManagementService.findUserById(userId)
            if (user == null) {
                failedUsers[userId] = "User not found"
            } else {
                validUserIds.add(userId)
            }
        }

        if (validUserIds.isEmpty()) {
            return ResultFactory.getFailResult(
                "Failed to create deny policy. No valid users found. Errors: ${failedUsers.entries.joinToString("; ") { "${it.key}: ${it.value}" }}"
            )
        }

        val denyPolicy = DenyPolicy(
            name = dto.name,
            reason = dto.reason,
            users = validUserIds.toMutableList(),
            deniedPermissions = dto.deniedPermissions.toMutableList(),
            createdBy = dto.createdBy,
            expiresAt = dto.expiresAt,
            payerId = dto.payerId
        )

        val savedPolicy = denyPolicyRepository.save(denyPolicy)

        // Fetch user details for all users in the policy
        val users = validUserIds.associateWith { userId -> userManagementService.findUserById(userId) }

        val result = mapToDenyPolicyDto(savedPolicy, users)

        if (failedUsers.isNotEmpty()) {
            return ResultFactory.getSuccessResult(
                result,
                "Partially successful: Created policy with ${validUserIds.size} users, " +
                        "failed for ${failedUsers.size} users. " +
                        "Errors: ${failedUsers.entries.joinToString("; ") { "${it.key}: ${it.value}" }}"
            )
        }

        return ResultFactory.getSuccessResult(result)
    }


    @Transactional(readOnly = true)
    fun getUserEffectiveRoles(userId: String): Result<UserEffectiveRolesDto> {
        val user: UserRepresentation?
        try {
            user = userManagementService.findUserById(userId)
        } catch (e: Exception) {
            logger.warn("User with ID {} not found in Keycloak: {}", userId, e.message)
            return ResultFactory.getSuccessResult(
                UserEffectiveRolesDto(
                    userId = userId,
                    userName = null,
                    userEmail = null,
                    directRoles = emptyList(),
                    inheritedRoles = emptyMap(),
                    deniedPermissions = emptyList(),
                    effectivePermissions = emptyList()
                )
            )
        }

        val directRoleNames = userManagementService.findEffectiveUserRealmRoles(userId)
            .map { it.name }

        val userGroupMembers = userGroupMemberRepository.findByUserId(userId)
        val inheritedRolesMap = mutableMapOf<String, List<String>>()

        for (member in userGroupMembers) {
            val group = member.userGroup
            val groupRoles = mutableListOf<String>()

            group.customRoles.forEach { role ->
                groupRoles.addAll(role.permissions)
            }

            groupRoles.addAll(group.predefinedRoles)

            inheritedRolesMap[group.name] = groupRoles
        }

        // Get denied permissions only from new DenyPolicy entities
        val denyPolicies = denyPolicyRepository.findActiveByUserId(userId)
        val deniedPermissionNames = denyPolicies.flatMap { it.deniedPermissions }

        val allPermissionNames = mutableSetOf<String>()
        allPermissionNames.addAll(directRoleNames)
        inheritedRolesMap.values.forEach { roles ->
            allPermissionNames.addAll(roles)
        }

        val effectivePermissionNames = allPermissionNames.filter { !deniedPermissionNames.contains(it) }

        // Convert all role names to PermissionDto objects
        val directRoles = getPermissionDtosFromNames(directRoleNames)

        val inheritedRoles = inheritedRolesMap.mapValues { (_, roleNames) ->
            getPermissionDtosFromNames(roleNames)
        }

        val deniedPermissions = getPermissionDtosFromNames(deniedPermissionNames)
        val effectivePermissions = getPermissionDtosFromNames(effectivePermissionNames)

        return ResultFactory.getSuccessResult(
            UserEffectiveRolesDto(
                userId = userId,
                userName = user?.username,
                userEmail = user?.email,
                directRoles = directRoles,
                inheritedRoles = inheritedRoles,
                deniedPermissions = deniedPermissions,
                effectivePermissions = effectivePermissions
            )
        )
    }


    @Transactional(readOnly = true)
    fun getUserDirectRoles(userId: String): Result<UserDirectRolesDto> {
        val user: UserRepresentation?
        try {
            user = userManagementService.findUserById(userId)
        } catch (e: Exception) {
            logger.warn("User with ID {} not found in Keycloak: {}", userId, e.message)
            return ResultFactory.getSuccessResult(
                UserDirectRolesDto(
                    userId = userId,
                    userName = null,
                    userEmail = null,
                    directRoles = emptyList()
                )
            )
        }

        val directRoleNames = userManagementService.findDirectUserRealmRoles(userId)
            .map { it.name }

        val directRoles = getPermissionDtosFromNames(directRoleNames)

        return ResultFactory.getSuccessResult(
            UserDirectRolesDto(
                userId = userId,
                userName = user?.username,
                userEmail = user?.email,
                directRoles = directRoles
            )
        )
    }


    /**
     * Retrieves all available permissions (realm roles) with their descriptions
     */
    @Transactional(readOnly = true)
    fun getAllPermissions(): Result<List<PermissionDto>> {
        val allRealmRoles = userManagementService.getAllSystemRealmRoles()
        val permissionDtos = allRealmRoles.map { role ->
            PermissionDto(
                name = role.name,
                description = role.description
            )
        }

        return ResultFactory.getSuccessResult(permissionDtos)
    }


    @Transactional
    fun deleteCustomRole(id: Long, deletedBy: String, payerId: Long): Result<String> {
        try {
            val customRole = customRoleRepository.findById(id)
                .orElseThrow { EntityNotFoundException("Custom role not found with id: $id") }

            if (customRole.isPredefined) {
                return ResultFactory.getFailResult("Predefined roles cannot be deleted")
            }

            if (customRole.payerId != payerId) {
                return ResultFactory.getFailResult("Access denied: This role does not belong to your payer account")
            }

            val userGroups = userGroupRepository.findAll()
                .filter { group -> group.customRoles.any { it.id == id } }

            val userCustomRoles = userCustomRoleRepository.findByRoleId(id)

            val validUsers = userCustomRoles.mapNotNull { userCustomRole ->
                try {
                    val user = userManagementService.findUserById(userCustomRole.userId)
                    if (user != null) userCustomRole else null
                } catch (e: Exception) {
                    logger.warn("User with ID {} not found in Keycloak: {}", userCustomRole.userId, e.message)
                    null
                }
            }

            if (userGroups.isNotEmpty() && validUsers.isNotEmpty()) {
                return ResultFactory.getFailResult("You can't delete this role because it's still assigned to ${validUsers.size} ${if (validUsers.size == 1) "user" else "users"} and ${userGroups.size} ${if (userGroups.size == 1) "group" else "groups"}.")
            } else if (userGroups.isNotEmpty()) {
                return ResultFactory.getFailResult("You can't delete this role because it's still assigned to ${userGroups.size} ${if (userGroups.size == 1) "group" else "groups"}.")
            } else if (validUsers.isNotEmpty()) {
                return ResultFactory.getFailResult("You can't delete this role because it's still assigned to ${validUsers.size} ${if (validUsers.size == 1) "user" else "users"}.")
            }

            if (userCustomRoles.size > validUsers.size) {
                val invalidUsers = userCustomRoles.filter { userCustomRole ->
                    !validUsers.any { it.userId == userCustomRole.userId }
                }

                logger.info("Cleaning up {} invalid user assignments before deleting role", invalidUsers.size)

                for (userCustomRole in invalidUsers) {
                    try {
                        userCustomRoleRepository.delete(userCustomRole)
                        logger.info("Successfully removed invalid user assignment for user {}", userCustomRole.userId)
                    } catch (e: Exception) {
                        logger.warn(
                            "Error removing invalid user assignment for user {}: {}",
                            userCustomRole.userId,
                            e.message
                        )
                    }
                }
            }

            try {
                customRole.permissions.clear()
                val savedRole = customRoleRepository.save(customRole)

                customRoleRepository.delete(savedRole)
                return ResultFactory.getSuccessResult(data = "Custom role deleted successfully")
            } catch (e: Exception) {
                logger.error("Error during final deletion of custom role: {}", e.message)
                return ResultFactory.getFailResult("Error deleting custom role: ${e.message}")
            }
        } catch (e: Exception) {
            return ResultFactory.getFailResult("Error deleting custom role: ${e.message}")
        }
    }

    fun getUserGroups(page: Int, size: Int, payerId: Long, name: String? = null): Page<UserGroupDto> {
        val pageable = PageRequest.of(if (page > 0) page - 1 else 0, size)
        val groups = if (name.isNullOrBlank()) {
            userGroupRepository.findByPayerId(payerId, pageable)
        } else {
            userGroupRepository.findByNameContainingIgnoreCaseAndPayerId(name, payerId, pageable)
        }

        return groups.map { group ->
            val customRoleDtos = group.customRoles.map { role ->
                mapToCustomRoleDto(role)
            }

            UserGroupDto(
                id = group.id,
                keycloakGroupId = group.keycloakGroupId,
                name = group.name,
                description = group.description,
                customRoles = customRoleDtos,
                predefinedRoles = group.predefinedRoles,
                createdBy = group.createdBy,
                createdAt = group.createdAt,
                updatedAt = group.updatedAt,
                payerId = group.payerId
            )
        }
    }

    fun getUserGroupById(id: Long, payerId: Long): Result<UserGroupDto> {
        try {
            val userGroup = userGroupRepository.findById(id)
                .orElseThrow { EntityNotFoundException("User group not found with id: $id") }

            if (userGroup.payerId != payerId) {
                return ResultFactory.getFailResult("Access denied: This user group does not belong to your payer account")
            }

            val customRoleDtos = userGroup.customRoles.map { role ->
                mapToCustomRoleDto(role)
            }

            val dto = UserGroupDto(
                id = userGroup.id,
                keycloakGroupId = userGroup.keycloakGroupId,
                name = userGroup.name,
                description = userGroup.description,
                customRoles = customRoleDtos,
                predefinedRoles = userGroup.predefinedRoles,
                createdBy = userGroup.createdBy,
                createdAt = userGroup.createdAt,
                updatedAt = userGroup.updatedAt,
                payerId = userGroup.payerId
            )

            return ResultFactory.getSuccessResult(dto)
        } catch (e: Exception) {
            return ResultFactory.getFailResult("Error fetching user group: ${e.message}")
        }
    }

    @Transactional
    fun deleteUserGroup(id: Long, deletedBy: String, payerId: Long): Result<String> {
        try {
            val userGroup = userGroupRepository.findById(id)
                .orElseThrow { EntityNotFoundException("User group not found with id: $id") }

            if (userGroup.payerId != payerId) {
                return ResultFactory.getFailResult("Access denied: This user group does not belong to your payer account")
            }

            val members = userGroupMemberRepository.findByGroupId(id)
            if (members.isNotEmpty()) {
                val kRealm = keycloak.realm(realm)

                val allRoles = mutableSetOf<String>()
                userGroup.customRoles.forEach { role -> allRoles.addAll(role.permissions) }
                allRoles.addAll(userGroup.predefinedRoles)

                try {
                    kRealm.groups().group(userGroup.keycloakGroupId).toRepresentation()

                    for (member in members) {
                        try {
                            kRealm.users().get(member.userId).leaveGroup(userGroup.keycloakGroupId)
                            logger.debug(
                                "Successfully removed user {} from Keycloak group {}",
                                member.userId,
                                userGroup.keycloakGroupId
                            )
                        } catch (e: Exception) {
                            // user might not exist in Keycloak anymore, continue with deletion
                            logger.warn(
                                "Failed to remove user {} from Keycloak group {}: {}",
                                member.userId,
                                userGroup.keycloakGroupId,
                                e.message
                            )
                        }
                    }
                } catch (e: Exception) {
                    logger.warn(
                        "Keycloak group {} doesn't exist during deletion, continuing with database cleanup",
                        userGroup.keycloakGroupId
                    )
                }

                for (member in members) {
                    userGroupMemberRepository.delete(member)

                    try {
                        userRolesAuditLogService.logUserRoleChange(
                            member.userId,
                            deletedBy,
                            emptyList(),
                            allRoles.toList()
                        )
                    } catch (e: Exception) {
                        // log error but continue with deletion
                        logger.warn("Failed to log role changes for user {}: {}", member.userId, e.message)
                    }
                }
            }

            // clear collections to avoid constraint violations
            userGroup.customRoles.clear()
            userGroup.predefinedRoles.clear()
            val savedGroup = userGroupRepository.save(userGroup)

            // remove from Keycloak
            try {
                keycloak.realm(realm).groups().group(savedGroup.keycloakGroupId).remove()
            } catch (e: Exception) {
                // group might already be deleted in Keycloak, continue with deletion
                logger.warn("Failed to remove group from Keycloak: {}", e.message)
            }

            userGroupRepository.delete(savedGroup)
            return ResultFactory.getSuccessResult(data = "User group deleted successfully")
        } catch (e: Exception) {
            return ResultFactory.getFailResult("Error deleting user group: ${e.message}")
        }
    }

    fun getGroupMembers(groupId: Long, pageable: Pageable): Result<Page<UserGroupMemberDto>> {
        try {
            userGroupRepository.findById(groupId)
                .orElseThrow { EntityNotFoundException("User group not found with id: $groupId") }

            val members = userGroupMemberRepository.findByGroupId(groupId)
            val memberDtos = members.mapNotNull { member ->
                try {
                    val user = userManagementService.findUserById(member.userId)
                    UserGroupMemberDto(
                        id = member.id,
                        userId = member.userId,
                        userName = user?.username,
                        userEmail = user?.email,
                        name = listOfNotNull(user?.firstName, user?.lastName)
                            .takeIf { it.isNotEmpty() }?.joinToString(" "),
                        addedBy = member.addedBy,
                        addedAt = member.addedAt
                    )
                } catch (e: Exception) {
                    logger.warn("User with ID {} not found in Keycloak: {}", member.userId, e.message)
                    null
                }
            }

            val start = pageable.pageNumber * pageable.pageSize
            val end = minOf(start + pageable.pageSize, memberDtos.size)

            if (start >= memberDtos.size) {
                return ResultFactory.getSuccessResult(Page.empty(pageable))
            }

            val pagedMembers = memberDtos.subList(start, end)
            val page = PageImpl(pagedMembers, pageable, memberDtos.size.toLong())

            return ResultFactory.getSuccessResult(page)
        } catch (e: Exception) {
            return ResultFactory.getFailResult("Error fetching group members: ${e.message}")
        }
    }


    fun getUserGroups(userId: String): Result<List<UserGroupDto>> {
        try {
            try {
                userManagementService.findUserById(userId)
            } catch (e: Exception) {
                logger.warn("User with ID {} not found in Keycloak: {}", userId, e.message)
                return ResultFactory.getSuccessResult(emptyList())
            }

            val userGroupMembers = userGroupMemberRepository.findByUserId(userId)
            val groups = userGroupMembers.map { member -> member.userGroup }

            val groupDtos = groups.map { group ->
                val customRoleDtos = group.customRoles.map { role ->
                    mapToCustomRoleDto(role)
                }

                UserGroupDto(
                    id = group.id,
                    keycloakGroupId = group.keycloakGroupId,
                    name = group.name,
                    description = group.description,
                    customRoles = customRoleDtos,
                    predefinedRoles = group.predefinedRoles,
                    createdBy = group.createdBy,
                    createdAt = group.createdAt,
                    updatedAt = group.updatedAt,
                    payerId = group.payerId
                )
            }

            return ResultFactory.getSuccessResult(groupDtos)
        } catch (e: Exception) {
            return ResultFactory.getFailResult("Error fetching user groups: ${e.message}")
        }
    }


    @Transactional
    fun assignCustomRoleToUser(userId: String, roleId: Long, assignedBy: String): Result<String> {
        try {
            userManagementService.findUserById(userId)
                ?: return ResultFactory.getFailResult("User not found with id: $userId")

            val customRole = customRoleRepository.findById(roleId)
                .orElseThrow { EntityNotFoundException("Custom role not found with id: $roleId") }

            if (userCustomRoleRepository.existsByUserIdAndRoleId(userId, roleId)) {
                return ResultFactory.getSuccessResult("User already has this custom role!")
            }

            val userCustomRole = UserCustomRole(
                userId = userId,
                customRole = customRole,
                assignedBy = assignedBy
            )

            userCustomRoleRepository.save(userCustomRole)

            // instead of directly assigning permissions to the user in Keycloak,
            // we'll create a Keycloak group for this custom role if it doesn't exist already
            val kRealm = keycloak.realm(realm)
            val groupName =
                "custom_role_payer_${customRole.payerId}_${customRole.id}_${customRole.name.replace(" ", "_")}"

            val existingGroups = kRealm.groups().groups(groupName, 0, 1)
            val groupId = if (existingGroups.isEmpty()) {
                val groupRep = GroupRepresentation()
                groupRep.name = groupName

                val response = kRealm.groups().add(groupRep)
                if (!HttpStatus.valueOf(response.status).is2xxSuccessful) {
                    return ResultFactory.getFailResult("Failed to create group for custom role in Keycloak")
                }

                val createdGroupId = response.location.path.substringAfterLast('/')

                assignRolesToKeycloakGroup(kRealm, createdGroupId, customRole.permissions)

                createdGroupId
            } else {
                existingGroups[0].id
            }

            kRealm.users().get(userId).joinGroup(groupId)

            userRolesAuditLogService.logUserRoleChange(
                userId,
                assignedBy,
                customRole.permissions,
                emptyList()
            )

            return ResultFactory.getSuccessResult(data = "Custom role assigned to user successfully")
        } catch (e: Exception) {
            return ResultFactory.getFailResult("Error assigning custom role to user: ${e.message}")
        }
    }


    @Transactional
    fun removeCustomRoleFromUser(userId: String, roleId: Long, actionedBy: String): Result<String> {
        try {
            val userCustomRole = userCustomRoleRepository.findByUserId(userId)
                .find { it.customRole.id == roleId }
                ?: return ResultFactory.getFailResult("User does not have this custom role")

            val customRole = userCustomRole.customRole

            // remove the user-custom role relationship from our database
            userCustomRoleRepository.delete(userCustomRole)

            // find the Keycloak group for this custom role
            val kRealm = keycloak.realm(realm)
            val groupName =
                "custom_role_payer_${customRole.payerId}_${customRole.id}_${customRole.name.replace(" ", "_")}"
            val groups = kRealm.groups().groups(groupName, 0, 1)

            if (groups.isNotEmpty()) {
                val groupId = groups[0].id
                kRealm.users().get(userId).leaveGroup(groupId)
            } else {
                // if the group doesn't exist, we need to check if we need to remove any direct permissions
                // this is a fallback in case the group approach wasn't used previously

                // get all permissions the user should have from groups
                val userGroupMembers = userGroupMemberRepository.findByUserId(userId)
                val groupPermissions = mutableSetOf<String>()

                userGroupMembers.forEach { member ->
                    val group = member.userGroup
                    groupPermissions.addAll(group.predefinedRoles)

                    group.customRoles.forEach { role ->
                        groupPermissions.addAll(role.permissions)
                    }
                }

                // get all permissions from other directly assigned custom roles
                val otherCustomRoles = userCustomRoleRepository.findByUserId(userId)
                    .flatMap { it.customRole.permissions }
                    .toSet()

                // determine which permissions to remove (those that are not needed anymore)
                val permissionsToRemove = customRole.permissions.filter { permission ->
                    !groupPermissions.contains(permission) && !otherCustomRoles.contains(permission)
                }

                // remove permissions from Keycloak if they're not needed anymore
                if (permissionsToRemove.isNotEmpty()) {
                    val userResource = keycloak.realm(realm).users().get(userId)
                    val rolesToRemove = permissionsToRemove.map { permissionName ->
                        keycloak.realm(realm).roles().get(permissionName).toRepresentation()
                    }

                    userResource.roles().realmLevel().remove(rolesToRemove)
                }
            }

            userRolesAuditLogService.logUserRoleChange(
                userId,
                actionedBy,
                emptyList(),
                customRole.permissions
            )

            return ResultFactory.getSuccessResult(data = "Custom role removed from user successfully")
        } catch (e: Exception) {
            return ResultFactory.getFailResult("Error removing custom role from user: ${e.message}")
        }
    }


    @Transactional(readOnly = true)
    fun getUserCustomRoles(userId: String): Result<List<CustomRoleDto>> {
        try {
            val user = userManagementService.findUserById(userId)
                ?: return ResultFactory.getFailResult("User not found with id: $userId")

            val userCustomRoleAssignments = userCustomRoleRepository.findByUserId(userId)
            val customRoleDtos = userCustomRoleAssignments.map { mapToCustomRoleDto(it.customRole) }

            return ResultFactory.getSuccessResult(customRoleDtos)
        } catch (e: Exception) {
            return ResultFactory.getFailResult("Error fetching user custom roles: ${e.message}")
        }
    }

    fun getUserStandaloneRoles(userId: String): Result<UserStandaloneRolesDto> {
        try {
            val user = userManagementService.findUserById(userId)
                ?: return ResultFactory.getFailResult("User not found with id: $userId")

            val directUserRoles = userManagementService.findDirectUserRealmRoles(userId)
            val directUserRoleNames = directUserRoles.map { it.name }

            val userGroupMembers = userGroupMemberRepository.findByUserId(userId)
            val groupRoleNames = mutableSetOf<String>()

            userGroupMembers.forEach { member ->
                val group = member.userGroup
                groupRoleNames.addAll(group.predefinedRoles)

                group.customRoles.forEach { role ->
                    groupRoleNames.addAll(role.permissions)
                }
            }

            val userCustomRoleAssignments = userCustomRoleRepository.findByUserId(userId)
            val userCustomRoles = userCustomRoleAssignments.map { it.customRole }

            val customRolePermissions = userCustomRoles.flatMap { it.permissions }.toSet()

            val standaloneRoleNames = directUserRoleNames.filter { roleName ->
                !groupRoleNames.contains(roleName) && !customRolePermissions.contains(roleName)
            }

            val standaloneRoles = getPermissionDtosFromNames(standaloneRoleNames)
            val customRoleDtos = userCustomRoles.map { mapToCustomRoleDto(it) }

            return ResultFactory.getSuccessResult(
                UserStandaloneRolesDto(
                    userId = userId,
                    userName = user.username,
                    userEmail = user.email,
                    customRoles = customRoleDtos,
                    standaloneRoles = standaloneRoles
                )
            )
        } catch (e: Exception) {
            return ResultFactory.getFailResult("Error fetching user standalone roles: ${e.message}")
        }
    }

    fun getCustomRoleUsers(roleId: Long, payerId: Long, pageable: Pageable): Result<Page<UserBasicDto>> {
        try {
            val customRole = customRoleRepository.findById(roleId)
                .orElseThrow { EntityNotFoundException("Custom role not found with id: $roleId") }

            if (customRole.payerId != payerId) {
                return ResultFactory.getFailResult("Access denied: This role does not belong to your payer account")
            }

            val allUserCustomRoles = userCustomRoleRepository.findByRoleId(roleId)

            val allUserDtos = allUserCustomRoles.mapNotNull { userCustomRole ->
                try {
                    val user = userManagementService.findUserById(userCustomRole.userId)
                    if (user != null) {
                        UserBasicDto(
                            userId = userCustomRole.userId,
                            userName = user.username,
                            userEmail = user.email,
                            name = listOfNotNull(user.firstName, user.lastName)
                                .takeIf { it.isNotEmpty() }?.joinToString(" ")
                        )
                    } else null
                } catch (e: Exception) {
                    logger.warn("User with ID {} not found in Keycloak: {}", userCustomRole.userId, e.message)
                    null
                }
            }

            val start = pageable.pageNumber * pageable.pageSize
            val end = minOf(start + pageable.pageSize, allUserDtos.size)

            if (start >= allUserDtos.size) {
                return ResultFactory.getSuccessResult(Page.empty(pageable))
            }

            val paginatedUsers = allUserDtos.subList(start, end)
            val page = PageImpl(paginatedUsers, pageable, allUserDtos.size.toLong())

            return ResultFactory.getSuccessResult(page)
        } catch (e: Exception) {
            return ResultFactory.getFailResult("Error fetching custom role users: ${e.message}")
        }
    }

    @Transactional(readOnly = true)
    fun getPayerDenyPolicies(page: Int, size: Int, payerId: Long): Page<DenyPolicy> {
        val pageable = PageRequest.of(if (page > 0) page - 1 else page, size)
        val payerDenyPolicies = denyPolicyRepository.findByPayerId(payerId, pageable)
        return payerDenyPolicies
    }


    @Transactional(readOnly = true)
    fun getUserDenyPolicies(userId: String): Result<List<DenyPolicyDto>> {
        try {
            try {
                userManagementService.findUserById(userId)
            } catch (e: Exception) {
                logger.warn("User with ID {} not found in Keycloak: {}", userId, e.message)
                return ResultFactory.getSuccessResult(emptyList())
            }

            val denyPolicies = denyPolicyRepository.findByUserId(userId)

            val allUserIds = denyPolicies.flatMap { it.users }.distinct()

            val users = allUserIds.associateWith { uid ->
                try {
                    userManagementService.findUserById(uid)
                } catch (e: Exception) {
                    // handle case where a user doesn't exist in Keycloak
                    logger.warn("User with ID {} not found in Keycloak: {}", uid, e.message)
                    null
                }
            }

            val denyPolicyDtos = denyPolicies.map { mapToDenyPolicyDto(it, users) }

            return ResultFactory.getSuccessResult(denyPolicyDtos)
        } catch (e: Exception) {
            return ResultFactory.getFailResult("Error fetching user deny policies: ${e.message}")
        }
    }

    fun getDenyPolicyUsers(id: Long, payerId: Long, pageable: Pageable): Result<Page<UserBasicDto>> {
        try {
            val policyExists = denyPolicyRepository.existsById(id)
            if (!policyExists) {
                logger.warn("Deny policy does not exist - id: {}", id)
                return ResultFactory.getFailResult("Deny policy not found with id: $id")
            }

            val denyPolicy = denyPolicyRepository.findById(id).orElse(null)
            if (denyPolicy != null) {
                if (denyPolicy.payerId != payerId) {
                    return ResultFactory.getFailResult("Access denied: This deny policy does not belong to your payer account")
                }
            } else {
                return ResultFactory.getFailResult("Deny policy not found with id: $id")
            }

            val userIdsPage = denyPolicyRepository.findUserIdsByPolicyIdAndPayerId(id, payerId, pageable)

            if (userIdsPage.content.isEmpty()) {
                return ResultFactory.getSuccessResult(Page.empty(pageable))
            }

            val userDtos = mutableListOf<UserBasicDto>()

            for (userId in userIdsPage.content) {
                try {
                    val user = userManagementService.findUserById(userId)
                    if (user != null) {
                        val dto = UserBasicDto(
                            userId = userId,
                            userName = user.username,
                            userEmail = user.email,
                            name = listOfNotNull(user.firstName, user.lastName)
                                .takeIf { it.isNotEmpty() }?.joinToString(" ")
                        )
                        userDtos.add(dto)
                    } else {
                        logger.warn("User returned null from Keycloak: {}", userId)
                    }
                } catch (e: Exception) {
                    logger.error(
                        "Error looking up user {} in Keycloak: {} - {}",
                        userId,
                        e.javaClass.simpleName,
                        e.message,
                        e
                    )
                }
            }

            val resultPage = PageImpl(userDtos, pageable, userIdsPage.totalElements)

            return ResultFactory.getSuccessResult(resultPage)
        } catch (e: Exception) {
            logger.error(
                "Error in getDenyPolicyUsers (EFFICIENT) - id: {}, payerId: {}: {} - {}",
                id,
                payerId,
                e.javaClass.simpleName,
                e.message,
                e
            )
            return ResultFactory.getFailResult("Error fetching deny policy users: ${e.message}")
        }
    }

    @Transactional(readOnly = true)
    fun getDenyPolicyById(id: Long, payerId: Long): Result<DenyPolicyDto> {
        try {
            val denyPolicy = denyPolicyRepository.findById(id)
                .orElseThrow { EntityNotFoundException("Deny policy not found with id: $id") }

            if (denyPolicy.payerId != payerId) {
                return ResultFactory.getFailResult("Access denied: This deny policy does not belong to your payer account")
            }

            val userIds = denyPolicy.users
            val users = userIds.associateWith { userId ->
                try {
                    userManagementService.findUserById(userId)
                } catch (e: Exception) {
                    // handle the case where a user doesn't exist in Keycloak
                    logger.warn("User with ID {} not found in Keycloak: {}", userId, e.message)
                    null
                }
            }

            return ResultFactory.getSuccessResult(mapToDenyPolicyDto(denyPolicy, users))
        } catch (e: Exception) {
            return ResultFactory.getFailResult("Error fetching deny policy: ${e.message}")
        }
    }


    @Transactional
    fun updateDenyPolicy(id: Long, dto: UpdateDenyPolicyDto): Result<DenyPolicyDto> {
        try {
            val denyPolicy = denyPolicyRepository.findById(id)
                .orElseThrow { EntityNotFoundException("Deny policy not found with id: $id") }

            val updatedPolicy = denyPolicy.copy(
                name = dto.name ?: denyPolicy.name,
                reason = dto.reason ?: denyPolicy.reason,
                expiresAt = dto.expiresAt,
                updatedAt = LocalDateTime.now()
            )

            if (dto.addUsers != null && dto.addUsers.isNotEmpty()) {
                val validUserIds = mutableListOf<String>()
                val failedUsers = mutableMapOf<String, String>()

                for (userId in dto.addUsers) {
                    if (!updatedPolicy.users.contains(userId)) {
                        try {
                            val user = userManagementService.findUserById(userId)
                            if (user == null) {
                                failedUsers[userId] = "User not found"
                            } else {
                                validUserIds.add(userId)
                            }
                        } catch (e: Exception) {
                            failedUsers[userId] = "Error finding user: ${e.message}"
                        }
                    }
                }

                updatedPolicy.users.addAll(validUserIds)
            }

            if (dto.removeUsers != null && dto.removeUsers.isNotEmpty()) {
                updatedPolicy.users.removeAll(dto.removeUsers)
            }

            if (dto.addPermissions != null && dto.addPermissions.isNotEmpty()) {
                val allRealmRoles = userManagementService.getAllSystemRealmRoles()
                val allRealmRoleNames = allRealmRoles.map { it.name }.toSet()

                val validPermissions = dto.addPermissions.filter { allRealmRoleNames.contains(it) }
                validPermissions.forEach { permission ->
                    if (!updatedPolicy.deniedPermissions.contains(permission)) {
                        updatedPolicy.deniedPermissions.add(permission)
                    }
                }
            }

            if (dto.removePermissions != null && dto.removePermissions.isNotEmpty()) {
                val remainingPermissions = updatedPolicy.deniedPermissions.toSet().minus(dto.removePermissions)

                if (remainingPermissions.isEmpty()) {
                    return ResultFactory.getFailResult("Cannot remove all permissions. A deny policy must retain at least one permission.")
                }

                if (updatedPolicy.deniedPermissions.toSet() == dto.removePermissions) {
                    return ResultFactory.getFailResult("The permissions to remove match exactly the existing permissions. A deny policy must retain at least one permission.")
                }

                updatedPolicy.deniedPermissions.removeAll(dto.removePermissions)
            }

            val savedPolicy = denyPolicyRepository.save(updatedPolicy)

            val users = savedPolicy.users.associateWith { userId ->
                try {
                    userManagementService.findUserById(userId)
                } catch (e: Exception) {
                    // handle the case where a user doesn't exist in Keycloak
                    logger.warn("User with ID {} not found in Keycloak: {}", userId, e.message)
                    null
                }
            }

            return ResultFactory.getSuccessResult(mapToDenyPolicyDto(savedPolicy, users))
        } catch (e: Exception) {
            return ResultFactory.getFailResult("Error updating deny policy: ${e.message}")
        }
    }


    @Transactional
    fun deleteDenyPolicy(id: Long, payerId: Long): Result<String> {
        try {
            val denyPolicy = denyPolicyRepository.findById(id)
                .orElseThrow { EntityNotFoundException("Deny policy not found with id: $id") }

            if (denyPolicy.payerId != payerId) {
                return ResultFactory.getFailResult("Access denied: This deny policy does not belong to your payer account")
            }

            denyPolicy.users.clear()
            denyPolicy.deniedPermissions.clear()
            val savedPolicy = denyPolicyRepository.save(denyPolicy)

            denyPolicyRepository.delete(savedPolicy)
            return ResultFactory.getSuccessResult(data = "Deny policy deleted successfully")
        } catch (e: Exception) {
            return ResultFactory.getFailResult("Error deleting deny policy: ${e.message}")
        }
    }

    @Transactional
    fun updateUserGroup(groupId: Long, dto: UpdateUserGroupDto, payerId: Long): Result<UserGroupDto> {
        try {
            val userGroup = userGroupRepository.findById(groupId)
                .orElseThrow { EntityNotFoundException("User group not found with id: $groupId") }

            if (userGroup.payerId != payerId) {
                return ResultFactory.getFailResult("Access denied: This user group does not belong to your payer account")
            }

            val updatedGroup = userGroup.copy(
                name = dto.name ?: userGroup.name,
                description = dto.description ?: userGroup.description,
                updatedAt = LocalDateTime.now()
            )

            if (dto.usersToAdd != null && dto.usersToAdd.isNotEmpty()) {
                val addUsersDto = AddUsersToGroupDto(
                    userIds = dto.usersToAdd,
                    addedBy = dto.updatedBy
                )
                val addResult = addUsersToGroup(groupId, addUsersDto)
                if (!addResult.success) {
                    return ResultFactory.getFailResult("Error adding users to group: ${addResult.msg}")
                }
            }

            if (dto.usersToRemove != null && dto.usersToRemove.isNotEmpty()) {
                val removeUsersDto = RemoveUsersFromGroupDto(
                    userIds = dto.usersToRemove,
                    actionedBy = dto.updatedBy
                )
                val removeResult = removeUsersFromGroup(groupId, removeUsersDto)
                if (!removeResult.success) {
                    return ResultFactory.getFailResult("Error removing users from group: ${removeResult.msg}")
                }
            }

            val currentCustomRoleIds = userGroup.customRoles.map { it.id }.toMutableSet()

            if (dto.customRolesToAdd != null && dto.customRolesToAdd.isNotEmpty()) {
                for (roleId in dto.customRolesToAdd) {
                    if (!currentCustomRoleIds.contains(roleId)) {
                        val role = customRoleRepository.findById(roleId)
                            .orElseThrow { EntityNotFoundException("Custom role not found with id: $roleId") }

                        if (role.payerId != payerId) {
                            return ResultFactory.getFailResult("Access denied: Custom role with ID $roleId does not belong to your payer account")
                        }

                        updatedGroup.customRoles.add(role)
                        currentCustomRoleIds.add(roleId)
                    }
                }
            }

            if (dto.customRolesToRemove != null && dto.customRolesToRemove.isNotEmpty()) {
                val rolesToRemove = updatedGroup.customRoles.filter { dto.customRolesToRemove.contains(it.id) }
                updatedGroup.customRoles.removeAll(rolesToRemove)
                currentCustomRoleIds.removeAll(dto.customRolesToRemove)
            }

            val currentPredefinedRoles = updatedGroup.predefinedRoles.toMutableSet()

            if (dto.predefinedRolesToAdd != null && dto.predefinedRolesToAdd.isNotEmpty()) {
                for (roleName in dto.predefinedRolesToAdd) {
                    if (!currentPredefinedRoles.contains(roleName)) {
                        updatedGroup.predefinedRoles.add(roleName)
                        currentPredefinedRoles.add(roleName)
                    }
                }
            }

            if (dto.predefinedRolesToRemove != null && dto.predefinedRolesToRemove.isNotEmpty()) {
                updatedGroup.predefinedRoles.removeAll(dto.predefinedRolesToRemove)
                currentPredefinedRoles.removeAll(dto.predefinedRolesToRemove)
            }

            val kRealm = keycloak.realm(realm)

            try {
                kRealm.groups().group(userGroup.keycloakGroupId).toRepresentation()
            } catch (e: Exception) {
                logger.error(
                    "Keycloak group with ID {} does not exist for group {}: {}",
                    userGroup.keycloakGroupId,
                    groupId,
                    e.message
                )
                return ResultFactory.getFailResult("Group does not exist in Keycloak. Group ID: ${userGroup.keycloakGroupId}")
            }

            if (dto.name != null && dto.name != userGroup.name) {
                try {
                    val groupRep = kRealm.groups().group(userGroup.keycloakGroupId).toRepresentation()
                    groupRep.name = "payer_${payerId}_${dto.name}"
                    kRealm.groups().group(userGroup.keycloakGroupId).update(groupRep)
                } catch (e: Exception) {
                    logger.error("Failed to update group name in Keycloak for group {}: {}", groupId, e.message)
                    return ResultFactory.getFailResult("Failed to update group name in Keycloak: ${e.message}")
                }
            }

            val allRoles = mutableSetOf<String>()

            updatedGroup.customRoles.forEach { role ->
                allRoles.addAll(role.permissions)
            }

            allRoles.addAll(updatedGroup.predefinedRoles)

            val existingRoles = kRealm.groups().group(userGroup.keycloakGroupId).roles().realmLevel().listAll()
            val existingRoleNames = existingRoles.map { it.name }.toSet()

            val addedRoles = allRoles.filter { !existingRoleNames.contains(it) }
            val removedRoles = existingRoleNames.filter { !allRoles.contains(it) }

            if (existingRoles.isNotEmpty()) {
                kRealm.groups().group(userGroup.keycloakGroupId).roles().realmLevel().remove(existingRoles)
            }

            assignRolesToKeycloakGroup(kRealm, userGroup.keycloakGroupId, allRoles.toList())

            // log role changes for all members
            val members = userGroupMemberRepository.findByGroupId(userGroup.id)
            members.forEach { member ->
                try {
                    userRolesAuditLogService.logUserRoleChange(
                        member.userId,
                        dto.updatedBy,
                        addedRoles.toList(),
                        removedRoles.toList()
                    )
                } catch (e: Exception) {
                    // log the error but continue processing other members
                    logger.warn("Failed to log role changes for user {}: {}", member.userId, e.message)
                }
            }

            val savedGroup = userGroupRepository.save(updatedGroup)

            val customRoleDtos = savedGroup.customRoles.map { role ->
                mapToCustomRoleDto(role)
            }

            val groupDto = UserGroupDto(
                id = savedGroup.id,
                keycloakGroupId = savedGroup.keycloakGroupId,
                name = savedGroup.name,
                description = savedGroup.description,
                customRoles = customRoleDtos,
                predefinedRoles = savedGroup.predefinedRoles,
                createdBy = savedGroup.createdBy,
                createdAt = savedGroup.createdAt,
                updatedAt = savedGroup.updatedAt,
                payerId = savedGroup.payerId
            )

            return ResultFactory.getSuccessResult(groupDto)
        } catch (e: Exception) {
            return ResultFactory.getFailResult("Error updating user group: ${e.message}")
        }
    }

    @Transactional
    fun updateGroupRoles(groupId: Long, dto: UpdateGroupRolesDto, payerId: Long): Result<UserGroupDto> {
        try {
            val userGroup = userGroupRepository.findById(groupId)
                .orElseThrow { EntityNotFoundException("User group not found with id: $groupId") }

            if (userGroup.payerId != payerId) {
                return ResultFactory.getFailResult("Access denied: This user group does not belong to your payer account")
            }

            if ((dto.customRoleIds == null || dto.customRoleIds.isEmpty()) &&
                (dto.predefinedRoles == null || dto.predefinedRoles.isEmpty())
            ) {
                return ResultFactory.getFailResult("At least one role (custom or predefined) must be provided")
            }

            val customRoles = mutableListOf<CustomRole>()
            if (dto.customRoleIds != null && dto.customRoleIds.isNotEmpty()) {
                for (roleId in dto.customRoleIds) {
                    val role = customRoleRepository.findById(roleId)
                        .orElseThrow { EntityNotFoundException("Custom role not found with id: $roleId") }
                    customRoles.add(role)
                }
            }

            userGroup.customRoles.clear()
            userGroup.customRoles.addAll(customRoles)

            if (dto.predefinedRoles != null) {
                userGroup.predefinedRoles.clear()
                userGroup.predefinedRoles.addAll(dto.predefinedRoles)
            }

            val kRealm = keycloak.realm(realm)

            try {
                kRealm.groups().group(userGroup.keycloakGroupId).toRepresentation()
            } catch (e: Exception) {
                logger.error(
                    "Keycloak group with ID {} does not exist for group {}: {}",
                    userGroup.keycloakGroupId,
                    groupId,
                    e.message
                )
                return ResultFactory.getFailResult("Group does not exist in Keycloak. Group ID: ${userGroup.keycloakGroupId}")
            }

            val allRoles = mutableSetOf<String>()

            customRoles.forEach { role ->
                allRoles.addAll(role.permissions)
            }

            if (dto.predefinedRoles != null) {
                allRoles.addAll(dto.predefinedRoles)
            }

            val existingRoles = kRealm.groups().group(userGroup.keycloakGroupId).roles().realmLevel().listAll()
            val existingRoleNames = existingRoles.map { it.name }.toSet()

            val addedRoles = allRoles.filter { !existingRoleNames.contains(it) }
            val removedRoles = existingRoleNames.filter { !allRoles.contains(it) }

            if (existingRoles.isNotEmpty()) {
                kRealm.groups().group(userGroup.keycloakGroupId).roles().realmLevel().remove(existingRoles)
            }

            assignRolesToKeycloakGroup(kRealm, userGroup.keycloakGroupId, allRoles.toList())

            val members = userGroupMemberRepository.findByGroupId(userGroup.id)
            members.forEach { member ->
                try {
                    userRolesAuditLogService.logUserRoleChange(
                        member.userId,
                        dto.updatedBy,
                        addedRoles.toList(),
                        removedRoles.toList()
                    )
                } catch (e: Exception) {
                    // log the error but continue processing other members
                    logger.warn("Failed to log role changes for user {}: {}", member.userId, e.message)
                }
            }

            val updatedGroup = userGroup.copy(updatedAt = LocalDateTime.now())
            val savedGroup = userGroupRepository.save(updatedGroup)

            // Manually create the DTO to avoid LazyInitializationException
            val customRoleDtos = savedGroup.customRoles.map { role ->
                mapToCustomRoleDto(role)
            }

            val dto = UserGroupDto(
                id = savedGroup.id,
                keycloakGroupId = savedGroup.keycloakGroupId,
                name = savedGroup.name,
                description = savedGroup.description,
                customRoles = customRoleDtos,
                predefinedRoles = savedGroup.predefinedRoles,
                createdBy = savedGroup.createdBy,
                createdAt = savedGroup.createdAt,
                updatedAt = savedGroup.updatedAt,
                payerId = savedGroup.payerId
            )

            return ResultFactory.getSuccessResult(dto)
        } catch (e: Exception) {
            return ResultFactory.getFailResult("Error updating group roles: ${e.message}")
        }
    }


    /**
     * Creates a Keycloak group with roles in a single atomic operation
     */
    private fun createKeycloakGroupWithRoles(
        realm: RealmResource,
        groupName: String,
        customRoles: List<CustomRole>,
        predefinedRoles: Set<String>?
    ): String {
        logger.debug(
            "Creating Keycloak group '{}' with {} custom roles and {} predefined roles",
            groupName, customRoles.size, predefinedRoles?.size ?: 0
        )

        val existingGroups = realm.groups().groups(groupName, 0, 1)
        if (existingGroups.isNotEmpty()) {
            val existingGroupId = existingGroups[0].id
            logger.debug("Found existing group with ID: {}", existingGroupId)
            return existingGroupId
        }

        val group = GroupRepresentation().apply {
            name = groupName
        }

        val response = realm.groups().add(group)

        if (!HttpStatus.valueOf(response.status).is2xxSuccessful) {
            val error = try {
                response.readEntity(String::class.java)
            } catch (e: Exception) {
                "Unknown error"
            }
            logger.error("Failed to create Keycloak group. Status: {}, Error: {}", response.status, error)
            throw IllegalStateException("Failed to create Keycloak group: $error")
        }

        val groupId = try {
            response.location.path.substringAfterLast('/')
        } catch (e: Exception) {
            logger.error("Failed to extract group ID from response location: {}", e.message)
            throw IllegalStateException("Failed to get group ID from response", e)
        }

        logger.debug("Successfully created group with ID: {}", groupId)

        try {
            val allRoles = buildSet {
                customRoles.forEach { role -> addAll(role.permissions) }
                predefinedRoles?.let { addAll(it) }
            }

            if (allRoles.isNotEmpty()) {
                assignRolesToKeycloakGroup(realm, groupId, allRoles.toList())
                logger.debug("Successfully assigned {} roles to group", allRoles.size)
            }
        } catch (e: Exception) {
            logger.error("Failed to assign roles to group {}: {}", groupId, e.message)
            try {
                realm.groups().group(groupId).remove()
                logger.debug("Cleaned up group after role assignment failure")
            } catch (cleanupError: Exception) {
                logger.warn("Failed to clean up group after role assignment failure: {}", cleanupError.message)
            }
            throw KeycloakException("Failed to assign roles to group", e)
        }

        return groupId
    }

    /**
     * Ensures a Keycloak group exists and is properly configured
     */
    private fun ensureKeycloakGroupExists(
        realm: RealmResource,
        userGroup: UserGroup
    ): String {
        logger.debug(
            "Ensuring Keycloak group exists for group '{}' (ID: {})",
            userGroup.name, userGroup.keycloakGroupId
        )

        try {
            realm.groups().group(userGroup.keycloakGroupId).toRepresentation()
            logger.debug("Found existing Keycloak group with ID: {}", userGroup.keycloakGroupId)
            return userGroup.keycloakGroupId
        } catch (e: Exception) {
            logger.info("Keycloak group {} not found, creating new group", userGroup.keycloakGroupId)
        }

        val groupName = "payer_${userGroup.payerId}_${userGroup.name.replace(" ", "_")}"
        val group = GroupRepresentation().apply {
            name = groupName
        }

        val response = realm.groups().add(group)

        if (!HttpStatus.valueOf(response.status).is2xxSuccessful) {
            val error = try {
                response.readEntity(String::class.java)
            } catch (e: Exception) {
                "Unknown error"
            }
            logger.error("Failed to create Keycloak group. Status: {}, Error: {}", response.status, error)
            throw IllegalStateException("Failed to create Keycloak group: $error")
        }

        val newGroupId = try {
            response.location.path.substringAfterLast('/')
        } catch (e: Exception) {
            logger.error("Failed to extract group ID from response location: {}", e.message)
            throw IllegalStateException("Failed to get group ID from response", e)
        }

        logger.debug("Successfully created new group with ID: {}", newGroupId)

        try {
            val allRoles = buildSet {
                userGroup.customRoles.forEach { role -> addAll(role.permissions) }
                addAll(userGroup.predefinedRoles)
            }

            if (allRoles.isNotEmpty()) {
                assignRolesToKeycloakGroup(realm, newGroupId, allRoles.toList())
                logger.debug("Successfully assigned {} roles to group", allRoles.size)
            }

            userGroup.keycloakGroupId = newGroupId
            userGroupRepository.save(userGroup)
            logger.info("Updated group ID in database: {} -> {}", userGroup.id, newGroupId)

            return newGroupId

        } catch (e: Exception) {
            logger.error("Failed to configure new group {}: {}", newGroupId, e.message)
            try {
                realm.groups().group(newGroupId).remove()
                logger.debug("Cleaned up group after role assignment failure")
            } catch (cleanupError: Exception) {
                logger.warn("Failed to clean up group after role assignment failure: {}", cleanupError.message)
            }
            throw KeycloakException("Failed to configure group", e)
        }
    }

    private fun assignRolesToKeycloakGroup(realm: RealmResource, groupId: String, roles: List<String>) {
        val rolesList = mutableListOf<RoleRepresentation>()

        for (roleName in roles) {
            try {
                val role = realm.roles().get(roleName).toRepresentation()
                rolesList.add(role)
            } catch (e: Exception) {
                logger.warn("Failed to get role '{}' for group assignment: {}", roleName, e.message)
            }
        }

        if (rolesList.isNotEmpty()) {
            try {
                realm.groups().group(groupId).roles().realmLevel().add(rolesList)
            } catch (e: Exception) {
                logger.error("Failed to assign roles to Keycloak group {}: {}", groupId, e.message)
                throw e
            }
        }
    }


    private fun mapToCustomRoleDto(customRole: CustomRole): CustomRoleDto {
        val permissionDtos = getPermissionDtosFromNames(customRole.permissions)

        return CustomRoleDto(
            id = customRole.id,
            name = customRole.name,
            description = customRole.description,
            isPredefined = customRole.isPredefined,
            permissions = permissionDtos,
            createdBy = customRole.createdBy,
            createdAt = customRole.createdAt,
            updatedAt = customRole.updatedAt,
            payerId = customRole.payerId,
        )
    }

    /**
     * Converts a list of permission names to PermissionDto objects with descriptions
     */
    private fun getPermissionDtosFromNames(permissionNames: List<String>): List<PermissionDto> {
        val allRealmRoles = userManagementService.getAllSystemRealmRoles()
        val roleMap = allRealmRoles.associateBy { it.name }

        return permissionNames.mapNotNull { name ->
            roleMap[name]?.let { role ->
                PermissionDto(
                    name = role.name,
                    description = role.description
                )
            }
        }
    }


    private fun mapToUserGroupDto(userGroup: UserGroup): UserGroupDto {
        return UserGroupDto(
            id = userGroup.id,
            keycloakGroupId = userGroup.keycloakGroupId,
            name = userGroup.name,
            description = userGroup.description,
            customRoles = userGroup.customRoles.map { mapToCustomRoleDto(it) },
            predefinedRoles = userGroup.predefinedRoles,
            createdBy = userGroup.createdBy,
            createdAt = userGroup.createdAt,
            updatedAt = userGroup.updatedAt,
            payerId = userGroup.payerId
        )
    }


    private fun mapToUserGroupMemberDto(
        userGroupMember: UserGroupMember,
        user: UserRepresentation?
    ): UserGroupMemberDto {
        return UserGroupMemberDto(
            id = userGroupMember.id,
            userId = userGroupMember.userId,
            userName = user?.username,
            userEmail = user?.email,
            name = listOfNotNull(user?.firstName, user?.lastName)
                .takeIf { it.isNotEmpty() }?.joinToString(" "),
            addedBy = userGroupMember.addedBy,
            addedAt = userGroupMember.addedAt
        )
    }


    private fun mapToDenyPolicyDto(denyPolicy: DenyPolicy, users: Map<String, UserRepresentation?>): DenyPolicyDto {
        return DenyPolicyDto(
            id = denyPolicy.id,
            name = denyPolicy.name,
            payerId = denyPolicy.payerId,
            users = denyPolicy.users.map { userId ->
                val user = users[userId]
                UserBasicDto(
                    userId = userId,
                    userName = user?.username,
                    userEmail = user?.email,
                    name = listOfNotNull(user?.firstName, user?.lastName)
                        .takeIf { it.isNotEmpty() }?.joinToString(" ")
                )
            },
            deniedPermissions = denyPolicy.deniedPermissions,
            reason = denyPolicy.reason,
            createdBy = denyPolicy.createdBy,
            createdAt = denyPolicy.createdAt,
            updatedAt = denyPolicy.updatedAt,
            expiresAt = denyPolicy.expiresAt
        )
    }


    @Transactional
    fun createPayerUser(dto: CreatePayerUserDto): Result<UserRepresentation> {
        val validCustomRoleIds = mutableSetOf<Long>()
        if (dto.customRoleIds != null && dto.customRoleIds.isNotEmpty()) {
            for (roleId in dto.customRoleIds) {
                val role = customRoleRepository.findById(roleId).orElse(null)
                if (role == null) {
                    logger.warn("Custom role with ID $roleId does not exist. Skipping this role.")
                    continue
                }
                if (role.payerId != dto.payerId) {
                    logger.warn("Custom role '${role.name}' (ID: $roleId) does not belong to payer ${dto.payerId}. Skipping this role.")
                    continue
                }
                validCustomRoleIds.add(roleId)
            }
        }

        val validGroupIds = mutableSetOf<Long>()
        if (dto.groupIds != null && dto.groupIds.isNotEmpty()) {
            for (groupId in dto.groupIds) {
                val userGroup = userGroupRepository.findById(groupId).orElse(null)
                if (userGroup == null) {
                    logger.warn("User group with ID $groupId does not exist. Skipping this group.")
                    continue
                }
                if (userGroup.payerId != dto.payerId) {
                    logger.warn("User group '${userGroup.name}' (ID: $groupId) does not belong to payer ${dto.payerId}. Skipping this group.")
                    continue
                }
                validGroupIds.add(groupId)
            }
        }

        if (dto.predefinedRoles != null && dto.predefinedRoles.isNotEmpty()) {
            val allRealmRoles = userManagementService.getAllSystemRealmRoles()
            val allRealmRoleNames = allRealmRoles.map { it.name }.toSet()
            val invalidRoles = dto.predefinedRoles.filter { !allRealmRoleNames.contains(it) }
            if (invalidRoles.isNotEmpty()) {
                return ResultFactory.getFailResult(
                    "The following predefined roles do not exist: ${invalidRoles.joinToString(", ")}"
                )
            }
        }

        val credentials = CredentialRepresentation()
        credentials.type = CredentialRepresentation.PASSWORD
        credentials.value = dto.password
        credentials.isTemporary = true

        val userRepresentation = UserRepresentation()
        userRepresentation.username = dto.username
        userRepresentation.email = dto.email
        userRepresentation.firstName = dto.firstName
        userRepresentation.lastName = dto.lastName
        userRepresentation.credentials = listOf(credentials)
        userRepresentation.isEnabled = true

        val attributes: MutableMap<String, List<String>> = HashMap()
        attributes["payerId"] = listOf(dto.payerId.toString())
        userRepresentation.attributes = attributes

        val response: Response = keycloak.realm(realm).users().create(userRepresentation)

        if (!HttpStatus.valueOf(response.status).is2xxSuccessful) {
            val errorMessage = KeycloakResponseUtil.parseUserCreationError(response, dto.username, dto.email)
            return ResultFactory.getFailResult(errorMessage)
        }

        val locationHeader = response.getHeaderString("Location")
        val userId = locationHeader.replace(".*/(.*)$".toRegex(), "$1")
        val userResource = keycloak.realm(realm).users().get(userId)

        try {
            val payerRole = keycloak.realm(realm).roles().get("PAYER").toRepresentation()
            userResource.roles().realmLevel().add(listOf(payerRole))
        } catch (e: Exception) {
            logger.error("Failed to assign PAYER role to user $userId: ${e.message}")
            return ResultFactory.getFailResult("Failed to assign PAYER role to user: ${e.message}")
        }

        if (validCustomRoleIds.isNotEmpty()) {
            for (roleId in validCustomRoleIds) {
                try {
                    val result = assignCustomRoleToUser(userId, roleId, dto.createdBy)
                    if (!result.success) {
                        val role = customRoleRepository.findById(roleId).orElse(null)
                        val roleName = role?.name ?: "ID $roleId"
                        logger.warn("Failed to assign custom role '$roleName': ${result.msg}")
                    }
                } catch (e: Exception) {
                    val role = customRoleRepository.findById(roleId).orElse(null)
                    val roleName = role?.name ?: "ID $roleId"
                    logger.warn("Failed to assign custom role '$roleName': ${e.message}")
                }
            }
        }

        if (dto.predefinedRoles != null && dto.predefinedRoles.isNotEmpty()) {
            val rolesList = mutableListOf<RoleRepresentation>()
            val failedRoles = mutableListOf<String>()

            for (roleName in dto.predefinedRoles) {
                try {
                    val role = keycloak.realm(realm).roles().get(roleName).toRepresentation()
                    rolesList.add(role)
                } catch (e: Exception) {
                    failedRoles.add(roleName)
                    logger.warn("Failed to get predefined role '$roleName': ${e.message}")
                }
            }

            if (failedRoles.isNotEmpty()) {
                return ResultFactory.getFailResult(
                    "Failed to assign the following predefined roles: ${failedRoles.joinToString(", ")}"
                )
            }

            if (rolesList.isNotEmpty()) {
                try {
                    userResource.roles().realmLevel().add(rolesList)
                } catch (e: Exception) {
                    return ResultFactory.getFailResult("Failed to assign predefined roles to user: ${e.message}")
                }
            }
        }

        if (validGroupIds.isNotEmpty()) {
            for (groupId in validGroupIds) {
                try {
                    val userGroup = userGroupRepository.findById(groupId)
                        .orElseThrow { EntityNotFoundException("User group not found with id: $groupId") }

                    try {
                        keycloak.realm(realm).users().get(userId).joinGroup(userGroup.keycloakGroupId)
                    } catch (e: Exception) {
                        logger.warn("Failed to add user to Keycloak group '${userGroup.name}': ${e.message}")
                        continue
                    }

                    try {
                        val userGroupMember = UserGroupMember(
                            userGroup = userGroup,
                            userId = userId,
                            addedBy = dto.createdBy
                        )
                        userGroupMemberRepository.save(userGroupMember)
                    } catch (e: Exception) {
                        logger.warn("Failed to save group membership for '${userGroup.name}': ${e.message}")
                        continue
                    }

                } catch (e: EntityNotFoundException) {
                    logger.warn("User group with ID $groupId not found")
                    continue
                } catch (e: Exception) {
                    logger.warn("Failed to add user to group with ID $groupId: ${e.message}")
                    continue
                }
            }
        }

        val addedRoles = mutableListOf<String>()
        if (validCustomRoleIds.isNotEmpty()) {
            for (roleId in validCustomRoleIds) {
                try {
                    val role = customRoleRepository.findById(roleId).orElse(null)
                    if (role != null) {
                        addedRoles.addAll(role.permissions)
                    }
                } catch (e: Exception) {
                    logger.warn("Failed to get permissions for custom role $roleId during audit logging: ${e.message}")
                }
            }
        }

        if (dto.predefinedRoles != null) {
            addedRoles.addAll(dto.predefinedRoles)
        }

        addedRoles.add("PAYER")

        userRolesAuditLogService.logUserRoleChange(
            userId,
            dto.createdBy,
            addedRoles,
            emptyList()
        )

        return ResultFactory.getSuccessResult(userResource.toRepresentation())
    }
}
