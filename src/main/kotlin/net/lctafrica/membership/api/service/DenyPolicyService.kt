package net.lctafrica.membership.api.service

import net.lctafrica.membership.api.domain.DenyPolicyRepository
import net.lctafrica.membership.api.util.Result
import net.lctafrica.membership.api.util.ResultFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class DenyPolicyService(
    private val denyPolicyRepository: DenyPolicyRepository
) {
    @Transactional
    fun removeUserFromAllDenyPolicies(userId: String): Result<String> {
        try {
            val denyPolicies = denyPolicyRepository.findByUserId(userId)
            
            if (denyPolicies.isEmpty()) {
                return ResultFactory.getSuccessResult("User is not in any deny policies")
            }
            
            for (denyPolicy in denyPolicies) {
                val updatedUsers = denyPolicy.users.toMutableList()
                
                updatedUsers.remove(userId)
                
                denyPolicy.users.clear()

                denyPolicy.users.addAll(updatedUsers)
                
                denyPolicyRepository.save(denyPolicy)
            }
            
            return ResultFactory.getSuccessResult("User removed from ${denyPolicies.size} deny policies")
        } catch (e: Exception) {
            return ResultFactory.getFailResult("Error removing user from deny policies: ${e.message}")
        }
    }
}
