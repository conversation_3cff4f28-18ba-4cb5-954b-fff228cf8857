package net.lctafrica.membership.api.service

import java.time.LocalDateTime
import net.lctafrica.membership.api.domain.Beneficiary
import net.lctafrica.membership.api.domain.BeneficiaryRepository
import net.lctafrica.membership.api.domain.BeneficiaryType
import net.lctafrica.membership.api.domain.CategoryConfig
import net.lctafrica.membership.api.domain.CategoryConfigRepository
import net.lctafrica.membership.api.domain.ChangeLogType
import net.lctafrica.membership.api.domain.ConfigGroup
import net.lctafrica.membership.api.domain.MemberStatus
import net.lctafrica.membership.api.dtos.AuditLoggingDTO
import net.lctafrica.membership.api.util.AppConstants.REMOVE_CHILD_FROM_COVER_IF_ABOVE_AGE
import net.lctafrica.membership.api.util.AppConstants.REMOVE_PRINCIPAL_AND_FAMILY_FROM_COVER_IF_ABOVE_AGE
import org.springframework.data.domain.PageRequest
import org.springframework.stereotype.Service

@Service
class ConfigServiceImpl(
    private val categoryConfigRepository: CategoryConfigRepository,
    private val beneficiaryRepository: BeneficiaryRepository,
    private val auditLogService: AuditLogService,
) : IConfigService {
    override suspend fun removePrincipalFromCoverIfAboveAge() {
        val categoryConfigs = categoryConfigRepository.findByConfigNameAndGroup(
            REMOVE_PRINCIPAL_AND_FAMILY_FROM_COVER_IF_ABOVE_AGE,
            ConfigGroup.CATEGORY_CONFIG
        )
        categoryConfigs.forEach { categoryConfig ->
            val aboveDob = LocalDateTime.now().minusYears(categoryConfig.value.toDouble().toLong())
            val foundBeneficiaries = beneficiaryRepository.findBeneficiariesToExemptFromCover(
                memberStatus = MemberStatus.ACTIVE,
                category = categoryConfig.category,
                aboveDob = aboveDob.toLocalDate(),
                beneficiaryType = BeneficiaryType.PRINCIPAL
            )
            foundBeneficiaries.forEach { beneficiary ->
                val copyBeneficiary = beneficiary.copy()
                beneficiary.status = MemberStatus.SUSPENDED
                beneficiaryRepository.save(beneficiary)
                beneficiaryDeactivationLog(
                    beneficiary = beneficiary,
                    prevBeneficiary = copyBeneficiary,
                    categoryConfig = categoryConfig
                )
                beneficiary.dependants.forEach{ dependant ->
                    beneficiary.status = MemberStatus.SUSPENDED
                    beneficiaryRepository.save(beneficiary)
                }
            }
        }
    }

    override suspend fun removeChildFromCoverIfAboveAge() {
        val categoryConfigs = categoryConfigRepository.findByConfigNameAndGroup(
            REMOVE_CHILD_FROM_COVER_IF_ABOVE_AGE,
            ConfigGroup.CATEGORY_CONFIG
        )
        categoryConfigs.forEach { categoryConfig ->
            val aboveDob = LocalDateTime.now().minusYears(categoryConfig.value.toDouble().toLong())
            val rogueBeneficiaries = beneficiaryRepository.findBeneficiariesToExemptFromCover(
                memberStatus = MemberStatus.ACTIVE,
                category = categoryConfig.category,
                aboveDob = aboveDob.toLocalDate(),
                beneficiaryType = BeneficiaryType.CHILD
            )
            rogueBeneficiaries.forEach { beneficiary ->
                val copyBeneficiary = beneficiary.copy()
                beneficiary.status = MemberStatus.SUSPENDED
                beneficiaryRepository.save(beneficiary)
                beneficiaryDeactivationLog(
                    beneficiary = beneficiary,
                    prevBeneficiary = copyBeneficiary,
                    categoryConfig = categoryConfig
                )
            }
        }
    }

    private suspend fun beneficiaryDeactivationLog(
        beneficiary: Beneficiary,
        prevBeneficiary: Beneficiary,
        categoryConfig: CategoryConfig
    ) {
        auditLogService.log(AuditLoggingDTO(
            beneficiary = beneficiary,
            reason = "Category config applied ${categoryConfig.config.name} - ${categoryConfig.value}",
            user = "system",
            action = "Member Deactivated",
            type = ChangeLogType.MEMBERSTATUS_UPDATE,
            organisation = beneficiary.category.policy.plan.name,
            data = null,
            narration = "Beneficiary status change from ${prevBeneficiary.status} to ${beneficiary.status}"
        ))
    }
}