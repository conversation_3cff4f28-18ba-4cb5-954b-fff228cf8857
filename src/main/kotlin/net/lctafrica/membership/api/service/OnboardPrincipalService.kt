package net.lctafrica.membership.api.service

import net.lctafrica.membership.api.domain.*
import net.lctafrica.membership.api.dtos.*
import net.lctafrica.membership.api.util.calculateOnboardInpatientLimit
import net.lctafrica.membership.api.util.getAge
import net.lctafrica.membership.api.util.toResponseDTO
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.LocalDate
import javax.persistence.EntityNotFoundException
import javax.transaction.Transactional

@Service
class OnboardPrincipalService(
    private val onboardPrincipalRepository: OnboardPrincipalRepository,
    private val onboardDependantService: OnboardDependantService,
    private val onboardBeneficiaryService: OnboardBeneficiaryService,
    private val onboardBenefitService: OnboardBenefitService,
    private val onboardAgentService: OnboardAgentService,
    private val onboardDocumentService: OnboardDocumentService,
    private val onboardPrincipalFormIdService: OnboardPrincipalFormIdService
) {
    private fun calculatePrincipalCategory(age: Int): OnboardPrincipalCategory {
        return if (age < 75) {
            OnboardPrincipalCategory.BIRTH_TO_75_YEARS
        } else {
            OnboardPrincipalCategory.EXISTING_OVER_75_YEARS
        }
    }

    private fun buildPrincipal(
        request: OnboardPrincipalDTO,
        category: OnboardPrincipalCategory,
        agent: OnboardAgent
    ): OnboardPrincipal {
        return OnboardPrincipal(
            firstName = request.firstName,
            middleName = request.middleName,
            surname = request.surname,
            dateOfBirth = request.dateOfBirth,
            applicationDate = request.applicationDate,
            idNumber = request.idNumber,
            growerNumber = request.growerNumber,
            gender = request.gender,
            maritalStatus = request.maritalStatus,
            mobileNumber = request.mobileNumber,
            town = request.town,
            county = request.county,
            hospital = request.hospital,
            inpatientPremium = request.inpatientPremium,
            outpatientPremium = request.outpatientPremium,
            paymentMode = request.paymentMode,
            category = category,
            agent = agent,
            createdByName = request.createdByName,
            createdByUsername = request.createdByUsername,
            premiumPayerGrowerNumber = request.premiumPayerGrowerNumber,
        )
    }

    private fun createPrincipalBenefits(savedPrincipal: OnboardPrincipal) {
        val inpatientBenefitDTO = OnboardBenefitDTO(
            benefitType = OnboardBenefitType.INPATIENT,
            benefitLimit = calculateOnboardInpatientLimit(savedPrincipal.inpatientPremium),
            benefitMonthlyAmount = savedPrincipal.inpatientPremium
        )

        val outpatientBenefitDTO = OnboardBenefitDTO(
            benefitType = OnboardBenefitType.OUTPATIENT,
            benefitLimit = BigDecimal(20_000),
            benefitMonthlyAmount = savedPrincipal.outpatientPremium
        )

        onboardBenefitService.createBenefit(savedPrincipal, inpatientBenefitDTO)
        onboardBenefitService.createBenefit(savedPrincipal, outpatientBenefitDTO)
    }

    @Transactional
    fun createPrincipalWithoutDependantsAndBeneficiaries(
        request: OnboardPrincipalDTO
    ) {
        val agent = if (request.agent.id != null) {
            onboardAgentService.findOnboardAgentById(request.agent.id)
        } else {
            val agentRequest = request.agent
            val res = onboardAgentService.createAgent(agentRequest)
            res[true]
        }
        val principalAge = getAge(request.dateOfBirth)
        val principalCategory = calculatePrincipalCategory(principalAge)
        val principal = buildPrincipal(request, principalCategory, agent!!)
        val savedPrincipal = onboardPrincipalRepository.save(principal)
        createPrincipalBenefits(savedPrincipal)

        request.applicationFormContextIds?.forEach {
            onboardPrincipalFormIdService.addApplicationFormContextId(savedPrincipal, it)
        }
        request.applicationFormInfoIds?.forEach { 
            onboardPrincipalFormIdService.addApplicationFormInfoId(savedPrincipal, it)
        }
        request.hospitalFormContextIds?.forEach { 
            onboardPrincipalFormIdService.addHospitalFormContextId(savedPrincipal, it)
        }
        request.hospitalFormInfoIds?.forEach { 
            onboardPrincipalFormIdService.addHospitalFormInfoId(savedPrincipal, it)
        }
        request.studentIdContextIds?.forEach { 
            onboardPrincipalFormIdService.addStudentIdContextId(savedPrincipal, it)
        }
        request.studentIdInfoIds?.forEach { 
            onboardPrincipalFormIdService.addStudentIdInfoId(savedPrincipal, it)
        }
    }

    fun createPrincipal(request: OnboardPrincipalDTO, agent: OnboardAgent): OnboardPrincipalResponseDTO {
        val principalAge = getAge(request.dateOfBirth)
        val principalCategory = calculatePrincipalCategory(principalAge)
        val principal = buildPrincipal(request, principalCategory, agent)
        val savedPrincipal = onboardPrincipalRepository.save(principal)

        request.applicationFormContextIds?.forEach {
            onboardPrincipalFormIdService.addApplicationFormContextId(savedPrincipal, it)
        }
        request.applicationFormInfoIds?.forEach { 
            onboardPrincipalFormIdService.addApplicationFormInfoId(savedPrincipal, it)
        }
        request.hospitalFormContextIds?.forEach { 
            onboardPrincipalFormIdService.addHospitalFormContextId(savedPrincipal, it)
        }
        request.hospitalFormInfoIds?.forEach { 
            onboardPrincipalFormIdService.addHospitalFormInfoId(savedPrincipal, it)
        }
        request.studentIdContextIds?.forEach { 
            onboardPrincipalFormIdService.addStudentIdContextId(savedPrincipal, it)
        }
        request.studentIdInfoIds?.forEach { 
            onboardPrincipalFormIdService.addStudentIdInfoId(savedPrincipal, it)
        }

        return savedPrincipal.toResponseDTO()
    }

    fun getPrincipalById(id: Long): OnboardPrincipalResponseDTO {
        val principal = onboardPrincipalRepository.findById(id).orElseThrow {
            EntityNotFoundException("Principal not found!")
        }
        return principal.toResponseDTO()
    }

    fun getAllPrincipals(): List<OnboardPrincipalResponseDTO> {
        val principals = onboardPrincipalRepository.findAll().map { it.toResponseDTO() }
        return principals
    }

    @Transactional
    fun addNewDependants(principalId: Long, request: AddPrincipalDependantsRequestDTO) {
        val principal = onboardPrincipalRepository.findById(principalId).orElseThrow {
            EntityNotFoundException("Principal not found!")
        }

        val sortedDependants = request.dependants.sortedBy { dependantRequest ->
            when (dependantRequest.beneficiaryType) {
                OnboardDependantBeneficiaryType.SPOUSE -> 0
                OnboardDependantBeneficiaryType.CHILD -> 1
            }
        }

        sortedDependants.forEach { dependantRequest ->
            onboardDependantService.createDependant(
                dependantRequest,
                principal
            )
        }

        val extraOutpatientToBePaid = request.dependants.sumOf { it.outpatientPremium }
        val extraInpatientToBePaid = request.dependants.sumOf { it.inpatientPremium }

        val principalBenefits = onboardBenefitService.getAllPrincipalBenefits(principal.id)
        val inpatientBenefit = principalBenefits.find { it.benefitType == OnboardBenefitType.INPATIENT }
        val outpatientBenefit = principalBenefits.find { it.benefitType == OnboardBenefitType.OUTPATIENT }

        if (inpatientBenefit != null && outpatientBenefit != null) {
            val inpatientBenefitUpdateDTO = OnboardBenefitDTO(
                benefitType = OnboardBenefitType.INPATIENT,
                benefitLimit = inpatientBenefit.benefitLimit,
                benefitMonthlyAmount = inpatientBenefit.benefitMonthlyAmount.plus(extraInpatientToBePaid)
            )
            val outpatientBenefitUpdateDTO = OnboardBenefitDTO(
                benefitType = OnboardBenefitType.OUTPATIENT,
                benefitLimit = outpatientBenefit.benefitLimit,
                benefitMonthlyAmount = outpatientBenefit.benefitMonthlyAmount.plus(extraOutpatientToBePaid)
            )
            onboardBenefitService.updateBenefit(inpatientBenefit.id, inpatientBenefitUpdateDTO)
            onboardBenefitService.updateBenefit(outpatientBenefit.id, outpatientBenefitUpdateDTO)
        } else {
            throw IllegalArgumentException("Required benefits not found for principal.")
        }

        request.documents.forEach {
            document -> onboardDocumentService.createOnboardDocument(principal, document)
        }

        request.applicationFormContextIds?.forEach {
            onboardPrincipalFormIdService.addApplicationFormContextId(principal, it)
        }
        request.applicationFormInfoIds?.forEach { 
            onboardPrincipalFormIdService.addApplicationFormInfoId(principal, it)
        }
        request.hospitalFormContextIds?.forEach { 
            onboardPrincipalFormIdService.addHospitalFormContextId(principal, it)
        }
        request.hospitalFormInfoIds?.forEach { 
            onboardPrincipalFormIdService.addHospitalFormInfoId(principal, it)
        }
        request.studentIdContextIds?.forEach { 
            onboardPrincipalFormIdService.addStudentIdContextId(principal, it)
        }
        request.studentIdInfoIds?.forEach { 
            onboardPrincipalFormIdService.addStudentIdInfoId(principal, it)
        }
    }

    @Transactional
    fun addNewBeneficiary(principalId: Long, request: OnboardBeneficiaryDTO) {
        val principal = onboardPrincipalRepository.findById(principalId).orElseThrow {
            EntityNotFoundException("Principal not found!")
        }
        onboardBeneficiaryService.createBeneficiary(request, principal)
    }

    fun countAllPrincipals(): Long {
        return onboardPrincipalRepository.count()
    }

    fun getPrincipalBySearchTerm(searchTerm: String): List<OnboardPrincipalResponseDTO?> {
        return onboardPrincipalRepository.findByFullNameOrGrowerNumber(searchTerm).map {
            it?.toResponseDTO()
        }
    }

    fun filterPrincipals(
        fromDate: LocalDate?,
        toDate: LocalDate?,
        page: Int? = 0,
        size: Int? = 10,
        sort: String? = "createdAt",
        direction: Sort.Direction? = Sort.Direction.DESC
    ): Page<OnboardPrincipalResponseDTO> {
        val pageable = PageRequest.of(page ?: 0, size ?: 0, Sort.by(direction ?: Sort.Direction.DESC, sort))
        return onboardPrincipalRepository.filterPrincipals(fromDate, toDate, pageable).map { it.toResponseDTO() }
    }
}
