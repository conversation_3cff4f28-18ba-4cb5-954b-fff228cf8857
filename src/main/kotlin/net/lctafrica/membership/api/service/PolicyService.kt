package net.lctafrica.membership.api.service

import java.time.LocalDate
import java.util.stream.Collectors
import net.lctafrica.membership.api.domain.PayerPolicyMapping
import net.lctafrica.membership.api.domain.PayerPolicyMappingRepository
import net.lctafrica.membership.api.domain.PayerRepository
import net.lctafrica.membership.api.domain.PlanRepository
import net.lctafrica.membership.api.domain.PlanType
import net.lctafrica.membership.api.domain.Policy
import net.lctafrica.membership.api.domain.PolicyRepository
import net.lctafrica.membership.api.domain.PolicyStatus
import net.lctafrica.membership.api.dtos.AddPayerPolicyMappingDto
import net.lctafrica.membership.api.dtos.PolicyDTO
import net.lctafrica.membership.api.dtos.UpdatePayerPolicyMappingDto
import net.lctafrica.membership.api.util.AppConstants.SAVED_SUCCESSFULLY
import net.lctafrica.membership.api.util.AppConstants.UPDATED_SUCCESSFULLY
import net.lctafrica.membership.api.util.Result
import net.lctafrica.membership.api.util.ResultFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service("policyService")
@Transactional
class PolicyService(
    private val repo: PolicyRepository,
    private val planRepo: PlanRepository,
    private val payerPolicyMappingRepository: PayerPolicyMappingRepository,
    private val validationService: IValidationService, private val payerRepository: PayerRepository
) : IPolicyService {

    @Transactional(readOnly = true)
    override fun findAll(): Result<MutableList<Policy>> {
        return ResultFactory.getSuccessResult(repo.findAll())
    }

    @Transactional(readOnly = true)
    override fun findByPlan(planId: Long): Result<MutableList<Policy>> {
        val plan = planRepo.findById(planId)
        return if (plan.isPresent) {
            val policies = repo.findByPlan(plan.get())
            ResultFactory.getSuccessResult(policies)
        } else {
            ResultFactory.getFailResult("No plan with ID $planId was found")
        }
    }

    override fun findByPayerId(payerId: Long): Result<MutableList<Policy>> {
       val payer = validationService.validateAndGetPayer(payerId)
        val policies = repo.findByPayer(payer)
        policies.map {
            val active = it.endDate.isAfter(LocalDate.now())
            it.status = if (active) PolicyStatus.ACTIVE else PolicyStatus.EXPIRED
        }
        return ResultFactory.getSuccessResult(policies)
    }

    @Transactional(readOnly = false, rollbackFor = [Exception::class])
    override fun addPolicy(dto: PolicyDTO): Result<Policy> {
        try {
            val plan = planRepo.findById(dto.planId)
            return if (plan.isPresent) {

                // Check that there's no previous policy with overlapping dates with this one
                if (plan.get().type == PlanType.SCHEME) {
                    val oldPol = repo.findByPlan(plan.get()).stream()
                        .filter { p -> p.endDate.isAfter(dto.startDate) }
                        .findFirst()
                    if (oldPol.isPresent)
                        return ResultFactory.getFailResult("Policy overlaps with one starting ${oldPol.get().startDate}")
                }
                val payer = dto.policyAdmin?.let { payerRepository.findById(it) }

                val policy = Policy(
                    plan = plan.get(),
                    policyNumber = dto.policyNumber.trim(),
                    startDate = dto.startDate,
                    endDate = dto.endDate,
                    policyAdmin = payer?.get()

                )
                repo.save(policy)
                ResultFactory.getSuccessResult(policy)
            } else {
                ResultFactory.getFailResult("No plan with ID ${dto.planId} was found")
            }
        } catch (ex: IllegalArgumentException) {
            return ResultFactory.getFailResult(ex.message)
        }
    }

    @Transactional(readOnly = false, rollbackFor = [Exception::class])
    override fun processPolicy(policyNumber: String): Result<Policy> {
        TODO("Not yet implemented")
    }

    override fun findById(policyId: Long): Result<Policy> {
        val policy = repo.findById(policyId).get()
        return ResultFactory.getSuccessResult(policy)
    }

    override fun addPayerPolicyMapping(dto: AddPayerPolicyMappingDto): Result<Boolean> {
        val payer = validationService.validateAndGetPayer(dto.payerId)
        val policy = validationService.validateAndGetPolicy(dto.policyId)
        val mapping = payerPolicyMappingRepository.findByPayerAndPolicy(payer, policy)
        val map = if(mapping.isPresent){
            val map = mapping.get()
            map.mappingEnabled = true
            map
        }else{
            PayerPolicyMapping(payer = payer, policy = policy)
        }
        payerPolicyMappingRepository.save(map)
        return ResultFactory.getSuccessResult(SAVED_SUCCESSFULLY)
    }

    override fun updatePayerPolicyMapping(mappingId: Long, dto: UpdatePayerPolicyMappingDto): Result<Boolean> {
        val map =validationService.validateAndGetPayerPolicyMapping(mappingId)
        dto.mappingEnabled?.let {
            map.mappingEnabled = it
        }
        payerPolicyMappingRepository.save(map)
        return ResultFactory.getSuccessResult(UPDATED_SUCCESSFULLY)
    }

    override fun findPoliciesByPayerAndPlan(payerId: Long, planId: Long): Result<MutableList<Policy>> {
        val mappings = payerPolicyMappingRepository.findMappedPoliciesByPayerIdAndPlanId(payerId = payerId, planId = planId)
        val policies = mappings.stream().map { it.policy }.collect(Collectors.toList())
        return ResultFactory.getSuccessResult(policies)
    }
}