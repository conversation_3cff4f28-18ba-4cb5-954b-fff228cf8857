package net.lctafrica.membership.api.service

import net.lctafrica.membership.api.domain.OnboardDependant
import net.lctafrica.membership.api.domain.OnboardDependantRepository
import net.lctafrica.membership.api.domain.OnboardPrincipal
import net.lctafrica.membership.api.dtos.OnboardDependantDTO
import net.lctafrica.membership.api.dtos.OnboardDependantResponseDTO
import net.lctafrica.membership.api.util.toResponseDTO
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.stereotype.Service
import java.time.LocalDate
import javax.persistence.EntityNotFoundException

@Service
class OnboardDependantService(private val onboardDependantRepository: OnboardDependantRepository) {
    fun createDependant(request: OnboardDependantDTO, principal: OnboardPrincipal): OnboardDependantResponseDTO {
        val existingDependants = getDependantsByPrincipalId(principal.id).sortedBy { it.memberNumber }
        val nextAvailableNumber = existingDependants.size + 1
        val nextMemberNumber = "${principal.growerNumber}-0$nextAvailableNumber"

        val dependant = OnboardDependant(
            firstName = request.firstName,
            middleName = request.middleName,
            surname = request.surname,
            gender = request.gender,
            inpatientPremium = request.inpatientPremium,
            outpatientPremium = request.outpatientPremium,
            beneficiaryType = request.beneficiaryType,
            idNumber = request.idNumber,
            memberNumber = nextMemberNumber,
            mobileNumber = request.mobileNumber,
            dateOfBirth = request.dateOfBirth,
            hospital = request.hospital,
            principal = principal
        )
        val savedDependant = onboardDependantRepository.save(dependant)
        return savedDependant.toResponseDTO()
    }

    fun getAllDependants(): List<OnboardDependantResponseDTO> {
        val dependants = onboardDependantRepository.findAll().map { it.toResponseDTO() }
        return dependants
    }

    fun getDependantById(id: Long): OnboardDependantResponseDTO {
        val dependant = onboardDependantRepository.findById(id).orElseThrow {
            throw EntityNotFoundException("Dependant not found!")
        }
        return dependant.toResponseDTO()
    }

    fun getDependantsByPrincipalId(principalId: Long): List<OnboardDependantResponseDTO> {
        val dependants = onboardDependantRepository.findByPrincipalId(principalId).map { it.toResponseDTO() }
        return dependants
    }

    fun countAllDependants(): Long {
        return onboardDependantRepository.count()
    }

    fun filterDependants(
        fromDate: LocalDate?,
        toDate: LocalDate?,
        page: Int? = 0,
        size: Int? = 10,
        sort: String? = "createdAt",
        direction: Sort.Direction? = Sort.Direction.DESC
    ): Page<OnboardDependantResponseDTO> {
        val pageable = PageRequest.of(page ?: 0, size ?: 10, Sort.by(direction ?: Sort.Direction.DESC, sort))
        return onboardDependantRepository.filterDependants(fromDate, toDate, pageable).map { it.toResponseDTO() }
    }
}
