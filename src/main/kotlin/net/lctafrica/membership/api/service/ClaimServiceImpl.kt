package net.lctafrica.membership.api.service

import net.lctafrica.membership.api.dtos.BeneficiaryBenefitChangeResponseDto
import net.lctafrica.membership.api.dtos.BeneficiaryBenefitsDto
import net.lctafrica.membership.api.dtos.EditBeneficiaryInfoDto
import net.lctafrica.membership.api.dtos.MigrateBenefitsDto
import net.lctafrica.membership.api.util.ApiResult
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import org.springframework.web.reactive.function.BodyInserters
import org.springframework.web.reactive.function.client.WebClient
import org.springframework.web.reactive.function.client.awaitBody
import org.springframework.web.reactive.function.client.awaitExchange
import org.springframework.web.reactive.function.client.createExceptionAndAwait

@Service
class ClaimServiceImpl(
    @Qualifier("claimWebClientConfig") val webClient: WebClient
) : IClaimService {

    val appLogger: Logger = LoggerFactory.getLogger(ClaimServiceImpl::class.java)

    override suspend fun migrateBenefits(dto: MigrateBenefitsDto): ApiResult<Boolean> {
        return webClient.post()
            .uri("/api/v1/benefit/migrate")
            .body(BodyInserters.fromValue(dto))
            .awaitExchange { clientResponse ->
                return@awaitExchange if (clientResponse.statusCode() == HttpStatus.OK) {
                    appLogger.info("migrateBenefits OK")
                    clientResponse.awaitBody<ApiResult<Boolean>>()
                } else if (clientResponse.statusCode().is4xxClientError || clientResponse.statusCode().is5xxServerError) {
                    throw Exception("migrateBenefits Failed with Status: ${clientResponse.rawStatusCode()}")
                } else {
                    throw clientResponse.createExceptionAndAwait()
                }
            }
    }


    override suspend fun categoryChange(dto: BeneficiaryBenefitsDto): ApiResult<BeneficiaryBenefitChangeResponseDto> {
        return webClient.post()
            .uri("/api/v1/benefit/category/change")
            .body(BodyInserters.fromValue(dto))
            .awaitExchange { clientResponse ->
                return@awaitExchange if (clientResponse.statusCode() == HttpStatus.OK) {
                    appLogger.info("categoryChange OK")
                    clientResponse.awaitBody<ApiResult<BeneficiaryBenefitChangeResponseDto>>()
                } else if (clientResponse.statusCode().is4xxClientError || clientResponse.statusCode().is5xxServerError) {
                    throw Exception("categoryChange Failed with Status: ${clientResponse.rawStatusCode()}")
                } else {
                    throw clientResponse.createExceptionAndAwait()
                }
            }
    }

    override suspend fun processBenefits(dto: BeneficiaryBenefitsDto): ApiResult<Boolean> {
        return webClient.post()
            .uri("/api/v1/benefit/process")
            .body(BodyInserters.fromValue(dto))
            .awaitExchange { clientResponse ->
                return@awaitExchange if (clientResponse.statusCode() == HttpStatus.OK) {
                    appLogger.info("processBenefits OK")
                    clientResponse.awaitBody<ApiResult<Boolean>>()
                } else if (clientResponse.statusCode().is4xxClientError || clientResponse.statusCode().is5xxServerError) {
                    throw Exception("processBenefits Failed with Status: ${clientResponse.rawStatusCode()}")
                } else {
                    throw clientResponse.createExceptionAndAwait()
                }
            }
    }

    override suspend fun editBeneficiaryInfo(dto: EditBeneficiaryInfoDto): ApiResult<Boolean> {
        return webClient.post()
            .uri("/api/v1/benefit/editBeneficiaryInfo")
            .body(BodyInserters.fromValue(dto))
            .awaitExchange { clientResponse ->
                return@awaitExchange if (clientResponse.statusCode() == HttpStatus.OK) {
                    appLogger.info("editBeneficiaryInfo OK")
                    clientResponse.awaitBody<ApiResult<Boolean>>()
                } else if (clientResponse.statusCode().is4xxClientError || clientResponse.statusCode().is5xxServerError) {
                    throw Exception("editBeneficiaryInfo Failed with Status: ${clientResponse.rawStatusCode()}")
                } else {
                    throw clientResponse.createExceptionAndAwait()
                }
            }
    }
}