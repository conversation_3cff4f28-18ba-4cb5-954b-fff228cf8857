package net.lctafrica.membership.api.service

import net.lctafrica.membership.api.config.NotFoundRequestException
import net.lctafrica.membership.api.domain.ResolveDataRepository
import net.lctafrica.membership.api.dtos.KeycloakUserResolve
import net.lctafrica.membership.api.util.Result
import net.lctafrica.membership.api.util.ResultFactory
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.stereotype.Service

@Service
class KeycloakServiceImpl(
    private val resolveDataRepository: ResolveDataRepository
) : IKeycloakService {
    override fun getPayerKeycloakUsers(payerId: Long, page: Int, size: Int): Result<Page<KeycloakUserResolve>> {
        val request = PageRequest.of(page - 1, size)
        val users = resolveDataRepository.findKeycloakUsers(name = "payerId", value = "$payerId", request)
        if (users.isEmpty) {
            throw NotFoundRequestException("No users found for this payer")
        }
        return ResultFactory.getSuccessResult(users)
    }

    override fun getProviderKeycloakUsers(providerId: Long, page: Int, size: Int): Result<Page<KeycloakUserResolve>> {
        val request = PageRequest.of(page - 1, size)
        val users = resolveDataRepository.findKeycloakUsers(name = "providerId", value = "$providerId", request)
        if (users.isEmpty) {
            throw NotFoundRequestException("No users found for this payer")
        }
        return ResultFactory.getSuccessResult(users)
    }

}