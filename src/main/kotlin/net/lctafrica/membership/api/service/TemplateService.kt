package net.lctafrica.membership.api.service;

import org.apache.poi.ss.usermodel.FillPatternType
import org.apache.poi.ss.usermodel.IndexedColors
import org.apache.poi.ss.usermodel.Row
import org.apache.poi.ss.usermodel.Sheet
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.springframework.stereotype.Service

@Service
class TemplateService {
    /**
     * Generic function to add sample rows to any Excel sheet
     *
     * @param sheet The sheet to add rows to
     * @param startRowIndex The starting row index (after headers)
     * @param data List of rows, where each row is a list of cell values
     */
    private fun addSampleRows(sheet: Sheet, startRowIndex: Int, data: List<List<Any>>) {
        data.forEachIndexed { rowIndex, rowData ->
            val row = sheet.createRow(startRowIndex + rowIndex)
            rowData.forEachIndexed { cellIndex, cellValue ->
                val cell = row.createCell(cellIndex)
                when (cellValue) {
                    is String -> cell.setCellValue(cellValue)
                    is Double -> cell.setCellValue(cellValue)
                    is Int -> cell.setCellValue(cellValue.toDouble())
                    is Boolean -> cell.setCellValue(cellValue)
                    is Long -> cell.setCellValue(cellValue.toDouble())
                    else -> cell.setCellValue(cellValue.toString())
                }
            }
        }
    }

    /**
     * Creates and formats headers for Excel templates
     *
     * @param sheet The sheet to add headers to
     * @param headers Array of header titles
     * @param highlightRequiredFields Whether to highlight required fields (those containing " - required")
     * @return The created header row
     */
    private fun createFormattedHeaders(
        sheet: Sheet,
        headers: Array<String>,
        highlightRequiredFields: Boolean = true
    ): Row {
        val headerRow = sheet.createRow(0)

        headers.forEachIndexed { index, header ->
            headerRow.createCell(index).apply {
                setCellValue(header)
                cellStyle = sheet.workbook.createCellStyle().apply {
                    setFont(sheet.workbook.createFont().apply {
                        bold = true
                    })

                    // apply light yellow background for required fields if enabled
                    val requiredPatterns = listOf(" - required", "(Required)", "*")
                    val isRequiredField = requiredPatterns.any { header.contains(it) }
                    if (highlightRequiredFields && isRequiredField) {
                        fillForegroundColor = IndexedColors.LIGHT_YELLOW.index
                        fillPattern = FillPatternType.SOLID_FOREGROUND
                    }
                }
            }
            sheet.autoSizeColumn(index)
        }

        return headerRow
    }

    fun createMemberAdditionTemplate(): XSSFWorkbook {
        return XSSFWorkbook().apply {
            createSheet("Members").apply {
                val headerRow = createRow(0)
                val headers = arrayOf(
                    "FAMILY NUMBER",
                    "NAME",
                    "DATE OF BIRTH",
                    "SEX",
                    "MEMBER TYPE",
                    "MEMBER NUMBER",
                    "EMAIL",
                    "PHONE NUMBER",
                    "NHIF",
                    "ID NUMBER",
                    "JOIN DATE"
                )

                headers.forEachIndexed { index, header ->
                    headerRow.createCell(index).apply {
                        setCellValue(header)
                        cellStyle = createCellStyle().apply {
                            setFont(createFont().apply {
                                bold = true
                            })
                        }
                    }
                    autoSizeColumn(index)
                }

                val memberData = listOf(
                    listOf(
                        "1",
                        "John Doe",
                        "30/05/1990",
                        "MALE",
                        "PRINCIPAL",
                        "29004309-00",
                        "<EMAIL>",
                        "254106888899",
                        "",
                        "",
                        ""
                    ),
                    listOf(
                        "1",
                        "Jane Doe",
                        "20/09/1995",
                        "FEMALE",
                        "SPOUSE",
                        "29004309-01",
                        "<EMAIL>",
                        "254756848484",
                        "",
                        "",
                        ""
                    ),
                    listOf("1", "Jack Doe", "10/10/2015", "MALE", "CHILD", "29004309-02", "", "", "", "", ""),
                    listOf("1", "Jill Doe", "11/11/2018", "FEMALE", "CHILD", "29004309-03", "", "", "", "", ""),
                    listOf(
                        "2",
                        "Mark Bett",
                        "20/05/1980",
                        "MALE",
                        "PRINCIPAL",
                        "20554489-00",
                        "<EMAIL>",
                        "254780776655",
                        "",
                        "",
                        ""
                    ),
                    listOf("2", "Mary Bett", "09/09/1985", "FEMALE", "SPOUSE", "20554489-01", "", "", "", "", ""),
                    listOf("2", "Milka Bett", "01/01/2010", "FEMALE", "CHILD", "20554489-02", "", "", "", "", ""),
                    listOf(
                        "3",
                        "Joana Maia",
                        "08/08/1998",
                        "FEMALE",
                        "PRINCIPAL",
                        "34233450-00",
                        "<EMAIL>",
                        "254722565656",
                        "",
                        "",
                        ""
                    ),
                    listOf("3", "Lupita Maia", "07/07/2020", "FEMALE", "CHILD", "34233450-01", "", "", "", "", "")
                )

                addSampleRows(this, 1, memberData)
            }
        }
    }

    fun createDeviceUploadTemplate(): XSSFWorkbook {
        return XSSFWorkbook().apply {
            createSheet("Devices").apply {
                val headerRow = createRow(0)
                val headers = arrayOf(
                    "DEVICE_ID - required",
                    "IMEI - required",
                    "MODEL - required",
                    "REGISTERED_BY - required",
                    "DESCRIPTION - optional",
                    "ACCESSORIES - optional"
                )
                headers.forEachIndexed { index, header ->
                    headerRow.createCell(index).apply {
                        setCellValue(header)
                        cellStyle = createCellStyle().apply {
                            setFont(createFont().apply {
                                bold = true
                            })
                            // light yellow background for required fields
                            if (header.contains(" - required")) {
                                fillForegroundColor = IndexedColors.LIGHT_YELLOW.index
                                fillPattern = FillPatternType.SOLID_FOREGROUND
                            }
                        }
                    }
                    autoSizeColumn(index)
                }

                val sampleDeviceData = listOf(
                    listOf(
                        "DEV-001",
                        "123456789012345",
                        "iPhone 13",
                        "admin",
                        "Company device for testing",
                        "Charger, Case"
                    ),
                    listOf(
                        "DEV-002",
                        "987654321098765",
                        "Samsung S22",
                        "supervisor",
                        "Field operations device",
                        "Charger"
                    ),
                )

                addSampleRows(this, 1, sampleDeviceData)
            }
        }
    }

    fun createMemberEditTemplate(): XSSFWorkbook {
        return XSSFWorkbook().apply {
            createSheet("Members").apply {
                val headerRow = createRow(0)
                val headers = arrayOf(
                    "MEMBER NUMBER (Required)",
                    "OTHER NUMBER",
                    "MEMBER NAME",
                    "PHONE NUMBER",
                    "EMAIL",
                    "NHIF NUMBER",
                    "JOIN DATE",
                    "REASON"
                )

                headers.forEachIndexed { index, header ->
                    headerRow.createCell(index).apply {
                        setCellValue(header)
                        cellStyle = createCellStyle().apply {
                            setFont(createFont().apply {
                                bold = true
                            })
                        }
                    }
                    autoSizeColumn(index)
                }
            }
        }
    }

    fun createMemberActivationDeactivationTemplate(): XSSFWorkbook {
        return XSSFWorkbook().apply {
            createSheet("Members").apply {
                val headerRow = createRow(0)
                val headers = arrayOf(
                    "MEMBER NUMBER (Required)",
                    "REASON (Required)"
                )

                createFormattedHeaders(this, headers, false)
            }
        }
    }


    fun createSimUploadTemplate(): XSSFWorkbook {
        return XSSFWorkbook().apply {
            createSheet("SIMs").apply {
                val headerRow = createRow(0)
                val headers = arrayOf(
                    "SIM_NUMBER - required",
                    "REGISTERED_BY - required"
                )

                createFormattedHeaders(this, headers, false)
            }
        }
    }

    fun createProviderBulkUpdateTemplate(): XSSFWorkbook {
        return XSSFWorkbook().apply {
            createSheet("Providers").apply {
                val headers = arrayOf(
                    "Provider ID - required",
                    "Code",
                    "Provider Name"
                )

                createFormattedHeaders(this, headers, true)

                val sampleProviderData = listOf(
                    listOf(1, "PROV001", "City Medical Center"),
                    listOf(2, "PROV002", "Downtown Clinic"),
                    listOf(3, "PROV003", "Westside Hospital")
                )

                addSampleRows(this, 1, sampleProviderData)
            }
        }
    }

    fun createProviderBulkMappingTemplate(): XSSFWorkbook {
        return XSSFWorkbook().apply {
            createSheet("Providers").apply {
                val headers = arrayOf(
                    "Provider ID - required",
                    "Code - required"
                )

                createFormattedHeaders(this, headers, true)

                val sampleProviderData = listOf(
                    listOf(1, "PROV001"),
                    listOf(2, "PROV002"),
                    listOf(3, "PROV003")
                )

                addSampleRows(this, 1, sampleProviderData)
            }
        }
    }
}