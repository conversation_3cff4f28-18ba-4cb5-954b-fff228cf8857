package net.lctafrica.membership.api.service

import net.lctafrica.membership.api.domain.*
import org.springframework.stereotype.Service
import javax.transaction.Transactional

@Service
class OnboardPrincipalFormIdService(
    private val applicationFormContextIdRepository: OnboardPrincipalApplicationFormContextIdRepository,
    private val applicationFormInfoIdRepository: OnboardPrincipalApplicationFormInfoIdRepository,
    private val hospitalFormContextIdRepository: OnboardPrincipalHospitalFormContextIdRepository,
    private val hospitalFormInfoIdRepository: OnboardPrincipalHospitalFormInfoIdRepository,
    private val studentIdContextIdRepository: OnboardPrincipalStudentIdContextIdRepository,
    private val studentIdInfoIdRepository: OnboardPrincipalStudentIdInfoIdRepository
) {
    @Transactional
    fun addApplicationFormContextId(principal: OnboardPrincipal, value: String) {
        val formId = OnboardPrincipalApplicationFormContextId(
            value = value,
            principal = principal
        )
        applicationFormContextIdRepository.save(formId)
    }

    @Transactional
    fun addApplicationFormInfoId(principal: OnboardPrincipal, value: String) {
        val formId = OnboardPrincipalApplicationFormInfoId(
            value = value,
            principal = principal
        )
        applicationFormInfoIdRepository.save(formId)
    }

    @Transactional
    fun addHospitalFormContextId(principal: OnboardPrincipal, value: String) {
        val formId = OnboardPrincipalHospitalFormContextId(
            value = value,
            principal = principal
        )
        hospitalFormContextIdRepository.save(formId)
    }

    @Transactional
    fun addHospitalFormInfoId(principal: OnboardPrincipal, value: String) {
        val formId = OnboardPrincipalHospitalFormInfoId(
            value = value,
            principal = principal
        )
        hospitalFormInfoIdRepository.save(formId)
    }

    @Transactional
    fun addStudentIdContextId(principal: OnboardPrincipal, value: String) {
        val formId = OnboardPrincipalStudentIdContextId(
            value = value,
            principal = principal
        )
        studentIdContextIdRepository.save(formId)
    }

    @Transactional
    fun addStudentIdInfoId(principal: OnboardPrincipal, value: String) {
        val formId = OnboardPrincipalStudentIdInfoId(
            value = value,
            principal = principal
        )
        studentIdInfoIdRepository.save(formId)
    }

    fun getApplicationFormContextIds(principalId: Long): List<String> {
        return applicationFormContextIdRepository.findByPrincipalId(principalId).map { it.value }
    }

    fun getApplicationFormInfoIds(principalId: Long): List<String> {
        return applicationFormInfoIdRepository.findByPrincipalId(principalId).map { it.value }
    }

    fun getHospitalFormContextIds(principalId: Long): List<String> {
        return hospitalFormContextIdRepository.findByPrincipalId(principalId).map { it.value }
    }

    fun getHospitalFormInfoIds(principalId: Long): List<String> {
        return hospitalFormInfoIdRepository.findByPrincipalId(principalId).map { it.value }
    }

    fun getStudentIdContextIds(principalId: Long): List<String> {
        return studentIdContextIdRepository.findByPrincipalId(principalId).map { it.value }
    }

    fun getStudentIdInfoIds(principalId: Long): List<String> {
        return studentIdInfoIdRepository.findByPrincipalId(principalId).map { it.value }
    }
}