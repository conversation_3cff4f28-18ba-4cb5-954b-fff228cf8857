package net.lctafrica.membership.api.service


import net.lctafrica.membership.api.config.NotFoundRequestException
import net.lctafrica.membership.api.domain.*
import net.lctafrica.membership.api.dtos.*
import net.lctafrica.membership.api.util.Result
import net.lctafrica.membership.api.util.ResultFactory
import net.lctafrica.membership.api.util.dbDateFormat
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.core.io.InputStreamResource
import org.springframework.core.io.Resource
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.http.*
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.reactive.function.client.*
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.IOException
import java.time.LocalDate

@Service("dataCollectionService")
@Transactional
class DataCollectionService(
    private val dataCollectionBeneficiaryRepository: DataCollectionBeneficiaryRepository,
    private val dataCollectionDocumentRepository: DataCollectionDocumentRepository,
    @Qualifier("documentWebClientConfig")
    val webClient: WebClient
) : IDataCollectionService {

    var fileHeader = arrayOf(
        "EMPLOYEE NAME",
        "GROWER NUMBER",
        "ID NUMBER",
        "GENDER",
        "TOWN",
        "COUNTY",
        "RELATIONSHIP",
        "MARITAL STATUS",
        "MOBILE NUMBER",
        "AGE",
        "CATEGORY",
        "BENEFIT TYPE",
        "PROVIDER",
        "DATE OF BIRTH",
        "STAFF",
        "BENEFICIARY NAME",
        "BENEFICIARY ID",
    )

    override fun addBeneficiary(dto: DataCollectionBeneficiaryDTO): Result<DataCollectionBeneficiary?> {
        var beneficiary: DataCollectionBeneficiary? = null
        val existingBeneficiaryList = dto.employeeNumber?.trim()
            ?.let { dataCollectionBeneficiaryRepository.findByEmployeeNumberIgnoreCase(it) }

        val dependants = mutableListOf<DataCollectionBeneficiary>()

        if (existingBeneficiaryList?.isNotEmpty() == true) {
            beneficiary = existingBeneficiaryList[0]
            if (dto.dependants?.isNotEmpty() == true) {
                dto.dependants?.forEach {
                    var newGender: Gender? = null
                    var newBenefitType: DataCollectionBenefitType? = null
                    var relationship: DataCollectionRelationship? = DataCollectionRelationship.PRINCIPAL
                    if (!it.dependantGender.isNullOrEmpty()) {
                        newGender = it.dependantGender?.let { Gender.valueOf(it.uppercase()) }
                    }
                    if (!it.dependantBenefitType.isNullOrEmpty()) {
                        newBenefitType =
                            it.dependantBenefitType?.let { DataCollectionBenefitType.valueOf(it.uppercase()) }
                    }
                    if (!it.dependantRelationship.isNullOrEmpty()) {
                        relationship =
                            it.dependantRelationship?.let { DataCollectionRelationship.valueOf(it.uppercase()) }
                    }
                    val dep = DataCollectionBeneficiary(
                        name = it.dependantName,
                        relationship = relationship,
                        dateOfBirth = it.dependantDateOfBirth,
                        gender = newGender,
                        idNumber = it.dependantIdorBirthCertificateNo,
                        benefitType = newBenefitType,
                        principal = beneficiary,
                        createdAt = LocalDate.now(),
                        age = it.age?.toLong(),
                        county = dto.county,
                        town = dto.employeeTown,
                        mobileNumber = if (relationship.toString() == DataCollectionRelationship.SPOUSE.toString()) {
                            it.dependantPhoneNumber
                        } else {
                            dto.employeeMobileNumber
                        },
                        provider = dto.provider,
                        employeeNumber = beneficiary!!.employeeNumber,
                        staffLoggedIn = dto.staff

                    )
                    dataCollectionBeneficiaryRepository.save(dep)
                }
            }
            if (dto.documents?.isNotEmpty() == true) {
                dto.documents?.forEach {
                    val document = DataCollectionDocument(
                        createdAt = LocalDate.now(),
                        fileUrl = it.fileUrl,
                        beneficiaryId = beneficiary!!.id,
                        type = it.type
                    )
                    dataCollectionDocumentRepository.save(document)
                }
            }
            if (dto.benefits?.isNotEmpty() == true) {
                dto.benefits?.forEach {

                    beneficiary?.apply {
                        this.category = it.category
                        this.adultPremiumAmount = it.adultPremiumAmount
                        this.childPremiumAmount = it.childPremiumAmount
                        this.noOfAdults = it.noOfAdults
                        this.noOfChildren = it.noOfChildren
                        this.paymentMode = it.paymentMode?.let { it1 -> DataCollectionPaymentMode.valueOf(it1) }
                        this.totalAdultPremiumAmount = it.totalAdultPremiumAmount
                        this.totalChildPremiumAmount = it.totalChildPremiumAmount
                        this.totalPremiumAmount = it.totalPremiumAmount
                        this.nextOfKin = it.nextOfkinName
                        this.nextOfKinDob = it.nextOfKinDob
                        this.nextOfKinIdNumber = it.nextOfKinIdNumber
                    }
                    dataCollectionBeneficiaryRepository.save(beneficiary!!)
                    val spouseBeneficiaryOpt = dataCollectionBeneficiaryRepository.findSpouse(beneficiary!!.id)
                    if (spouseBeneficiaryOpt.isPresent) {
                        val spouseBeneficiary = spouseBeneficiaryOpt.get()
                        spouseBeneficiary.apply {
                            this.category = it.spouseCategory
                            this.totalAdultPremiumAmount = it.totalSpousePremiumAmount
                            this.adultPremiumAmount = it.spousePremiumAmount
                        }
                        dataCollectionBeneficiaryRepository.save(spouseBeneficiary)
                    }

                }
            }
            return ResultFactory.getSuccessResult(beneficiary)
        } else if (existingBeneficiaryList?.size!! < 1) {
            var newGender: Gender? = null
            var newDependantGender: Gender? = null
            var newPaymentMode: DataCollectionPaymentMode? = DataCollectionPaymentMode.ANNUAL
            var newBenefitType: DataCollectionBenefitType? = null
            var relationship: DataCollectionRelationship? = DataCollectionRelationship.PRINCIPAL
            if (!dto.employeeGender.isNullOrEmpty()) {
                newGender = dto.employeeGender?.let { Gender.valueOf(it.uppercase()) }
            }
            if (!dto.employeeBenefitType.isNullOrEmpty()) {
                newBenefitType = dto.employeeBenefitType?.let { DataCollectionBenefitType.valueOf(it.uppercase()) }
            }
            if (!dto.paymentMode.isNullOrEmpty()) {
                newPaymentMode = dto.paymentMode?.let { DataCollectionPaymentMode.valueOf(it.uppercase()) }
            }
            beneficiary = DataCollectionBeneficiary(
                employeeNumber = dto.employeeNumber,
                policyNumber = dto.policyNumber,
                employeeName = dto.employeeName,
                employeeKrapin = dto.employeeKrapin,
                name = dto.employeeName,
                idNumber = dto.employeeIdNumber,
                dateOfBirth = dto.employeeDateOfBirth,
                mobileNumber = dto.employeeMobileNumber,
                age = dto.age?.toLong(),
                postalCode = dto.employeePostalCode,
                gender = newGender,
                poBox = dto.employeePobox,
                maritalStatus = dto.employeeMaritalStatus,
                town = dto.employeeTown,
                memberType = dto.employeeMembertype,
                benefitType = DataCollectionBenefitType.OUTPATIENT,
                relationship = relationship,
                county = dto.county,
                createdAt = LocalDate.now(),
                principal = null,
                dependants = dependants,
                paymentMode = newPaymentMode,
                category = dto.category,
                provider = dto.provider,
                staffLoggedIn = dto.staff
            )
            beneficiary = dataCollectionBeneficiaryRepository.save(beneficiary)

            if (dto.dependants?.isNotEmpty() == true) {
                dto.dependants?.forEach {
                    if (!it.dependantGender.isNullOrEmpty()) {
                        newDependantGender = it.dependantGender?.let { Gender.valueOf(it.uppercase()) }
                    }
                    if (!it.dependantBenefitType.isNullOrEmpty()) {
                        newBenefitType =
                            it.dependantBenefitType?.let { DataCollectionBenefitType.valueOf(it.uppercase()) }
                    }
                    if (!it.dependantRelationship.isNullOrEmpty()) {
                        relationship =
                            it.dependantRelationship?.let { DataCollectionRelationship.valueOf(it.uppercase()) }
                    }

                    val dep = DataCollectionBeneficiary(
                        name = it.dependantName,
                        relationship = relationship,
                        dateOfBirth = it.dependantDateOfBirth,
                        gender = newDependantGender,
                        idNumber = it.dependantIdorBirthCertificateNo,
                        benefitType = newBenefitType,
                        principal = beneficiary,
                        age = it.age?.toLong(),
                        createdAt = LocalDate.now(),
                        county = dto.county,
                        town = dto.employeeTown,
                        provider = dto.provider,
                        employeeNumber = dto.employeeNumber,
                        staffLoggedIn = dto.staff,
                        mobileNumber = if (relationship.toString() == DataCollectionRelationship.SPOUSE.toString()) {
                            it.dependantPhoneNumber
                        } else {
                            dto.employeeMobileNumber
                        },
                        maritalStatus = if (relationship.toString() == DataCollectionRelationship.SPOUSE.toString()) {
                            dto.employeeMaritalStatus
                        } else {
                            ""
                        }
                    )
                    println(dep)
                    dataCollectionBeneficiaryRepository.save(dep)
                }
            }

            if (dto.documents?.isNotEmpty() == true) {
                dto.documents?.forEach {
                    val document = DataCollectionDocument(
                        createdAt = LocalDate.now(),
                        fileUrl = it.fileUrl,
                        beneficiaryId = beneficiary!!.id,
                        type = it.type
                    )
                    dataCollectionDocumentRepository.save(document)
                }
            }
            if (dto.benefits?.isNotEmpty() == true) {
                dto.benefits?.forEach {

                    beneficiary.apply {
                        this.category = it.category
                        this.adultPremiumAmount = it.adultPremiumAmount
                        this.childPremiumAmount = it.childPremiumAmount
                        this.noOfAdults = it.noOfAdults
                        this.noOfChildren = it.noOfChildren
                        this.paymentMode = it.paymentMode?.let { it1 -> DataCollectionPaymentMode.valueOf(it1) }
                        this.totalAdultPremiumAmount = it.totalAdultPremiumAmount
                        this.totalChildPremiumAmount = it.totalChildPremiumAmount
                        this.totalPremiumAmount = it.totalPremiumAmount

                        this.nextOfKin = it.nextOfkinName
                        this.nextOfKinDob = it.nextOfKinDob
                        this.nextOfKinIdNumber = it.nextOfKinIdNumber
                    }
                    dataCollectionBeneficiaryRepository.save(beneficiary)
                    val spouseBeneficiaryOpt = dataCollectionBeneficiaryRepository.findSpouse(beneficiary.id)
                    if (spouseBeneficiaryOpt.isPresent) {
                        val spouseBeneficiary = spouseBeneficiaryOpt.get()
                        spouseBeneficiary.apply {
                            this.category = it.spouseCategory
                            this.totalAdultPremiumAmount = it.totalSpousePremiumAmount
                            this.adultPremiumAmount = it.spousePremiumAmount
                        }
                        dataCollectionBeneficiaryRepository.save(spouseBeneficiary)
                    }
                }
            }
            return ResultFactory.getSuccessResult(beneficiary)

        } else {
            return ResultFactory.getFailResult(msg = "Failed to add beneficiary")
        }
        //return ResultFactory.getFailResult(msg = "Failed to add beneficiary")
    }

    override fun search(search: String): Result<MutableList<DataCollectionBeneficiary>> {
        val trimmed = search.trim()
        val found = dataCollectionBeneficiaryRepository.search(trimmed)
        return ResultFactory.getSuccessResult(found)
    }

    fun searchForPrincipals(search: String): Result<List<DataCollectionBeneficiary>> {
        val principals = dataCollectionBeneficiaryRepository.findPrincipalsByFullNameOrGrowerNumber(search.trim())
        return ResultFactory.getSuccessResult(principals)
    }

    override fun findFamily(beneficiaryId: Long): Result<MutableList<DataCollectionBeneficiary>> {
        val beneficiaryOpt = dataCollectionBeneficiaryRepository.findById(beneficiaryId)
        val beneficiary = beneficiaryOpt.get()
        val family = mutableListOf<DataCollectionBeneficiary>()
        if (beneficiary.principal == null) {
            family.add(beneficiary)
            family.addAll(beneficiary.dependants)
            return ResultFactory.getSuccessResult(family)
        } else {
            val principal = beneficiary.principal
            val principalOpt = dataCollectionBeneficiaryRepository.findById(principal?.id ?: 0)
            if (principalOpt.isPresent) {
                if (principal != null) {
                    family.add(principal)
                }
                family.addAll(principalOpt.get().dependants)
                return ResultFactory.getSuccessResult(family)
            }
        }
        throw NotFoundRequestException("No Family found by beneficiaryId $beneficiaryId")
    }

    override fun findDependants(beneficiaryId: Long): Result<MutableList<DataCollectionBeneficiary>> {
        val family = mutableListOf<DataCollectionBeneficiary>()
        val principalOpt = dataCollectionBeneficiaryRepository.findById(beneficiaryId)
        if (principalOpt.isPresent) {
            family.addAll(principalOpt.get().dependants)
            return ResultFactory.getSuccessResult(family)
        }
        throw NotFoundRequestException("No Family found by beneficiaryId $beneficiaryId")
    }

    override fun filterBeneficiaries(
        payerId: Long,
        fromDate: String,
        toDate: String,
        reportType: String,
        page: Int,
        size: Int
    ): Result<Page<DataCollectionBeneficiary>?> {
        val request = PageRequest.of(page - 1, size)
        var dependantPage: Page<DataCollectionBeneficiary>? = null
        var principalPage: Page<DataCollectionBeneficiary>? = null
        val filterFromDate = dbDateFormat(fromDate)
        val filterToDate = dbDateFormat(toDate)
        return when (reportType) {
            DataCollectionReportType.DEPENDANTS.toString() -> {
                dependantPage =
                    dataCollectionBeneficiaryRepository.findDependants(filterFromDate, filterToDate, request)

                ResultFactory.getSuccessResult(dependantPage)
            }

            DataCollectionReportType.PRINCIPALS.toString() -> {
                principalPage =
                    dataCollectionBeneficiaryRepository.findPrincipals(filterFromDate, filterToDate, request)
                ResultFactory.getSuccessResult(principalPage)
            }

            else -> {
                val pages = dataCollectionBeneficiaryRepository.findAll(request)
                ResultFactory.getSuccessResult(pages)
            }
        }
    }

    override fun findPrincipalsCount(): Result<Long> {
        return ResultFactory.getSuccessResult(data = dataCollectionBeneficiaryRepository.findPrincipalsCount().get())
    }

    override fun findAllBeneficiariesCount(): Result<Long> {
        return ResultFactory.getSuccessResult(data = dataCollectionBeneficiaryRepository.findAllCount().get())

    }

    override fun exportBeneficiaries(
        payerId: Long,
        fromDate: String,
        toDate: String,
        reportType: DataCollectionReportType,
        page: Int,
        size: Long
    ): ResponseEntity<Resource>? {
        var beneficiaries: MutableList<DataCollectionBeneficiary> = mutableListOf()
        val filterFromDate = dbDateFormat(fromDate)
        val filterToDate = dbDateFormat(toDate)
        beneficiaries = when (reportType) {
            DataCollectionReportType.DEPENDANTS -> {
                dataCollectionBeneficiaryRepository.findDependantReport(filterFromDate, filterToDate)
            }

            DataCollectionReportType.PRINCIPALS -> {
                dataCollectionBeneficiaryRepository.findPrincipalReport(filterFromDate, filterToDate)
            }

            else -> {
                dataCollectionBeneficiaryRepository.findAllBeneficiariesReport(filterFromDate, filterToDate)
            }
        }

        if (beneficiaries.isNotEmpty()) {
            try {
                XSSFWorkbook().use { workbook ->
                    ByteArrayOutputStream().use { out ->
                        val sheet = workbook.createSheet("Beneficiaries")
                        val boldFont = workbook.createFont()
                        boldFont.bold = true
                        val boldStyle = workbook.createCellStyle()
                        boldStyle.setFont(boldFont)
                        val headerRow = sheet.createRow(0)
                        for (col in fileHeader.indices) {
                            val cell = headerRow.createCell(col)
                            cell.setCellValue(fileHeader[col])
                            cell.cellStyle = boldStyle
                            sheet.autoSizeColumn(col)
                        }
                        var rowIdx = 1
                        for (beneficiary in beneficiaries) {
                            val row = sheet.createRow(rowIdx++)
                            row.createCell(0).setCellValue(beneficiary.name)
                            row.createCell(1).setCellValue(beneficiary.employeeNumber)
                            row.createCell(2).setCellValue(beneficiary.idNumber)
                            row.createCell(3).setCellValue(beneficiary.gender?.name)
                            row.createCell(4).setCellValue(beneficiary.town)
                            row.createCell(5).setCellValue(beneficiary.county)
                            row.createCell(6).setCellValue(beneficiary.relationship?.name)
                            row.createCell(7).setCellValue(beneficiary.maritalStatus)
                            row.createCell(8).setCellValue(beneficiary.mobileNumber)
                            row.createCell(9).setCellValue(beneficiary.age?.toString())
                            row.createCell(10).setCellValue(beneficiary.category)
                            row.createCell(11).setCellValue(beneficiary.benefitType?.toString())
                            row.createCell(12).setCellValue(beneficiary.provider)
                            row.createCell(13).setCellValue(beneficiary.dateOfBirth)
                            row.createCell(14).setCellValue(beneficiary.staffLoggedIn)
                            row.createCell(15).setCellValue(beneficiary.nextOfKin)
                            row.createCell(16).setCellValue(beneficiary.nextOfKinIdNumber)
                        }
                        workbook.write(out)
                        val reportName = when (reportType) {
                            DataCollectionReportType.DEPENDANTS -> "Dependants"
                            DataCollectionReportType.PRINCIPALS -> "Principals"
                            else -> "PrincipalsAndDependants"
                        }
                        val filename = "$reportName.xlsx"
                        val file = InputStreamResource(ByteArrayInputStream(out.toByteArray()))

                        return ResponseEntity.ok()
                            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=$filename")
                            .contentType(MediaType.parseMediaType("application/vnd.ms-excel"))
                            .body(file)
                    }
                }
            } catch (e: IOException) {
                throw RuntimeException("Error exporting data to Excel: " + e.message)
            }
        }
        return ResponseEntity.ok().build()
    }

    override suspend fun getDocumentDownloadByDocumentId(id: Long): ResponseEntity<Any> {
        val docRepo = dataCollectionDocumentRepository.getById(id)

        val byteArrRes = webClient
            .get()
            .uri { u ->
                u.queryParam("name", docRepo.fileUrl)
                    .build()
            }
            .accept(MediaType.ALL)
            .awaitExchange { clientResponse: ClientResponse ->
                if (clientResponse.statusCode() == HttpStatus.OK) {
                    return@awaitExchange clientResponse.awaitBody<FileDownloadResponse>().data
                } else {
                    return@awaitExchange clientResponse.createExceptionAndAwait()
                }
            }
        return ResponseEntity.ok()
            .contentType(MediaType.APPLICATION_OCTET_STREAM)
            .header(
                HttpHeaders.CONTENT_DISPOSITION,
                ContentDisposition.inline()
                    .filename("Document.pdf")
                    .build().toString()
            )
            .body(byteArrRes)

    }

    override fun updateBeneficiaryBenefit(beneficiaryId: Long, benefitName: String): Result<DataCollectionBeneficiary> {
        var benefitType: DataCollectionBenefitType? = DataCollectionBenefitType.INPATIENT
        val beneficiary = dataCollectionBeneficiaryRepository.findById(beneficiaryId).orElseThrow {
            NotFoundRequestException("Invalid Beneficiary Id")
        }
        if (benefitName.isNotEmpty()) {
            benefitType = benefitName.let { DataCollectionBenefitType.valueOf(benefitName.uppercase()) }
        }
        beneficiary.apply {
            if (benefitType != null) {
                this.benefitType = DataCollectionBenefitType.valueOf(benefitType.name)
            }
        }
        dataCollectionBeneficiaryRepository.save(beneficiary)
        return ResultFactory.getSuccessResult(msg = "Beneficiary Updated Successfully")

    }
}