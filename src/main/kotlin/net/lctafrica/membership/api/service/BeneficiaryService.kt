package net.lctafrica.membership.api.service

import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import java.util.stream.Collectors
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import net.lctafrica.membership.api.config.BadRequestException
import net.lctafrica.membership.api.config.BatchProcessingException
import net.lctafrica.membership.api.config.InvalidDateException
import net.lctafrica.membership.api.config.InvalidEmailException
import net.lctafrica.membership.api.config.InvalidExcelFormatException
import net.lctafrica.membership.api.config.InvalidMemberDataException
import net.lctafrica.membership.api.config.InvalidPhoneNumberException
import net.lctafrica.membership.api.config.NotFoundRequestException
import net.lctafrica.membership.api.domain.Beneficiary
import net.lctafrica.membership.api.domain.BeneficiaryLink
import net.lctafrica.membership.api.domain.BeneficiaryLinkRepository
import net.lctafrica.membership.api.domain.BeneficiaryRepository
import net.lctafrica.membership.api.domain.BeneficiaryStaging
import net.lctafrica.membership.api.domain.BeneficiaryStagingRepository
import net.lctafrica.membership.api.domain.BeneficiaryType
import net.lctafrica.membership.api.domain.Category
import net.lctafrica.membership.api.domain.CategoryRepository
import net.lctafrica.membership.api.domain.CategoryStatus
import net.lctafrica.membership.api.domain.ChangeLogType
import net.lctafrica.membership.api.domain.Gender
import net.lctafrica.membership.api.domain.MemberStatus
import net.lctafrica.membership.api.domain.PayerProviderMapping
import net.lctafrica.membership.api.domain.PayerProviderMappingRepository
import net.lctafrica.membership.api.domain.PayerRepository
import net.lctafrica.membership.api.domain.PlanDate
import net.lctafrica.membership.api.domain.PlanType
import net.lctafrica.membership.api.domain.PolicyRepository
import net.lctafrica.membership.api.domain.StagingStatus
import net.lctafrica.membership.api.dtos.AddyStagedBeneficiaryDTO
import net.lctafrica.membership.api.dtos.AuditLoggingDTO
import net.lctafrica.membership.api.dtos.BatchBeneficiaryUpdateDTO
import net.lctafrica.membership.api.dtos.BeneficiaryBenefitProcess
import net.lctafrica.membership.api.dtos.BeneficiaryDTO
import net.lctafrica.membership.api.dtos.BeneficiaryInfoToEdit
import net.lctafrica.membership.api.dtos.BeneficiaryQueryDto
import net.lctafrica.membership.api.dtos.BeneficiaryStagedMultiUpdateDto
import net.lctafrica.membership.api.dtos.BeneficiaryStagedUpdateDto
import net.lctafrica.membership.api.dtos.BeneficiaryStagingDTO
import net.lctafrica.membership.api.dtos.BeneficiaryUpdateDTO
import net.lctafrica.membership.api.dtos.BeneficiaryUpdateStatementDateDTO
import net.lctafrica.membership.api.dtos.DeactivateBenefitDTO
import net.lctafrica.membership.api.dtos.EditBeneficiaryInfoDto
import net.lctafrica.membership.api.dtos.LinkCardDto
import net.lctafrica.membership.api.dtos.PayerSearchDTO
import net.lctafrica.membership.api.dtos.ProcessSelectBenefitsDto
import net.lctafrica.membership.api.dtos.UpdateMemberDTO
import net.lctafrica.membership.api.dtos.UpdateProfileDto
import net.lctafrica.membership.api.dtos.UpdateType
import net.lctafrica.membership.api.dtos.eHealtheneficiaryData
import net.lctafrica.membership.api.dtos.eHealtheneficiaryResponseDto
import net.lctafrica.membership.api.dtos.toBeneficiaryDto
import net.lctafrica.membership.api.gson
import net.lctafrica.membership.api.util.AppConstants
import net.lctafrica.membership.api.util.ReadExcelFile
import net.lctafrica.membership.api.util.Result
import net.lctafrica.membership.api.util.ResultFactory
import net.lctafrica.membership.api.util.dateExpired
import net.lctafrica.membership.api.util.emailValidation
import net.lctafrica.membership.api.util.findFamilyNumber
import net.lctafrica.membership.api.util.formatPolicyDate
import net.lctafrica.membership.api.util.phoneValidation
import net.lctafrica.membership.api.util.sanitizeDateCellValue
import net.lctafrica.membership.api.util.sanitizeStringCellValue
import net.lctafrica.membership.api.util.sanitizeStringValue
import net.lctafrica.membership.api.util.toJsonString
import net.lctafrica.membership.api.util.unifyPhoneNumber
import net.lctafrica.membership.api.util.validateDateNotAfter
import net.lctafrica.membership.api.util.validateFullName
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.multipart.MultipartFile
import org.springframework.web.reactive.function.client.WebClient
import reactor.core.publisher.Mono

@Service("beneficiaryService")
@Transactional
class BeneficiaryService(
	private val repo: BeneficiaryRepository,
	private val beneficiaryStagingRepository: BeneficiaryStagingRepository,
	private val policyRepository: PolicyRepository,
	private val categoryRepo: CategoryRepository,
	private val linkRepo: BeneficiaryLinkRepository,
	private val payerRepository: PayerRepository,
	private val payerProviderMappingRepository: PayerProviderMappingRepository,
	private val beneficiaryRepository: BeneficiaryRepository,
	private val restrictionService: IProviderRestrictionService,
	private val validationService: IValidationService,
	private val notificationService: INotificationService,
	private val auditLogService: IAuditLogService,
	private val claimService: IClaimService,
	private val profileService: IProfileService,
	private val xlFileReader: ReadExcelFile
) :
	IBeneficiaryService {

	@Autowired
	lateinit var benefitService:IBenefitService

	@Value("\${lct-africa.benefit.command.base-url}")
	lateinit var benefitUrl: String

	@Value("\${lct-africa.member.url}")
	lateinit var memberSearchUrl: String

	@Transactional(readOnly = true)
	override fun findByCategory(categoryId: Long, page: Int, size: Int): Result<Page<Beneficiary>> {
		val request = PageRequest.of(page - 1, size)
		val category = categoryRepo.findById(categoryId)
		if (category.isEmpty) return ResultFactory.getFailResult("No category with ID $categoryId was found")
		return category.map { repo.findByCategory(it, request) }
			.map {
				ResultFactory.getSuccessResult(it)
			}.get()
	}

	@Transactional(readOnly = true)
	override fun findByNameOrMemberNumber(search: String): Result<MutableList<Beneficiary>> {
		val trimmed = search.trim()
		val found = repo.searchByNameOrMemberNumberAndActive(trimmed)
		found.forEach {
			if ((it.category.policy.plan.type == PlanType.RETAIL || it.category.policy.plan.planDate == PlanDate.MEMBER_JOIN_DATE) && it.joinDate != null) {
				it.category.policy.startDate = it.joinDate!!
				it.category.policy.endDate = it.joinDate!!.plusYears(1).minusDays(1)
			}
		}
		return ResultFactory.getSuccessResult(found)
	}

	@Transactional(readOnly = true)
	override fun searchByNameOrMemberNumber(search: String): Result<MutableList<Beneficiary>> {
		val trimmed = search.trim()
		val found = repo.searchByNameOrMemberNumberAndActive(trimmed)
		found.forEach {
			if ((it.category.policy.plan.type == PlanType.RETAIL || it.category.policy.plan.planDate == PlanDate.MEMBER_JOIN_DATE) && it.joinDate != null) {
				it.category.policy.startDate = it.joinDate!!
				it.category.policy.endDate = it.joinDate!!.plusYears(1).minusDays(1)
			}
		}
		return ResultFactory.getSuccessResult(found)
	}


	override fun findByNameOrMemberNumberActiveUtilizationSms(search: String):
			Result<MutableList<Beneficiary>> {
		val trimmed = search.trim()
		val found = repo.searchByNameOrMemberNumberAndActive(trimmed)
		return ResultFactory.getSuccessResult(found)
	}

	override fun getBeneficiaryByBeneficiaryId(beneficiaryId: Long): Result<Beneficiary> {
		val beneficiaryOpt = repo.searchByBeneficiaryIdAndStatus(beneficiaryId)
		if (beneficiaryOpt.isPresent) {
			return ResultFactory.getSuccessResult(beneficiaryOpt.get())
		} else {
			return ResultFactory.getFailResult("Beneficiary Id $beneficiaryId is Invalid/Inactive")
		}
	}

	override fun findByMemberNumberActive(memberNumber: String): Result<Beneficiary> {
		val trimmed = memberNumber.trim()
		if(trimmed.length > 5) {
			val found = repo.findByMemberNumberAndActive(trimmed)
			if (found.size > 0) {
				val beneficiary: Beneficiary = found[0]
				val endDate = beneficiary.category.policy.endDate

				if (dateExpired(endDate)) {
					beneficiary.restriction = true
					beneficiary.restrictionMsg = "Policy Expired on ${formatPolicyDate(endDate)}"
				}else if(beneficiary.category.status !=CategoryStatus.PROCESSED){
					beneficiary.restriction = true
					beneficiary.restrictionMsg = "Scheme Category ${beneficiary.category.name} is ${beneficiary.category.status.name}"
				}else if(beneficiary.status != MemberStatus.ACTIVE) {
					beneficiary.restriction = true
					beneficiary.restrictionMsg = "This member is ${beneficiary.status?.name}"
				}
				return  ResultFactory.getSuccessResult(data = beneficiary)
			}
		}
		return ResultFactory.getFailResult(msg = "Member not found or Member policy has expired")
	}

	override fun findByNameOrMemberNumberActive(search: String, providerId: Long):
			Result<MutableList<Beneficiary>> {
		val trimmed = search.trim()
		if(trimmed.length > 5) {
			val found = repo.searchByNameOrMemberNumberAndActive(trimmed)
			if (found.size > 0) {
				found.forEach {
					val restrictionRes =
						restrictionService.findRestrictionByCategoryAndProviderId(it.category.id, providerId)
					return if (restrictionRes.success) {
						ResultFactory.getFailResult(
							msg = "Member is not eligible to get services in this facility"
						)

					} else {
						ResultFactory.getSuccessResult(data = found)
					}
				}
			}
		}
		return ResultFactory.getFailResult(msg = "Member not found or Member policy has expired")
	}
	override fun findByNameOrMemberNumberActiveV2(search: String, providerId: Long):
			Result<MutableList<Beneficiary>> {
		val trimmed = search.trim()
		if (trimmed.length > 5) {
			val found = repo.searchByNameOrMemberNumberAndActive(trimmed)
			if (found.size > 0) {
				val beneficiaryListWithRestrictionCheck = mutableListOf<Beneficiary>()
				found.forEach {
					val restrictionRes =
						restrictionService.findRestrictionByCategoryAndProviderId(
							it.category.id,
							providerId
						)
					val endDate = it.category.policy.endDate
					val payer = it.category.benefits.first().payer
					val mapping = payerProviderMappingRepository.findByPayerAndProviderAndStatus(payer = payer,
						provider = validationService.validateAndGetProvider(providerId),
						status = PayerProviderMapping.PayerProviderMappingStatus.ACTIVE)
					if (dateExpired(endDate)) {
						it.restriction = true
						it.restrictionMsg = "Policy Expired on ${formatPolicyDate(endDate)}"
					}else if(it.category.status !=CategoryStatus.PROCESSED){
						it.restriction = true
						it.restrictionMsg = "Scheme Category ${it.category.name} is ${it.category.status.name}"
					}else if(it.status != MemberStatus.ACTIVE){
						it.restriction = true
						it.restrictionMsg = "This member is ${it.status?.name}"
					}else if(!mapping.isPresent){
						it.restriction = true
						it.restrictionMsg = "No Active Payer Provider mapping found"
					}else{
						it.restriction = restrictionRes.success
						it.restrictionMsg = if (restrictionRes.success) {
							"Member is not eligible to get services in this facility"
						} else {
							"Member is eligible to get services in this facility"
						}
					}
					beneficiaryListWithRestrictionCheck.add(it)
				}
				return  ResultFactory.getSuccessResult(data = beneficiaryListWithRestrictionCheck)
			}
		}
		return ResultFactory.getFailResult(msg = "Member not found or Member policy has expired")
	}


	override fun findByMemberNumberAndDateOfBirthAndActive(
		memberNumber: String,
		dateOfBirth: String,
		schemeId: Int
	):
			Result<eHealtheneficiaryResponseDto> {
		try {
			val memberNumber = memberNumber.trim()
			val dateOfBirth = dateOfBirth.trim()


			var data = repo.findBySchemeIdAndActive(memberNumber, schemeId, LocalDateTime.now())
			if (data == null || data.size == 0) {
				data = repo.findByMemberNumberAndDateOfBirthAndActive(memberNumber, dateOfBirth)
			}

			if (data.size > 0) {

				if (data.size > 1) {
					return ResultFactory.getFailResult(msg = "More than one member found. ")
				}

				var record = data[0];
				val filteredData = eHealtheneficiaryResponseDto(
					record.id,
					record.name,
					record.memberNumber,
					record.gender,
					record.beneficiaryType,
					record.phoneNumber,
					record.status,
					record.dob
				)
				return ResultFactory.getSuccessResult(data = filteredData)
			} else {
				return ResultFactory.getFailResult(msg = "Member Not Found. Please ensure that Member Number and Date of Birth are correct.")
			}
		} catch (ex: Exception) {
			println(ex.message)
			return ResultFactory.getFailResult(msg = "An error occurred processing your request ")
		}

		return ResultFactory.getFailResult(msg = "Please try again ")
	}


	override fun findByBeneficiaryIdAndActive(beneficiaryId: Long):
			Result<eHealtheneficiaryData> {
		try {
			val data = repo.findByBeneficiaryIdAndActive(beneficiaryId)

			if (data.size > 0) {

				if (data.size > 1) {
					return ResultFactory.getFailResult(msg = "More than one member found. ")
				}

				var record = data[0];
				val filteredData = eHealtheneficiaryData(
					record.id,
					record.memberNumber,
					record.name,
					record.gender,
					record.beneficiaryType,
					record.phoneNumber,
					record.status,
					record.category.id
				)
				return ResultFactory.getSuccessResult(data = filteredData)
			} else {
				return ResultFactory.getFailResult(msg = "Member Not Found. Please ensure that Member Number and Date of Birth are correct.")
			}
		} catch (ex: Exception) {
			println(ex.message)
			return ResultFactory.getFailResult(msg = "An error occurred processing your request ")
		}

		return ResultFactory.getFailResult(msg = "Please try again ")
	}

	override fun findByPayerIdNameOrMemberNumber(
		payerId: Long,
		search: String
	): Result<MutableList<Beneficiary>> {
		val trimmed = search.trim()
		return if (trimmed.length > 5) {
			val found: MutableList<Beneficiary> =
				repo.searchByPayerIdNameOrMemberNumber(payerId, trimmed)
			val memberNumbers = found.stream().map { it.memberNumber }.distinct().collect(Collectors.toList())
			val memberList = mutableListOf<Beneficiary>()
			memberNumbers.forEach { memberNumber ->
				val uniqueRecordOpt = found.stream().filter { it.memberNumber == memberNumber }.findFirst()
				if(uniqueRecordOpt.isPresent){
					memberList.add(uniqueRecordOpt.get())
				}
			}

			memberList.forEach {
				if ((it.category.policy.plan.type == PlanType.RETAIL || it.category.policy.plan.planDate == PlanDate.MEMBER_JOIN_DATE) && it.joinDate != null) {
					it.category.policy.startDate = it.joinDate!!
					it.category.policy.endDate = it.joinDate!!.plusYears(1).minusDays(1)
				}
			}
			val memberListOrdered = memberList.stream().sorted(Comparator.comparing(Beneficiary::id,Comparator.naturalOrder())).collect(Collectors.toList())
			ResultFactory.getSuccessResult(memberListOrdered)
		} else {
			ResultFactory.getSuccessResult(
				msg = "Enter 3 or more characters to search",
				data = mutableListOf()
			)
		}
	}

	override fun findByPayerIdNameOrMemberNumberMemberInquiryPayer(
		payerId: Long,
		search: String
	): Result<MutableList<Beneficiary>> {
		val trimmed = search.trim()
		val found: MutableList<Beneficiary> =
			repo.searchByPayerIdNameOrMemberNumberFuzzySearch(payerId, trimmed)

		return ResultFactory.getSuccessResult(found)
	}

	override fun findByPayerIdPlanIdNameOrMemberNumber(
		payerId: Long,
		search: String,
		planId: Long
	): Result<MutableList<PayerSearchDTO>> {
		val trimmed = search.trim()
		val found: MutableList<PayerSearchDTO> =
			repo.searchByPayerIdPlanIdNameOrMemberNumber(payerId, trimmed, planId)

		return ResultFactory.getSuccessResult(found)
	}


	@Transactional(readOnly = false, rollbackFor = [Exception::class])
	override fun addBeneficiary(dto: BeneficiaryDTO): Result<Beneficiary> {
		val principalId = dto.principalId
		var principal: Beneficiary? = null
		if (principalId != null && principalId > 0 && dto.beneficiaryType != BeneficiaryType.PRINCIPAL) {
			val optPrincipal = repo.findById(principalId)
			if (optPrincipal.isEmpty) return ResultFactory.getFailResult("No Principal member with ID $principalId was found")
			principal = optPrincipal.get()
		}
		val category = categoryRepo.findById(dto.categoryId)
		if (category.isPresent) {
			val byCategoryAndMemberNumber =
				repo.findByCategoryAndBeneficiary(category.get(), dto.memberNumber);
			if (byCategoryAndMemberNumber.isPresent) return ResultFactory.getFailResult("Member number already exists in the category")
		}
		try {

			return category.map { cat ->
				return@map Beneficiary(
					principal = principal,
					name = dto.name.trim(),
					memberNumber = dto.memberNumber.trim(),
					nhifNumber = dto.nhifNumber,
					dob = dto.dob,
					gender = dto.gender,
					beneficiaryType = dto.beneficiaryType,
					phoneNumber = dto.phoneNumber,
					email = dto.email,
					category = cat,
					processed = false,
					processedTime = null,
					jicEntityId = dto.jicEntityId,
					apaEntityId = dto.apaEntityId,
					joinDate = dto.joinDate
				)
			}.map {
				repo.save(it)
				ResultFactory.getSuccessResult(it)
			}.get()
		} catch (ex: IllegalArgumentException) {
			return ResultFactory.getFailResult(ex.message)
		}
	}

	@Transactional(readOnly = true)
	override fun findPrincipalsByCategory(categoryId: Long): Result<MutableList<Beneficiary>> {
		val category = categoryRepo.findById(categoryId)
		if (category.isEmpty) return ResultFactory.getFailResult("No category with ID $categoryId was found")
		return category.map {
			repo.findPrincipalsByCategory(it)
		}.map { list ->
			return@map ResultFactory.getSuccessResult(list)
		}.get()
	}

	override fun addBeneficiaries(
		categoryId: Long,
		dtos: List<BeneficiaryDTO>
	): Result<MutableList<Beneficiary>> {
		TODO("Not yet implemented")
	}

	override fun removeBeneficiary(beneficiary: Beneficiary): Result<Beneficiary> {
		TODO("Not yet implemented")
	}

	@Transactional(readOnly = true)
	override fun findBeneficiaries(
		phoneNumber: String, beneficiaryType: String, page: Int, size: Int
	): Result<List<BeneficiaryQueryDto>> {
		val request = PageRequest.of(page - 1, size)
		val covers =
			if (beneficiaryType.lowercase() == BeneficiaryType.PRINCIPAL.name.lowercase()) {
				repo.findPrincipalCoversByPhoneNumber(
					phoneNumber,
					BeneficiaryType.PRINCIPAL,
					request
				)
			} else {
				repo.findLinkedDependantCoversByPhoneNumber(phoneNumber, request)
			}
		val list = mutableListOf<BeneficiaryQueryDto>()
		covers.forEach {
			val payers = payerRepository.findPayerByBeneficiaryId(it.id)
			list.add(it.toBeneficiaryDto(payers))
		}
		return ResultFactory.getSuccessResult(list)
	}

	@Transactional(readOnly = true)
	override fun findCoversByPhoneNumber(
		phoneNumber: String, page: Int, size: Int
	): Result<List<BeneficiaryQueryDto>> {
		val pg = page - 1
		val covers = repo.findCoversByPhoneNumber(phoneNumber, pg, size)
		val list = mutableListOf<BeneficiaryQueryDto>()
		covers.forEach {
			val payers = payerRepository.findPayerByBeneficiaryId(it.id)
			if ((it.category.policy.plan.type == PlanType.RETAIL || it.category.policy.plan.planDate == PlanDate.MEMBER_JOIN_DATE) && it.joinDate != null) {
				it.category.policy.startDate = it.joinDate!!
				it.category.policy.endDate = it.joinDate!!.plusYears(1).minusDays(1)
			}
			list.add(it.toBeneficiaryDto(payers))
		}
		return ResultFactory.getSuccessResult(list)
	}

	override suspend fun findCoverBeneficiaries(beneficiaryId: Long): Result<List<Beneficiary>> {
		val principalOpt = repo.findById(beneficiaryId)
		if (!principalOpt.isPresent) {
			return ResultFactory.getFailResult("Invalid Beneficiary Id")
		}
		val principal = principalOpt.get()
		val beneficiaries = mutableListOf<Beneficiary>()
		beneficiaries.add(principal)

		val allDependants = principal.dependants
		val activeDependants = allDependants.filter { it.status == MemberStatus.ACTIVE }

		beneficiaries.addAll(activeDependants)
		return ResultFactory.getSuccessResult(beneficiaries)
	}

	override suspend fun findByCategoryAndMemberNumber(
		categoryId: Long,
		memberNumber: String
	): Result<Beneficiary> {
		val category = validationService.validateAndGetCategory(categoryId)
		val beneficiary = repo.findByCategoryAndBeneficiary(category,memberNumber)
		if(beneficiary.isPresent){
			return ResultFactory.getSuccessResult(beneficiary.get())
		}
		return ResultFactory.getFailResult("No Member found in category ${category.name} with member no $memberNumber")
	}

	override fun findBeneficiaries(ids: Set<Long>): Result<List<Beneficiary>> {
		return ResultFactory.getSuccessResult(repo.findAllById(ids))
	}

	override suspend fun findFamily(beneficiaryId: Long): Result<List<Beneficiary>> {
		val beneficiary = validationService.validateAndGetBeneficiary(beneficiaryId)
		val family = mutableListOf<Beneficiary>()
		if (beneficiary.beneficiaryType == BeneficiaryType.PRINCIPAL) {
			family.add(beneficiary)
			val dependants = beneficiaryRepository.findByPrincipal(beneficiary)
			family.addAll(dependants)
			return ResultFactory.getSuccessResult(family)
		} else {
			val principal = beneficiary.principal
			val principalOpt = beneficiaryRepository.findById(principal?.id ?: 0)
			if (principalOpt.isPresent) {
				if (principal != null) {
					family.add(principal)
				}
				family.addAll(principalOpt.get().dependants)
				return ResultFactory.getSuccessResult(family)
			}
		}
		throw NotFoundRequestException("No Family found by beneficiaryId $beneficiaryId")
	}

	override suspend fun updateBeneficiary(
		beneficiaryId: Long,
		dto: BeneficiaryUpdateDTO
	): Result<Boolean> {
		val beneficiaryOpt = repo.findById(beneficiaryId)

		if (!beneficiaryOpt.isPresent) {
			return ResultFactory.getFailResult("Invalid Beneficiary Id")
		}
		val beneficiaryFromId = beneficiaryOpt.get()
		val beneficiariesToUpdate = mutableListOf<Beneficiary>()
		if(dto.updateType == UpdateType.FAMILY) {
			val family = findFamily(beneficiaryFromId.id)
			family.data?.let {
				beneficiariesToUpdate.addAll(it)
			}
		}else{
			beneficiariesToUpdate.add(beneficiaryFromId)
		}
		var newName:String? = null
		var oldPhoneNumber:String? = null
		var newDob:LocalDate? = null
		var newJoinDate:LocalDate? = null
		var newGender:Gender? = null
		beneficiariesToUpdate.forEach { beneficiary ->
			var logType = ChangeLogType.MEMBER_UPDATE
			var action="Member details updated"
			dto.canUseBiometrics?.let {
				beneficiary.canUseBiometrics = it
			}
			dto.nhifNumber.sanitizeStringValue()?.let {
				beneficiary.nhifNumber = it
			}
			dto.dob?.let {
				beneficiary.dob = it
				newDob = it
			}
			dto.gender?.let {
				beneficiary.gender = it
				newGender = it
			}
			dto.phoneNumber.sanitizeStringValue()?.let { phoneNumber->
				if(phoneValidation(phoneNumber)) {
					if(unifyPhoneNumber(beneficiary.phoneNumber ?:"") != unifyPhoneNumber(phoneNumber)){
						oldPhoneNumber = beneficiary.phoneNumber
						beneficiary.phoneNumber = phoneNumber
					}
				}
			}
			dto.email.sanitizeStringValue()?.let { email ->
				if(emailValidation(email)) {
					beneficiary.email = email
				}
			}
			dto.status?.let {
				if(it != beneficiary.status) {
					action = when(it){
						MemberStatus.ACTIVE -> "Member Activated"
						MemberStatus.SUSPENDED -> "Member Suspended"
						else -> "Member Deactivated"
					}
					beneficiary.status = it
					logType = ChangeLogType.MEMBERSTATUS_UPDATE
				}
			}
			dto.privilege?.let {
				beneficiary.privilege = it
			}
			dto.joinDate?.let {
				beneficiary.joinDate = it
				newJoinDate = it
			}
			dto.name.sanitizeStringValue()?.let {
				beneficiary.name = it
				newName = it
			}
			dto.migrated?.let {
				beneficiary.migrated = it
			}

			dto.otherNumber?.let { otherNumber ->
				val sanitizedOtherNumber = otherNumber.sanitizeStringValue()
				sanitizedOtherNumber?.let {
					val existingBeneficiaries = repo.findByOtherNumberAndIdNot(it, beneficiary.id)
					if (existingBeneficiaries.isNotEmpty()) {
						return ResultFactory.getFailResult("Other Number already exists for another beneficiary")
					}
					beneficiary.otherNumber = it
				}
			}

			repo.save(beneficiary)

			val auditLog = AuditLoggingDTO(
				action = action,
				user = dto.updateBy,
				data = dto.toJsonString(),
				organisation = beneficiary.category.policy.plan.name,
				reason = dto.reason ?: "",
				beneficiary = beneficiary,
				type = logType
			)
			auditLogService.log(auditLog)

			if(!newName.isNullOrBlank() || newGender !=null || newDob !=null || newJoinDate !=null) {
				updateOnClaims(beneficiary,BeneficiaryInfoToEdit(
					memberName = newName,
					dob = newDob,
					joinDate = newJoinDate,
					gender = newGender
				))
			}
			oldPhoneNumber?.let {
				updateProfilePhoneNumber(beneficiary, it)
			}
		}
		return ResultFactory.getSuccessResult("Beneficiary updated successfully")
	}

	override suspend fun updateBeneficiaryStatementDate(
		beneficiaryId: Long,
		dto: BeneficiaryUpdateStatementDateDTO
	): Result<Boolean> {
		val beneficiaryOpt = repo.findById(beneficiaryId)

		if (!beneficiaryOpt.isPresent) {
			return ResultFactory.getFailResult("Invalid Beneficiary Id")
		}
		val beneficiary = beneficiaryOpt.get()
		beneficiary.recentStatementDate  = dto.statementDate
		repo.save(beneficiary)
		return ResultFactory.getSuccessResult("Beneficiary updated successfully")
	}

	private fun updateOnClaims(beneficiary: Beneficiary, beneficiaryInfoToEdit:BeneficiaryInfoToEdit) {
		CoroutineScope(Dispatchers.IO).launch {
			claimService.editBeneficiaryInfo(
				EditBeneficiaryInfoDto(
					beneficiaryId = beneficiary.id,
					categoryId = beneficiary.category.id,
					memberName = beneficiaryInfoToEdit.memberName,
					dob = beneficiaryInfoToEdit.dob,
					joinDate = beneficiaryInfoToEdit.joinDate,
					gender = beneficiaryInfoToEdit.gender,
				)
			)

			if(beneficiaryInfoToEdit.dob !=null || beneficiaryInfoToEdit.joinDate !=null || beneficiaryInfoToEdit.gender !=null){
				benefitService.processBeneficiaryBenefits(beneficiary)
			}

		}
	}

	private fun updateProfilePhoneNumber(beneficiary: Beneficiary, oldPhoneNumber:String) {
		CoroutineScope(Dispatchers.IO).launch {
			profileService.updatePhoneNumber(
				UpdateProfileDto(
					oldPhoneNumber = oldPhoneNumber,
					newPhoneNumber = beneficiary.phoneNumber ?: ""
				)
			)
		}
	}

	override suspend fun beneficiariesStatusUpdate(
		policyId: Long,
		file: MultipartFile,
		updateBy: String,
		emailCallBack: String?,
		reason: String?,
		status: MemberStatus
	): Result<Boolean> {
		xlFileReader.validateExcelFile(file)
		val input = file.inputStream
		val workBook = XSSFWorkbook(input)
		val data = mutableListOf<BatchBeneficiaryUpdateDTO>()
		workBook.map { sheet ->
			val category = validationService.validateAndGetCategoryByPolicyIdAndCategoryName(policyId, sheet.sheetName)
			sheet.rowIterator().forEach { record ->
				if (record.rowNum != 0) {
					if (record.getCell(0) != null && !record.getCell(0).sanitizeStringCellValue().isNullOrBlank()) {
						val memberNumber = record.getCell(0).stringCellValue ?: ""
						val specificReason = record.getCell(1).sanitizeStringCellValue()
						val memberCheck = beneficiaryRepository.findBeneficiaryByCategoryAndMemberNumber(category,memberNumber)
						if(memberCheck.isEmpty()){
							throw NotFoundRequestException("Member number $memberNumber not found in Category ${category.name}")
						}
						val dataRow = BatchBeneficiaryUpdateDTO(
							memberNumber = memberNumber,
							beneficiaryId = memberCheck[0].id,
							status = status,
							reason = specificReason ?: reason)
						data.add(dataRow)
					}
				}
			}
		}
		CoroutineScope(Dispatchers.IO).launch {
			data.forEach { beneficiary ->
				val benDto = BeneficiaryUpdateDTO(
					status = beneficiary.status,
					updateBy = updateBy,
					reason = beneficiary.reason)
				updateBeneficiary(beneficiary.beneficiaryId,benDto)
			}
			val msg = "Beneficiary batch update complete"
			println(msg)
			emailCallBack?.let { email ->
				notificationService.sendEmail("Update Process Complete", msg, email)
			}
		}
		return ResultFactory.getSuccessResult(msg = AppConstants.UPLOADED_SUCCESSFULLY_P)
	}

	override suspend fun updateBeneficiaryBatch(
		policyId: Long,
		file: MultipartFile,
		updateType: UpdateType?,
		updateBy: String,
		reason: String?,
		emailCallBack: String?
	): Result<Boolean> {
		try {
			if (!xlFileReader.isExcelFormat(file)) {
				throw InvalidExcelFormatException("Invalid file format. Please upload an Excel file.")
			}

			val input = file.inputStream
			val workBook = XSSFWorkbook(input)
			val data = mutableListOf<BatchBeneficiaryUpdateDTO>()

			workBook.map { sheet ->
				val category = try {
					validationService.validateAndGetCategoryByPolicyIdAndCategoryName(policyId, sheet.sheetName)
				} catch (e: Exception) {
					throw InvalidMemberDataException("Invalid category in sheet '${sheet.sheetName}': ${e.message}")
				}

				sheet.rowIterator().forEach { record ->
					if (record.rowNum != 0) {
						if (record.getCell(0) != null && !record.getCell(0).sanitizeStringCellValue().isNullOrBlank()) {
							try {
								val memberNumber = record.getCell(0).stringCellValue 
									?: throw InvalidMemberDataException("Member number cannot be empty")
								val otherNumber = record.getCell(1).sanitizeStringCellValue()
								val name = record.getCell(2).sanitizeStringCellValue()
								val phoneNumber = record.getCell(3).sanitizeStringCellValue()
								val email = record.getCell(4).sanitizeStringCellValue()
								val nhifNumber = record.getCell(5).sanitizeStringCellValue()
								val joinDate = record.getCell(6).sanitizeDateCellValue()
								val specificReason = record.getCell(7).sanitizeStringCellValue()

								val theReason = specificReason ?: reason
								if(theReason.isNullOrBlank()){
									throw InvalidMemberDataException("Please enter a reason for updating member $memberNumber")
								}

								val memberCheck = beneficiaryRepository.findBeneficiaryByCategoryAndMemberNumber(category, memberNumber)
								if(memberCheck.isEmpty()){
									throw NotFoundRequestException("Member number $memberNumber not found in Category ${category.name}")
								}

								if(!phoneNumber.isNullOrBlank() && !phoneValidation(phoneNumber)){
									throw InvalidPhoneNumberException("Invalid phone number $phoneNumber for member $memberNumber")
								}

								if(!email.isNullOrBlank() && !emailValidation(email)){
									throw InvalidEmailException("Invalid email address $email for member $memberNumber")
								}

								if(joinDate?.isAfter(LocalDate.now()) == true){
									throw InvalidDateException("Join date cannot be in the future for member $memberNumber")
								}

								val dataRow = BatchBeneficiaryUpdateDTO(
									memberNumber = memberNumber,
									beneficiaryId = memberCheck[0].id,
									name = name,
									phoneNumber = phoneNumber,
									email = email,
									nhifNumber = nhifNumber,
									joinDate = joinDate,
									otherNumber = otherNumber,
									reason = theReason
								)
								data.add(dataRow)
							} catch (e: Exception) {
								when (e) {
									is NotFoundRequestException,
									is InvalidPhoneNumberException,
									is InvalidEmailException,
									is InvalidDateException,
									is InvalidMemberDataException -> throw e
									else -> throw BatchProcessingException("Error processing row ${record.rowNum + 1}: ${e.message}")
								}
							}
						}
					}
				}
			}

			CoroutineScope(Dispatchers.IO).launch {
				try {
					data.forEach { beneficiary ->
						val benDto = BeneficiaryUpdateDTO(
							name = beneficiary.name,
							phoneNumber = beneficiary.phoneNumber,
							email = beneficiary.email,
							nhifNumber = beneficiary.nhifNumber,
							dob = beneficiary.dob,
							gender = beneficiary.gender,
							joinDate = beneficiary.joinDate,
							canUseBiometrics = beneficiary.canUseBiometrics,
							updateType = updateType,
							updateBy = updateBy,
							otherNumber = beneficiary.otherNumber,
							reason = beneficiary.reason
						)
						updateBeneficiary(beneficiary.beneficiaryId, benDto)
					}

					emailCallBack?.let { email ->
						notificationService.sendEmail(
							"Update Process Complete", 
							"Beneficiary batch update complete", 
							email
						)
					}
				} catch (e: Exception) {
					throw BatchProcessingException("Error during batch update: ${e.message}")
				}
			}

			return ResultFactory.getSuccessResult(msg = AppConstants.UPLOADED_SUCCESSFULLY_P)
		} catch (e: Exception) {
			when (e) {
				is InvalidExcelFormatException,
				is InvalidMemberDataException,
				is NotFoundRequestException,
				is InvalidPhoneNumberException,
				is InvalidEmailException,
				is InvalidDateException,
				is BatchProcessingException -> throw e
				else -> throw BatchProcessingException("Unexpected error during batch processing: ${e.message}")
			}
		}
	}

	override fun linkCover(dto: LinkCardDto): Result<BeneficiaryLink> {
		val linkOptional =
			linkRepo.findByPhoneNumberAndBeneficiary_Id(dto.phoneNumber, dto.beneficiaryId)
		if (linkOptional.isPresent) {
			val link = linkOptional.get()
			link.status = dto.link
			link.updatedOn = LocalDateTime.now()
			linkRepo.save(link)
			return ResultFactory.getSuccessResult(link)
		} else {
			val beneficiaryOptional = repo.findById(dto.beneficiaryId)
			if (!beneficiaryOptional.isPresent) {
				return ResultFactory.getFailResult("Invalid Beneficiary Id")
			}
			val link = BeneficiaryLink(
				beneficiary = beneficiaryOptional.get(),
				status = dto.link,
				phoneNumber = dto.phoneNumber
			)
			linkRepo.save(link)
			return ResultFactory.getSuccessResult(link)
		}
	}

	override fun getBeneficiaryLinkedCovers(beneficiaryId: Long): Result<List<BeneficiaryLink>> {
		return ResultFactory.getSuccessResult(
			linkRepo.findByBeneficiary_IdAndStatus(
				beneficiaryId,
				true
			)
		)
	}

	override fun updateMember(dto: UpdateMemberDTO): Result<Beneficiary> {
		val member = repo.findById(dto.id)
		if (member.isEmpty) return ResultFactory.getFailResult(msg = "No member with id ${dto.id}")

		return if (member.isPresent) {
			member.get().apply {
				name = dto.name.toString()
				phoneNumber = dto.phoneNumber
				email = dto.email
				dob = dto.dob!!
				canUseBiometrics = dto.canUseBiometrics
			}
			val savedmember = repo.save(member.get())
			ResultFactory.getSuccessResult(
				data = savedmember, msg = "Member updated " +
						"successfully"
			)
		} else {
			ResultFactory.getFailResult(msg = "No member present")
		}
	}

	override fun activateBeneficiary(beneficiaryId: Long): Result<Beneficiary> {
		val claimsClient = WebClient.builder()
			.baseUrl(benefitUrl).build()
		val member = repo.findById(beneficiaryId)
		if (member.isEmpty) return ResultFactory.getFailResult(msg = "No member with id $beneficiaryId exists")

		return if (member.isPresent) {
			val updatedMember = member.get().apply {
				this.status = MemberStatus.ACTIVE
			}
			claimsClient
				.post()
				.uri { u ->
					u
						.path("/api/v1/visit/activateBenefits/")
						.build()
				}
				.header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
				.body(
					Mono.just(
						gson.toJson(
							DeactivateBenefitDTO(
								beneficiaryId = member.get().id,
								categoryId = member.get().category.id
							)
						)
					),
					String::class.java
				)
				.exchange()
				.doOnSuccess { res ->
					println("-------------------------------")
					println(res)
				}
				.block()!!.toString()
			ResultFactory.getSuccessResult(
				data = repo.save(updatedMember), msg = "Member " +
						"Activated Successfully"
			)
		} else {
			ResultFactory.getFailResult(msg = "No member with id $beneficiaryId exists")
		}

	}

//	override fun deactivateBeneficiary(beneficiaryId: Long): Result<Beneficiary> {
//		val member = repo.findById(beneficiaryId)
//		if (member.isEmpty) return ResultFactory.getFailResult(msg = "No member with id $beneficiaryId exists")
//
//		return if (member.isPresent) {
//
//			val claimsClient = WebClient.builder()
//				.baseUrl(benefitUrl).build()
//			val updatedMember = member.get().apply {
//				this.status = MemberStatus.DEACTIVATED
//			}
//
//			claimsClient
//				.post()
//				.uri { u ->
//					u
//						.path("/api/v1/visit/deactivateBenefits/")
//						.build()
//				}
//				.header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
//				.body(
//					Mono.just(
//						gson.toJson(
//							DeactivateBenefitDTO(
//								beneficiaryId = member.get().id,
//								categoryId = member.get().category.id
//							)
//						)
//					),
//					String::class.java
//				)
//				.exchange()
//				.doOnSuccess { res ->
//					println("-------------------------------")
//					println(res)
//				}
//				.block()!!.toString()
//
//			ResultFactory.getSuccessResult(
//				data = repo.save(updatedMember), msg = "Member " +
//						"Deactivated Successfully"
//			)
//		} else {
//			ResultFactory.getFailResult(msg = "No member with id $beneficiaryId exists")
//		}
//	}

	@Transactional(readOnly = true)
	override fun findByCategoryAndName(
		categoryId: Long,
		beneficiaryName: String,
		page: Int,
		size: Int
	): Result<Page<Beneficiary>> {
		val category = categoryRepo.findById(categoryId)
		if (category.isPresent) {
			val request = PageRequest.of(page - 1, size);
			val benefits =
				repo.findByCategoryAndBeneficiaryName(category.get(), beneficiaryName, request)
			return ResultFactory.getSuccessResult(benefits)
		}
		return ResultFactory.getFailResult("No category with ID $categoryId was found")
	}

	override fun findByPlanIdAndMemberNumber(planId: Long, memberNumber: String): Result<Beneficiary> {
		validationService.validateAndGetPlan(planId)
		val benList = repo.findByPlanIdAndMemberNumber(planId, memberNumber)
		if(benList.isEmpty()){
			return ResultFactory.getFailResult("No Beneficiary found with member number $memberNumber")
		}
		return ResultFactory.getSuccessResult(benList[0])
	}

	override fun getStagedBeneficiaries(policyId: Long, page: Int, size: Int): Result<Page<BeneficiaryStaging>> {
		validationService.validateAndGetPolicy(policyId)
		val request = PageRequest.of(page - 1, size)
		return ResultFactory.getSuccessResult(beneficiaryStagingRepository.filterStagedBeneficiaries(policyId,request))
	}

	override fun updateStagedBeneficiary(stagedId: Long, dto: BeneficiaryStagedUpdateDto): Result<BeneficiaryStaging> {
		val staged = validationService.validateAndGetBeneficiaryStaging(stagedId)
		dto.name?.let {
			if(!validateFullName(it)) {
				throw BadRequestException("Invalid Full Name $it. Name should have a First and Last name")
			}
			staged.name = it
		}
		dto.memberNumber?.let {
			staged.memberNumber = it
		}
		dto.idNumber?.let {
			staged.idNumber = it
		}
		dto.nhifNumber?.let {
			staged.nhifNumber = it
		}
		dto.category?.let {
			validationService.validateAndGetCategoryByPolicyIdAndCategoryName(policyId = staged.policyId ?: 0, categoryName = it)
			staged.category = it
		}
		dto.familyNumber?.let {
			staged.familyNumber = it
		}
		dto.gender?.let {
			staged.gender = it
		}
		dto.memberType?.let {
			staged.memberType = it
		}
		dto.email?.let {
			if(!emailValidation(it)) {
				throw BadRequestException("Invalid Email Address $it")
			}
			staged.email = it
		}
		dto.phone?.let {
			if(!phoneValidation(it)) {
				throw BadRequestException("Invalid Phone Number $it")
			}
			staged.phone = it
		}
		dto.joinDate?.let {
			if(!validateDateNotAfter(it)) {
				throw BadRequestException("Join Date $it cannot be in the future")
			}
			staged.joinDate = it
		}
		dto.dob?.let {
			if(!validateDateNotAfter(it)) {
				throw BadRequestException("Date of Birth $it cannot be in the future")
			}
			staged.dob = it
		}

		dto.stagingStatus?.let {
			staged.status = it
		}

		dto.processBenefits?.let {
			staged.processBenefits = it
		}
		staged.error = null
		beneficiaryStagingRepository.save(staged)

		if(staged.status == StagingStatus.ACTIVATE){
			processStagedBeneficiary(staged)
		}
		return ResultFactory.getSuccessResult(staged)
	}

	override fun multiUpdateStagedBeneficiary(updateList: List<BeneficiaryStagedMultiUpdateDto>): Result<Boolean> {
		CoroutineScope(Dispatchers.IO).launch {
			val beneficiariesToProcess = mutableListOf<BeneficiaryStaging>()
			updateList.forEach { dto ->
				val stagedOpt = beneficiaryStagingRepository.findById(dto.stagedId)
				if(!stagedOpt.isPresent) return@forEach
				val staged = stagedOpt.get()
				var error = ""
				dto.name?.let {
					if (!validateFullName(it)) {
						error += "Invalid Full Name $it. Name should have a First and Last name"
					}
					staged.name = it
				}
				dto.memberNumber?.let {
					staged.memberNumber = it
				}
				dto.idNumber?.let {
					staged.idNumber = it
				}
				dto.nhifNumber?.let {
					staged.nhifNumber = it
				}
				dto.category?.let {
					validationService.validateAndGetCategoryByPolicyIdAndCategoryName(
						policyId = staged.policyId ?: 0,
						categoryName = it
					)
					staged.category = it
				}
				dto.familyNumber?.let {
					staged.familyNumber = it
				}
				dto.gender?.let {
					staged.gender = it
				}
				dto.memberType?.let {
					staged.memberType = it
				}
				dto.email?.let {
					if (!emailValidation(it)) {
						error += "Invalid Email Address $it"
					}
					staged.email = it
				}
				dto.phone?.let {
					if (!phoneValidation(it)) {
						error += "Invalid Phone Number $it"
					}
					staged.phone = it
				}
				dto.joinDate?.let {
					if (!validateDateNotAfter(it)) {
						error += "Join Date $it cannot be in the future"
					}
					staged.joinDate = it
				}
				dto.dob?.let {
					if (!validateDateNotAfter(it)) {
						error += "Date of Birth $it cannot be in the future"
					}
					staged.dob = it
				}

				dto.stagingStatus?.let {
					staged.status = it
				}

				dto.processBenefits?.let {
					staged.processBenefits = it
				}

				staged.error = error.ifBlank { null }
				beneficiaryStagingRepository.save(staged)

				if (staged.status == StagingStatus.ACTIVATE && staged.error.isNullOrBlank()) {
					beneficiariesToProcess.add(staged)
				}
			}

			processPrincipalsThenDependants(beneficiariesToProcess)

		}
		return ResultFactory.getSuccessResult("Update successful")
	}

	override fun addStagedBeneficiary(dtoList: List<BeneficiaryStagingDTO>): Result<Boolean> {
		val categoryIds = dtoList.stream().map { it.categoryId }.distinct().collect(Collectors.toList())
		categoryIds.forEach { categoryId ->
			val category = validationService.validateAndGetCategory(categoryId)
			val endDate = category.policy.endDate
			if (dateExpired(endDate)) { throw BadRequestException("Failed to process. Policy expired on ${formatPolicyDate(endDate)}.") }
		}

		val beneficiariesToProcess = mutableListOf<BeneficiaryStaging>()

		val batchNo = UUID.randomUUID().toString()
		dtoList.forEach { dto ->
			val category = categoryRepo.findById(dto.categoryId).get()
			var error = ""
			if (!validateFullName(dto.name)) {
				error += "Invalid Full Name ${dto.name}. Name should have a First and Last name \n"
			}
			if (dto.memberNumber.isBlank()) {
				error += "Member number cannot be blank \n"
			}
			dto.email?.let {
				if (!emailValidation(it)) {
					error += "Invalid Email Address $it \n"
				}
			}
			dto.phoneNumber?.let {
				if (!phoneValidation(it)) {
					error += "Invalid Phone Number $it \n"
				}
			}
			dto.joinDate?.let {
				if (!validateDateNotAfter(it)) {
					error += "Join Date $it cannot be in the future \n"
				}
			}

			if (!validateDateNotAfter(dto.dob)) {
				error += "Date of Birth ${dto.dob} cannot be in the future \n"
			}
			val addedOpt = beneficiaryStagingRepository.findByMemberNoAndCategoryNameAndPolicyId(memberNo = dto.memberNumber,
				categoryName = category.name, policyId = category.policy.id)
			val stagedBeneficiary = if(addedOpt.isPresent){
				val staged = addedOpt.get()
				staged.processed = false
				staged.familyNumber = findFamilyNumber(dto.memberNumber)
				staged.name = dto.name
				staged.email = dto.email
				staged.phone = dto.phoneNumber
				staged.nhifNumber = dto.nhifNumber
				staged.idNumber = dto.idNumber
				staged.memberType = dto.beneficiaryType
				staged.dob = dto.dob
				staged.joinDate = dto.joinDate
				staged.gender = dto.gender
				staged.status = dto.status
				staged.processBenefits = dto.processBenefits
				staged.error = error.ifBlank { null }
				beneficiaryStagingRepository.save(staged)
			}else {
				val staged = BeneficiaryStaging(
					batchNo = batchNo,
					familyNumber = findFamilyNumber(dto.memberNumber),
					name = dto.name,
					dob = dto.dob,
					gender = dto.gender,
					memberType = dto.beneficiaryType,
					memberNumber = dto.memberNumber,
					email = dto.email,
					phone = dto.phoneNumber,
					nhifNumber = dto.nhifNumber,
					idNumber = dto.idNumber,
					joinDate = dto.joinDate,
					policyId = category.policy.id,
					category = category.name,
					status = dto.status,
					jicEntityId = dto.jicEntityId,
					apaEntityId = dto.apaEntityId,
					processBenefits = dto.processBenefits,
					error = error.ifBlank { null }
				)
				beneficiaryStagingRepository.save(staged)
			}

			if(stagedBeneficiary.status == StagingStatus.ACTIVATE && stagedBeneficiary.error.isNullOrBlank()){
				beneficiariesToProcess.add(stagedBeneficiary)
			}
		}
		processPrincipalsThenDependants(beneficiariesToProcess)
		return ResultFactory.getSuccessResult("Beneficiary addition successful")
	}

	override fun addAStagedBeneficiary(dto: BeneficiaryStagingDTO): Result<Boolean> {
		val category = validationService.validateAndGetCategory(dto.categoryId)
		val endDate = category.policy.endDate
		if (dateExpired(endDate)) { throw BadRequestException("Failed to process. Policy expired on ${formatPolicyDate(endDate)}.") }
		if (!validateFullName(dto.name)) {
			throw BadRequestException("Invalid Full Name ${dto.name}. Name should have a First and Last name")
		}
		if (dto.memberNumber.isBlank()) {
			throw BadRequestException("Member number cannot be blank")
		}
		dto.email?.let {
			if (!emailValidation(it)) {
				throw BadRequestException("Invalid Email Address $it")
			}
		}
		dto.phoneNumber?.let {
			if (!phoneValidation(it)) {
				throw BadRequestException("Invalid Phone Number $it")
			}
		}
		dto.joinDate?.let {
			if (!validateDateNotAfter(it)) {
				throw BadRequestException("Join Date $it cannot be in the future")
			}
		}

		if (!validateDateNotAfter(dto.dob)) {
			throw BadRequestException("Date of Birth ${dto.dob} cannot be in the future")
		}
		val addedOpt = beneficiaryStagingRepository.findByMemberNoAndCategoryNameAndPolicyId(memberNo = dto.memberNumber,
			categoryName = category.name, policyId = category.policy.id)
		val stagedBeneficiary = if(addedOpt.isPresent){
			val staged = addedOpt.get()
			staged.processed = false
			staged.familyNumber = findFamilyNumber(dto.memberNumber)
			staged.name = dto.name
			staged.email = dto.email
			staged.phone = dto.phoneNumber
			staged.nhifNumber = dto.nhifNumber
			staged.idNumber = dto.idNumber
			staged.memberType = dto.beneficiaryType
			staged.dob = dto.dob
			staged.joinDate = dto.joinDate
			staged.gender = dto.gender
			staged.status = dto.status
			staged.processBenefits = dto.processBenefits
			staged.error = null
			beneficiaryStagingRepository.save(staged)
		}else {
			val staged = BeneficiaryStaging(
				batchNo = UUID.randomUUID().toString(),
				familyNumber = findFamilyNumber(dto.memberNumber),
				name = dto.name,
				dob = dto.dob,
				gender = dto.gender,
				memberType = dto.beneficiaryType,
				memberNumber = dto.memberNumber,
				email = dto.email,
				phone = dto.phoneNumber,
				nhifNumber = dto.nhifNumber,
				idNumber = dto.idNumber,
				joinDate = dto.joinDate,
				policyId = category.policy.id,
				category = category.name,
				status = dto.status,
				jicEntityId = dto.jicEntityId,
				apaEntityId = dto.apaEntityId,
				processBenefits = dto.processBenefits,
			)
			beneficiaryStagingRepository.save(staged)
		}
		if(stagedBeneficiary.status == StagingStatus.ACTIVATE){
			processPrincipalsThenDependants(mutableListOf(stagedBeneficiary))
		}
		return ResultFactory.getSuccessResult("Beneficiary addition successful")
	}

	override fun addStagedBeneficiaryV2(dtoList: List<AddyStagedBeneficiaryDTO>): Result<Boolean> {
		val categoryIds = dtoList.stream().map { it.categoryId }.distinct().collect(Collectors.toList())
		categoryIds.forEach { categoryId ->
			val category = validationService.validateAndGetCategory(categoryId)
			val endDate = category.policy.endDate
			if (dateExpired(endDate)) { throw BadRequestException("Failed to process. Policy expired on ${formatPolicyDate(endDate)}.") }
		}

		val beneficiariesToProcess = mutableListOf<BeneficiaryStaging>()

		val batchNo = UUID.randomUUID().toString()
		dtoList.forEach { dto ->
			val category = categoryRepo.findById(dto.categoryId).get()
			var error = ""
			if (!validateFullName(dto.name)) {
				error += "Invalid Full Name ${dto.name}. Name should have a First and Last name \n"
			}
			if (dto.memberNumber.isBlank()) {
				error += "Member number cannot be blank \n"
			}
			dto.email?.let {
				if (!emailValidation(it)) {
					error += "Invalid Email Address $it \n"
				}
			}
			dto.phoneNumber?.let {
				if (!phoneValidation(it)) {
					error += "Invalid Phone Number $it \n"
				}
			}
			dto.joinDate?.let {
				if (!validateDateNotAfter(it)) {
					error += "Join Date $it cannot be in the future \n"
				}
			}

			if (!validateDateNotAfter(dto.dob)) {
				error += "Date of Birth ${dto.dob} cannot be in the future \n"
			}
			val addedOpt = beneficiaryStagingRepository.findByMemberNoAndCategoryNameAndPolicyId(memberNo = dto.memberNumber,
				categoryName = category.name, policyId = category.policy.id)
			val stagedBeneficiary = if(addedOpt.isPresent){
				val staged = addedOpt.get()
				staged.processed = false
				staged.familyNumber = findFamilyNumber(dto.memberNumber)
				staged.name = dto.name
				staged.email = dto.email
				staged.phone = dto.phoneNumber
				staged.nhifNumber = dto.nhifNumber
				staged.idNumber = dto.idNumber
				staged.memberType = dto.beneficiaryType
				staged.dob = dto.dob
				staged.joinDate = dto.joinDate
				staged.gender = dto.gender
				staged.status = StagingStatus.STAGED
				staged.processBenefits = dto.processBenefits
				staged.error = error.ifBlank { null }
				beneficiaryStagingRepository.save(staged)
			}else {
				val staged = BeneficiaryStaging(
					batchNo = batchNo,
					familyNumber = findFamilyNumber(dto.memberNumber),
					name = dto.name,
					dob = dto.dob,
					gender = dto.gender,
					memberType = dto.beneficiaryType,
					memberNumber = dto.memberNumber,
					email = dto.email,
					phone = dto.phoneNumber,
					nhifNumber = dto.nhifNumber,
					idNumber = dto.idNumber,
					joinDate = dto.joinDate,
					policyId = category.policy.id,
					category = category.name,
					status = StagingStatus.STAGED,
					jicEntityId = dto.jicEntityId,
					apaEntityId = dto.apaEntityId,
					processBenefits = dto.processBenefits,
					error = error.ifBlank { null }
				)
				beneficiaryStagingRepository.save(staged)
			}

			if(stagedBeneficiary.status == StagingStatus.ACTIVATE && stagedBeneficiary.error.isNullOrBlank()){
				beneficiariesToProcess.add(stagedBeneficiary)
			}
		}
		processPrincipalsThenDependants(beneficiariesToProcess)
		return ResultFactory.getSuccessResult("Beneficiary addition successful")
	}

	private fun processPrincipalsThenDependants(beneficiariesToProcess: MutableList<BeneficiaryStaging>) {
		val principals = beneficiariesToProcess.stream().filter { it.memberType == BeneficiaryType.PRINCIPAL }.collect(Collectors.toList())
		principals.forEach {
			processStagedBeneficiary(it)
		}
		val dependants = beneficiariesToProcess.stream().filter { it.memberType != BeneficiaryType.PRINCIPAL }.collect(Collectors.toList())
		dependants.forEach {
			processStagedBeneficiary(it)
		}
	}

	override fun processStagedBeneficiary(beneficiaryStaging: BeneficiaryStaging) {
		try {
			val policyOpt = policyRepository.findById(beneficiaryStaging.policyId)
			if (!policyOpt.isPresent) {
				beneficiaryStaging.processed = true
				beneficiaryStaging.error = "Policy with Id ${beneficiaryStaging.policyId} doesn't exist"
				beneficiaryStagingRepository.save(beneficiaryStaging)
			} else {
				val policy = policyOpt.get()
				val category = categoryRepo.searchByPolicyAndName(policy = policy, beneficiaryStaging.category ?: "")
				if (category == null) {
					beneficiaryStaging.processed = true
					beneficiaryStaging.error = "Category ${beneficiaryStaging.category} not found"
					beneficiaryStagingRepository.save(beneficiaryStaging)
				} else {
					val checkBeneficiaryExists = beneficiaryRepository.findBeneficiaryByCategoryAndMemberNumber(
						category,
						beneficiaryStaging.memberNumber
					)
					if (checkBeneficiaryExists.size > 0) {
						if(beneficiaryStaging.status == StagingStatus.ACTIVATE){
							checkBeneficiaryExists.forEach { beneficiary ->
								if(!beneficiary.processed && beneficiary.status == MemberStatus.ACTIVE && beneficiaryStaging.processBenefits == true) {
									processBenefitsAfterBeneficiaryActivation(category, beneficiary)
								}
							}
							beneficiaryStagingRepository.delete(beneficiaryStaging)
						}else {
							beneficiaryStaging.processed = true
							beneficiaryStaging.error = "Beneficiary with Member No. ${beneficiaryStaging.memberNumber} already exists"
							beneficiaryStagingRepository.save(beneficiaryStaging)
						}
					} else {
						if (beneficiaryStaging.memberType == BeneficiaryType.PRINCIPAL) {
							val dependants =
								beneficiaryStagingRepository.findByUnprocessedAndFamilyNumberAndCategoryNotPrincipal(
									familyNumber = beneficiaryStaging.familyNumber ?: "",
									category = beneficiaryStaging.category ?: "", memberType = BeneficiaryType.PRINCIPAL
								)
							val principal = addBeneficiary(category, null, beneficiaryStaging)
							dependants.forEach { dependant ->
								addBeneficiary(category, principal, dependant)
							}
						} else {
							val familyNumber = findFamilyNumber(beneficiaryStaging.memberNumber)
							val principal = beneficiaryRepository.findPrincipalByCategoryAndFamilyNumberLike(
								category,
								familyNumber
							)
							if (principal.isEmpty()) {
								beneficiaryStaging.processed = true
								beneficiaryStaging.error = "No Principal found"
								beneficiaryStagingRepository.save(beneficiaryStaging)
							} else {
								addBeneficiary(category, principal[0], beneficiaryStaging)
							}
						}
					}
				}
			}
		}catch (e:Exception){
			e.printStackTrace()
			println("ProcessStagedBeneficiary: ${e.message}")
		}
	}

	override fun getBeneficiaryCoverPeriods(planId: Long, memberNo: String): Result<MutableList<Beneficiary>> {
		val coversPeriods =beneficiaryRepository.findBeneficiaryCoverPeriods(planId,memberNo)
		return ResultFactory.getSuccessResult(coversPeriods)
	} 

	private fun addBeneficiary(category: Category, principal: Beneficiary?, beneficiary: BeneficiaryStaging) : Beneficiary{
		val newBeneficiary = Beneficiary(
			principal = principal,
			name = beneficiary.name,
			nhifNumber = beneficiary.nhifNumber,
			idNumber = beneficiary.idNumber,
			memberNumber = beneficiary.memberNumber,
			dob = beneficiary.dob!!,
			beneficiaryType = beneficiary.memberType,
			email = beneficiary.email,
			phoneNumber = beneficiary.phone,
			category = category,
			gender = beneficiary.gender,
			processed = false,
			processedTime = null,
			jicEntityId = null,
			apaEntityId = null,
			joinDate = beneficiary.joinDate,
			toRenew = beneficiary.toRenew
		)
		beneficiaryRepository.save(newBeneficiary)
		beneficiaryStagingRepository.delete(beneficiary)

		if(beneficiary.processBenefits == true){
			processBenefitsAfterBeneficiaryActivation(category, newBeneficiary)
		}
		return newBeneficiary
	}

	private fun processBenefitsAfterBeneficiaryActivation(category: Category, newBeneficiary: Beneficiary) {
		val parentBenefits = category.benefits.stream().filter { it.parentBenefit == null }.collect(Collectors.toList())
		val parentBenefitIds = parentBenefits.stream().map { it.id }.collect(Collectors.toSet())
		runBlocking {
			benefitService.processSelectBenefitsInCategory(
				dto = ProcessSelectBenefitsDto(
					benefitIds = parentBenefitIds,
					processSubBenefits = true,
					processBeneficiaries = BeneficiaryBenefitProcess.ONLY_SELECTED_BENEFICIARIES
				),
				beneficiariesList = listOf(newBeneficiary),
				processingCategory = false,
				skipValidation = true
			)
		}
	}

}
