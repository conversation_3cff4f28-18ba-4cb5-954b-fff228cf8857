package net.lctafrica.membership.api.service

import java.time.LocalDateTime
import java.util.stream.Collectors
import net.lctafrica.membership.api.config.BadRequestException
import net.lctafrica.membership.api.config.NotFoundRequestException
import net.lctafrica.membership.api.domain.BeneficiaryBenefitProviderMappingRepo
import net.lctafrica.membership.api.domain.BenefitRestriction
import net.lctafrica.membership.api.domain.BenefitRestrictionMappingRepo
import net.lctafrica.membership.api.domain.BenefitRestrictionProviderSet
import net.lctafrica.membership.api.domain.BenefitRestrictionProviderSetRepo
import net.lctafrica.membership.api.domain.BenefitRestrictionRepo
import net.lctafrica.membership.api.domain.BenefitStatus
import net.lctafrica.membership.api.domain.CategoryStatus
import net.lctafrica.membership.api.domain.MemberStatus
import net.lctafrica.membership.api.domain.Payer
import net.lctafrica.membership.api.domain.PayerProviderMapping
import net.lctafrica.membership.api.domain.PayerProviderMappingRepository
import net.lctafrica.membership.api.domain.PayerRepository
import net.lctafrica.membership.api.domain.Provider
import net.lctafrica.membership.api.domain.ProviderRepository
import net.lctafrica.membership.api.domain.RestrictionType
import net.lctafrica.membership.api.domain.ServiceProviderMappingRepository
import net.lctafrica.membership.api.dtos.AllowedResponseDto
import net.lctafrica.membership.api.dtos.BatchProviderRestrictionDto
import net.lctafrica.membership.api.dtos.BatchProvidersRestrictionDto
import net.lctafrica.membership.api.dtos.BenefitProviderRestrictionCheckDto
import net.lctafrica.membership.api.dtos.BenefitProviderRestrictionResponseDto
import net.lctafrica.membership.api.dtos.CreateRestrictionDto
import net.lctafrica.membership.api.dtos.CustomPaging
import net.lctafrica.membership.api.dtos.PayerProviderWithRestrictionStatusDTO
import net.lctafrica.membership.api.dtos.ProviderRestrictionSetProjectionDTO
import net.lctafrica.membership.api.util.AppConstants
import net.lctafrica.membership.api.util.Result
import net.lctafrica.membership.api.util.ResultFactory
import net.lctafrica.membership.api.util.customPaging
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service("restrictionServiceImpl")
@Transactional
class RestrictionServiceImpl(
    private val benefitRestrictionRepo: BenefitRestrictionRepo,
    private val payerRepository: PayerRepository,
    private val providerRepository: ProviderRepository,
    private val benefitRestrictionProviderSetRepo: BenefitRestrictionProviderSetRepo,
    private val payerProviderMappingRepo: PayerProviderMappingRepository,
    private val beneficiaryBenefitProviderMappingRepo: BeneficiaryBenefitProviderMappingRepo,
    private val serviceProviderMappingRepository: ServiceProviderMappingRepository,
    private val benefitRestrictionMappingRepo: BenefitRestrictionMappingRepo,
    private val validationService: IValidationService
) : IRestrictionService {
    override fun getRestrictions(payerId: Long): Result<MutableList<BenefitRestriction>> {
        val payer = validateAndGetPayer(payerId)
        return ResultFactory.getSuccessResult(benefitRestrictionRepo.findByPayerAndMigrated(payer, false))
    }

    override fun createRestriction(dto: CreateRestrictionDto): Result<Boolean> {
        val payer = validateAndGetPayer(dto.payerId)
        val checkRestrictionExists = benefitRestrictionRepo.findByPayerAndNameAndMigrated(payer, dto.name, false)
        if (checkRestrictionExists.isPresent) {
            throw BadRequestException("Restriction with name ${dto.name} already exists")
        } else {
            val restriction = BenefitRestriction(
                restrictionType = dto.restrictionType,
                name = dto.name,
                payer = payer,
            )
            benefitRestrictionRepo.save(restriction)
        }
        return ResultFactory.getSuccessResult(AppConstants.CREATED_SUCCESSFULLY)
    }

    override fun removeRestriction(restrictionId: Long): Result<Boolean> {
        val restriction = validateAndGetRestriction(restrictionId)
        restriction.migrated = true
        benefitRestrictionRepo.save(restriction)
        return ResultFactory.getSuccessResult(AppConstants.REMOVED_SUCCESSFULLY)
    }

    override fun getActiveProvidersInARestriction(
        restrictionId: Long,
        page: Int,
        size: Int
    ): Result<Page<ProviderRestrictionSetProjectionDTO>> {
        val restriction = validateAndGetRestriction(restrictionId)
        val request = PageRequest.of(page - 1, size)
        return ResultFactory.getSuccessResult(
            benefitRestrictionProviderSetRepo.findByRestrictionAndActive(
                restriction = restriction,
                active = true,
                pageable = request
            )
        )
    }

    override fun getPayerProvidersWithRestrictionCheck(
        restrictionId: Long,
        page: Int,
        size: Int
    ): Result<CustomPaging<PayerProviderWithRestrictionStatusDTO>> {
        val restriction = validateAndGetRestriction(restrictionId)
        val request = PageRequest.of(page - 1, size)
        val payerProviders = payerProviderMappingRepo.findByPayerAndCodeNotNull(restriction.payer, request)
        val providers = payerProviders.stream().map { payerProviderMapping ->
            val checkInRestriction = benefitRestrictionProviderSetRepo.findByRestrictionAndProviderAndActive(
                restriction,
                Provider(id = payerProviderMapping.providerId), true
            )
            val inRestriction = checkInRestriction.isPresent
            var dateAddedToRestriction: LocalDateTime? = null
            if (inRestriction) {
                dateAddedToRestriction = checkInRestriction.get().createDate
            }
            PayerProviderWithRestrictionStatusDTO(
                id = payerProviderMapping.id,
                providerId = payerProviderMapping.providerId,
                providerName = payerProviderMapping.providerName,
                code = payerProviderMapping.code,
                tier = payerProviderMapping.tier,
                region = payerProviderMapping.region,
                country = payerProviderMapping.country,
                inRestriction = inRestriction,
                dateAddedToRestriction = dateAddedToRestriction
            )
        }.sorted(Comparator.comparing(PayerProviderWithRestrictionStatusDTO::inRestriction, Comparator.reverseOrder()))
            .collect(Collectors.toList())
        val data = customPaging(providers, payerProviders)
        return ResultFactory.getSuccessResult(data)
    }

    override fun addProvidersToARestriction(dto: BatchProviderRestrictionDto): Result<Boolean> {
        val restriction = validateAndGetRestriction(dto.restrictionId)
        if (restriction.migrated) {
            throw BadRequestException("Restriction with Id ${dto.restrictionId} is inactive")
        }
        dto.providerIds.forEach {
            val provider = validateAndGetProvider(it)
            val providerSetOpt = benefitRestrictionProviderSetRepo.findByRestrictionAndProvider(restriction, provider)
            val benefitRestrictionProviderSet = if (providerSetOpt.isPresent) {
                providerSetOpt.get().apply {
                    active = true
                }
            } else {
                BenefitRestrictionProviderSet(
                    provider = provider,
                    restriction = restriction,
                    active = true
                )
            }
            benefitRestrictionProviderSetRepo.save(benefitRestrictionProviderSet)
        }
        return ResultFactory.getSuccessResult(AppConstants.ADDED_SUCCESSFULLY)
    }

    override fun removeProvidersFromARestriction(
        restrictionId: Long,
        dto: BatchProvidersRestrictionDto
    ): Result<Boolean> {
        val restriction = validateAndGetRestriction(restrictionId)
        dto.providerIds.forEach {
            val provider = validateAndGetProvider(it)
            val providerSetOpt = benefitRestrictionProviderSetRepo.findByRestrictionAndProviderAndActive(
                restriction,
                provider,
                true
            )
            if (providerSetOpt.isPresent) {
                val providerSet = providerSetOpt.get()
                providerSet.active = false
                benefitRestrictionProviderSetRepo.save(providerSet)
            }
        }
        return ResultFactory.getSuccessResult(AppConstants.REMOVED_SUCCESSFULLY)
    }

    override fun checkProviderRestriction(dto: BenefitProviderRestrictionCheckDto): Result<List<BenefitProviderRestrictionResponseDto>> {
        val benefitsRes = mutableListOf<BenefitProviderRestrictionResponseDto>()
        val beneficiary = validationService.validateAndGetBeneficiary(dto.beneficiaryId)
        dto.benefitIds.forEach { benefitId ->
            val data = BenefitProviderRestrictionResponseDto(
                benefitId = benefitId,
                beneficiaryId = dto.beneficiaryId,
                providerId = dto.providerId
            )
            if(beneficiary.category.status != CategoryStatus.PROCESSED){
                data.restricted = true
                data.message = "Scheme Category ${beneficiary.category.name} is ${beneficiary.category.status.name}"
                benefitsRes.add(data)
                return@forEach
            }
            if(beneficiary.status != MemberStatus.ACTIVE){
                data.restricted = true
                data.message =  "Member is ${beneficiary.status?.name}"
                benefitsRes.add(data)
                return@forEach
            }
            val benefit = validationService.validateAndGetBenefit(benefitId)

            if (benefit.status != BenefitStatus.ACTIVE) {
                data.restricted = true
                data.message = "${benefit.name} benefit is ${benefit.status.name}"
                benefitsRes.add(data)
                return@forEach
            }
            val isMappedToPayer = isProviderMappedToPayer(benefitId, dto.providerId)
            if (!isMappedToPayer) {
                data.restricted = true
                data.message = "No Active Payer Provider mapping found"
                benefitsRes.add(data)
                return@forEach
            }
            val isRestricted = isProviderRestricted(benefitId, dto.providerId)
            if (isRestricted) {
                data.restricted = true
                data.message = "Benefit restricted. An ${benefit.restriction?.restrictionType?.name} restriction exist"
                benefitsRes.add(data)
                return@forEach
            }
            if (benefit.requireBeneficiaryToSelectProvider == true) {
                val isBeneficiaryProviderRestricted =
                    isBeneficiaryProviderRestricted(dto.beneficiaryId, benefitId, dto.providerId)
                if (isBeneficiaryProviderRestricted) {
                    data.restricted = true
                    data.message =
                        "This benefit cannot be utilized at this Facility. Beneficiary Facility not selected or mismatch"
                    benefitsRes.add(data)
                    return@forEach
                }
            }
            //Todo Provider Service mapping check
            benefitsRes.add(data)
        }
        return ResultFactory.getSuccessResult(benefitsRes)
    }

    override fun checkIfProviderIsAllowedToOfferServiceForBenefit(
        benefitId: Long,
        providerId: Long
    ): Result<AllowedResponseDto> {
        val allowed = AllowedResponseDto(
            allowed = isProviderAllowedToOfferServiceForBenefit(benefitId, providerId)
        )
        return ResultFactory.getSuccessResult(allowed)
    }

    private fun validateAndGetPayer(payerId: Long): Payer {
        return payerRepository.findById(payerId).orElseThrow {
            NotFoundRequestException("Invalid Payer Id")
        }
    }

    private fun validateAndGetRestriction(restrictionId: Long): BenefitRestriction {
        return benefitRestrictionRepo.findById(restrictionId).orElseThrow {
            NotFoundRequestException("Invalid restriction Id")
        }
    }

    private fun validateAndGetProvider(providerId: Long): Provider {
        return providerRepository.findById(providerId).orElseThrow {
            NotFoundRequestException("Provider with Id $providerId is invalid")
        }
    }

    private fun isProviderMappedToPayer(benefitId: Long, providerId: Long): Boolean {
        val benefit = validationService.validateAndGetBenefit(benefitId)
        val provider = validationService.validateAndGetProvider(providerId)
        val checkExists = payerProviderMappingRepo.findByPayerAndProviderAndStatus(benefit.payer, provider,PayerProviderMapping.PayerProviderMappingStatus.ACTIVE)
        return checkExists.isPresent
    }

    private fun isProviderRestricted(benefitId: Long, providerId: Long): Boolean {
        val benefit = validationService.validateAndGetBenefit(benefitId)
        if (benefit.restriction == null) {
            return false
        }
        val provider = validationService.validateAndGetProvider(providerId)
        val checkBenefitRestriction = benefitRestrictionRepo.findByIdAndMigrated(benefit.restriction!!.id ?: 0, false)
        if (!checkBenefitRestriction.isPresent) {
            return false
        }

        val payerRestriction = checkBenefitRestriction.get()
        val providerInRestriction = benefitRestrictionProviderSetRepo.findByRestrictionAndProvider(payerRestriction, provider)
        when (payerRestriction.restrictionType) {
            RestrictionType.INCLUSIVE -> {
                if (providerInRestriction.isPresent && providerInRestriction.get().active) {
                    return false
                }
            }

            RestrictionType.EXCLUSIVE -> {
                if (!providerInRestriction.isPresent || !providerInRestriction.get().active) {
                    return false
                }
            }

            else ->{}
        }
        return true
    }

    private fun isBeneficiaryProviderRestricted(beneficiaryId: Long, benefitId: Long, providerId: Long): Boolean {
        val beneficiary = validationService.validateAndGetBeneficiary(beneficiaryId)
        val benefit = validationService.validateAndGetBenefit(benefitId)
        val provider = validationService.validateAndGetProvider(providerId)

        if (benefit.requireBeneficiaryToSelectProvider != null && benefit.requireBeneficiaryToSelectProvider != false) {
            val checkExist = beneficiaryBenefitProviderMappingRepo.findByBeneficiaryAndBenefitAndProvider(
                beneficiary,
                benefit,
                provider
            )
            return !(checkExist.isPresent && checkExist.get().mappingEnabled)
        }
        return true
    }

    private fun isProviderAllowedToOfferServiceForBenefit(benefitId: Long, providerId: Long): Boolean {
        val benefit = validationService.validateAndGetBenefit(benefitId)
        val provider = validationService.validateAndGetProvider(providerId)
        val benRef = benefit.benefitRef

        val mappedServices = serviceProviderMappingRepository.getProviderServiceMappingByProviderAndBenefitCatalog(
            providerId = provider.id ?: 0,
            benefitCatalogId = benRef.id ?: 0
        )
        return mappedServices.isNotEmpty()
    }
}