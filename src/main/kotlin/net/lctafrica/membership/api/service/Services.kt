package net.lctafrica.membership.api.service

import net.lctafrica.membership.api.domain.AuditLog
import net.lctafrica.membership.api.domain.Beneficiary
import net.lctafrica.membership.api.domain.BeneficiaryLink
import net.lctafrica.membership.api.domain.BeneficiaryStaging
import net.lctafrica.membership.api.domain.Benefit
import net.lctafrica.membership.api.domain.BenefitCatalog
import net.lctafrica.membership.api.domain.BenefitProviderMapping
import net.lctafrica.membership.api.domain.BenefitRestriction
import net.lctafrica.membership.api.domain.CardBatch
import net.lctafrica.membership.api.domain.Category
import net.lctafrica.membership.api.domain.CategoryRestriction
import net.lctafrica.membership.api.domain.ConfigCatalog
import net.lctafrica.membership.api.domain.Copayment
import net.lctafrica.membership.api.domain.Country
import net.lctafrica.membership.api.domain.DataCollectionBeneficiary
import net.lctafrica.membership.api.domain.DeviceAllocation
import net.lctafrica.membership.api.domain.DeviceCatalog
import net.lctafrica.membership.api.domain.DeviceManagementAuditLog
import net.lctafrica.membership.api.domain.DeviceModel
import net.lctafrica.membership.api.domain.DeviceSim
import net.lctafrica.membership.api.domain.DeviceStatus
import net.lctafrica.membership.api.domain.ExportFileType
import net.lctafrica.membership.api.domain.MemberStatus
import net.lctafrica.membership.api.domain.Payer
import net.lctafrica.membership.api.domain.PayerBenefitMapping
import net.lctafrica.membership.api.domain.PayerConfig
import net.lctafrica.membership.api.domain.PayerPolicyMapping
import net.lctafrica.membership.api.domain.PayerProviderMapping
import net.lctafrica.membership.api.domain.PayerRegion
import net.lctafrica.membership.api.domain.PayerType
import net.lctafrica.membership.api.domain.Plan
import net.lctafrica.membership.api.domain.Policy
import net.lctafrica.membership.api.domain.Provider
import net.lctafrica.membership.api.domain.ProviderBankAccount
import net.lctafrica.membership.api.domain.ProviderExclusion
import net.lctafrica.membership.api.domain.ProviderTier
import net.lctafrica.membership.api.domain.Region
import net.lctafrica.membership.api.domain.ServiceBenefitMapping
import net.lctafrica.membership.api.domain.ServiceCatalog
import net.lctafrica.membership.api.domain.ServiceGroup
import net.lctafrica.membership.api.domain.ServiceProviderMapping
import net.lctafrica.membership.api.domain.SimStatus
import net.lctafrica.membership.api.domain.StagingStatus
import net.lctafrica.membership.api.domain.Whitelist
import net.lctafrica.membership.api.dtos.*
import net.lctafrica.membership.api.util.ApiResult
import net.lctafrica.membership.api.util.Result
import org.keycloak.representations.idm.UserRepresentation
import org.springframework.core.io.InputStreamResource
import org.springframework.core.io.Resource
import org.springframework.data.domain.Page
import org.springframework.http.ResponseEntity
import org.springframework.web.multipart.MultipartFile

interface IPayerService {
    fun findAll(): Result<MutableList<Payer>>
    fun addPayer(dto: PayerDTO): Result<Payer>
    fun addPayerConfig(dto: PayerConfigDto): Result<PayerConfig>
    fun findByType(type: PayerType): Result<MutableList<Payer>>
    fun findById(payerId: Long): Result<Payer>
    fun findPayersByIds(dto: IdsListDto): Result<List<Payer>>
    fun findByPolicyAdminId(policyAdminId: Long): Result<List<Payer>>
    fun addBenefitMapping(dto: PayerBenefitMappingDTO): Result<PayerBenefitMapping>
    fun findBenefitMapping(payerId: Long): Result<MutableList<PayerBenefitMapping>>
    fun addProviderMapping(dto: PayerProviderMappingDTO): Result<PayerProviderMapping>
    fun updatePayerProviderMappingBatch(dto: List<PayerProviderMappingUpdateDTO>): Result<PayerProviderMapping>
    fun findProviderMapping(payerId: Long, page: Int, size: Int): Result<Page<PayerProviderMapping>>
    fun findPayerProviders(
        payerId: Long,
        filters: FilterPayerProvidersDto,
        page: Int,
        size: Int
    ): Result<Page<Provider>>

    fun exportPayerProvidersPDF(payerId: Long, filters: FilterPayerProvidersDto): ResponseEntity<InputStreamResource>
    fun exportPayerProvidersXLSX(payerId: Long, filters: FilterPayerProvidersDto): ResponseEntity<ByteArray?>?
    fun findPayerMapping(providerId: Long, query: String?, page: Int, size: Int): Result<Page<PayerProviderMapping>>
    fun findBenefitMapping(payerId: Long, page: Int, size: Int): Result<Page<PayerBenefitMapping>>
    fun findAdministrators(): Result<MutableList<Payer>>
    fun findBenefitCode(payerId: String, benefitRef: String): Result<PayerBenefitCodeMappingDTO?>
    fun findPayerProviderMapping(payerId: Long, providerId: Long): Result<PayerProviderMap>
    fun findPayerProviderMappingEntity(payerId: Long, providerId: Long): Result<PayerProviderMapping>
    fun findPayerProviderAndBenefitMappings(
        providerId: Long,
        payerId: Long,
        benefitId: Long,
        categoryId: Long
    ): Result<PayerMappings>

    fun findPayerProviderMappingByPayerRegion(payerRegionId: Long): Result<MutableList<PayerProviderMapping>>
}

interface IPlanService {
    fun findAll(page: Int, size: Int): Result<Page<Plan>>
    fun findSchemes(): Result<MutableList<Plan>>

    //fun findByPayer(payerId: Long): Result<MutableList<Plan>>
    fun addPlan(dto: PlanDTO): Result<Plan>
    fun findByCategory(categoryId: Long): Result<Plan>
    fun findByPayerId(payerId: Long): Result<MutableList<Plan>>
}

interface IPolicyService {
    fun findAll(): Result<MutableList<Policy>>
    fun findByPlan(planId: Long): Result<MutableList<Policy>>
    fun findByPayerId(payerId: Long): Result<MutableList<Policy>>
    fun addPolicy(dto: PolicyDTO): Result<Policy>
    fun processPolicy(policyNumber: String): Result<Policy>
    fun findById(policyId: Long): Result<Policy>
    fun addPayerPolicyMapping(dto: AddPayerPolicyMappingDto): Result<Boolean>
    fun updatePayerPolicyMapping(mappingId: Long, dto: UpdatePayerPolicyMappingDto): Result<Boolean>
    fun findPoliciesByPayerAndPlan(payerId: Long, planId: Long): Result<MutableList<Policy>>
}

interface ICategoryService {
    fun findByPolicy(policyId: Long): Result<MutableList<Category>>
    suspend fun findByPolicyIds(dto: IdsSetDto): Result<MutableList<Category>>
    suspend fun findByPlanIds(dto: IdsSetDto): Result<MutableList<Category>>
    fun removeCategory(categoryId: Long): Result<Category>
    fun addCategory(dto: CategoryDTO): Result<MutableList<Category>>
    fun dependantsUpload(policyId: Long, file: MultipartFile): Result<Boolean>
    fun uploadBeneficiaries(policyId: Long, file: MultipartFile): Result<Boolean>
    fun batchUpload(policyId: Long, file: MultipartFile): Result<MutableMap<String, MutableList<Beneficiary>>>
    fun findCategoryProviderExclusion(categoryId: Long, providerId: Long): Result<Boolean>
    fun findById(categoryId: Long): Result<Category>
    fun findByCategoryIds(dto: IdsListDto): Result<List<Category>>
    fun findPoliciesByCategoryIds(dto: IdsSetDto): Result<List<Policy>>
    fun findByPayerIdAndProviderIds(payerId: Long, dto: IdsSetDto): Result<List<PayerProviderMapping>>
    fun findBenefitsCatalogByPayerId(payerId: Long): Result<List<QueryBenefitCatalog>>


}

interface IBenefitService {
    fun findByCategory(categoryId: Long): Result<MutableList<Benefit>>
    fun findByCategory(categoryId: Long, page: Int, size: Int): Result<Page<Benefit>>
    fun findBenefitsByPayerId(payerId: Long): Result<MutableList<Benefit>>
    fun findProviderAccountsByPayerId(payerId: Long): Result<MutableList<ProviderBankAccount>>
    fun findByCategoryAndName(categoryId: Long, benefitName: String, page: Int, size: Int):
            Result<Page<Benefit>>

    fun findMainBenefitsByCategory(categoryId: Long): Result<MutableList<Benefit>>
    fun addBenefit(dto: BenefitDTO): Result<Benefit>
    fun findBenefit(benefitId: Long): Result<Benefit>
    fun findBenefits(benefitIds: Set<Long>): Result<List<Benefit>>
    fun addMultipleBenefits(dto: NestedBenefitDTO): Result<MutableList<Benefit>>
    fun removeBenefit(benefit: Benefit): Result<Benefit>
    fun processBenefits(categoryId: Long): Result<Benefit>
    fun processSubBenefit(benefitId: Long): Result<Benefit>
    fun processNewMembers(categoryId: Long): Result<Benefit>
    fun processBenefit(benefit: Benefit): Result<Benefit>
    fun changeCategory(dto: ChangeCategoryDTO): Result<Boolean>
    fun updateCategory(categoryId: Long, dto: UpdateCategoryDTO): Result<Boolean>
    fun findPayersByBenefitIds(benefitIds: Collection<Long>): Result<MutableList<BenefitPayerMappingDTO>>
    fun findPlanByBenefitIds(benefitIds: Collection<Long>): Result<MutableList<GetPlanFromBenefitProjectionDTO>>
    fun findByServiceGroups(serviceGroups: Collection<ServiceGroup>): Result<MutableList<FindBenefitsByServiceGroupsProjectionDTO>>
    fun batchBenefitProviderMapping(dto: BatchBenefitPayerProviderMappingDto): Result<Boolean>
    fun batchBenefitProviderUnmapping(benefitId: Long, dto: BatchBenefitProviderUnmappingDto): Result<Boolean>
    fun benefitProviderMapping(dto: BenefitProviderMappingDto): Result<Boolean>
    fun beneficiaryBenefitProviderMapping(dto: BeneficiaryBenefitProviderMappingDto): Result<Boolean>
    fun getPayerBenefitProviderMappings(benefitId: Long, page: Int, size: Int): Result<Page<BenefitProviderMapping>>
    fun getBeneficiaryBenefitProviderMapping(
        beneficiaryId: Long,
        benefitId: Long
    ): Result<MutableList<BeneficiaryProviderMappingProjectionDTO>>

    suspend fun updateCategoriesStatus(dto: CategoryStatusUpdateDTO): Result<Boolean>
    suspend fun updateBenefitsStatus(dto: BenefitStatusUpdateDTO): Result<Boolean>
    suspend fun updateBeneficiariesStatus(dto: BeneficiaryStatusUpdateDTO): Result<Boolean>
    suspend fun categoryChange(beneficiaryId: Long, dto: CategoryChangeDto): Result<Boolean>
    suspend fun beneficiariesCategoryChange(dto: BeneficiariesCategoryChangeDto): Result<Boolean>
    suspend fun processBenefitsInCategory(dto: ProcessCategoryDto): Result<Boolean>
    suspend fun processSelectBenefitsInCategory(
        dto: ProcessSelectBenefitsDto,
        beneficiariesList: List<Beneficiary> = mutableListOf(),
        processingCategory: Boolean = false,
        skipValidation: Boolean = false
    ): Result<Boolean>

    suspend fun processBeneficiaryBenefits(beneficiary: Beneficiary): Result<Boolean>
    suspend fun addBenefitPayerAdmin(dto: AddBenefitPayerAdminDto): Result<Boolean>
    suspend fun getSchemesAdministeredByPayerOrPayerAdmin(payerId: Long): Result<MutableList<BenefitAdminPlansProjectionDTO>>
}

interface IBeneficiaryService {
    fun findByCategory(categoryId: Long, page: Int, size: Int): Result<Page<Beneficiary>>
    fun findByNameOrMemberNumber(search: String): Result<MutableList<Beneficiary>>
    fun searchByNameOrMemberNumber(search: String): Result<MutableList<Beneficiary>>
    fun findByMemberNumberActive(memberNumber: String): Result<Beneficiary>
    fun findByNameOrMemberNumberActive(search: String, providerId: Long): Result<MutableList<Beneficiary>>
    fun findByNameOrMemberNumberActiveV2(search: String, providerId: Long):
            Result<MutableList<Beneficiary>>

    fun findByMemberNumberAndDateOfBirthAndActive(
        memberNumber: String,
        dateOfBirth: String,
        schemeId: Int
    ): Result<eHealtheneficiaryResponseDto>

    fun findByBeneficiaryIdAndActive(beneficiaryId: Long): Result<eHealtheneficiaryData>
    fun findByNameOrMemberNumberActiveUtilizationSms(search: String): Result<MutableList<Beneficiary>>
    fun getBeneficiaryByBeneficiaryId(beneficiaryId: Long): Result<Beneficiary>

    //    fun findByNameOrMemberNumberActive(search: String): Result<MutableList<Beneficiary>>
    fun findByPayerIdNameOrMemberNumber(payerId: Long, search: String): Result<MutableList<Beneficiary>>
    fun findByPayerIdNameOrMemberNumberMemberInquiryPayer(payerId: Long, search: String):
            Result<MutableList<Beneficiary>>

    fun findByPayerIdPlanIdNameOrMemberNumber(payerId: Long, search: String, planId: Long):
            Result<MutableList<PayerSearchDTO>>

    fun addBeneficiary(dto: BeneficiaryDTO): Result<Beneficiary>
    fun addBeneficiaries(categoryId: Long, dtos: List<BeneficiaryDTO>): Result<MutableList<Beneficiary>>
    fun removeBeneficiary(beneficiary: Beneficiary): Result<Beneficiary>
    fun findPrincipalsByCategory(categoryId: Long): Result<MutableList<Beneficiary>>
    fun findBeneficiaries(
        phoneNumber: String,
        beneficiaryType: String,
        page: Int,
        size: Int
    ): Result<List<BeneficiaryQueryDto>>

    fun findCoversByPhoneNumber(phoneNumber: String, page: Int, size: Int): Result<List<BeneficiaryQueryDto>>
    suspend fun findCoverBeneficiaries(beneficiaryId: Long): Result<List<Beneficiary>>
    suspend fun findByCategoryAndMemberNumber(categoryId: Long, memberNumber: String): Result<Beneficiary>
    fun findBeneficiaries(ids: Set<Long>): Result<List<Beneficiary>>
    suspend fun findFamily(beneficiaryId: Long): Result<List<Beneficiary>>
    suspend fun updateBeneficiary(beneficiaryId: Long, dto: BeneficiaryUpdateDTO): Result<Boolean>
    suspend fun updateBeneficiaryStatementDate(beneficiaryId: Long, dto: BeneficiaryUpdateStatementDateDTO): Result<Boolean>
    suspend fun beneficiariesStatusUpdate(
        policyId: Long,
        file: MultipartFile,
        updateBy: String,
        emailCallBack: String?,
        reason: String?,
        status: MemberStatus
    ): Result<Boolean>

    suspend fun updateBeneficiaryBatch(
        policyId: Long,
        file: MultipartFile,
        updateType: UpdateType?,
        updateBy: String,
        reason: String?,
        emailCallBack: String?
    ): Result<Boolean>

    fun linkCover(dto: LinkCardDto): Result<BeneficiaryLink>
    fun getBeneficiaryLinkedCovers(beneficiaryId: Long): Result<List<BeneficiaryLink>>
    fun updateMember(dto: UpdateMemberDTO): Result<Beneficiary>
    fun activateBeneficiary(beneficiaryId: Long): Result<Beneficiary>
//    fun deactivateBeneficiary(beneficiaryId: Long): Result<Beneficiary>
    fun findByCategoryAndName(
        categoryId: Long,
        beneficiaryName: String,
        page: Int,
        size: Int
    ): Result<Page<Beneficiary>>

    fun findByPlanIdAndMemberNumber(planId: Long, memberNumber: String): Result<Beneficiary>
    fun getStagedBeneficiaries(policyId: Long, page: Int, size: Int): Result<Page<BeneficiaryStaging>>
    fun updateStagedBeneficiary(stagedId: Long, dto: BeneficiaryStagedUpdateDto): Result<BeneficiaryStaging>
    fun multiUpdateStagedBeneficiary(updateList: List<BeneficiaryStagedMultiUpdateDto>): Result<Boolean>
    fun addStagedBeneficiary(dtoList: List<BeneficiaryStagingDTO>): Result<Boolean>
    fun addAStagedBeneficiary(dto: BeneficiaryStagingDTO): Result<Boolean>
    fun addStagedBeneficiaryV2(dtoList: List<AddyStagedBeneficiaryDTO>): Result<Boolean>
    fun processStagedBeneficiary(beneficiaryStaging: BeneficiaryStaging)
    fun getBeneficiaryCoverPeriods(planId: Long, memberNo: String): Result<MutableList<Beneficiary>>
}

interface IBenefitCatalogService {
    fun addBenefitCatalog(dto: BenefitCatalogDTO): Result<BenefitCatalog>
    fun searchByName(search: String, page: Int, size: Int): Result<Page<BenefitCatalog>>
    fun getAllCatalogBenefits(page: Int, size: Int): Result<Page<BenefitCatalog>>
    fun batchAddition(services: List<BenefitCatalogDTO>): Result<MutableList<BenefitCatalog>>
    fun findByServiceGroup(serviceGroup: ServiceGroup, page: Int, size: Int): Result<Page<BenefitCatalog>>
}

interface ICountryService {
    fun addCountry(dto: CountryDTO): Result<Country>
    fun findAllCountries(): Result<MutableList<Country>>
    fun addRegion(dto: RegionDTO): Result<Region>
    fun findRegionByCountry(countryId: Long, page: Int, size: Int): Result<Page<Region>>
    fun findRegionById(regionId: Long): Result<Region>
}

interface IProviderService {
    fun findById(providerId: Long): Result<Provider>
    suspend fun findByIds(dto: IdsListDto): Result<List<Provider>>
    suspend fun findBranchesByProviderIds(dto: IdsSetDto): Result<List<Provider>>
    fun findProviderBranchesById(providerId: Long): Result<MutableList<BranchesDTO?>>
    fun searchProviders(
        searchType: SearchType,
        providerName: String?,
        mainFacilityId: Long?,
        page: Int,
        size: Int
    ): Result<Page<Provider>>

    fun addProvider(dto: ProviderDTO): Result<Provider>
    fun findByTier(tier: ProviderTier, page: Int, size: Int): Result<Page<Provider>>
    fun findByName(name: String, page: Int, size: Int): Result<Page<Provider>>
    fun findAllProviders(page: Int, size: Int): Result<Page<Provider>>
    fun findByRegion(regionId: Long, page: Int, size: Int): Result<Page<Provider>>
    fun findWhiteListByProvider(providerId: Long, page: Int, size: Int): Result<Page<Whitelist>>
    fun findWhiteListByBenefit(benefitId: Long, page: Int, size: Int): Result<Page<Whitelist>>
    fun addWhitelist(dto: WhiteListDTO): Result<Whitelist>
    fun addMultipleMappings(providerId: Long, dto: MultipleWhiteListDTO): Result<MutableList<Whitelist>>
    fun saveProvidersFromFile(file: MultipartFile): Result<List<Provider>>
    fun saveProviderMappingFromFile(file: MultipartFile): Result<List<PayerProviderMapping>>
    fun updateProviderMappingFromFile(file: MultipartFile): Result<List<PayerProviderMapping>>
    fun getNearbyProviders(
        beneficiaryId: Long,
        latitude: Double,
        longitude: Double,
        radius: Double,
        page: Int,
        size: Int
    ): Result<List<NearbyProvidersDTO>>

    fun updateProvider(providerId: Long, dto: ProviderUpdateDTO): Result<Boolean>
    fun multiProviderUpdate(dto: MultiProviderUpdateDTO): Result<Boolean>
    fun getProvidersForCategoryFilteredByRestriction(
        payerId: Long,
        categoryId: Long,
        page: Int,
        size: Int
    ): Result<Page<PayerProviderMapping>>

    fun getProvidersForBenefitFilteredByRestriction(
        benefitId: Long,
        page: Int,
        size: Int
    ): Result<CustomPaging<PayerProviderMappingProjectionDTO>>

    fun getProvidersForBenefitFilteredByRestrictionAndRegion(
        benefitId: Long,
        regionId: Long,
        page: Int,
        size: Int
    ): Result<List<PayerProviderMappingProjectionDTO>>

    fun searchMappedProvidersForABenefit(
        benefitId: Long,
        regionId: Long,
        facilityName: String,
        page: Int,
        size: Int
    ): Result<Page<BenefitProviderMappingProjectionDTO>>

    fun getProvidersByPayer(
        payerId: Long,
        providerName: String?,
        page: Int,
        size: Int
    ): Result<Page<PayerProviderMappingProjectionDTO>>

    fun getProviderAccounts(providerId: Long): Result<List<ProviderBankAccount>>
    fun getProviderAccount(accountId: Long): Result<ProviderBankAccount>
    suspend fun createProviderAccount(dto: CreateProviderAccountDto): Result<Boolean>
    suspend fun createPayerMappedProviderAccount(dto: CreateMappedProviderAccountDto): Result<Boolean>
    suspend fun updateProviderAccount(accountId: Long, dto: UpdateProviderAccountDto): Result<Boolean>
    suspend fun mapProviderAccountToPayer(dto: MapProviderAccountToPayerDto): Result<Boolean>
    suspend fun mapProviderUsersToPayer(dto: MapProviderUserToPayerDto): Result<Boolean>

}

interface IProviderRestrictionService {
    fun addExclusion(dto: ExclusionDTO): Result<ProviderExclusion>
    fun addRestriction(dto: RestrictionDTO): Result<CategoryRestriction>
    fun findRestrictionByCategory(categoryId: Long): Result<MutableList<CategoryRestriction>>
    fun deactivate(restrictionDTO: RemoveRestrictionDTO): Result<CategoryRestriction>
    fun process(category: Category): Result<MutableList<ProviderExclusion>>
    fun findExclusionsByProvider(providerId: Long): Result<MutableList<ProviderExclusion>>
    fun findExclusionsByCategory(categoryId: Long, page: Int, size: Int): Result<Page<ProviderExclusion>>
    fun findExclusionsByCategoryAndProvider(categoryId: Long, providerId: Long): Result<Boolean>
    fun findRestrictionByCategoryAndProviderId(categoryId: Long, providerId: Long): Result<Boolean?>
}

interface ICopaySetupService {
    fun addCopay(dto: CopayDTO): Result<Copayment>
    fun deactivate(copayment: Copayment): Result<Copayment>
    fun findByProvider(providerId: Long): Result<MutableList<Copayment>>
    fun findByCategory(categoryId: Long): Result<MutableList<Copayment>>
    fun process(category: Category): Result<MutableList<Copayment>>
    fun findByCategoryAndProvider(categoryId: Long, providerId: Long): Result<Copayment?>
}

interface ICardRequestService {
    fun newRequest(dto: CardDTO): Result<CardBatch>
    fun issueCardBatch(batchId: Long): Result<CardBatch>
    fun findByMember(memberNumber: String): Result<List<CardBatch>>
}

interface IJubileeService {
    fun findPolicyIdAndDate(categoryId: Long, memberNumber: String): Result<JubileeResponseMemberDTO?>
    fun findProviderCode(providerId: Long, payerId: Long): Result<JubileePayerProviderCodeMappingDTO>
    fun findBenefit(benefitId: Long, policyId: Long): Result<JubileeResponseBenefitNameDTO?>
    fun findBenefit(subBenefitId: Long): Result<JubileeResponseBenefitNameDTO?>
}

interface IUserManagementService {
    fun addCashier(dto: CashierUserDTO): Result<String>
    fun addCreditControlCashier(dto: CashierUserDTO): Result<String>
    fun addPayerAdmin(dto: PayerUserDTO): Result<PayerUserDTO>
    fun addAdminUser(dto: AdminUserDTO): Result<AdminUserDTO>
    fun getProviderUsers(providerId: Long): List<UserRepresentation?>?
    fun getPayerUsers(providerId: Long): List<UserRepresentation?>?
    fun resetPassword(username: String): List<UserRepresentation?>?
    fun addCreditControlRole(username: String): List<UserRepresentation?>?
    fun savePayerAdminRole(dto: UpdatedRolesDTO): Result<String?>?
    fun getUserRoles(username: String): Result<MutableList<String>>
    fun addGroup(dto:AddKeycloakGroupDto):Result<String>
    fun updatePayerUserInfo(dto: PayerUserUpdateDTO): Result<UserRepresentation>
    fun updateUserStatus(dto: UserStatusUpdateDTO): Result<UserRepresentation>
    fun requestPasswordReset(dto: PasswordResetRequestDTO): Result<PasswordResetResponseDTO>
}

interface IAuditLogService {
    fun saveLog(auditLogDTO: AuditLogDTO): Result<AuditLog>
    suspend fun categoryAuditLog(auditLogDTO: MembershipAuditLogDTO): Result<AuditLog>
    suspend fun benefitAuditLog(auditLogDTO: MembershipAuditLogDTO): Result<AuditLog>
    suspend fun beneficiaryAuditLog(auditLogDTO: MembershipAuditLogDTO): Result<AuditLog>
    suspend fun log(dto: AuditLoggingDTO): Result<AuditLog>
    suspend fun getAuditLogs(page:Int,size:Int,dto: AuditLogFilter): Result<Page<AuditLog>>
}

interface IDeviceManagementService {
    suspend fun searchDevice(
        deviceId: String?,
        imei: String?,
        deviceStatus: DeviceStatus?,
        page: Int,
        size: Int
    ): Result<Page<DeviceCatalogResponseDto>>

    suspend fun searchSIM(simNumber: String?, simStatus: SimStatus?, page: Int, size: Int): Result<Page<DeviceSim>>
    suspend fun getDeviceModels(page: Int, size: Int): Result<Page<DeviceModel>>
    suspend fun addDeviceModel(dto: AddDeviceModel): Result<Boolean>
    suspend fun addSIM(dto: AddSIM): Result<Boolean>
    suspend fun getAllSim(page: Int, size: Int): Result<Page<DeviceSim>>
    suspend fun getAllDevicesInCatalog(page: Int, size: Int): Result<Page<DeviceCatalogResponseDto>>
    suspend fun getUnallocatedDevices(page: Int, size: Int): Result<Page<DeviceCatalog>>
    suspend fun registerDevice(dto: DeviceRegistrationRequestDTO): Result<Boolean>
    suspend fun allocateBatch(dto: AllocateDeviceRequestDTO): Result<Boolean>
    suspend fun allocateDevice(dto: AllocateProviderDeviceSimAndAccessory): Result<Boolean>
    suspend fun getAllocation(providerId: Long, deviceId: String): Result<DeviceAllocation>
    suspend fun allocateDeviceAndSim(dto: AllocateDeviceAndSIMRequestDTO): Result<DeviceAllocation>
    suspend fun uploadDocuments(dto: SupportingDocumentRequest): Result<Boolean>
    suspend fun batchUploadDevice(file: MultipartFile, emailCallBack: String?): Result<Boolean>
    suspend fun batchUploadSim(file: MultipartFile, emailCallBack: String?): Result<Boolean>
    suspend fun deviceAuditTrail(log: DeviceManagementAuditLog)
    suspend fun getDevicesAllocatedToAProvider(providerId: Long, page: Int, size: Int): Result<Page<DeviceAllocation>>
    suspend fun getDevicesAllocations(page: Int, size: Int): Result<Page<DeviceAllocation>>
    suspend fun updateDeviceStatus(deviceId: Long, dto: DeviceStatusUpdate): Result<Boolean>
    suspend fun updateSIMStatus(simCatalogId: Long, dto: SIMStatusUpdate): Result<Boolean>
    suspend fun updateAllocationStatus(allocationId: Long, dto: AllocationStatusUpdate): Result<Boolean>
    suspend fun allocateDeviceToProvider(dto: ProviderDeviceAllocation): Result<Boolean>
}

interface IRestrictionService {
    fun getRestrictions(payerId: Long): Result<MutableList<BenefitRestriction>>
    fun createRestriction(dto: CreateRestrictionDto): Result<Boolean>
    fun removeRestriction(restrictionId: Long): Result<Boolean>

    fun getActiveProvidersInARestriction(
        restrictionId: Long,
        page: Int,
        size: Int
    ): Result<Page<ProviderRestrictionSetProjectionDTO>>

    fun getPayerProvidersWithRestrictionCheck(
        restrictionId: Long,
        page: Int,
        size: Int
    ): Result<CustomPaging<PayerProviderWithRestrictionStatusDTO>>

    fun addProvidersToARestriction(dto: BatchProviderRestrictionDto): Result<Boolean>
    fun removeProvidersFromARestriction(restrictionId: Long, dto: BatchProvidersRestrictionDto): Result<Boolean>
    fun checkProviderRestriction(dto: BenefitProviderRestrictionCheckDto): Result<List<BenefitProviderRestrictionResponseDto>>
    fun checkIfProviderIsAllowedToOfferServiceForBenefit(benefitId: Long, providerId: Long): Result<AllowedResponseDto>
}

interface IValidationService {
    fun validateAndGetCategory(categoryId: Long): Category
    fun validateAndGetBeneficiary(beneficiaryId: Long): Beneficiary
    fun validateAndGetBenefit(benefitId: Long): Benefit
    fun validateAndGetPayer(payerId: Long): Payer
    fun validateAndGetPlan(planId: Long): Plan
    fun validateAndGetRestriction(restrictionId: Long): BenefitRestriction
    fun validateAndGetProvider(providerId: Long): Provider
    fun validateAndGetServiceCatalog(catalogId: Long): ServiceCatalog
    fun validateAndGetBenefitCatalog(catalogId: Long): BenefitCatalog
    fun validateAndGetPolicy(policyId: Long): Policy
    fun validateAndGetDeviceCatalog(catalogId: Long): DeviceCatalog
    fun validateAndGetDeviceSIM(simId: Long): DeviceSim
    fun validateAndGetDeviceAllocation(allocationId: Long): DeviceAllocation
    fun validateAndGetCategoryByPolicyIdAndCategoryName(policyId: Long, categoryName: String): Category
    fun validateAndGetBeneficiaryStaging(stagedId: Long): BeneficiaryStaging
    fun validateAndGetConfig(configId: Long): ConfigCatalog
    fun validateAndGetProviderBankAccount(accountId: Long): ProviderBankAccount
    fun validateAndGetPayerPolicyMapping(mappingId: Long): PayerPolicyMapping
}


interface IManageProviderBenefitService {
    fun getServiceCatalog(page: Int, size: Int): Result<Page<ServiceCatalog>>
    fun addToServiceCatalog(dto: CreateServiceCatalogDto): Result<Boolean>
    fun removeFromServiceCatalog(catalogId: Long): Result<Boolean>
    fun batchMapServiceAndBenefitCatalog(dto: MapServiceAndBenefitCatalogBatchDto): Result<Boolean>
    fun batchUnmapServiceBenefits(serviceCatalogId: Long, dto: UnmapServiceAndBenefitCatalogBatchDto): Result<Boolean>
    fun getMappedBenefits(serviceCatalogId: Long, page: Int, size: Int): Result<Page<ServiceBenefitMapping>>
    fun batchMapServiceProviders(dto: BatchMapServiceProvidersDto): Result<Boolean>
    fun batchUnmapServiceProviders(serviceCatalogId: Long, dto: IdsListDto): Result<Boolean>
    fun getMappedProviders(serviceCatalogId: Long, page: Int, size: Int): Result<Page<ServiceProviderMapping>>
    fun getRequestTypes(providerId: Long): Result<List<RequestTypeProjectionDto>>
    fun getRequestService(providerId: Long, requestName: String): Result<List<RequestServiceProjectionDto>>
    fun addRequestTypesAndServices(dto: AddRequestTypeServiceDto): Result<Boolean>
}

interface IMembershipReportService {
    suspend fun filterBeneficiaries(
        dto: FilterBeneficiariesDto,
        page: Int,
        size: Int
    ): Result<Page<Beneficiary>>

    suspend fun exportFilteredBeneficiariesToExcel(
        dto: FilterBeneficiariesDto,
        fileType: ExportFileType,
        page: Int,
        size: Int
    ): ResponseEntity<Resource>

    suspend fun exportFilteredBeneficiariesToCSV(
        dto: FilterBeneficiariesDto,
        fileType: ExportFileType,
        page: Int,
        size: Int
    ): ResponseEntity<Resource>

    suspend fun exportFilteredBeneficiariesToPDF(
        dto: FilterBeneficiariesDto,
        fileType: ExportFileType,
        page: Int,
        size: Int
    ): ResponseEntity<InputStreamResource>
}

interface IProfileService {
    suspend fun findBiometricsForBeneficiaries(
        beneficiaryIds: List<Long>,
        memberNumbers: List<String>
    ): ApiResult<List<FingerPrintDto>>

    suspend fun updatePhoneNumber(dto: UpdateProfileDto): ApiResult<Boolean>
}

interface INotificationService {
    suspend fun sendSms(message: String, phoneNumber: String?)
    suspend fun sendEmail(subject: String, body: String, recipient: String?)
}

interface IPostHereService {
    suspend fun post(data: String)
}

interface IClaimService {
    suspend fun migrateBenefits(dto: MigrateBenefitsDto): ApiResult<Boolean>
    suspend fun categoryChange(dto: BeneficiaryBenefitsDto): ApiResult<BeneficiaryBenefitChangeResponseDto>
    suspend fun processBenefits(dto: BeneficiaryBenefitsDto): ApiResult<Boolean>
    suspend fun editBeneficiaryInfo(dto: EditBeneficiaryInfoDto): ApiResult<Boolean>
}

interface IBatchUploadService {
    suspend fun uploadBeneficiaries(
        policyId: Long,
        status: StagingStatus?,
        file: MultipartFile,
        autoProcessBenefits: Boolean?
    ): Result<Boolean>
}

interface IPayerRegionService {
    fun findByPayerId(payerId: Long): Result<MutableList<PayerRegion>>
}

interface IDataCollectionService {
    fun addBeneficiary(dto: DataCollectionBeneficiaryDTO): Result<DataCollectionBeneficiary?>
    fun search(search: String): Result<MutableList<DataCollectionBeneficiary>>
    fun findFamily(beneficiaryId: Long): Result<MutableList<DataCollectionBeneficiary>>
    fun findDependants(beneficiaryId: Long): Result<MutableList<DataCollectionBeneficiary>>
    fun filterBeneficiaries(
        payerId: Long,
        fromDate: String,
        toDate: String,
        reportType: String,
        page: Int,
        size: Int
    ): Result<Page<DataCollectionBeneficiary>?>

    fun findPrincipalsCount(): Result<Long>
    fun findAllBeneficiariesCount(): Result<Long>
    fun exportBeneficiaries(
        payerId: Long,
        fromDate: String,
        toDate: String,
        reportType: DataCollectionReportType,
        page: Int,
        size: Long
    ): ResponseEntity<Resource>?

    suspend fun getDocumentDownloadByDocumentId(id: Long): ResponseEntity<Any>
    fun updateBeneficiaryBenefit(beneficiaryId: Long, benefitName: String): Result<DataCollectionBeneficiary>

}

interface IConfigService {
    suspend fun removePrincipalFromCoverIfAboveAge()
    suspend fun removeChildFromCoverIfAboveAge()
}

interface IKeycloakService {
    fun getPayerKeycloakUsers(payerId: Long, page: Int, size: Int): Result<Page<KeycloakUserResolve>>
    fun getProviderKeycloakUsers(providerId: Long, page: Int, size: Int): Result<Page<KeycloakUserResolve>>
}
