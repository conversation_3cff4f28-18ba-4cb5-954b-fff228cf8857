package net.lctafrica.membership.api.service

import net.lctafrica.membership.api.domain.OnboardBenefit
import net.lctafrica.membership.api.domain.OnboardBenefitRepository
import net.lctafrica.membership.api.domain.OnboardPrincipal
import net.lctafrica.membership.api.dtos.OnboardBenefitDTO
import net.lctafrica.membership.api.dtos.OnboardBenefitResponseDTO
import net.lctafrica.membership.api.util.toResponseDTO
import org.springframework.stereotype.Service

@Service
class OnboardBenefitService(private val onboardBenefitRepository: OnboardBenefitRepository) {
    fun createBenefit(
        principal: OnboardPrincipal,
        request: OnboardBenefitDTO
    ): OnboardBenefitResponseDTO {
        if (principal.benefits.any { it.benefitType == request.benefitType }) {
            throw IllegalArgumentException("Benefit ${request.benefitType} already exists for this principal.")
        }
        val onboardPrincipalBenefit = OnboardBenefit(
            benefitType = request.benefitType,
            benefitLimit = request.benefitLimit,
            benefitMonthlyAmount = request.benefitMonthlyAmount,
            principal = principal
        )

        val savedBenefit = onboardBenefitRepository.save(onboardPrincipalBenefit)
        return savedBenefit.toResponseDTO()
    }

    fun getAllPrincipalBenefits(principalId: Long): List<OnboardBenefitResponseDTO> {
        return onboardBenefitRepository.findByPrincipalId(principalId).map { it.toResponseDTO() }
    }

    fun updateBenefit(benefitId: Long, request: OnboardBenefitDTO): OnboardBenefitResponseDTO? {
        var benefit = onboardBenefitRepository.findById(benefitId).orElse(null) ?: return null
        benefit = benefit.copy(
            benefitLimit = request.benefitLimit,
            benefitMonthlyAmount = request.benefitMonthlyAmount
        )
        return onboardBenefitRepository.save(benefit).toResponseDTO()
    }
}