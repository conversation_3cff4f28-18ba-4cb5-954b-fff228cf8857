package net.lctafrica.membership.api.service.massactions

import net.lctafrica.membership.api.dtos.massactions.ProviderMappingSummary
import net.lctafrica.membership.api.dtos.massactions.ProviderUpdateSummary
import org.springframework.web.multipart.MultipartFile


interface IProviderBulkUpdateService {
    suspend fun processProviderUpdates(
        payerId: Long,
        file: MultipartFile
    ): ProviderUpdateSummary

    suspend fun processProviderMappings(
        payerId: Long,
        file: MultipartFile
    ): ProviderMappingSummary
}