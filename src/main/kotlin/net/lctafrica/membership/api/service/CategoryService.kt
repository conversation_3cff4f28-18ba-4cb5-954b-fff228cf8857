package net.lctafrica.membership.api.service

import java.time.LocalDate
import java.time.ZoneId
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import net.lctafrica.membership.api.config.BadRequestException
import net.lctafrica.membership.api.domain.Beneficiary
import net.lctafrica.membership.api.domain.BeneficiaryRepository
import net.lctafrica.membership.api.domain.BeneficiaryType
import net.lctafrica.membership.api.domain.BeneficiaryUploadError
import net.lctafrica.membership.api.domain.BeneficiaryUploadErrorRepository
import net.lctafrica.membership.api.domain.BenefitCatalogRepository
import net.lctafrica.membership.api.domain.BenefitRepository
import net.lctafrica.membership.api.domain.Category
import net.lctafrica.membership.api.domain.CategoryRepository
import net.lctafrica.membership.api.domain.PayerProviderMapping
import net.lctafrica.membership.api.domain.PayerProviderMappingRepository
import net.lctafrica.membership.api.domain.Policy
import net.lctafrica.membership.api.domain.PolicyRepository
import net.lctafrica.membership.api.domain.PolicyStatus
import net.lctafrica.membership.api.domain.ProviderExclusionRepository
import net.lctafrica.membership.api.domain.ProviderRepository
import net.lctafrica.membership.api.dtos.BeneficiaryDTO
import net.lctafrica.membership.api.dtos.CategoryDTO
import net.lctafrica.membership.api.dtos.IdsListDto
import net.lctafrica.membership.api.dtos.IdsSetDto
import net.lctafrica.membership.api.dtos.QueryBenefitCatalog
import net.lctafrica.membership.api.util.AppConstants.UPLOADED_SUCCESSFULLY_P
import net.lctafrica.membership.api.util.MemberInput
import net.lctafrica.membership.api.util.ReadExcelFile
import net.lctafrica.membership.api.util.Result
import net.lctafrica.membership.api.util.ResultFactory
import net.lctafrica.membership.api.util.findFamilyNumber
import org.hibernate.validator.internal.util.Contracts
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.multipart.MultipartFile

@Service("categoryService")
@Transactional
class CategoryService(
	private val repo: CategoryRepository,
	private val policyRepo: PolicyRepository,
	private val beneficiaryRepo: BeneficiaryRepository,
	private val xlFileReader: ReadExcelFile,
	private val providerRepo: ProviderRepository,
	private val providerExclusionRepository: ProviderExclusionRepository,
	private val beneficiaryUploadErrorRepository: BeneficiaryUploadErrorRepository,
	private val benefitCatalogRepository: BenefitCatalogRepository,
	private val validationService: IValidationService,
	private val payerProviderMappingRepository: PayerProviderMappingRepository,
	private val benefitRepository: BenefitRepository
) : ICategoryService {

	private val LOGGER = LoggerFactory.getLogger(CategoryService::class.java)

	@Transactional(readOnly = true)
	override fun findByPolicy(policyId: Long): Result<MutableList<Category>> {
		val policy = policyRepo.findById(policyId)
		if (policy.isPresent) {
			var catsByPolicy = repo.findByPolicy(policy.get())
			return ResultFactory.getSuccessResult(catsByPolicy)
		}
		return ResultFactory.getFailResult("No policy with policy ID $policyId was found")
	}

	override suspend fun findByPolicyIds(dto: IdsSetDto): Result<MutableList<Category>> {
		val categories = repo.findByPolicyIds(dto.ids)
		categories.map {
			val active = it.policy.endDate.isAfter(LocalDate.now())
			it.policy.status = if (active) PolicyStatus.ACTIVE else PolicyStatus.EXPIRED
		}
		return ResultFactory.getSuccessResult(categories)
	}

	override suspend fun findByPlanIds(dto: IdsSetDto): Result<MutableList<Category>> {
		val categories = repo.findByPlanIds(dto.ids)
		categories.map {
			val active = it.policy.endDate.isAfter(LocalDate.now())
			it.policy.status = if (active) PolicyStatus.ACTIVE else PolicyStatus.EXPIRED
		}
		return ResultFactory.getSuccessResult(categories)
	}


	@Transactional(readOnly = false, rollbackFor = [Exception::class])
	override fun removeCategory(categoryId: Long): Result<Category> {
		return ResultFactory.getFailResult("Not yet implemented")
	}

	@Transactional(readOnly = false, rollbackFor = [Exception::class])
	override fun addCategory(dto: CategoryDTO): Result<MutableList<Category>> {
		val policy = policyRepo.findById(dto.policyId)
		var categories = mutableListOf<Category>()
		var catNames = mutableListOf<String>()
		return try {
			if (policy.isPresent) {
				dto.categories.forEach { cat ->
					val c = Category(
						name = cat.name.trim(),
						description = cat.description.trim(),
						policy = policy.get(),
						jicSchemeCode = cat.jicSchemeCode,
						apaSchemeCode = cat.apaSchemeCode,
						policyPayerCode = cat.policyPayerCode,
						restrictionType = cat.restrictionType
					)
					catNames.add(cat.name.trim().uppercase())
					categories.add(c)
				}
				// check if any category already exists in the database
				val alreadyExists = repo.findByPolicyIdAndNameIn(dto.policyId, catNames)
				if (alreadyExists.isNotEmpty()) return ResultFactory.getFailResult("Category already exists")

				repo.saveAll(categories)
				ResultFactory.getSuccessResult(categories)
			} else {
				ResultFactory.getFailResult("No policy with ID ${dto.policyId} was found")
			}
		} catch (ex: IllegalArgumentException) {
			return ResultFactory.getFailResult(ex.message)
		}

	}

	override fun dependantsUpload(
		policyId: Long,
		file: MultipartFile
	): Result<Boolean> {
		var errors: String? = null
		val optPolicy = policyRepo.findById(policyId)

		if (optPolicy.isPresent) {
			val policy = optPolicy.get()
			var map = mutableMapOf<String, MutableList<MemberInput>>()
			if (xlFileReader.isExcelFormat(file)) {
				val input = file.inputStream
				map = xlFileReader.read(input)
			} else {
				/*val input = file.inputStream as */
				return ResultFactory.getFailResult("File is not a valid excel format, ${file.contentType}")
			}
			var finalMap = mutableMapOf<String, MutableList<Beneficiary>>()

			val keys = map.keys
			println(keys)
//			for (key in keys) {
//				val category = getCategoryFromName(policy, key)
//				val added = mutableListOf<Beneficiary>()
//				if (category != null) {
//					val members = map[key]
//					for (m in members!!){
//						println(m.memberNumber.substring(0, m.memberNumber.length-2))
//					}

			for (key in keys) {
				val category = getCategoryFromName(policy, key)
				val added = mutableListOf<Beneficiary>()
				if (category != null) {
					val members = map[key]

					val famGroups = members?.groupBy({ member ->
						member.memberNumber.substring(
							0,
							member.memberNumber.length - 3
						)
					}, { mi ->
						BeneficiaryDTO(
							name = mi.name,
							memberNumber = mi.memberNumber,
							nhifNumber = mi.nhif,
							dob = mi.dob.toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
							gender = mi.gender,
							beneficiaryType = mi.title,
							email = mi.email,
							phoneNumber = mi.phone,
							categoryId = category.id,
							principalId = null,
							jicEntityId = mi.jicEntityId,
							apaEntityId = mi.apaEntityId,
							joinDate = mi.joinDate?.toInstant()?.atZone(ZoneId.systemDefault())?.toLocalDate()

						)
					})

					for (fam in famGroups!!.keys) {
						var princi = "$fam-00"
						val depsList = mutableListOf<Beneficiary>()
						try {

							var principal = beneficiaryRepo.findFamilyByCategoryAndMemberNumber(
								category,
								princi
							)


							var deps =
								famGroups[fam]!!.filter { huyu -> huyu.beneficiaryType != BeneficiaryType.PRINCIPAL }

							for (dep in deps) {
								var dependant = Beneficiary(
									principal = principal[0],
									name = dep.name,
									nhifNumber = dep.nhifNumber,
									memberNumber = dep.memberNumber,
									dob = dep.dob,
									beneficiaryType = dep.beneficiaryType,
									email = dep.email,
									phoneNumber = dep.phoneNumber,
									category = category,
									gender = dep.gender,
									processed = false,
									processedTime = null,
									jicEntityId = dep.jicEntityId,
									apaEntityId = dep.apaEntityId
								)
								depsList.add(dependant)
							}
							println("$depsList")
							beneficiaryRepo.saveAll(depsList)
							///added.add(principal)
							added.addAll(depsList)
						} catch (ex: java.util.NoSuchElementException) {
							if (errors != null) {
								errors = errors.plus("Check family number $fam for correctness. ")
							}
							errors = "Check family number $fam for correctness. "
							continue
						}
					}

				}
				finalMap[key] = added
			}
		}
		return ResultFactory.getSuccessResult(data = true)
	}

    override fun uploadBeneficiaries(policyId: Long, file: MultipartFile): Result<Boolean> {
        val policy = validationService.validateAndGetPolicy(policyId)
        if (xlFileReader.isExcelFormat(file)) {
            val input = file.inputStream
			val map: MutableMap<String, MutableList<MemberInput>> = xlFileReader.read(input)
            val finalMap = mutableMapOf<String, MutableList<Beneficiary>>()
            val keys = map.keys
			for (key in keys) {
				getCategoryFromName(policy, key) ?: throw BadRequestException("Category $key not found")
			}
			LOGGER.info("Sheets: $keys")
			CoroutineScope(Dispatchers.IO).launch {
            for (key in keys) {
                val category = getCategoryFromName(policy, key) ?: throw BadRequestException("Category $key not found")
				val added = mutableListOf<Beneficiary>()
				val members = map[key]
				val famGroups = members?.groupBy({ member ->
					member.tracker
				}, { mi ->
					BeneficiaryDTO(
						name = mi.name,
						memberNumber = mi.memberNumber,
						nhifNumber = mi.nhif,
						dob = mi.dob.toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
						gender = mi.gender,
						beneficiaryType = mi.title,
						email = mi.email,
						phoneNumber = mi.phone,
						categoryId = category.id,
						principalId = null,
						jicEntityId = mi.jicEntityId,
						apaEntityId = mi.apaEntityId,
						joinDate = mi.joinDate?.toInstant()?.atZone(ZoneId.systemDefault())?.toLocalDate()
					)
				})

				Contracts.assertNotNull(famGroups!!, "Family Groups is null")

				for (fam in famGroups.keys) {
					val depsList = mutableListOf<Beneficiary>()
					try {
						val principalData =
							famGroups[fam]?.filter { family -> family.beneficiaryType == BeneficiaryType.PRINCIPAL }
								?.firstOrNull()

						val principalList = if (principalData == null) {
							val depMemberNumber = famGroups[fam]?.get(0)?.memberNumber
							val familyNumber = findFamilyNumber(depMemberNumber ?: "")
							beneficiaryRepo.findPrincipalByCategoryAndFamilyNumberLike(
								category,
								familyNumber
							)
						} else {
							beneficiaryRepo.findFamilyByCategoryAndMemberNumber(
								category,
								principalData.memberNumber
							)
						}

						if(principalData == null && principalList.isEmpty()){
							val error="No Principal found in both Excel or DB for family number $fam"
							val exists = BeneficiaryUploadError(
								memberNumber = fam,
								error = error
							)
							beneficiaryUploadErrorRepository.save(exists)
							continue
						}

						val principal = if (principalList.size > 0) {
							principalList[0]
						} else {
							Contracts.assertNotNull(principalData!!, "No principal found - is null")
							val principalObj = Beneficiary(
								principal = null,
								name = principalData.name,
								nhifNumber = principalData.nhifNumber,
								memberNumber = principalData.memberNumber,
								dob = principalData.dob,
								beneficiaryType = principalData.beneficiaryType,
								email = principalData.email,
								phoneNumber = principalData.phoneNumber,
								category = category,
								gender = principalData.gender,
								processed = false,
								processedTime = null,
								jicEntityId = principalData.jicEntityId,
								apaEntityId = principalData.apaEntityId,
								joinDate = principalData.joinDate
							)
							beneficiaryRepo.save(principalObj)
						}


						val deps =
							famGroups[fam]?.filter { family -> family.beneficiaryType != BeneficiaryType.PRINCIPAL }

						if (deps != null) {
							for (dep in deps) {
								val checkBeneficiaryExists = beneficiaryRepo.findBeneficiaryByCategoryAndMemberNumber(
									category,
									dep.memberNumber
								)
								if (checkBeneficiaryExists.size > 0) {
									val exists = BeneficiaryUploadError(
										memberNumber = dep.memberNumber,
										error = "Member No. ${dep.memberNumber} already exists"
									)
									beneficiaryUploadErrorRepository.save(exists)
								} else {
									val dependant = Beneficiary(
										principal = principal,
										status = principal.status,
										name = dep.name,
										nhifNumber = dep.nhifNumber,
										memberNumber = dep.memberNumber,
										dob = dep.dob,
										beneficiaryType = dep.beneficiaryType,
										email = dep.email,
										phoneNumber = dep.phoneNumber,
										category = category,
										gender = dep.gender,
										processed = false,
										processedTime = null,
										jicEntityId = dep.jicEntityId,
										apaEntityId = dep.apaEntityId,
										joinDate = dep.joinDate
									)
									beneficiaryRepo.save(dependant)
									depsList.add(dependant)
									added.addAll(depsList)
								}
							}
						}

					} catch (ex: java.util.NoSuchElementException) {
						LOGGER.error(ex.message)
						val exists = BeneficiaryUploadError(
							memberNumber = fam,
							error = "Check family number $fam for correctness"
						)
						beneficiaryUploadErrorRepository.save(exists)
						continue
					}
				}

				finalMap[key] = added
            }
				LOGGER.info("uploadBeneficiaries => Processing complete")
			}
			return ResultFactory.getSuccessResult(msg = UPLOADED_SUCCESSFULLY_P)
        } else {
            return ResultFactory.getFailResult("File is not a valid excel format, ${file.contentType}")
        }
    }

    @Transactional(rollbackFor = [Exception::class])
	override fun batchUpload(
		policyId: Long,
		file: MultipartFile
	): Result<MutableMap<String, MutableList<Beneficiary>>> {
		var errors: String? = null
		val optPolicy = policyRepo.findById(policyId)
		if (optPolicy.isPresent) {
			val policy = optPolicy.get()
			var map = mutableMapOf<String, MutableList<MemberInput>>()
			if (xlFileReader.isExcelFormat(file)) {
				val input = file.inputStream
				map = xlFileReader.read(input)
			} else {
				/*val input = file.inputStream as */
				return ResultFactory.getFailResult("File is not a valid excel format, ${file.contentType}")
			}
			val finalMap = mutableMapOf<String, MutableList<Beneficiary>>()

			val keys = map.keys
			println(keys)

			for (key in keys) {
				val category = getCategoryFromName(policy, key)
				val added = mutableListOf<Beneficiary>()
				if (category != null) {
					val members = map[key]

					val famGroups = members?.groupBy({ member -> member.tracker }, { mi ->
						BeneficiaryDTO(
							name = mi.name,
							memberNumber = mi.memberNumber,
							nhifNumber = mi.nhif,
							dob = mi.dob.toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
							gender = mi.gender,
							beneficiaryType = mi.title,
							email = mi.email,
							phoneNumber = mi.phone,
							categoryId = category.id,
							principalId = null,
							jicEntityId = mi.jicEntityId,
							apaEntityId = mi.apaEntityId,
							joinDate = mi.joinDate?.toInstant()?.atZone(ZoneId.systemDefault())?.toLocalDate()
						)
					})

					for (fam in famGroups!!.keys) {
						val depsList = mutableListOf<Beneficiary>()
						try {
							val pr =
								famGroups[fam]!!.first { dto -> dto.beneficiaryType == BeneficiaryType.PRINCIPAL }
							val principal = beneficiaryRepo.save(
								Beneficiary(
									name = pr.name,
									memberNumber = pr.memberNumber,
									dob = pr.dob,
									nhifNumber = pr.nhifNumber,
									beneficiaryType = pr.beneficiaryType,
									email = pr.email,
									phoneNumber = pr.phoneNumber,
									category = category,
									gender = pr.gender,
									principal = null,
									processed = false,
									processedTime = null,
									jicEntityId = pr.jicEntityId,
									apaEntityId = pr.apaEntityId
								)
							)


							val deps =
								famGroups[fam]!!.filter { huyu -> huyu.beneficiaryType != BeneficiaryType.PRINCIPAL }

							for (dep in deps) {
								val dependant = Beneficiary(
									principal = principal,
									name = dep.name,
									nhifNumber = dep.nhifNumber,
									memberNumber = dep.memberNumber,
									dob = dep.dob,
									beneficiaryType = dep.beneficiaryType,
									email = dep.email,
									phoneNumber = dep.phoneNumber,
									category = category,
									gender = dep.gender,
									processed = false,
									processedTime = null,
									jicEntityId = dep.jicEntityId,
									apaEntityId = dep.apaEntityId
								)
								depsList.add(dependant)
							}
							println("$depsList")
							beneficiaryRepo.saveAll(depsList)
							added.add(principal)
							added.addAll(depsList)
						} catch (ex: java.util.NoSuchElementException) {
							if (errors != null) {
								errors = errors.plus("Check family number $fam for correctness. ")
							}
							errors = "Check family number $fam for correctness. "
							continue
						}
					}

				}
				finalMap[key] = added
			}

			/*map.keys.forEach { cat ->
				println("mapping inside $cat in order to fetch category")
				val category = getCategoryFromName(policy, cat)
				var additions = mutableListOf<Beneficiary>()
				val members = map[category!!.name.uppercase().trim()]
				val famGroups = members?.groupBy({ member -> member.tracker }, { mi ->
					BeneficiaryDTO(
						name = mi.name, memberNumber = mi.memberNumber,
						dob = mi.dob.toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
						gender = mi.gender, beneficiaryType = mi.title, email = mi.email, phoneNumber = mi.phone,
						categoryId = category.id, principalId = null
					)
				})
				println("Fam groups $famGroups")
				famGroups!!.keys.forEach { grp ->
					val principal = famGroups[grp]!!.filter { it.beneficiaryType == BeneficiaryType.PRINCIPAL }
						.map {
							Beneficiary(
								name = it.name,
								memberNumber = it.memberNumber,
								dob = it.dob,
								beneficiaryType = it.beneficiaryType,
								email = it.email,
								phoneNumber = it.phoneNumber,
								category = category,
								gender = it.gender,
								principal = null,
								processed = false,
								processedTime = null
							)
						}.map { ben ->
							println("saving beneficiary ${ben.name}")
							return@map beneficiaryRepo.save(ben)
						}.first()
					additions.add(principal)
					var deps = famGroups[grp]!!.filter { it.beneficiaryType != BeneficiaryType.PRINCIPAL }
						.map {
							return@map Beneficiary(
								name = it.name,
								memberNumber = it.memberNumber,
								dob = it.dob,
								beneficiaryType = it.beneficiaryType,
								email = it.email,
								phoneNumber = it.phoneNumber,
								category = category,
								gender = it.gender,
								principal = principal,
								processed = false,
								processedTime = null
							)
						}
					beneficiaryRepo.saveAll(deps)
					additions.addAll(deps)
					finalMap[category.name] = additions
				}
			}*/
			return ResultFactory.getSuccessResult(
				data = finalMap,
				msg = errors ?: "Successfully created beneficiaries"
			)
		}
		return ResultFactory.getFailResult("No policy with ID $policyId was found. Batch upload failed")
	}

	override fun findCategoryProviderExclusion(
		categoryId: Long,
		providerId: Long
	): Result<Boolean> {

		val category = repo.findById(categoryId)
		if (category.isEmpty) return ResultFactory.getFailResult("No Category with ID $categoryId was found")
		val provider = providerRepo.findById(categoryId)
		if (provider.isEmpty) return ResultFactory.getFailResult("No Provider with ID $providerId was found")

		val optional =
			providerExclusionRepository.findByCategoryAndProvider(category.get(), provider.get())

		return if (optional.isPresent) {
			ResultFactory.getSuccessResult(
				data = true,
			)
		} else {
			ResultFactory.getSuccessResult(
				data = false,
			)
		}
	}

	override fun findById(categoryId: Long): Result<Category> {
		val category = repo.findById(categoryId)
		return if(category.isEmpty) ResultFactory.getFailResult("No Such category with id $categoryId was found")
		else
			ResultFactory.getSuccessResult(data = category.get())
	}

	override fun findByCategoryIds(dto: IdsListDto): Result<List<Category>> {
		return ResultFactory.getSuccessResult(data = repo.findByCategoryIds(dto.ids))
	}

	override fun findPoliciesByCategoryIds(dto: IdsSetDto): Result<List<Policy>> {
		return ResultFactory.getSuccessResult(data = policyRepo.findPoliciesByCategoryIds(categoryIds = dto.ids))
	}

	override fun findByPayerIdAndProviderIds(payerId: Long, dto: IdsSetDto): Result<List<PayerProviderMapping>> {
		return ResultFactory.getSuccessResult(data = payerProviderMappingRepository.findByPayerIdAndProviderIds(payerId = payerId, providerIds = dto.ids))
	}

	override fun findBenefitsCatalogByPayerId(payerId: Long): Result<List<QueryBenefitCatalog>> {
		val catalogList = benefitCatalogRepository.findByPayerId(payerId)
		val refs =catalogList.map(fun(catalog):QueryBenefitCatalog{
			val benefits = benefitRepository.findByPayerAndCatalog(payerId = payerId, catalogId =catalog.id ?:0)
			return QueryBenefitCatalog(
				id = catalog.id,
				name = catalog.name,
				code = catalog.code,
				serviceGroup = catalog.serviceGroup,
				benefitName = if(benefits.isNotEmpty()) benefits[0].name else "",
			)
		})
		return ResultFactory.getSuccessResult(refs)
	}

	fun getCategoryFromName(policy: Policy, name: String): Category? {
		val findByPolicyAndNameIgnoreCase = repo.searchByPolicyAndName(policy = policy, name)
		if(findByPolicyAndNameIgnoreCase == null){
			val exists = BeneficiaryUploadError(
				memberNumber = "POL_ID = ${policy.id}",
				error = "Category $name with a policy Id ${policy.id} not found."
			)
			beneficiaryUploadErrorRepository.save(exists)
		}
		println("showing category found $findByPolicyAndNameIgnoreCase")
		return findByPolicyAndNameIgnoreCase
	}
}
