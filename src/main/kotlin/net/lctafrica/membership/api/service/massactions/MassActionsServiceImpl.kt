package net.lctafrica.membership.api.service.massactions

import net.lctafrica.membership.api.dtos.massactions.MassActionResult
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import kotlin.system.measureTimeMillis


@Service
class MassActionsServiceImpl : IMassActionsService {

    private val logger = LoggerFactory.getLogger(MassActionsServiceImpl::class.java)

    /**
     * Execute an operation with comprehensive logging and timing
     */
    override suspend fun <T> executeWithLogging(
        operation: String,
        block: suspend () -> T
    ): T {
        logger.info("Starting mass action operation: {}", operation)

        val result: T
        val executionTime = measureTimeMillis {
            try {
                result = block()
                logger.debug("Mass action operation '{}' executed successfully", operation)
            } catch (e: Exception) {
                logger.error("Mass action operation '{}' failed: {}", operation, e.message, e)
                throw e
            }
        }

        logger.info("Completed mass action operation: {} in {} ms", operation, executionTime)
        return result
    }

    /**
     * Create a standardized summary for mass action results
     */
    override fun createSummary(
        totalProcessed: Int,
        successCount: Int,
        errors: List<String>,
        processingTimeMs: Long,
        details: Any?
    ): MassActionResult {
        val errorCount = errors.size
        val success = errorCount == 0 && successCount > 0

        val message = when {
            success -> "Mass action completed successfully. Processed: $successCount/$totalProcessed items"
            errorCount > 0 && successCount > 0 -> "Mass action completed with partial success. Successful: $successCount, Failed: $errorCount"
            errorCount > 0 -> "Mass action failed. All $totalProcessed items failed processing"
            else -> "Mass action completed with no items processed"
        }

        logger.info(
            "Mass action summary - Total: {}, Success: {}, Errors: {}, Time: {}ms",
            totalProcessed, successCount, errorCount, processingTimeMs
        )

        if (errors.isNotEmpty()) {
            logger.debug("Mass action errors: {}", errors.joinToString("; "))
        }

        return MassActionResult(
            success = success,
            message = message,
            totalProcessed = totalProcessed,
            successCount = successCount,
            errorCount = errorCount,
            processingTimeMs = processingTimeMs,
            details = details
        )
    }
}