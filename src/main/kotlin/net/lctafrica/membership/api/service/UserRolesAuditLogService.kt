package net.lctafrica.membership.api.service

import net.lctafrica.membership.api.domain.UserRolesAuditLog
import net.lctafrica.membership.api.domain.UserRolesAuditLogRepository
import net.lctafrica.membership.api.dtos.UserRolesAuditLogResponse
import net.lctafrica.membership.api.helper.NoRoleChangesException
import net.lctafrica.membership.api.util.toResponseDTO
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class UserRolesAuditLogService(
    private val userRolesAuditLogRepository: UserRolesAuditLogRepository,
) {
    fun logUserRoleChange(userId: String, actionedBy: String, addedRoles: List<String>, removedRoles: List<String>) {
        if (addedRoles.isEmpty() && removedRoles.isEmpty()) {
            throw NoRoleChangesException()
        }
        val auditLog = UserRolesAuditLog(
            userId = userId,
            actionedBy = actionedBy,
            addedRoles = addedRoles.toMutableList(),
            removedRoles = removedRoles.toMutableList()
        )
        userRolesAuditLogRepository.save(auditLog)
    }

    fun getAuditLogsByUserId(userId: String, page: Int, size: Int): Page<UserRolesAuditLogResponse> {
        val adjustedPage = if (page > 0) page - 1 else 0
        val pageable: Pageable = PageRequest.of(adjustedPage, size)
        return userRolesAuditLogRepository.findByUserId(userId, pageable).map { it.toResponseDTO() }
    }

    fun getAuditLogsByActionedBy(actionedBy: String, page: Int, size: Int): Page<UserRolesAuditLogResponse> {
        val adjustedPage = if (page > 0) page - 1 else 0
        val pageable: Pageable = PageRequest.of(adjustedPage, size)
        return userRolesAuditLogRepository.findByActionedBy(actionedBy, pageable).map { it.toResponseDTO() }
    }
}