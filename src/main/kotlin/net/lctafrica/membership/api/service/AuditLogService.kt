package net.lctafrica.membership.api.service

import java.time.LocalDate
import java.time.LocalDateTime
import net.lctafrica.membership.api.domain.AuditLog
import net.lctafrica.membership.api.domain.AuditLogRepo
import net.lctafrica.membership.api.domain.BeneficiaryRepository
import net.lctafrica.membership.api.domain.ChangeLogType
import net.lctafrica.membership.api.dtos.AuditLogDTO
import net.lctafrica.membership.api.dtos.AuditLogFilter
import net.lctafrica.membership.api.dtos.AuditLoggingDTO
import net.lctafrica.membership.api.dtos.MembershipAuditLogDTO
import net.lctafrica.membership.api.util.Result
import net.lctafrica.membership.api.util.ResultFactory
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service("auditLogService")
@Transactional
class AuditLogService(
	private val auditLogRepo: AuditLogRepo,
	private val beneficiaryRepository: BeneficiaryRepository,
	private val validationService: IValidationService
) : IAuditLogService {

	@Transactional(readOnly = false, rollbackFor = [Exception::class])
	override fun saveLog(auditLogDTO: AuditLogDTO): Result<AuditLog> {

		if (auditLogDTO.beneficiaryId !== null) {
			val beneficiary = beneficiaryRepository.findById(auditLogDTO.beneficiaryId!!)
			if (beneficiary.isEmpty)
				return ResultFactory.getFailResult(
					msg = "Beneficiary with id " +
							"${auditLogDTO.beneficiaryId} not found "
				)
			val audit = AuditLog(
				data = auditLogDTO.data,
				user = auditLogDTO.user,
				action = auditLogDTO.action,
				beneficiary = beneficiary.get(),
				organisation = auditLogDTO.organisation,
				reason = auditLogDTO.reason,
				time = LocalDate.now().atStartOfDay(),
				memberNumber = auditLogDTO.memberNumber,
				changeLogType = ChangeLogType.valueOf(auditLogDTO.type.toString())
			)

			val auditlogResponse = auditLogRepo.save(audit)
			return ResultFactory.getSuccessResult(data = auditlogResponse)
		} else {
			val audit = AuditLog(
				data = auditLogDTO.data,
				user = auditLogDTO.user,
				action = auditLogDTO.action,
				beneficiary = null,
				organisation = auditLogDTO.organisation,
				reason = auditLogDTO.reason,
				time = LocalDate.now().atStartOfDay(),
				memberNumber = auditLogDTO.memberNumber,
				changeLogType = ChangeLogType.valueOf(auditLogDTO.type.toString())
			)

			val auditlogResponse = auditLogRepo.save(audit)
			return ResultFactory.getSuccessResult(data = auditlogResponse)
		}


	}


	@Transactional(readOnly = false, rollbackFor = [Exception::class])
	override suspend fun categoryAuditLog(auditLogDTO: MembershipAuditLogDTO): Result<AuditLog> {
		val category = validationService.validateAndGetCategory(auditLogDTO.categoryId ?:0)
		val audit = AuditLog(
			data = auditLogDTO.data,
			narration = auditLogDTO.narration,
			user = auditLogDTO.user,
			action = auditLogDTO.action,
			category = category,
			organisation = auditLogDTO.organisation,
			reason = auditLogDTO.reason,
			time = LocalDateTime.now(),
			changeLogType = ChangeLogType.valueOf(auditLogDTO.type.toString())
		)
		val auditLogResponse = auditLogRepo.save(audit)
		return ResultFactory.getSuccessResult(data = auditLogResponse)
	}

	override suspend fun benefitAuditLog(auditLogDTO: MembershipAuditLogDTO): Result<AuditLog> {
		val benefit = validationService.validateAndGetBenefit(auditLogDTO.benefitId ?:0)
		val audit = AuditLog(
			data = auditLogDTO.data,
			user = auditLogDTO.user,
			action = auditLogDTO.action,
			benefit = benefit,
			organisation = auditLogDTO.organisation,
			reason = auditLogDTO.reason,
			time = LocalDateTime.now(),
			changeLogType = ChangeLogType.valueOf(auditLogDTO.type.toString())
		)
		val auditLogResponse = auditLogRepo.save(audit)
		return ResultFactory.getSuccessResult(data = auditLogResponse)
	}

	override suspend fun beneficiaryAuditLog(auditLogDTO: MembershipAuditLogDTO): Result<AuditLog> {
		val beneficiary = validationService.validateAndGetBeneficiary(auditLogDTO.beneficiaryId ?:0)
		val audit = AuditLog(
			data = auditLogDTO.data,
			user = auditLogDTO.user,
			action = auditLogDTO.action,
			beneficiary = beneficiary,
			organisation = auditLogDTO.organisation,
			reason = auditLogDTO.reason,
			time = LocalDateTime.now(),
			memberNumber = auditLogDTO.memberNumber,
			changeLogType = ChangeLogType.valueOf(auditLogDTO.type.toString()),
			narration = auditLogDTO.narration
		)

		val auditLogResponse = auditLogRepo.save(audit)
		return ResultFactory.getSuccessResult(data = auditLogResponse)
	}

	override suspend fun log(dto: AuditLoggingDTO): Result<AuditLog> {
		val audit = AuditLog(
			data = dto.data,
			user = dto.user,
			action = dto.action,
			beneficiary = dto.beneficiary,
			bankAccount = dto.bankAccount,
			payerProviderUserMapping = dto.payerProviderUserMapping,
			benefitPayerAdmin = dto.benefitPayerAdmin,
			organisation = dto.organisation,
			reason = dto.reason,
			narration = dto.narration,
			changeLogType = ChangeLogType.valueOf(dto.type.toString()),
		)
		auditLogRepo.save(audit)
		return ResultFactory.getSuccessResult(data = audit)
	}

	override suspend fun getAuditLogs(page: Int, size: Int, dto: AuditLogFilter): Result<Page<AuditLog>> {
		val logs = auditLogRepo.filterAuditLog(beneficiaryId = dto.beneficiaryId,
			pageable = PageRequest.of((page -1), size, Sort.by("id").descending()))
		return ResultFactory.getSuccessResult(data = logs)
	}

}