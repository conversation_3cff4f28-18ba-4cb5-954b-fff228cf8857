package net.lctafrica.membership.api.service

import net.lctafrica.membership.api.config.NotFoundRequestException
import net.lctafrica.membership.api.domain.*
import net.lctafrica.membership.api.dtos.*
import net.lctafrica.membership.api.helper.ExcelHelper
import net.lctafrica.membership.api.util.AppConstants.CREATED_SUCCESSFULLY
import net.lctafrica.membership.api.util.AppConstants.SUCCESSFULLY_MAPPED
import net.lctafrica.membership.api.util.AppConstants.UPDATED_SUCCESSFULLY
import net.lctafrica.membership.api.util.Result
import net.lctafrica.membership.api.util.ResultFactory
import net.lctafrica.membership.api.util.customPaging
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Lazy
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.multipart.MultipartFile
import java.util.stream.Collectors

@Service("providerService")
@Transactional
class ProviderService(
	private val repo: ProviderRepository,
	private val countryRepo: CountryRepository,
	private val regionRepo: RegionRepository,
	private val whiteListRepo: WhiteListRepository,
	private val benefitCatalogRepo: BenefitCatalogRepository,
	private val providerRepository: ProviderRepository,
	private val payerProviderMappingRepository: PayerProviderMappingRepository,
	private val payerRepository: PayerRepository,
	private val categoryRestrictionMappingRepo: CategoryRestrictionMappingRepo,
	@Lazy @Autowired val excelHelper: ExcelHelper,
	private val categoryRepository: CategoryRepository,
	private val benefitRepository: BenefitRepository,
	private val benefitProviderMappingRepo: BenefitProviderMappingRepo,
	private val validationService: IValidationService,
	private val bankAccountRepository: ProviderBankAccountRepository,
	private val payerProviderBankAccountMappingRepository: PayerProviderBankAccountMappingRepository,
	private val providerBankAccountRepository: ProviderBankAccountRepository,
	private val payerProviderUserMappingRepository: PayerProviderUserMappingRepository,
	private val auditLogService: IAuditLogService,
	private val benefitRestrictionRepo: BenefitRestrictionRepo,
	private val benefitRestrictionProviderSetRepo: BenefitRestrictionProviderSetRepo,
) : IProviderService {

	override fun findById(providerId: Long): Result<Provider> {
		val optional = repo.findById(providerId)
		return when {
			optional.isPresent -> {
				ResultFactory.getSuccessResult(optional.get())
			}
			else -> {
				ResultFactory.getFailResult("Invalid Provider Id")
			}
		}
	}

	override suspend fun findByIds(dto: IdsListDto): Result<List<Provider>> {
		return ResultFactory.getSuccessResult(providerRepository.findByIds(dto.ids))
	}

	override suspend fun findBranchesByProviderIds(dto: IdsSetDto): Result<List<Provider>> {
		return ResultFactory.getSuccessResult(providerRepository.findBranchesByProviderIds(dto.ids))
	}

	override fun findProviderBranchesById(providerId: Long): Result<MutableList<BranchesDTO?>> {
		val optional = repo.findById(providerId)
		return if(optional.isPresent){
			val providers = providerRepository.findProviderBranches(providerId)
			ResultFactory.getSuccessResult(providers)
		}else{
			ResultFactory.getFailResult("No Provider with id $providerId")
		}

	}

	override fun searchProviders(searchType: SearchType, providerName: String?, mainFacilityId: Long?, page: Int, size: Int): Result<Page<Provider>> {
		val pageable = PageRequest.of(page - 1, size)
		val searchName = if(providerName.isNullOrBlank()) null else providerName
		return when(searchType){
			SearchType.ALL -> {
				ResultFactory.getSuccessResult(providerRepository.searchAllProviders(searchName,pageable))
			}
			SearchType.MAIN -> {
				ResultFactory.getSuccessResult(providerRepository.searchMainProviders(searchName,pageable))
			}
			SearchType.BRANCH -> {
				ResultFactory.getSuccessResult(providerRepository.searchBranchProviders(searchName,mainFacilityId,pageable))
			}
		}
	}


	@Transactional(rollbackFor = [Exception::class])
	override fun addProvider(dto: ProviderDTO): Result<Provider> {
		var mainFacility: Provider? = null
		repo.findById(dto.mainFacilityId).ifPresent { mainFacility = it }
		val country = countryRepo.findByNameIgnoreCase(dto.country.trim())
		val region: Region = country?.let { c ->
			return@let regionRepo.findByCountryAndNameIgnoreCase(c, dto.region.trim())
		} ?: return ResultFactory.getFailResult("No such region was found")


		if (dto.providerId !== null) {
			val optional = repo.findById(dto.providerId)

			val provider = optional.get()
			provider.apply {
				this.name = dto.name
				this.email = dto.email
				//county = dto.county,
				this.billsOnHmis = dto.billsOnHmis!!
				this.billsOnDevice = dto.billsOnDevice!!
				this.billsOnPortal = dto.billsOnPoral!!
				this.latitude = dto.latitude
				this.longitude = dto.longitude
				this.tier = dto.tier
				this.region = region
				this.mainFacility = mainFacility
				this.canUseOtpVerificationFailOver = dto.canUseOtpVerificationFailOver!!
				this.verificationType = VerificationType.valueOf(dto.verificationType.toString())
			}
			repo.save(provider)
			return ResultFactory.getSuccessResult(provider)

		} else {
			val provider = Provider(
				id = null,
				name = dto.name,
				email = dto.email,
				//county = dto.county,
				latitude = dto.latitude,
				longitude = dto.longitude,
				tier = dto.tier,
				mainFacility = mainFacility,
				region = region
			)
			repo.save(provider)
			return ResultFactory.getSuccessResult(provider)
		}


	}

	@Transactional
	override fun findByTier(tier: ProviderTier, page: Int, size: Int): Result<Page<Provider>> {
		val request = PageRequest.of(page - 1, size)
		val providers = repo.findByTier(tier, request)
		return ResultFactory.getSuccessResult(providers)
	}

	override fun findByName(name: String, page: Int, size: Int): Result<Page<Provider>> {
		val request = PageRequest.of(page - 1, size)
		val providers = repo.searchByName(name, request)
		return ResultFactory.getSuccessResult(providers)
	}

	@Transactional(readOnly = true)
	override fun findAllProviders(page: Int, size: Int): Result<Page<Provider>> {
		val request = PageRequest.of(page - 1, size)
		val pages = repo.findAll(request)
		return ResultFactory.getSuccessResult(pages)
	}

	@Transactional(readOnly = true)
	override fun findByRegion(regionId: Long, page: Int, size: Int): Result<Page<Provider>> {
		val region = regionRepo.findById(regionId)
		if (region.isEmpty) return ResultFactory.getFailResult("No such region was found")
		val pageRequest = PageRequest.of(page - 1, size)
		region.get().let { r ->
			val providers = repo.findByRegion(r, pageRequest)
			return ResultFactory.getSuccessResult(providers)
		}
	}

	@Transactional(readOnly = true)
	override fun findWhiteListByProvider(
		providerId: Long,
		page: Int,
		size: Int
	): Result<Page<Whitelist>> {
		val provider = repo.findById(providerId)
		val pageRequest = PageRequest.of(page - 1, size)
		if (provider.isPresent) {
			val whitelist = whiteListRepo.findByProvider(provider.get(), pageRequest)
			return ResultFactory.getSuccessResult(whitelist)
		}
		return ResultFactory.getFailResult("No provider with ID $providerId was found")
	}

	@Transactional(readOnly = true)
	override fun findWhiteListByBenefit(
		benefitId: Long,
		page: Int,
		size: Int
	): Result<Page<Whitelist>> {
		val benefit = benefitCatalogRepo.findById(benefitId)
		val pageRequest = PageRequest.of(page - 1, size)
		if (benefit.isPresent) {
			val whitelist = whiteListRepo.findByBenefit(benefit.get(), pageRequest)
			return ResultFactory.getSuccessResult(whitelist)
		}
		return ResultFactory.getFailResult("No benefit with ID $benefitId was found")
	}

	@Transactional(rollbackFor = [Exception::class])
	override fun addWhitelist(dto: WhiteListDTO): Result<Whitelist> {
		val benefit = benefitCatalogRepo.findById(dto.benefitId)
		val provider = repo.findById(dto.providerId)
		return if (benefit.isPresent) {
			if (provider.isPresent) {
				val white = Whitelist(
					benefit = benefit.get(),
					provider = provider.get()
				)
				whiteListRepo.save(white)
				ResultFactory.getSuccessResult(white)
			} else {
				ResultFactory.getFailResult("No provider with ID ${dto.providerId} was found")
			}
		} else {
			ResultFactory.getFailResult("No benefit with ID ${dto.benefitId} was found")
		}
	}

	@Transactional(rollbackFor = [Exception::class])
	override fun addMultipleMappings(
		providerId: Long,
		dto: MultipleWhiteListDTO
	): Result<MutableList<Whitelist>> {
		val provider = repo.findById(providerId)
		val benefits = benefitCatalogRepo.findByIds(dto.benefitIds)
		return if (provider.isPresent) {
			if (benefits.size > 0) {
				val mappings = mutableListOf<Whitelist>()
				benefits.forEach { b ->
					val white = Whitelist(
						benefit = b,
						provider = provider.get()
					)
					mappings.add(white)
				}
				whiteListRepo.saveAll(mappings)
				ResultFactory.getSuccessResult(mappings)
			} else {
				ResultFactory.getFailResult("No benefit in the catalog matched the identifier(s) provided")
			}
		} else {
			ResultFactory.getFailResult("No provider with ID $providerId was found")
		}
	}

	override fun saveProvidersFromFile(file: MultipartFile): Result<List<Provider>> {

		val importedProviders: List<Provider>? = excelHelper.excelToproviders(file.inputStream)
		if (importedProviders != null) {
			providerRepository.saveAll(importedProviders.toList())
		}
		return ResultFactory.getSuccessResult(msg = "Successfully saved providers")
	}

	@Transactional(rollbackFor = [Exception::class])
	override fun saveProviderMappingFromFile(file: MultipartFile): Result<List<PayerProviderMapping>> {
		val importedProviderMapping: List<PayerProviderMapping>? = excelHelper.excelToPayerProviderMapping(file.inputStream)
		if (importedProviderMapping != null) {
			payerProviderMappingRepository.saveAll(importedProviderMapping.toList())
		}
		return ResultFactory.getSuccessResult(msg = "Successfully saved provider mapping")
	}

	@Transactional(rollbackFor = [Exception::class])
	override fun updateProviderMappingFromFile(file: MultipartFile): Result<List<PayerProviderMapping>> {
		excelHelper.updateBritamPayerProviderMapping(file.inputStream)?.forEach { payerProviderMapping ->
			val newCode = payerProviderMapping.code
			val optional = payerProviderMappingRepository.findById(payerProviderMapping.id)
			val payerProviderMap = optional.get()
			payerProviderMap.apply {
				this.code = newCode
			}
			payerProviderMappingRepository.save(payerProviderMapping)

		}
		return ResultFactory.getSuccessResult(msg = "Successfully updated provider mapping")
	}

	override fun getNearbyProviders(beneficiaryId:Long, latitude:Double, longitude:Double, radius:Double, page:Int, size:Int): Result<List<NearbyProvidersDTO>> {
		val beneficiary = validationService.validateAndGetBeneficiary(beneficiaryId)
		val providersIn = mutableSetOf<Long>()
		val providersNotIn = mutableSetOf<Long>()
		beneficiary.category.restrictionType?.let { restrictionType ->
			val categoryRestrictions = categoryRestrictionMappingRepo.getProviderRestrictionsForACategory(category = beneficiary.category)
			val providerInRestrictionIds = categoryRestrictions.stream().map { it.provider.id ?:0 }.collect(Collectors.toSet())
			if(restrictionType == RestrictionType.INCLUSIVE){
				providersIn.addAll(providerInRestrictionIds)
			}else {
				providersNotIn.addAll(providerInRestrictionIds)
			}
		}

		val benefits = benefitRepository.findByCategory(category = beneficiary.category)
		benefits.forEach { benefit ->
			benefit.restriction?.let { restriction ->
					val benefitRestrictions = benefitRestrictionProviderSetRepo.findByRestriction(restriction.id ?:0)
					val providerInRestrictionIds = benefitRestrictions.stream().map { it.provider.id ?:0 }.collect(Collectors.toSet())
					if(restriction.restrictionType == RestrictionType.INCLUSIVE){
						providersIn.addAll(providerInRestrictionIds)
					}else {
						providersNotIn.addAll(providerInRestrictionIds)
					}
			}
		}

		val payers = payerRepository.findPayerByBeneficiaryId(beneficiaryId)
		val payerIds = payers.stream().map(Payer::id).distinct().collect(Collectors.toList())
		return ResultFactory.getSuccessResult(repo.findNearbyProviders(latitude = latitude,
			longitude = longitude, radius = radius, page =page -1, size = size,
			ids = payerIds, providersInIds = providersIn.ifEmpty { null },
			providersNotInIds = providersNotIn.ifEmpty { null }))
	}

	override fun updateProvider(providerId:Long, dto: ProviderUpdateDTO): Result<Boolean> {
		val providerOpt = repo.findById(providerId)
		if(providerOpt.isPresent) {
			val provider = providerOpt.get()
			dto.latitude?.let {
				provider.latitude = it
			}
			dto.longitude?.let {
				provider.longitude = it
			}
			dto.canUseOtpVerificationFailOver?.let {
				provider.canUseOtpVerificationFailOver = it
			}
			repo.save(provider)
			return ResultFactory.getSuccessResult("Provider updated successfully")
		}
		return ResultFactory.getFailResult("Invalid Provider Id")
	}

	override fun multiProviderUpdate(dto: MultiProviderUpdateDTO): Result<Boolean> {
		var countErrors =0
		dto.providers.forEach { providerDto ->
			val providerOpt = repo.findById(providerDto.providerId ?:0)
			if(providerOpt.isPresent) {

				val provider = providerOpt.get()
				providerDto.latitude?.let {
					provider.latitude = it
				}
				providerDto.longitude?.let {
					provider.longitude = it
				}
				repo.save(provider)
			}else{
				countErrors++
			}
		}
		return ResultFactory.getSuccessResult("Successfully Updated with $countErrors errors")
	}

	override fun getProvidersForCategoryFilteredByRestriction(
		payerId: Long,
		categoryId: Long,
		page: Int,
		size: Int
	): Result<Page<PayerProviderMapping>> {
		val payer = payerRepository.findById(payerId).orElseThrow {
			NotFoundRequestException("No Payer with Id $payerId")
		}
		val category = categoryRepository.findById(categoryId).orElseThrow {
			NotFoundRequestException("No Category with Id $categoryId")
		}
		val request = PageRequest.of(page - 1, size)
		val restriction = category.restrictionType
		val restrictions = categoryRestrictionMappingRepo.getProviderRestrictionsForACategory(category)
		val providerIdsInRestriction = restrictions.stream().map { entity -> entity.provider }.collect(Collectors.toList())
		return when (restriction) {
			RestrictionType.INCLUSIVE -> {
				ResultFactory.getSuccessResult(data = payerProviderMappingRepository.findByPayerAndProviderIn(payer, providerIdsInRestriction,request))
			}

			RestrictionType.EXCLUSIVE -> {
				ResultFactory.getSuccessResult(data = payerProviderMappingRepository.findByPayerAndProviderNotIn(payer, providerIdsInRestriction,request))
			}
			else -> {
				ResultFactory.getSuccessResult(data = payerProviderMappingRepository.findByPayer(payer,request))
			}
		}
	}

	override fun getProvidersForBenefitFilteredByRestriction(
		benefitId: Long,
		page: Int,
		size: Int
	): Result<CustomPaging<PayerProviderMappingProjectionDTO>> {
		val benefit = validateAndGetBenefitById(benefitId)
		val category = categoryRepository.findById(benefit.category.id).get()
		val request = PageRequest.of(page - 1, size)
		val restriction = category.restrictionType
		val restrictions = categoryRestrictionMappingRepo.getProviderRestrictionsForACategory(category)
		val providerIdsInRestriction = restrictions.stream().map { entity -> entity.provider }.collect(Collectors.toList())
		val payerProviderMapping : Page<PayerProviderMapping>
		val providers = when (restriction) {
			RestrictionType.INCLUSIVE -> {
				if(providerIdsInRestriction.isNullOrEmpty()){
					throw NotFoundRequestException("Category has an ${restriction.name} restriction but no providers in the restriction")
				}
				payerProviderMapping = payerProviderMappingRepository.findByPayerAndProviderIn(benefit.payer, providerIdsInRestriction,request)
				val mappedProviders = benefitProviderMappingRepo.getAllProvidersByBenefitIdInclusive(benefitId, providerIdsInRestriction)
				val mappedProviderIds = mappedProviders.stream().map { benProvMap -> benProvMap.provider?.id ?:0
				}.collect(Collectors.toList())

				payerProviderMapping.stream().map {
					val mapped = mappedProviderIds.contains(it.provider?.id)
					PayerProviderMappingProjectionDTO(it.id,it.code,it.provider?.id,
						it.provider?.name,it.provider?.tier?.name,it.provider?.region?.name,
						it.provider?.region?.country?.name,mapped)
				}.sorted(Comparator.comparing(PayerProviderMappingProjectionDTO::mapped,Comparator.reverseOrder())).collect(Collectors.toList())

			}
			RestrictionType.EXCLUSIVE -> {
				payerProviderMapping = if(providerIdsInRestriction.isNullOrEmpty()){
					payerProviderMappingRepository.findByPayer(benefit.payer,request)
				}else{
					payerProviderMappingRepository.findByPayerAndProviderNotIn(benefit.payer, providerIdsInRestriction,request)
				}

				val mappedProviders = benefitProviderMappingRepo.getAllProvidersByBenefitIdExclusive(benefitId, providerIdsInRestriction)
				val mappedProviderIds = mappedProviders.stream().map { benProvMap -> benProvMap.provider?.id ?:0
				}.collect(Collectors.toList())

				payerProviderMapping.stream().map {
					val mapped = mappedProviderIds.contains(it.provider?.id)
					PayerProviderMappingProjectionDTO(it.id,it.code,it.provider?.id,
						it.provider?.name,it.provider?.tier?.name,it.provider?.region?.name,
						it.provider?.region?.country?.name,mapped)
				}.sorted(Comparator.comparing(PayerProviderMappingProjectionDTO::mapped,Comparator.reverseOrder())).collect(Collectors.toList())
			}
			else -> {
				payerProviderMapping = payerProviderMappingRepository.findByPayer(benefit.payer,request)
				val mappedProviders = benefitProviderMappingRepo.getAllPayerBenefitMappings(benefitId)
				val mappedProviderIds = mappedProviders.stream().map { benProvMap -> benProvMap.provider?.id ?:0
				}.collect(Collectors.toList())

				payerProviderMapping.stream().map {
					val mapped = mappedProviderIds.contains(it.provider?.id)
					PayerProviderMappingProjectionDTO(it.id,it.code,it.provider?.id,
						it.provider?.name,it.provider?.tier?.name,it.provider?.region?.name,
						it.provider?.region?.country?.name,mapped)
				}.sorted(Comparator.comparing(PayerProviderMappingProjectionDTO::mapped,Comparator.reverseOrder())).collect(Collectors.toList())
			}
		}
		return ResultFactory.getSuccessResult(data = customPaging(providers,payerProviderMapping))
	}

	override fun getProvidersForBenefitFilteredByRestrictionAndRegion(
		benefitId: Long,
		regionId: Long,
		page: Int,
		size: Int
	): Result<List<PayerProviderMappingProjectionDTO>> {
		val benefit = validateAndGetBenefitById(benefitId)
		val region = validateAndGetRegionById(regionId)
		val category = categoryRepository.findById(benefit.category.id).get()
		val request = PageRequest.of(page - 1, size)
		val restriction = category.restrictionType
		val restrictions = categoryRestrictionMappingRepo.getProviderRestrictionsForACategory(category)
		val providerIdsInRestriction = restrictions.stream().map { entity -> entity.provider }.collect(Collectors.toList())
		return when (restriction) {
			RestrictionType.INCLUSIVE -> {
				if(providerIdsInRestriction.isNullOrEmpty()){
					throw NotFoundRequestException("Category has an ${restriction.name} restriction but no providers in the restriction")
				}

				val payerProviderMapping = payerProviderMappingRepository.findByPayerAndProviderInAndProvider_region(benefit.payer, providerIdsInRestriction,region,request)
				val mappedProviders = benefitProviderMappingRepo.getAllProvidersByBenefitIdInclusive(benefitId, providerIdsInRestriction)
				val mappedProviderIds = mappedProviders.stream().map { benProvMap -> benProvMap.provider?.id ?:0
				}.collect(Collectors.toList())

				val providers = payerProviderMapping.stream().map {
					val mapped = mappedProviderIds.contains(it.provider?.id)
					PayerProviderMappingProjectionDTO(it.id,it.code,it.provider?.id,
						it.provider?.name,it.provider?.tier?.name,it.provider?.region?.name,
						it.provider?.region?.country?.name,mapped)
				}.sorted(Comparator.comparing(PayerProviderMappingProjectionDTO::mapped,Comparator.reverseOrder())).collect(Collectors.toList())
				ResultFactory.getSuccessResult(data = providers)
			}
			RestrictionType.EXCLUSIVE -> {
				val payerProviderMapping = if(providerIdsInRestriction.isNullOrEmpty()){
					payerProviderMappingRepository.findByPayerAndProvider_region(benefit.payer,region,request)
				}else{
					payerProviderMappingRepository.findByPayerAndProviderNotInAndProvider_region(benefit.payer, providerIdsInRestriction,region,request)
				}

				val mappedProviders = benefitProviderMappingRepo.getAllProvidersByBenefitIdExclusive(benefitId, providerIdsInRestriction)
				val mappedProviderIds = mappedProviders.stream().map { benProvMap -> benProvMap.provider?.id ?:0
				}.collect(Collectors.toList())

				val providers = payerProviderMapping.stream().map {
					val mapped = mappedProviderIds.contains(it.provider?.id)
					PayerProviderMappingProjectionDTO(it.id,it.code,it.provider?.id,
						it.provider?.name,it.provider?.tier?.name,it.provider?.region?.name,
						it.provider?.region?.country?.name,mapped)
				}.sorted(Comparator.comparing(PayerProviderMappingProjectionDTO::mapped,Comparator.reverseOrder())).collect(Collectors.toList())
				ResultFactory.getSuccessResult(data = providers)
			}
			else -> {
				val payerProviderMapping = payerProviderMappingRepository.findByPayerAndProvider_region(benefit.payer,region,request)
				val mappedProviders = benefitProviderMappingRepo.getAllPayerBenefitMappings(benefitId)
				val mappedProviderIds = mappedProviders.stream().map { benProvMap -> benProvMap.provider?.id ?:0
				}.collect(Collectors.toList())

				val providers = payerProviderMapping.stream().map {
					val mapped = mappedProviderIds.contains(it.provider?.id)
					PayerProviderMappingProjectionDTO(it.id,it.code,it.provider?.id,
						it.provider?.name,it.provider?.tier?.name,it.provider?.region?.name,
						it.provider?.region?.country?.name,mapped)
				}.sorted(Comparator.comparing(PayerProviderMappingProjectionDTO::mapped,Comparator.reverseOrder())).collect(Collectors.toList())
				ResultFactory.getSuccessResult(data = providers)
			}
		}
	}

	override fun searchMappedProvidersForABenefit(
		benefitId: Long,
		regionId: Long,
		facilityName: String,
		page: Int,
		size: Int
	): Result<Page<BenefitProviderMappingProjectionDTO>> {
		val benefit = validateAndGetBenefitById(benefitId)
		val region = validateAndGetRegionById(regionId)
		val request = PageRequest.of(page - 1, size)
		return ResultFactory.getSuccessResult(benefitProviderMappingRepo.searchByBenefitAndProviderNameAndRegion(benefit,region,facilityName,request))
	}

	override fun getProviderAccounts(providerId: Long): Result<List<ProviderBankAccount>> {
		val provider = validationService.validateAndGetProvider(providerId)
		val accounts = bankAccountRepository.findByProvider(provider)
		return ResultFactory.getSuccessResult(accounts)
	}

	override fun getProviderAccount(accountId: Long): Result<ProviderBankAccount> {
		val account = validationService.validateAndGetProviderBankAccount(accountId)
		return ResultFactory.getSuccessResult(account)
	}

	override suspend fun createProviderAccount(dto: CreateProviderAccountDto): Result<Boolean> {
		val provider = validationService.validateAndGetProvider(dto.providerId)
		val account = ProviderBankAccount(
			 bankName = dto.bankName,
			 bankBranch = dto.bankBranch,
			 branchCode = dto.branchCode,
			 bankCode = dto.bankCode,
			 swiftCode = dto.swiftCode,
			 accountName = dto.accountName,
			 accountNumber = dto.accountNumber,
			 provider = provider
		)
		bankAccountRepository.save(account)
		auditLogService.log(AuditLoggingDTO(
			action = "Provider Account Created",
			bankAccount = account,
			user = dto.actionedBy,
			type = ChangeLogType.CREATE
		))
		return ResultFactory.getSuccessResult(CREATED_SUCCESSFULLY)
	}

	override suspend fun createPayerMappedProviderAccount(dto: CreateMappedProviderAccountDto): Result<Boolean> {
		val payer = validationService.validateAndGetPayer(dto.payerId)
		val provider = validationService.validateAndGetProvider(dto.providerId)
		val providerBankAccount = bankAccountRepository.save( ProviderBankAccount(
			bankName = dto.bankName,
			bankBranch = dto.bankBranch,
			branchCode = dto.branchCode,
			bankCode = dto.bankCode,
			swiftCode = dto.swiftCode,
			accountName = dto.accountName,
			accountNumber = dto.accountNumber,
			provider = provider
		))
		auditLogService.log(AuditLoggingDTO(
			action = "Provider Account Created",
			bankAccount = providerBankAccount,
			user = dto.actionedBy,
			type = ChangeLogType.CREATE
		))
		payerProviderBankAccountMappingRepository.save(PayerProviderBankAccountMapping(
			payer = payer,
			providerBankAccount = providerBankAccount
		))
		auditLogService.log(AuditLoggingDTO(
			action = "Provider Account Mapped",
			bankAccount = providerBankAccount,
			user = dto.actionedBy,
			type = ChangeLogType.UPDATE
		))
		return ResultFactory.getSuccessResult(CREATED_SUCCESSFULLY)
	}

	override suspend fun updateProviderAccount(accountId: Long, dto: UpdateProviderAccountDto): Result<Boolean> {
		val account = validationService.validateAndGetProviderBankAccount(accountId)
		dto.bankName?.let {
			account.bankName = it
		}
		dto.bankCode?.let {
			account.bankCode = it
		}
		dto.bankBranch?.let {
			account.bankBranch = it
		}
		dto.branchCode?.let {
			account.branchCode = it
		}
		dto.accountName?.let {
			account.accountName = it
		}
		dto.accountNumber?.let {
			account.accountNumber = it
		}
		dto.swiftCode?.let {
			account.swiftCode = it
		}
		providerBankAccountRepository.save(account)

		auditLogService.log(AuditLoggingDTO(
			action = "Provider Account Update",
			bankAccount = account,
			user = dto.actionedBy,
			type = ChangeLogType.UPDATE
		))

		return ResultFactory.getSuccessResult(UPDATED_SUCCESSFULLY)
	}

	override suspend fun mapProviderAccountToPayer(dto: MapProviderAccountToPayerDto): Result<Boolean> {
		val payer = validationService.validateAndGetPayer(dto.payerId)
		val account = validationService.validateAndGetProviderBankAccount(dto.accountId)

		val mapping = PayerProviderBankAccountMapping(
			payer = payer,
			providerBankAccount = account
		)
		payerProviderBankAccountMappingRepository.save(mapping)
		auditLogService.log(AuditLoggingDTO(
			action = "Provider Account Mapped",
			bankAccount = account,
			user = dto.actionedBy,
			type = ChangeLogType.UPDATE
		))
		return ResultFactory.getSuccessResult(SUCCESSFULLY_MAPPED)
	}

	override suspend fun mapProviderUsersToPayer(dto: MapProviderUserToPayerDto): Result<Boolean> {
		val payer = validationService.validateAndGetPayer(dto.payerId)
		val provider = validationService.validateAndGetProvider(dto.providerId)
		val mapping = PayerProviderUserMapping(
			payer = payer,
			provider = provider,
			userId = dto.userId,
			contactPerson = dto.contactPerson,
		)
		payerProviderUserMappingRepository.save(mapping)

		auditLogService.log(AuditLoggingDTO(
			action = "Mapped Provider User",
			payerProviderUserMapping = mapping,
			user = dto.actionedBy,
			type = ChangeLogType.CREATE
		))
		return ResultFactory.getSuccessResult(SUCCESSFULLY_MAPPED)
	}

	private fun validateAndGetRegionById(regionId: Long) : Region{
		return regionRepo.findById(regionId).orElseThrow {
			NotFoundRequestException("No Region with Id $regionId")
		}
	}

	private fun validateAndGetBenefitById(benefitId: Long) : Benefit{
		return benefitRepository.findById(benefitId).orElseThrow {
			NotFoundRequestException("No Benefit with Id $benefitId")
		}
	}

	override fun getProvidersByPayer(
		payerId: Long,
		providerName: String?,
		page: Int,
		size: Int
	): Result<Page<PayerProviderMappingProjectionDTO>> {
		val payer = payerRepository.findById(payerId)
		if (payer.isEmpty) {
			return ResultFactory.getFailResult("Payer with id $payerId not found")
		}

		val pageable = PageRequest.of(page - 1, size)

		val providers = payerProviderMappingRepository.filterPayerProviders(
			payerId = payerId,
			status = PayerProviderMapping.PayerProviderMappingStatus.ACTIVE,
			query = providerName,
			countryId = null,
			regionId = null,
			providerType = null,
			tier = null,
			mainFacilityId = null,
			pageable = pageable
		)

		val providerDtos = providers.map { mapping ->
			PayerProviderMappingProjectionDTO(
				id = mapping.id,
				code = mapping.code,
				providerId = mapping.provider?.id,
				providerName = mapping.provider?.name,
				tier = mapping.provider?.tier?.name,
				region = mapping.provider?.region?.name,
				country = mapping.provider?.region?.country?.name,
				mapped = true
			)
		}

		return ResultFactory.getSuccessResult(providerDtos)
	}


}
