package net.lctafrica.membership.api.service

import net.lctafrica.membership.api.config.NotFoundRequestException
import net.lctafrica.membership.api.domain.Beneficiary
import net.lctafrica.membership.api.domain.BeneficiaryRepository
import net.lctafrica.membership.api.domain.BeneficiaryStaging
import net.lctafrica.membership.api.domain.BeneficiaryStagingRepository
import net.lctafrica.membership.api.domain.Benefit
import net.lctafrica.membership.api.domain.BenefitCatalog
import net.lctafrica.membership.api.domain.BenefitCatalogRepository
import net.lctafrica.membership.api.domain.BenefitRepository
import net.lctafrica.membership.api.domain.Category
import net.lctafrica.membership.api.domain.CategoryRepository
import net.lctafrica.membership.api.domain.ConfigCatalog
import net.lctafrica.membership.api.domain.ConfigCatalogRepository
import net.lctafrica.membership.api.domain.DeviceAllocation
import net.lctafrica.membership.api.domain.DeviceAllocationRepo
import net.lctafrica.membership.api.domain.DeviceCatalog
import net.lctafrica.membership.api.domain.DeviceCatalogRepo
import net.lctafrica.membership.api.domain.DeviceSim
import net.lctafrica.membership.api.domain.DeviceSimRepo
import net.lctafrica.membership.api.domain.Payer
import net.lctafrica.membership.api.domain.PayerPolicyMapping
import net.lctafrica.membership.api.domain.PayerPolicyMappingRepository
import net.lctafrica.membership.api.domain.PayerRepository
import net.lctafrica.membership.api.domain.BenefitRestriction
import net.lctafrica.membership.api.domain.BenefitRestrictionRepo
import net.lctafrica.membership.api.domain.Plan
import net.lctafrica.membership.api.domain.PlanRepository
import net.lctafrica.membership.api.domain.Policy
import net.lctafrica.membership.api.domain.PolicyRepository
import net.lctafrica.membership.api.domain.Provider
import net.lctafrica.membership.api.domain.ProviderBankAccount
import net.lctafrica.membership.api.domain.ProviderBankAccountRepository
import net.lctafrica.membership.api.domain.ProviderRepository
import net.lctafrica.membership.api.domain.ServiceCatalog
import net.lctafrica.membership.api.domain.ServiceCatalogRepository
import org.springframework.stereotype.Service

@Service("validationServiceImpl")
class ValidationServiceImpl(
    private val benefitRepository: BenefitRepository,
    private val categoryRepository: CategoryRepository,
    private val beneficiaryRepository: BeneficiaryRepository,
    private val providerRepository: ProviderRepository,
    private val payerRepository: PayerRepository,
    private val benefitRestrictionRepo: BenefitRestrictionRepo,
    private val serviceCatalogRepository: ServiceCatalogRepository,
    private val benefitCatalogRepository: BenefitCatalogRepository,
    private val policyRepo: PolicyRepository,
    private val planRepository: PlanRepository,
    private val deviceCatalogRepo: DeviceCatalogRepo,
    private val deviceAllocationRepo: DeviceAllocationRepo,
    private val deviceSimRepo: DeviceSimRepo,
    private val beneficiaryStagingRepository: BeneficiaryStagingRepository,
    private val configCatalogRepository: ConfigCatalogRepository,
    private val providerBankAccountRepository: ProviderBankAccountRepository,
    private val payerPolicyMappingRepository: PayerPolicyMappingRepository,
) : IValidationService {

    override fun validateAndGetCategory(categoryId: Long): Category {
        return categoryRepository.findById(categoryId).orElseThrow {
            NotFoundRequestException("Invalid Category id")
        }
    }

    override fun validateAndGetBeneficiary(beneficiaryId: Long): Beneficiary {
        return beneficiaryRepository.findById(beneficiaryId).orElseThrow {
            NotFoundRequestException("Invalid Beneficiary id")
        }
    }

    override fun validateAndGetBenefit(benefitId: Long): Benefit {
        return benefitRepository.findById(benefitId).orElseThrow {
            NotFoundRequestException("Invalid Benefit id")
        }
    }

    override fun validateAndGetPayer(payerId: Long): Payer {
        return payerRepository.findById(payerId).orElseThrow {
            NotFoundRequestException("Invalid Payer id")
        }
    }

    override fun validateAndGetPlan(planId: Long): Plan {
        return planRepository.findById(planId).orElseThrow {
            NotFoundRequestException("Invalid Plan id")
        }
    }

    override fun validateAndGetRestriction(restrictionId: Long): BenefitRestriction {
        return benefitRestrictionRepo.findById(restrictionId).orElseThrow {
            NotFoundRequestException("Invalid restriction id")
        }
    }

    override fun validateAndGetProvider(providerId: Long): Provider {
        return providerRepository.findById(providerId).orElseThrow {
            NotFoundRequestException("Provider with id $providerId is invalid")
        }
    }

    override fun validateAndGetServiceCatalog(catalogId: Long): ServiceCatalog {
        return serviceCatalogRepository.findById(catalogId).orElseThrow {
            NotFoundRequestException("Service catalog with id $catalogId is invalid")
        }
    }

    override fun validateAndGetBenefitCatalog(catalogId: Long): BenefitCatalog {
        return benefitCatalogRepository.findById(catalogId).orElseThrow {
            NotFoundRequestException("Benefit catalog with id $catalogId doesn't exist")
        }
    }

    override fun validateAndGetPolicy(policyId: Long): Policy {
        return policyRepo.findById(policyId).orElseThrow {
            NotFoundRequestException("Policy with id $policyId doesn't exist")
        }
    }

    override fun validateAndGetDeviceCatalog(catalogId: Long): DeviceCatalog {
        return deviceCatalogRepo.findById(catalogId).orElseThrow {
            NotFoundRequestException("Device catalog with id $catalogId doesn't exist")
        }
    }

    override fun validateAndGetDeviceSIM(simId: Long): DeviceSim {
        return deviceSimRepo.findById(simId).orElseThrow {
            NotFoundRequestException("Device SIM with id $simId doesn't exist")
        }
    }

    override fun validateAndGetDeviceAllocation(allocationId: Long): DeviceAllocation {
        return deviceAllocationRepo.findById(allocationId).orElseThrow {
            NotFoundRequestException("Allocation id $allocationId doesn't exist")
        }
    }

    override fun validateAndGetCategoryByPolicyIdAndCategoryName(policyId: Long, categoryName: String): Category {
        val policy = validateAndGetPolicy(policyId)
        return categoryRepository.searchByPolicyIdAndCategoryName(
            policyId = policy.id,
            categoryName = categoryName
        ) ?: throw NotFoundRequestException("No category \"$categoryName\" found in policy ${policy.plan.name} (${policy.id})")
    }

    override fun validateAndGetBeneficiaryStaging(stagedId: Long): BeneficiaryStaging {
        return beneficiaryStagingRepository.findById(stagedId).orElseThrow {
            NotFoundRequestException("Invalid staging id $stagedId")
        }
    }

    override fun validateAndGetConfig(configId: Long): ConfigCatalog {
        return configCatalogRepository.findById(configId).orElseThrow {
            NotFoundRequestException("Invalid config id $configId")
        }
    }

    override fun validateAndGetProviderBankAccount(accountId: Long): ProviderBankAccount {
        return providerBankAccountRepository.findById(accountId).orElseThrow {
            NotFoundRequestException("Invalid account id $accountId")
        }
    }

    override fun validateAndGetPayerPolicyMapping(mappingId: Long): PayerPolicyMapping {
        return payerPolicyMappingRepository.findById(mappingId).orElseThrow {
            NotFoundRequestException("Invalid mapping id $mappingId")
        }
    }
}