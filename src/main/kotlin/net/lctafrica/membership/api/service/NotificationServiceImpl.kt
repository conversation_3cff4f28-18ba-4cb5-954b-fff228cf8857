package net.lctafrica.membership.api.service

import net.lctafrica.membership.api.dtos.SmsDTO
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.client.MultipartBodyBuilder
import org.springframework.stereotype.Service
import org.springframework.web.reactive.function.BodyInserters
import org.springframework.web.reactive.function.client.WebClient
import org.springframework.web.reactive.function.client.awaitExchange
import org.springframework.web.reactive.function.client.createExceptionAndAwait

@Service("notificationServiceImpl")
class NotificationServiceImpl(
    @Qualifier("notificationWebClientConfig")
    val webClient: WebClient
) : INotificationService {
    val appLogger: Logger = LoggerFactory.getLogger(NotificationServiceImpl::class.java)

    @Value("\${lct-africa.notification.sms-endpoint}")
    lateinit var smsEndPoint: String

    @Value("\${lct-africa.notification.email-plain-endpoint}")
    lateinit var plainEmail: String

    override suspend fun sendSms(message: String, phoneNumber: String?) {
        phoneNumber?.let { phone ->
            val request = SmsDTO(phone = phone, msg = message)
            webClient.post()
                .uri(smsEndPoint)
                .headers { h -> h.setBearerAuth("no-auth") }
                .body(BodyInserters.fromValue(request))
                .awaitExchange { clientResponse ->
                    return@awaitExchange if (clientResponse.statusCode() == HttpStatus.OK) {
                        appLogger.info("Send SMS Ok")
                    } else if (clientResponse.statusCode().is4xxClientError) {
                        throw Exception("sendSms to $phoneNumber => Failed with Status: ${clientResponse.rawStatusCode()}")
                    } else {
                        throw clientResponse.createExceptionAndAwait()
                    }
                }
        }
    }

    override suspend fun sendEmail(subject: String, body: String, recipient: String?) {
        recipient?.let {
            val builder = MultipartBodyBuilder()
            webClient.post()
                .uri { u ->
                    u.path(plainEmail)
                        .queryParam("message", body)
                        .queryParam("subject", subject)
                        .queryParam("recipient", recipient)
                        .build()
                }
                .contentType(MediaType.MULTIPART_FORM_DATA)
                .headers { h -> h.setBearerAuth("no-auth") }
                .body(BodyInserters.fromMultipartData(builder.build()))
                .awaitExchange { clientResponse ->
                    return@awaitExchange if (clientResponse.statusCode() == HttpStatus.OK) {
                        appLogger.info("Send Email Ok")
                    } else if (clientResponse.statusCode().is4xxClientError) {
                        throw Exception("sendEmail to $recipient => Failed with Status: ${clientResponse.rawStatusCode()}")
                    } else {
                        throw clientResponse.createExceptionAndAwait()
                    }
                }
        }
    }

}