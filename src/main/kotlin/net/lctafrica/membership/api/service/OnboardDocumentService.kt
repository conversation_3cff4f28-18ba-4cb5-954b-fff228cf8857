package net.lctafrica.membership.api.service

import net.lctafrica.membership.api.domain.OnboardDocument
import net.lctafrica.membership.api.domain.OnboardDocumentRepository
import net.lctafrica.membership.api.domain.OnboardPrincipal
import net.lctafrica.membership.api.domain.OnboardPrincipalRepository
import net.lctafrica.membership.api.dtos.OnboardDocumentDTO
import net.lctafrica.membership.api.dtos.OnboardDocumentResponseDTO
import net.lctafrica.membership.api.util.toResponseDTO
import org.springframework.stereotype.Service
import javax.persistence.EntityNotFoundException

@Service
class OnboardDocumentService(
    private val onboardDocumentRepository: OnboardDocumentRepository,
    private val onboardPrincipalRepository: OnboardPrincipalRepository,
) {
    fun createOnboardDocument(principal: OnboardPrincipal, document: OnboardDocumentDTO): OnboardDocumentResponseDTO {
        val onboardDocument = OnboardDocument(
            type = document.type,
            url = document.url,
            principal = principal
        )

        val savedDocument = onboardDocumentRepository.save(onboardDocument)
        return savedDocument.toResponseDTO()
    }

    fun getAllPrincipalDocuments(principalId: Long): List<OnboardDocumentResponseDTO> {
        val principal = onboardPrincipalRepository.findById(principalId).orElse(null)
        if (principal == null) {
            throw EntityNotFoundException("Principal not found!")
        }
        return onboardDocumentRepository.findByPrincipalId(principalId).map { it.toResponseDTO() }
    }
}
