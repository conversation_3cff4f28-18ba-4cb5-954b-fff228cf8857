package net.lctafrica.membership.api.service

import net.lctafrica.membership.api.domain.PayerRegion
import net.lctafrica.membership.api.domain.PayerRegionRepo
import net.lctafrica.membership.api.util.Result
import net.lctafrica.membership.api.util.ResultFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service("PayerRegionService")
@Transactional
class PayerRegionService(
    private val repo: PayerRegionRepo,
):IPayerRegionService {

    override fun findByPayerId(payerId: Long): Result<MutableList<PayerRegion>> {
        val payerRegions = repo.findByPayerId(payerId);
        return ResultFactory.getSuccessResult(data = payerRegions)
    }
}