package net.lctafrica.membership.api.service

import java.io.File
import java.util.UUID
import net.lctafrica.membership.api.domain.StagingStatus
import net.lctafrica.membership.api.util.AppConstants
import net.lctafrica.membership.api.util.Result
import net.lctafrica.membership.api.util.ResultFactory
import net.lctafrica.membership.api.util.getFileExtension
import net.lctafrica.membership.api.util.getFileNameWithNoExtension
import org.springframework.batch.core.Job
import org.springframework.batch.core.JobParametersBuilder
import org.springframework.batch.core.launch.JobLauncher
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Service
import org.springframework.web.multipart.MultipartFile
@Service("batchUploadServiceImpl")
class BatchUploadServiceImpl : IBatchUploadService {

    @Autowired
    @Qualifier("batchJobLauncher")
    lateinit var batchJobLauncher: JobLauncher

    @Autowired
    @Qualifier("beneficiaryUploadJob")
    lateinit var beneficiaryUploadJob: Job

    override suspend fun uploadBeneficiaries(
        policyId: Long,
        status: StagingStatus?,
        multipartFile: MultipartFile,
        autoProcessBenefits: Boolean?
    ): Result<Boolean> {
        try {
            val location = System.getProperty("java.io.tmpdir")
            val origFileName = multipartFile.originalFilename
            val fileFullPath = "$location${getFileNameWithNoExtension(origFileName)}_${System.currentTimeMillis()}${getFileExtension(origFileName)}"
            val file = File(fileFullPath)
            multipartFile.transferTo(file)
            val params = JobParametersBuilder()
            params.addString("batchNo", UUID.randomUUID().toString())
            params.addString("fileFullPath",fileFullPath)
            params.addLong("transactionsUploadTime",System.currentTimeMillis())
            params.addLong("policyId",policyId)
            params.addString("status",status?.name ?: StagingStatus.ACTIVATE.name)
            val processBenefits = (autoProcessBenefits == true)
            params.addString("autoProcessBenefits",processBenefits.toString())

            val execution = batchJobLauncher.run(beneficiaryUploadJob, params.toJobParameters())
            println("Job execution status : " + execution.status)
            return ResultFactory.getSuccessResult(msg = AppConstants.UPLOADED_SUCCESSFULLY_P)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return ResultFactory.getFailResult("Processing failed")
    }
}