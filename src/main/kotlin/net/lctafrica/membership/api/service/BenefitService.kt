package net.lctafrica.membership.api.service

import com.fasterxml.jackson.databind.ObjectMapper
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import java.util.stream.Collectors
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import net.lctafrica.membership.api.config.BadRequestException
import net.lctafrica.membership.api.config.NotFoundRequestException
import net.lctafrica.membership.api.domain.ApplicableGender
import net.lctafrica.membership.api.domain.ApplicableMember
import net.lctafrica.membership.api.domain.Beneficiary
import net.lctafrica.membership.api.domain.BeneficiaryBenefitProviderMappingRepo
import net.lctafrica.membership.api.domain.BeneficiaryProviderMapping
import net.lctafrica.membership.api.domain.BeneficiaryRepository
import net.lctafrica.membership.api.domain.BeneficiaryType
import net.lctafrica.membership.api.domain.Benefit
import net.lctafrica.membership.api.domain.BenefitCatalogRepository
import net.lctafrica.membership.api.domain.BenefitDistribution
import net.lctafrica.membership.api.domain.BenefitPayerAdmin
import net.lctafrica.membership.api.domain.BenefitPayerAdminRepository
import net.lctafrica.membership.api.domain.BenefitProviderMapping
import net.lctafrica.membership.api.domain.BenefitProviderMappingRepo
import net.lctafrica.membership.api.domain.BenefitRepository
import net.lctafrica.membership.api.domain.BenefitType
import net.lctafrica.membership.api.domain.Category
import net.lctafrica.membership.api.domain.CategoryRepository
import net.lctafrica.membership.api.domain.CategoryRestrictionMappingRepo
import net.lctafrica.membership.api.domain.CategoryStatus
import net.lctafrica.membership.api.domain.ChangeLogType
import net.lctafrica.membership.api.domain.Gender
import net.lctafrica.membership.api.domain.MemberStatus
import net.lctafrica.membership.api.domain.PayerProviderMappingRepository
import net.lctafrica.membership.api.domain.PayerRepository
import net.lctafrica.membership.api.domain.PlanDate
import net.lctafrica.membership.api.domain.PlanType
import net.lctafrica.membership.api.domain.Policy
import net.lctafrica.membership.api.domain.PreAuthType
import net.lctafrica.membership.api.domain.ProviderBankAccount
import net.lctafrica.membership.api.domain.ProviderBankAccountRepository
import net.lctafrica.membership.api.domain.ProviderRepository
import net.lctafrica.membership.api.domain.RestrictionType
import net.lctafrica.membership.api.domain.ServiceGroup
import net.lctafrica.membership.api.domain.SharedBenefitTracker
import net.lctafrica.membership.api.domain.SharedBenefitTrackerRepository
import net.lctafrica.membership.api.dtos.AccessModeType
import net.lctafrica.membership.api.dtos.AddBenefitPayerAdminDto
import net.lctafrica.membership.api.dtos.AuditLoggingDTO
import net.lctafrica.membership.api.dtos.BatchBenefitPayerProviderMappingDto
import net.lctafrica.membership.api.dtos.BatchBenefitProviderUnmappingDto
import net.lctafrica.membership.api.dtos.BeneficiariesCategoryChangeDto
import net.lctafrica.membership.api.dtos.BeneficiaryBenefitProcess
import net.lctafrica.membership.api.dtos.BeneficiaryBenefitProviderMappingDto
import net.lctafrica.membership.api.dtos.BeneficiaryBenefitsDto
import net.lctafrica.membership.api.dtos.BeneficiaryProviderMappingProjectionDTO
import net.lctafrica.membership.api.dtos.BeneficiaryStatusUpdateDTO
import net.lctafrica.membership.api.dtos.BenefitAdminPlansProjectionDTO
import net.lctafrica.membership.api.dtos.BenefitDTO
import net.lctafrica.membership.api.dtos.BenefitPayerMappingDTO
import net.lctafrica.membership.api.dtos.BenefitProviderMappingDto
import net.lctafrica.membership.api.dtos.BenefitStatusUpdateDTO
import net.lctafrica.membership.api.dtos.CategoryChangeDto
import net.lctafrica.membership.api.dtos.CategoryStatusUpdateDTO
import net.lctafrica.membership.api.dtos.ChangeCategoryDTO
import net.lctafrica.membership.api.dtos.CreateBeneficiaryDTO
import net.lctafrica.membership.api.dtos.CreateBenefitDTO
import net.lctafrica.membership.api.dtos.DeactivateBenefitDTO
import net.lctafrica.membership.api.dtos.FindBenefitsByServiceGroupsProjectionDTO
import net.lctafrica.membership.api.dtos.GetPlanFromBenefitProjectionDTO
import net.lctafrica.membership.api.dtos.MembershipAuditLogDTO
import net.lctafrica.membership.api.dtos.MigrateBenefitsDto
import net.lctafrica.membership.api.dtos.NestedBenefitDTO
import net.lctafrica.membership.api.dtos.ProcessCategoryDto
import net.lctafrica.membership.api.dtos.ProcessSelectBenefitsDto
import net.lctafrica.membership.api.dtos.SchemePayerDTO
import net.lctafrica.membership.api.dtos.SubBenefitDTO
import net.lctafrica.membership.api.dtos.UpdateCategoryDTO
import net.lctafrica.membership.api.dtos.UpdateType
import net.lctafrica.membership.api.gson
import net.lctafrica.membership.api.util.AppConstants.SAVED_SUCCESSFULLY
import net.lctafrica.membership.api.util.AppConstants.SUCCESS
import net.lctafrica.membership.api.util.AppConstants.UPDATED_SUCCESSFULLY
import net.lctafrica.membership.api.util.Result
import net.lctafrica.membership.api.util.ResultFactory
import net.lctafrica.membership.api.util.dateExpired
import net.lctafrica.membership.api.util.formatPolicyDate
import net.lctafrica.membership.api.util.getAge
import net.lctafrica.membership.api.util.toJsonString
import net.lctafrica.membership.api.webClient
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.kafka.core.KafkaTemplate
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import reactor.core.publisher.Mono


@Suppress("DEPRECATION")
@Service("benefitService")
@Transactional
class BenefitService(
	private val repo: BenefitRepository,
	private val categoryRepo: CategoryRepository,
	private val beneficiaryRepo: BeneficiaryRepository,
	private val trackerRepo: SharedBenefitTrackerRepository,
	private val benefitCatalogRepo: BenefitCatalogRepository,
	private val providerRepo: ProviderRepository,
	private val benefitProviderMappingRepo: BenefitProviderMappingRepo,
	private val payerProviderMappingRepo: PayerProviderMappingRepository,
	private val beneficiaryBenefitProviderMappingRepo: BeneficiaryBenefitProviderMappingRepo,
	private val categoryRestrictionMappingRepo: CategoryRestrictionMappingRepo,
	private val payerRepository: PayerRepository,
	private val benefitRepository: BenefitRepository,
	private val providerBankAccountRepository: ProviderBankAccountRepository,
	private val benefitPayerAdminRepository: BenefitPayerAdminRepository,
	private val validationService: IValidationService,
	private val auditLogService: IAuditLogService,
	private val claimService:IClaimService

	) : IBenefitService {

	val LOG = LoggerFactory.getLogger(BenefitService::class.java)

	@Autowired
	lateinit var objectMapper: ObjectMapper;

	@Autowired
	lateinit var kafka: KafkaTemplate<String, String>

	@Value("\${lct-africa.benefit.topic}")
	lateinit var benefitTopic: String

	@Value("\${lct-africa.benefit.command.base-url}")
	lateinit var benefitUrl: String

	@Value("\${lct-africa.endpoint.create-benefit}")
	lateinit var createBenefitEndpoint: String

	@Value("\${lct-africa.endpoint.suspend-benefits}")
	lateinit var suspendBenefits: String

	@Transactional(readOnly = true)
	override fun findByCategory(categoryId: Long): Result<MutableList<Benefit>> {
		val category = categoryRepo.findById(categoryId)
		if (category.isPresent) {
			val benefits = repo.findByCategory(category.get())
			return ResultFactory.getSuccessResult(benefits)
		}
		return ResultFactory.getFailResult("No category with ID $categoryId was found")
	}

	@Transactional(readOnly = true)
	override fun findByCategory(categoryId: Long, page: Int, size: Int): Result<Page<Benefit>> {
		val category = categoryRepo.findById(categoryId)
		if (category.isPresent) {
			val request = PageRequest.of(page - 1, size);
			val benefits = repo.findByCategory(category.get(), request)
			return ResultFactory.getSuccessResult(benefits)
		}
		return ResultFactory.getFailResult("No category with ID $categoryId was found")
	}

	override fun findBenefitsByPayerId(payerId: Long): Result<MutableList<Benefit>> {
		val benefits = benefitRepository.findByPayerId(payerId)
		return ResultFactory.getSuccessResult(benefits)
	}

	override fun findProviderAccountsByPayerId(payerId: Long): Result<MutableList<ProviderBankAccount>> {
		val accounts = providerBankAccountRepository.findByPayerId(payerId)
		return ResultFactory.getSuccessResult(accounts)
	}

	@Transactional(readOnly = true)
	override fun findByCategoryAndName(
		categoryId: Long,
		benefitName: String,
		page: Int,
		size: Int
	): Result<Page<Benefit>> {

		val category = categoryRepo.findById(categoryId)
		if (category.isPresent) {
			val request = PageRequest.of(page - 1, size);
			val benefits = repo.findByCategoryAndBenefitName(category.get(),benefitName, request)
			return ResultFactory.getSuccessResult(benefits)
		}
		return ResultFactory.getFailResult("No category with ID $categoryId was found")


	}

	@Transactional(readOnly = true)
	override fun findMainBenefitsByCategory(categoryId: Long): Result<MutableList<Benefit>> {
		val category = categoryRepo.findById(categoryId)
		if (category.isPresent) {
			val benefits = repo.findMainBenefitsByCategoryShallow(category.get())
			return ResultFactory.getSuccessResult(benefits)
		}
		return ResultFactory.getFailResult("No category with ID $categoryId was found")
	}

	@Transactional(readOnly = false, rollbackFor = [Exception::class])
	override fun addBenefit(dto: BenefitDTO): Result<Benefit> {
		val payer =validationService.validateAndGetPayer(dto.payer.id)
		val category = categoryRepo.findById(dto.categoryId)
		var parentBenefit: Benefit? = null
		if (category.isEmpty) return ResultFactory.getFailResult("No category with ID ${dto.categoryId} was found")
		val byCategoryAndName = repo.findByCategoryAndNameIgnoreCase(category.get(), dto.name)
		var copayAmount = BigDecimal.ZERO
		val benefitRef = benefitCatalogRepo.findById(dto.catalogRefId)
		if (benefitRef.isEmpty) return ResultFactory.getFailResult("No benefit with ID ${dto.catalogRefId} was found in the catalog")
		val byCategoryAndRef = repo.findByCategoryAndRef(category.get(), benefitRef.get())
		if (byCategoryAndRef.isPresent) return ResultFactory.getFailResult("${benefitRef.get().name} has already been setup for this category")
		return when {
			byCategoryAndName.isPresent -> ResultFactory.getFailResult(
				msg = "Benefit [${dto.name}] already exists for this category",
				data = byCategoryAndName.get()
			)
			else -> try {

				return category.map { cat ->
					if (dto.parentBenefitId != null && dto.parentBenefitId > 0) {
						parentBenefit = repo.getById(dto.parentBenefitId)
					}

					if (dto.coPaymentRequired) copayAmount = dto.coPaymentAmount

					return@map Benefit(
						category = cat,
						name = dto.name,
						benefitRef = benefitRef.get(),
						applicableGender = dto.applicableGender,
						applicableMember = dto.applicableMember,
						limit = dto.limit,
						suspensionThreshold = dto.suspensionThreshold,
						preAuthThreshold = dto.preAuthThreshold,
						preAuthType = dto.preAuthType,
						sharing = dto.sharing,
						coPaymentRequired = dto.coPaymentRequired,
						coPaymentAmount = copayAmount,
						waitingPeriod = dto.waitingPeriod,
						parentBenefit = parentBenefit,
						processed = false,
						processedTime = null,
						payer = payer,
						benefitType = dto.benefitType ?: BenefitType.INSURED,
						capitationPeriod = dto.capitationPeriod,
						visitCountPeriod = dto.visitCountPeriod,
						capitationMaxVisitCount = dto.capitationMaxVisitCount,
						capitationFacilitiesCount = dto.capitationFacilitiesCount,
						capitationType = dto.capitationType,
						requireBeneficiaryToSelectProvider=dto.requireBeneficiaryToSelectProvider,
						visitLimit = dto.visitLimit,
					    daysOfAdmissionLimit = dto.daysOfAdmissionLimit,
					    amountPerDayLimit = dto.amountPerDayLimit,
						applicableMinAge = dto.applicableMinAge,
						applicableMaxAge = dto.applicableMaxAge,
						transferable = dto.transferable,
						billable = dto.billable
					)
				}.map { benefit ->
					repo.save(benefit)
					return@map ResultFactory.getSuccessResult(benefit)
				}.get()
			} catch (ex: IllegalArgumentException) {
				return ResultFactory.getFailResult(ex.message)
			}
		}
	}

	override fun findBenefit(benefitId: Long): Result<Benefit> {
		val benefit = validationService.validateAndGetBenefit(benefitId)
		return ResultFactory.getSuccessResult(benefit)
	}

	override fun findBenefits(benefitIds: Set<Long>): Result<List<Benefit>> {
		val benefits = repo.findAllById(benefitIds)
		return ResultFactory.getSuccessResult(benefits)
	}

	@Transactional(readOnly = false, rollbackFor = [Exception::class])
	override fun addMultipleBenefits(dto: NestedBenefitDTO): Result<MutableList<Benefit>> {
		return ResultFactory.getFailResult("Not yet implemented")
	}

	@Transactional(readOnly = false, rollbackFor = [Exception::class])
	override fun removeBenefit(benefit: Benefit): Result<Benefit> {
		return ResultFactory.getFailResult("Not yet implemented")
	}

	override fun processSubBenefit(benefitId: Long): Result<Benefit> {
		val benefit = repo.findByBenefitId(benefit_id = benefitId.toString())
		if(benefit.isPresent) {
			val unprocessedSubBenefit = benefit.get()
			val category = categoryRepo.fetchWithPolicy(unprocessedSubBenefit.category.id)

			with(category) {
				if (category?.status == CategoryStatus.PROCESSED) {

					val policy = this?.policy
					val families = beneficiaryRepo.findFamiliesByCategory(category)

					val trackers = mutableSetOf<SharedBenefitTracker>()

					val unprocessedSubBenefitList= mutableListOf<Benefit>()
					 unprocessedSubBenefitList.add(unprocessedSubBenefit)

					for (family in families) {
						val members = mutableSetOf<Beneficiary>()
						members.add(family)
						members.addAll(family.dependants)

						val indivBenefits = unprocessedSubBenefitList
							.stream()
							.filter { b -> b.sharing == BenefitDistribution.INDIVIDUAL }
							.collect(Collectors.toList())
						println("Individual benefit count is ${indivBenefits.size}")
						for (benefit in indivBenefits) {
							for (m in members) {
								if (benefitEligibilityPasses(m, benefit)) {
									val dto = CreateBeneficiaryDTO(
										id = m.id,
										name = m.name,
										memberNumber = m.memberNumber,
										beneficiaryType = m.beneficiaryType,
										email = m.email,
										phoneNumber = m.phoneNumber,
										gender = m.gender,
										type = m.beneficiaryType,
										jicEntityId = m.jicEntityId,
										apaEntityId = m.apaEntityId,
										joinDate = m.joinDate,
										dob = m.dob
									)
									println("Building ${benefit.name} benefit for ${m.name}")
									val payer = benefit.payer.let { pyr ->
										return@let SchemePayerDTO(
											payerId = pyr.id,
											payerName = pyr.name
										)
									}

									val claimsOptional =  repo.findMainBenefitAggregateId(category
										.id,benefit.parentBenefit!!.id,m.id)


									claimsOptional.ifPresent {

										manualSubBenefitBuildAndGenerateAggregate(
											aggregateId=it.aggregateId,
											catalogId = benefit.benefitRef.id!!,
											benefit = benefit,
											beneficiaries = mutableSetOf(dto),
											policy = policy,
											category = category,
											payer = payer,
											parentBenefitId = benefit.parentBenefit!!.id
										)
									}


								}
							}
							benefit.processed = true
							benefit.processedTime = LocalDateTime.now()
							repo.save(benefit)
						}

						val famBenefits = unprocessedSubBenefitList
							.stream()
							.filter { b -> b.sharing == BenefitDistribution.FAMILY }
							.collect(Collectors.toList())
						for (benefit in famBenefits) {
							val passed = mutableSetOf<CreateBeneficiaryDTO>()
							for (m in members) {
								if (benefitEligibilityPasses(m, benefit)) {
									val dto = CreateBeneficiaryDTO(
										id = m.id,
										name = m.name,
										memberNumber = m.memberNumber,
										beneficiaryType = m.beneficiaryType,
										email = m.email,
										phoneNumber = m.phoneNumber,
										gender = m.gender,
										type = m.beneficiaryType,
										jicEntityId = m.jicEntityId,
										apaEntityId = m.apaEntityId,
										joinDate = m.joinDate,
										dob = m.dob
									)
									passed.add(dto)
								}
								/**
								 * Here, no need to test principal member since they have already been added to members set
								 */
								/*if (benefitEligibilityPasses(family, benefit)) {
								passed.add(
									CreateBeneficiaryDTO(
										id = family.id, name = family.name,
										memberNumber = family.memberNumber, beneficiaryType = family.beneficiaryType,
										email = family.email, phoneNumber = family.phoneNumber
									)
								)
							}*/
							}
							val payer = benefit.payer.let { pyr ->
								return@let SchemePayerDTO(
									payerId = pyr.id,
									payerName = pyr.name
								)
							}
							passed.forEach { beneficiary->
								val claimsOptional =  repo.findMainBenefitAggregateId(category
									.id,benefit.parentBenefit!!.id,beneficiary.id)


								claimsOptional.ifPresent { theClaimsBenefit->

									val result=manualSubBenefitBuildAndGenerateAggregate(
										aggregateId=theClaimsBenefit.aggregateId,
										catalogId = benefit.benefitRef.id!!,
										benefit = benefit,
										policy = policy,
										category = category,
										payer = payer,
										beneficiaries = passed,
										parentBenefitId = benefit.parentBenefit!!.id

									)
									println(result)
									if (result.success) {
										println(result)
										val tracker = SharedBenefitTracker(
											beneficiary = family,
											aggregateId = result.data!!,
											benefit = benefit
										)
										trackers.add(tracker)
										benefit.processed = true
										benefit.processedTime = LocalDateTime.now()
										repo.save(benefit)
									}
								}

							}

						}

						members.map {
							it.processed = true
							it.processedTime = LocalDateTime.now()
							beneficiaryRepo.save(it)
						}

					}
				}

			}
		}
		return ResultFactory.getSuccessResult("Completed processing benefits for subbenefit")
	}

	@Transactional(readOnly = false, rollbackFor = [Exception::class])
	override fun processBenefits(categoryId: Long): Result<Benefit> {

		val category = categoryRepo.fetchWithPolicy(categoryId)

		with(category) {
			if (category?.status == CategoryStatus.UNPROCESSED) {

				val policy = this?.policy
				val families = beneficiaryRepo.findFamiliesByCategory(category!!)
				val unprocessedBenefits = repo.findUnprocessedMainBenefitsByCategory(category)
				val trackers = mutableSetOf<SharedBenefitTracker>()

				for (family in families) {
					val members = mutableSetOf<Beneficiary>()
					members.add(family)
					members.addAll(family.dependants)

					val indivBenefits = unprocessedBenefits
						.stream()
						.filter { b -> b.sharing == BenefitDistribution.INDIVIDUAL }
						.collect(Collectors.toList())
					println("Individual benefit count is ${indivBenefits.size}")
					for (benefit in indivBenefits) {
						for (m in members) {
							if (benefitEligibilityPasses(m, benefit)) {
								val dto = CreateBeneficiaryDTO(
									id = m.id,
									name = m.name,
									memberNumber = m.memberNumber,
									beneficiaryType = m.beneficiaryType,
									email = m.email,
									phoneNumber = m.phoneNumber,
									gender = m.gender,
									type = m.beneficiaryType,
									jicEntityId = m.jicEntityId,
									apaEntityId = m.apaEntityId,
									joinDate = m.joinDate,
									dob = m.dob
								)
								println("Building ${benefit.name} benefit for ${m.name}")
								val payer = benefit.payer.let { pyr ->
									return@let SchemePayerDTO(
										payerId = pyr.id,
										payerName = pyr.name
									)
								}
								buildAndGenerateAggregate(
									catalogId = benefit.benefitRef.id!!,
									benefit = benefit,
									beneficiaries = mutableSetOf(dto),
									policy = policy,
									category = category,
									payer = payer
								)
							}
						}
						benefit.processed = true
						benefit.processedTime = LocalDateTime.now()
						repo.save(benefit)
					}

					val famBenefits = unprocessedBenefits
						.stream()
						.filter { b -> b.sharing == BenefitDistribution.FAMILY }
						.collect(Collectors.toList())
					for (benefit in famBenefits) {
						val passed = mutableSetOf<CreateBeneficiaryDTO>()
						for (m in members) {
							if (benefitEligibilityPasses(m, benefit)) {
								val dto = CreateBeneficiaryDTO(
									id = m.id,
									name = m.name,
									memberNumber = m.memberNumber,
									beneficiaryType = m.beneficiaryType,
									email = m.email,
									phoneNumber = m.phoneNumber,
									gender = m.gender,
									type = m.beneficiaryType,
									jicEntityId = m.jicEntityId,
									apaEntityId = m.apaEntityId,
									joinDate = m.joinDate,
									dob = m.dob
								)
								passed.add(dto)
							}
							/**
							 * Here, no need to test principal member since they have already been added to members set
							 */
							/*if (benefitEligibilityPasses(family, benefit)) {
							passed.add(
								CreateBeneficiaryDTO(
									id = family.id, name = family.name,
									memberNumber = family.memberNumber, beneficiaryType = family.beneficiaryType,
									email = family.email, phoneNumber = family.phoneNumber
								)
							)
						}*/
						}
						val payer = benefit.payer.let { pyr ->
							return@let SchemePayerDTO(
								payerId = pyr.id,
								payerName = pyr.name
							)
						}
						val result =
							buildAndGenerateAggregate(
								benefit.benefitRef.id!!,
								benefit,
								passed,
								policy,
								category,
								payer
							)
						println(result)
						if (result.success) {
							println(result)
							val tracker = SharedBenefitTracker(
								beneficiary = family,
								aggregateId = result.data!!,
								benefit = benefit
							)
							trackers.add(tracker)
							benefit.processed = true
							benefit.processedTime = LocalDateTime.now()
							repo.save(benefit)
						}


					}

					members.map {
						it.processed = true
						it.processedTime = LocalDateTime.now()
						beneficiaryRepo.save(it)
					}

				}

				/* families.forEach { beneficiary ->

				 */
				/**
				 * Push all non-shared benefits to benefit management module individually
				 *//*
                benefits.filter { !it.processed }
                    .filter { benInput -> benInput.sharing == BenefitDistribution.INDIVIDUAL }.map { benefit ->
                        val candidates = mutableSetOf<Beneficiary>()
                        candidates.add(beneficiary)
                        candidates.addAll(beneficiary.dependants)
                        LOG.debug("Inside loop with individual benefit ${benefit.name} for family of ${beneficiary.name}")
                        candidates.filter { input -> benefitEligibilityPasses(input, benefit) }.map { candidate ->
                            CreateBeneficiaryDTO(
                                id = candidate.id,
                                name = candidate.name,
                                memberNumber = candidate.memberNumber,
                                beneficiaryType = candidate.beneficiaryType,
                                email = candidate.email,
                                phoneNumber = candidate.phoneNumber
                            )
                        }.map { dto ->
                            buildAndGenerateAggregate(benefit, mutableSetOf(dto), policy, category, payer)
                        }

                        benefit.processed = true
                        benefit.processedTime = LocalDateTime.now()
                        repo.save(benefit)
                    }

                */
				/**
				 * Push all shared benefits to benefit management module
				 *//*
                benefits.filter { !it.processed }
                    .filter { it.sharing == BenefitDistribution.FAMILY }.map { benefit ->
                        val passed = mutableSetOf<CreateBeneficiaryDTO>()
                        beneficiary.dependants.filter {
                            benefitEligibilityPasses(it, benefit)
                        }.map {
                            CreateBeneficiaryDTO(
                                id = it.id,
                                name = it.name,
                                memberNumber = it.memberNumber,
                                beneficiaryType = it.beneficiaryType,
                                email = it.email,
                                phoneNumber = it.phoneNumber
                            )
                        }.map { passed.add(it) }
                        if (benefitEligibilityPasses(beneficiary, benefit)) passed.add(
                            CreateBeneficiaryDTO(
                                beneficiary.id,
                                beneficiary.name,
                                beneficiary.name,
                                beneficiary.beneficiaryType,
                                beneficiary.email,
                                beneficiary.phoneNumber
                            )
                        )
                        val result = buildAndGenerateAggregate(
                            benefit,
                            passed,
                            policy,
                            category,
                            payer
                        )
                        if (result.success) {
                            val tracker = SharedBenefitTracker(
                                beneficiary = beneficiary,
                                aggregateId = result.data!!,
                                benefit = benefit
                            )
                            trackers.add(tracker)
                            benefit.processed = true
                            benefit.processedTime = LocalDateTime.now()
                            repo.save(benefit)
                        }
                    }

                beneficiary.dependants.map {
                    it.processed = true
                    it.processedTime = LocalDateTime.now()
                    beneficiaryRepo.save(it)
                }
                beneficiary.processed = true
                beneficiary.processedTime = LocalDateTime.now()
                beneficiaryRepo.save(beneficiary)
            }*/

				trackerRepo.saveAll(trackers)
				categoryRepo.save(
					category.copy(
						status = CategoryStatus.PROCESSED
					)
				)
			}
			else {

				val policy = this?.policy
				val benefits = repo.findMainBenefitsByCategory(category!!)
				val unprocessedFamilies =
					beneficiaryRepo.findUnprocessedFamiliesByCategory(category)
				val unprocessedDependents =
					beneficiaryRepo.findUnprocessedDependentsByCategory(category)
				val trackers = mutableSetOf<SharedBenefitTracker>()

				val unprocessedBenefits = repo.findUnprocessedMainBenefitsByCategory(category)

				if(unprocessedBenefits.isNotEmpty()){
					val families = beneficiaryRepo.findFamiliesByCategory(category)
					goThroughEachFamily(families, unprocessedBenefits, policy, category, trackers)
				}else{
					for (dep in unprocessedDependents) {

						val indivBenefits = benefits
							.stream()
							.filter { b -> b.sharing == BenefitDistribution.INDIVIDUAL }
							.collect(Collectors.toList())

						for (benefit in indivBenefits) {
							if (benefitEligibilityPasses(dep, benefit)) {
								val dto = CreateBeneficiaryDTO(
									id = dep.id,
									name = dep.name,
									memberNumber = dep.memberNumber,
									beneficiaryType = dep.beneficiaryType,
									email = dep.email,
									phoneNumber = dep.phoneNumber,
									gender = dep.gender,
									type = dep.beneficiaryType,
									jicEntityId = dep.jicEntityId,
									apaEntityId = dep.apaEntityId,
									joinDate = dep.joinDate,
									dob = dep.dob
								)

								val payer = benefit.payer.let { pyr ->
									return@let SchemePayerDTO(
										payerId = pyr.id,
										payerName = pyr.name
									)
								}
								buildAndGenerateAggregate(
									catalogId = benefit.benefitRef.id!!,
									benefit = benefit,
									beneficiaries = mutableSetOf(dto),
									policy = policy,
									category = category,
									payer = payer
								)
							}

						}

						val famBenefits = benefits
							.stream()
							.filter { b -> b.sharing == BenefitDistribution.FAMILY }
							.collect(Collectors.toList())

						for (benefit in famBenefits) {
							val passed = mutableSetOf<CreateBeneficiaryDTO>()

							if (benefitEligibilityPasses(dep, benefit)) {
								val dto = CreateBeneficiaryDTO(
									id = dep.id,
									name = dep.name,
									memberNumber = dep.memberNumber,
									beneficiaryType = dep.beneficiaryType,
									email = dep.email,
									phoneNumber = dep.phoneNumber,
									gender = dep.gender,
									type = dep.beneficiaryType,
									jicEntityId = dep.jicEntityId,
									apaEntityId = dep.apaEntityId,
									joinDate = dep.joinDate,
									dob = dep.dob
								)
								passed.add(dto)
							}

							val payer = benefit.payer.let { pyr ->
								return@let SchemePayerDTO(
									payerId = pyr.id,
									payerName = pyr.name
								)
							}

							try {
								val tracker = dep.principal?.let {
									trackerRepo.findByBeneficiaryAndBenefit(
										it,
										benefit
									)
								}

								buildAndGenerateAggregate(
									benefit.benefitRef.id!!,
									benefit, passed, policy, category, payer, aggId = tracker?.aggregateId
								)
							} catch (e: Exception) {
							}

							dep.let {
								it.processed = true
								it.processedTime = LocalDateTime.now()
								beneficiaryRepo.save(it)
							}
						}

					}
					for (family in unprocessedFamilies) {
						val members = mutableSetOf<Beneficiary>()
						members.add(family)
						members.addAll(family.dependants)

						val indivBenefits = benefits
							.stream()
							.filter { b -> b.sharing == BenefitDistribution.INDIVIDUAL }
							.collect(Collectors.toList())
						println("Individual benefit count is ${indivBenefits.size}")
						for (benefit in indivBenefits) {
							for (m in members) {
								if (benefitEligibilityPasses(m, benefit)) {
									val dto = CreateBeneficiaryDTO(
										id = m.id,
										name = m.name,
										memberNumber = m.memberNumber,
										beneficiaryType = m.beneficiaryType,
										email = m.email,
										phoneNumber = m.phoneNumber,
										gender = m.gender,
										type = m.beneficiaryType,
										jicEntityId = m.jicEntityId,
										apaEntityId = m.apaEntityId,
										joinDate = m.joinDate,
										dob = m.dob
									)
									println("Building ${benefit.name} benefit for ${m.name}")
									val payer = benefit.payer.let { pyr ->
										return@let SchemePayerDTO(
											payerId = pyr.id,
											payerName = pyr.name
										)
									}
									buildAndGenerateAggregate(
										catalogId = benefit.benefitRef.id!!,
										benefit = benefit,
										beneficiaries = mutableSetOf(dto),
										policy = policy,
										category = category,
										payer = payer
									)
								}
							}
							benefit.processed = true
							benefit.processedTime = LocalDateTime.now()
							repo.save(benefit)
						}

						val famBenefits = benefits
							.stream()
							.filter { b -> b.sharing == BenefitDistribution.FAMILY }
							.collect(Collectors.toList())
						for (benefit in famBenefits) {
							val passed = mutableSetOf<CreateBeneficiaryDTO>()
							for (m in members) {
								if (benefitEligibilityPasses(m, benefit)) {
									val dto = CreateBeneficiaryDTO(
										id = m.id,
										name = m.name,
										memberNumber = m.memberNumber,
										beneficiaryType = m.beneficiaryType,
										email = m.email,
										phoneNumber = m.phoneNumber,
										gender = m.gender,
										type = m.beneficiaryType,
										jicEntityId = m.jicEntityId,
										apaEntityId = m.apaEntityId,
										joinDate = m.joinDate,
										dob = m.dob
									)
									passed.add(dto)
								}
							}
							val payer = benefit.payer.let { pyr ->
								return@let SchemePayerDTO(
									payerId = pyr.id,
									payerName = pyr.name
								)
							}
							val result =
								buildAndGenerateAggregate(
									benefit.benefitRef.id!!,
									benefit,
									passed,
									policy,
									category,
									payer
								)
							println(result)
							if (result.success) {
								println(result)
								val tracker = SharedBenefitTracker(
									beneficiary = family,
									aggregateId = result.data!!,
									benefit = benefit
								)
								trackers.add(tracker)
								benefit.processed = true
								benefit.processedTime = LocalDateTime.now()
								repo.save(benefit)
							}
						}

						members.map {
							it.processed = true
							it.processedTime = LocalDateTime.now()
							beneficiaryRepo.save(it)
						}

					}
				}




				trackerRepo.saveAll(trackers)
				/*categoryRepo.save(
                    category.copy(
                        status = CategoryStatus.PROCESSED
                    )
                )*/

			}
		}

		return ResultFactory.getSuccessResult("Completed processing benefits for category ${category!!.name} ")
	}

	private fun goThroughEachFamily(
		families: MutableList<Beneficiary>,
		unprocessedBenefits: MutableList<Benefit>,
		policy: Policy?,
		category: Category?,
		trackers: MutableSet<SharedBenefitTracker>
	) {
		for (family in families) {
			val members = mutableSetOf<Beneficiary>()
			members.add(family)
			members.addAll(family.dependants)

			val indivBenefits = unprocessedBenefits
				.stream()
				.filter { b -> b.sharing == BenefitDistribution.INDIVIDUAL }
				.collect(Collectors.toList())
			println("Individual benefit count is ${indivBenefits.size}")
			for (benefit in indivBenefits) {
				for (m in members) {
					if (benefitEligibilityPasses(m, benefit)) {
						val dto = CreateBeneficiaryDTO(
							id = m.id,
							name = m.name,
							memberNumber = m.memberNumber,
							beneficiaryType = m.beneficiaryType,
							email = m.email,
							phoneNumber = m.phoneNumber,
							gender = m.gender,
							type = m.beneficiaryType,
							jicEntityId = m.jicEntityId,
							apaEntityId = m.apaEntityId,
							joinDate = m.joinDate,
							dob = m.dob
						)
						println("Building ${benefit.name} benefit for ${m.name}")
						val payer = benefit.payer.let { pyr ->
							return@let SchemePayerDTO(
								payerId = pyr.id,
								payerName = pyr.name
							)
						}
						buildAndGenerateAggregate(
							catalogId = benefit.benefitRef.id!!,
							benefit = benefit,
							beneficiaries = mutableSetOf(dto),
							policy = policy,
							category = category!!,
							payer = payer
						)
					}
				}
				benefit.processed = true
				benefit.processedTime = LocalDateTime.now()
				repo.save(benefit)
			}

			val famBenefits = unprocessedBenefits
				.stream()
				.filter { b -> b.sharing == BenefitDistribution.FAMILY }
				.collect(Collectors.toList())
			for (benefit in famBenefits) {
				val passed = mutableSetOf<CreateBeneficiaryDTO>()
				for (m in members) {
					if (benefitEligibilityPasses(m, benefit)) {
						val dto = CreateBeneficiaryDTO(
							id = m.id,
							name = m.name,
							memberNumber = m.memberNumber,
							beneficiaryType = m.beneficiaryType,
							email = m.email,
							phoneNumber = m.phoneNumber,
							gender = m.gender,
							type = m.beneficiaryType,
							jicEntityId = m.jicEntityId,
							apaEntityId = m.apaEntityId,
							joinDate = m.joinDate,
							dob = m.dob
						)
						passed.add(dto)
					}
					/**
					 * Here, no need to test principal member since they have already been added to members set
					 */
					/*if (benefitEligibilityPasses(family, benefit)) {
								passed.add(
									CreateBeneficiaryDTO(
										id = family.id, name = family.name,
										memberNumber = family.memberNumber, beneficiaryType = family.beneficiaryType,
										email = family.email, phoneNumber = family.phoneNumber
									)
								)
							}*/
				}
				val payer = benefit.payer.let { pyr ->
					return@let SchemePayerDTO(
						payerId = pyr.id,
						payerName = pyr.name
					)
				}
				val result =
					buildAndGenerateAggregate(
						benefit.benefitRef.id!!,
						benefit,
						passed,
						policy,
						category!!,
						payer
					)
				println(result)
				if (result.success) {
					println(result)
					val tracker = SharedBenefitTracker(
						beneficiary = family,
						aggregateId = result.data!!,
						benefit = benefit
					)
					trackers.add(tracker)
					benefit.processed = true
					benefit.processedTime = LocalDateTime.now()
					repo.save(benefit)
				}
			}

			members.map {
				it.processed = true
				it.processedTime = LocalDateTime.now()
				beneficiaryRepo.save(it)
			}

		}
	}

	fun manualSubBenefitBuildAndGenerateAggregate(
		aggregateId:String,
		catalogId: Long,
		benefit: Benefit,
		beneficiaries: MutableSet<CreateBeneficiaryDTO>,
		policy: Policy?,
		category: Category,
		payer: SchemePayerDTO,
		aggId: String? = null,
		parentBenefitId:Long
	): Result<String> {

		val dto = CreateBenefitDTO(
			aggregateId = aggregateId,
			benefitName = benefit.name,
			balance = benefit.limit,
			suspensionThreshold = benefit.suspensionThreshold,
			preAuthThreshold = benefit.preAuthThreshold,
			beneficiaries = beneficiaries,
			waitingPeriod = benefit.waitingPeriod,
			startDate = policy!!.startDate.plus(benefit.waitingPeriod.period),
			endDate = policy.endDate,
			categoryId = category.id,
			payer = payer,
			policyNumber = policy.policyNumber,
			subBenefits = mutableSetOf(),
			benefitId = benefit.id,
			catalogId = benefit.benefitRef.id!!,
			manualSubBenefitParentId = parentBenefitId,
			benefitType = benefit.benefitType,
			capitationType = benefit.capitationType,
			capitationPeriod = benefit.capitationPeriod,
			visitCountPeriod = benefit.visitCountPeriod,
			capitationMaxVisitCount = benefit.capitationMaxVisitCount,
			capitationFacilitiesCount = benefit.capitationFacilitiesCount,
			requireBeneficiaryToSelectProvider=benefit.requireBeneficiaryToSelectProvider,
			daysOfAdmissionLimit = benefit.daysOfAdmissionLimit,
			amountPerDayLimit = benefit.amountPerDayLimit,
			applicableMinAge = benefit.applicableMinAge,
			applicableMaxAge = benefit.applicableMaxAge,
			transferable = benefit.transferable,
			planType = policy.plan.type,
			planDate = policy.plan.planDate
		)
		println("About to send Sub benefits over the wire: ${dto.benefitName} with aggregateId: " +
				"${dto.aggregateId}")
		println(gson.toJson(dto))

		return if (processFamilyBenefits(dto)) {
			ResultFactory.getSuccessResult(msg = dto.aggregateId, data = dto.aggregateId!!)
		} else {
			ResultFactory.getFailResult("Failed to publish")
		}
	}

	fun buildAndGenerateAggregate(
		catalogId: Long,
		benefit: Benefit,
		beneficiaries: MutableSet<CreateBeneficiaryDTO>,
		policy: Policy?,
		category: Category,
		payer: SchemePayerDTO,
		aggId: String? = null
	): Result<String> {
		val subBenefits = mutableSetOf<SubBenefitDTO>()
		benefit.subBenefits.forEach { sub ->
			val subDTO = SubBenefitDTO(
				name = sub.name,
				balance = sub.limit,
				startDate = policy!!.startDate.plus(sub.waitingPeriod.period),
				endDate = policy.endDate,
				suspensionThreshold = sub.suspensionThreshold,
				preAuthThreshold = sub.preAuthThreshold,
				benefitId = sub.id,
				gender = sub.applicableGender,
				memberType = sub.applicableMember,
				catalogId = sub.benefitRef!!.id!!,
				applicableMinAge = sub.applicableMinAge,
				applicableMaxAge = sub.applicableMaxAge,
				waitingPeriod = sub.waitingPeriod
			)
			subBenefits.add(subDTO)
		}
		val dto = CreateBenefitDTO(
			aggregateId = aggId ?: UUID.randomUUID().toString(),
			benefitName = benefit.name,
			balance = benefit.limit,
			suspensionThreshold = benefit.suspensionThreshold,
			preAuthThreshold = benefit.preAuthThreshold,
			beneficiaries = beneficiaries,
			waitingPeriod = benefit.waitingPeriod,
			startDate = policy!!.startDate.plus(benefit.waitingPeriod.period),
			endDate = policy.endDate,
			categoryId = category.id,
			payer = payer,
			policyNumber = policy.policyNumber,
			subBenefits = subBenefits,
			benefitId = benefit.id,
			catalogId = benefit.benefitRef!!.id!!,
			manualSubBenefitParentId = null,
			benefitType = benefit.benefitType,
			capitationType = benefit.capitationType,
			capitationPeriod = benefit.capitationPeriod,
			visitCountPeriod = benefit.visitCountPeriod,
			capitationMaxVisitCount = benefit.capitationMaxVisitCount,
			capitationFacilitiesCount = benefit.capitationFacilitiesCount,
			daysOfAdmissionLimit = benefit.daysOfAdmissionLimit,
			amountPerDayLimit = benefit.amountPerDayLimit,
			requireBeneficiaryToSelectProvider = benefit.requireBeneficiaryToSelectProvider,
			applicableMinAge = benefit.applicableMinAge,
			applicableMaxAge = benefit.applicableMaxAge,
			transferable = benefit.transferable,
			planType = policy.plan.type,
			planDate = policy.plan.planDate
		)
		println("About to send benefits over the wire: ${dto.benefitName} with aggregateId: ${dto.aggregateId}")
		println(gson.toJson(dto))

		return if (processFamilyBenefits(dto)) {
			ResultFactory.getSuccessResult(msg = dto.aggregateId, data = dto.aggregateId!!)
		} else {
			ResultFactory.getFailResult("Failed to publish")
		}
	}

	private fun benefitEligibilityPasses(ben: Beneficiary, benefit: Benefit): Boolean {
		return typePasses(beneficiaryType = ben.beneficiaryType, benefit = benefit)
				&& genderPasses(gender = ben.gender, benefit = benefit)
				&& minApplicableAgePasses(ben.dob,benefit.applicableMinAge)
				&& maxApplicableAgePasses(ben.dob, benefit.applicableMaxAge)
	}

	private fun minApplicableAgePasses(memberDob: LocalDate?, applicableMinAge: BigDecimal?): Boolean {
		if(memberDob == null || applicableMinAge ==null || applicableMinAge == BigDecimal.ZERO) {
			return true
		}
		return BigDecimal(getAge(memberDob)) >= applicableMinAge
	}

	private fun maxApplicableAgePasses(memberDob: LocalDate?, applicableMaxAge: BigDecimal?): Boolean {
		if(memberDob == null || applicableMaxAge ==null || applicableMaxAge == BigDecimal.ZERO) {
			return true
		}
		return BigDecimal(getAge(memberDob)) <= applicableMaxAge
	}

	private fun typePasses(beneficiaryType: BeneficiaryType, benefit: Benefit): Boolean {
		return when (benefit.applicableMember) {
			ApplicableMember.ALL -> true
			ApplicableMember.PRINCIPAL -> (beneficiaryType == BeneficiaryType.PRINCIPAL)
			ApplicableMember.SPOUSE -> (beneficiaryType == BeneficiaryType.SPOUSE)
			ApplicableMember.CHILD -> (beneficiaryType == BeneficiaryType.CHILD)
			ApplicableMember.PARENT -> (beneficiaryType == BeneficiaryType.PARENT)
			ApplicableMember.PRINCIPAL_AND_SPOUSE -> {
				(beneficiaryType == BeneficiaryType.PRINCIPAL || beneficiaryType == BeneficiaryType.SPOUSE)
			}
		}
	}

	private fun genderPasses(gender: Gender, benefit: Benefit): Boolean {
		if (benefit.applicableGender.name == gender.name) return true
		if (benefit.applicableGender == ApplicableGender.ALL) return true
		return false
	}

	@Transactional(readOnly = false, rollbackFor = [Exception::class])
	override fun processBenefit(benefit: Benefit): Result<Benefit> {
		return ResultFactory.getFailResult("Not yet implemented")
	}


	fun processFamilyBenefits(dto: CreateBenefitDTO): Boolean {
		// Sending to benefit management service
		var result = false
//        LOG.debug("Building: ${dto.benefitName} for ${dto.beneficiaries}")
		/////println(Gson().toJson(dto).toString())

		/*Kafka */
		/*var dtostring: String = objectMapper.writeValueAsString(dto)

		println(dtostring)


		println("----------------------------------------")


		kafka.send(benefitTopic, dtostring).addCallback({
			println("success")
			result = true
		}, {
			result = false
			println("fail")
		})*/
		/*Kafka */


		// val avroRecord = Avro.default.toRecord(CreateBenefitDTO.serializer(), dto)
//        val future = kafka.send(
//            benefitTopic,
//            avroRecord
//        ).addCallback({
//            println("Sent to bus at: ${it!!.recordMetadata.timestamp()}")
//            result = true
//        }, {
//            println("Failed with error: ${it.message}")
//            result = false
//        })

		println(benefitUrl)


        //val http = WebClient.create(benefitUrl)
		val http = webClient(benefitUrl)

		http.post()
			.uri(createBenefitEndpoint)
			.header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
			//.body(Mono.just(mapper.writeValueAsString(dto)), String::class.java)
			.body(Mono.just(gson.toJson(dto)), String::class.java)
			.exchange()
			.doOnSuccess { res ->
				println("-------------------------------")
				println(res)
				result = true
			}
			.block()!!.toString()

		return result
	}

	@Transactional(rollbackFor = [Exception::class])
	override fun processNewMembers(categoryId: Long): Result<Benefit> {
		TODO("Not yet implemented")
	}
	@Transactional(rollbackFor = [Exception::class])
	override fun changeCategory(dto: ChangeCategoryDTO): Result<Boolean> {
		val oldCategory = categoryRepo.findById(dto.oldCategoryId)
		if (oldCategory.isEmpty) return ResultFactory.getFailResult(
			"No Previous Category with ID ${dto.oldCategoryId} was " +
					"found"
		)
		val newCategory = categoryRepo.findById(dto.newCategoryId)
		if (newCategory.isEmpty) return ResultFactory.getFailResult(
			"No New Category with ID ${dto.newCategoryId} was" +
					" found"
		)

		val category = categoryRepo.fetchWithPolicy(dto.newCategoryId)

		with(category) {

			val policy = this?.policy
			val family =
				beneficiaryRepo.findFamilyByCategoryAndMemberNumber(oldCategory.get(), dto.memberNumber)
			val mainBenefits = repo.findMainBenefitsByCategory(category!!)
			val trackers = mutableSetOf<SharedBenefitTracker>()
			for (member in family) {
				val members = mutableSetOf<Beneficiary>()
				members.add(member)
				members.addAll(member.dependants)

				val indivBenefits = mainBenefits
					.stream()
					.filter { b -> b.sharing == net.lctafrica.membership.api.domain.BenefitDistribution.INDIVIDUAL }
					.collect(java.util.stream.Collectors.toList())
				println("Individual benefit count is ${indivBenefits.size}")
				for (benefit in indivBenefits) {
					for (m in members) {
						if (benefitEligibilityPasses(m, benefit)) {
							val dto = CreateBeneficiaryDTO(
								id = m.id,
								name = m.name,
								memberNumber = m.memberNumber,
								beneficiaryType = m.beneficiaryType,
								email = m.email,
								phoneNumber = m.phoneNumber,
								gender = m.gender,
								type = m.beneficiaryType,
								jicEntityId = m.jicEntityId,
								apaEntityId = m.apaEntityId,
								joinDate = m.joinDate,
								dob = m.dob
							)
							println("Building ${benefit.name} benefit for ${m.name}")
							val payer = benefit.payer.let { pyr ->
								return@let SchemePayerDTO(
									payerId = pyr.id,
									payerName = pyr.name
								)
							}
							buildAndGenerateAggregate(
								catalogId = benefit.benefitRef.id!!,
								benefit = benefit,
								beneficiaries = mutableSetOf(dto),
								policy = policy,
								category = category,
								payer = payer
							)
						}
					}
					benefit.processed = true
					benefit.processedTime = java.time.LocalDateTime.now()
					repo.save(benefit)
				}

				val famBenefits = mainBenefits
					.stream()
					.filter { b -> b.sharing == net.lctafrica.membership.api.domain.BenefitDistribution.FAMILY }
					.collect(java.util.stream.Collectors.toList())
				for (benefit in famBenefits) {
					val passed = mutableSetOf<CreateBeneficiaryDTO>()
					for (m in members) {
						if (benefitEligibilityPasses(m, benefit)) {
							val dto = CreateBeneficiaryDTO(
								id = m.id,
								name = m.name,
								memberNumber = m.memberNumber,
								beneficiaryType = m.beneficiaryType,
								email = m.email,
								phoneNumber = m.phoneNumber,
								gender = m.gender,
								type = m.beneficiaryType,
								jicEntityId = m.jicEntityId,
								apaEntityId = m.apaEntityId,
								joinDate = m.joinDate,
								dob = m.dob
							)
							passed.add(dto)
						}

					}
					val payer = benefit.payer.let { pyr ->
						return@let SchemePayerDTO(
							payerId = pyr.id,
							payerName = pyr.name
						)
					}
					val result = buildAndGenerateAggregate(
						benefit.benefitRef.id!!,
						benefit,
						passed,
						policy,
						category,
						payer
					)
					println(result)

					if (result.success) {
						println(result)
						val tracker = SharedBenefitTracker(
							beneficiary = member,
							aggregateId = result.data!!,
							benefit = benefit
						)
						trackers.add(tracker)
						benefit.processed = true
						benefit.processedTime = java.time.LocalDateTime.now()
						repo.save(benefit)
					}
				}

				for(m in members){
					suspendCurrentBenefits(m.id, m.category.id)
				}
				members.map {
					it.category = category
					it.processedTime = java.time.LocalDateTime.now()
					beneficiaryRepo.save(it)
				}

			}
		}
		return ResultFactory.getSuccessResult(data = true)
	}

	override fun updateCategory(categoryId:Long, dto: UpdateCategoryDTO): Result<Boolean> {
		val categoryCheck = categoryRepo.findById(categoryId)
		if (categoryCheck.isEmpty) return ResultFactory.getFailResult(
			"Invalid Category Id"
		)
		val category = categoryCheck.get()
		dto.allowOtpVerificationFailOver?.let {
			category.allowOtpVerificationFailOver = it
		}
		categoryRepo.save(category)
		return ResultFactory.getSuccessResult("Category updated successfully")
	}

	override fun findPayersByBenefitIds(benefitIds: Collection<Long>): Result<MutableList<BenefitPayerMappingDTO>> {
		return ResultFactory.getSuccessResult(repo.findPayersByBenefitIds(benefitIds))
	}

	override fun findPlanByBenefitIds(benefitIds: Collection<Long>): Result<MutableList<GetPlanFromBenefitProjectionDTO>> {
		return ResultFactory.getSuccessResult(repo.findPlanByBenefitIds(benefitIds))
	}

	override fun findByServiceGroups(serviceGroups: Collection<ServiceGroup>): Result<MutableList<FindBenefitsByServiceGroupsProjectionDTO>> {
		return ResultFactory.getSuccessResult(repo.findByServiceGroups(serviceGroups))
	}

	override fun batchBenefitProviderMapping(dto: BatchBenefitPayerProviderMappingDto): Result<Boolean> {
		mapBenefitToProvider(dto.benefitId, dto.providerIds)
		return ResultFactory.getSuccessResult(SAVED_SUCCESSFULLY)
	}

	override fun batchBenefitProviderUnmapping(benefitId:Long,dto: BatchBenefitProviderUnmappingDto): Result<Boolean> {
		val benefit = repo.findById(benefitId).orElseThrow{ NotFoundRequestException("Benefit with Id $benefitId doesn't exist") }
		benefitProviderMappingRepo.disableSelectedBenefitProviderMappings(benefit,dto.providerIds)
		return ResultFactory.getSuccessResult(SUCCESS)
	}

	override fun benefitProviderMapping(dto: BenefitProviderMappingDto): Result<Boolean> {
		val providerIntoList = mutableListOf<Long>()
		providerIntoList.add(dto.providerId)
		mapBenefitToProvider(dto.benefitId, providerIntoList)
		return ResultFactory.getSuccessResult(SAVED_SUCCESSFULLY)
	}

	override fun beneficiaryBenefitProviderMapping(dto: BeneficiaryBenefitProviderMappingDto): Result<Boolean> {
		val benefit = repo.findById(dto.benefitId).orElseThrow {
			throw NotFoundRequestException("Invalid benefit Id")
		}

		val provider=providerRepo.findById(dto.providerId).orElseThrow{
			NotFoundRequestException("Invalid Provider Id")
		}

		val beneficiary = beneficiaryRepo.findById(dto.beneficiaryId).orElseThrow{
			NotFoundRequestException("Invalid Beneficiary Id")
		}

		val mappedFacilities = beneficiaryBenefitProviderMappingRepo.findByBenefitAndBeneficiaryAndMappingEnabled(benefit, beneficiary,true)

		if(mappedFacilities.size >= benefit.capitationFacilitiesCount){
			return ResultFactory.getFailResult("You have reached the maximum number (${benefit.capitationFacilitiesCount}) of facility choices allowed")
		}

		val beneficiaryBenefitProviderMapping = beneficiaryBenefitProviderMappingRepo.findByBeneficiaryAndBenefitAndProvider(beneficiary,benefit,provider)
		val beneficiaryBenefitProviderMap = if(beneficiaryBenefitProviderMapping.isPresent){
            beneficiaryBenefitProviderMapping.get().apply {
                this.mappingEnabled = true
            }
		}else{
			 BeneficiaryProviderMapping(
				beneficiary =beneficiary,
				provider =  provider,
				benefit = benefit,
				mappingEnabled = true
			)
		}
		beneficiaryBenefitProviderMappingRepo.save(beneficiaryBenefitProviderMap)
		return ResultFactory.getSuccessResult(SAVED_SUCCESSFULLY)
	}

	override fun getPayerBenefitProviderMappings(
		benefitId: Long,
		page: Int,
		size: Int
	): Result<Page<BenefitProviderMapping>> {
		val benefitOpt = repo.findById(benefitId)
		if(!benefitOpt.isPresent){
			throw NotFoundRequestException("Invalid benefit Id")
		}
		val benefit =benefitOpt.get()
		val category = benefit.category
		val restrictions = categoryRestrictionMappingRepo.getProviderRestrictionsForACategory(category)
		val providers = restrictions.stream().map { map-> map.provider }.collect(Collectors.toList())
		val request = PageRequest.of(page - 1, size)

		val benefitPayerProviderMapping = when(category.restrictionType){
			RestrictionType.INCLUSIVE ->{
				benefitProviderMappingRepo.getProvidersByBenefitIdInclusive(benefitId, providers, request)
			}
			RestrictionType.EXCLUSIVE ->{
				benefitProviderMappingRepo.getProvidersByBenefitIdExclusive(benefitId, providers, request)
			}
			else ->{
				benefitProviderMappingRepo.getPayerBenefitMappingsByPayerIdAndBenefitId(benefitId, request)
			}
		}

		return ResultFactory.getSuccessResult(benefitPayerProviderMapping)
	}

	override fun getBeneficiaryBenefitProviderMapping(beneficiaryId: Long,benefitId: Long): Result<MutableList<BeneficiaryProviderMappingProjectionDTO>> {
		val beneficiary = beneficiaryRepo.findById(beneficiaryId).orElseThrow{
			NotFoundRequestException("Invalid Beneficiary Id")
		}
		val benefit = repo.findById(benefitId).orElseThrow{
			NotFoundRequestException("Invalid Benefit Id")
		}

		val result = beneficiaryBenefitProviderMappingRepo.findByBeneficiaryAndBenefitAndMappingEnabled(beneficiary, benefit,true)

		if(result.size > 0){
			return ResultFactory.getSuccessResult(result)
		}

		return ResultFactory.getFailResult("No Provider found for this Beneficiary and Benefit")
	}

	override suspend fun updateCategoriesStatus(dto: CategoryStatusUpdateDTO): Result<Boolean> {
		dto.categoryIds.forEach { categoryId ->
			val category = validationService.validateAndGetCategory(categoryId)
			category.status = dto.categoryStatus
			categoryRepo.save(category)
			val auditLog = MembershipAuditLogDTO(
				categoryId = category.id,
				reason = dto.reason,
				user = dto.updateBy,
				action = ChangeLogType.CATEGORY_STATUS_UPDATE.name,
				time = LocalDateTime.now(),
				type = ChangeLogType.CATEGORY_STATUS_UPDATE,
				organisation = category.policy.plan.name,
				data = dto.toJsonString()
			)
			auditLogService.categoryAuditLog(auditLog)
		}
		return ResultFactory.getSuccessResult(UPDATED_SUCCESSFULLY)
	}

	override suspend fun updateBenefitsStatus(dto: BenefitStatusUpdateDTO): Result<Boolean> {
		dto.benefitIds.forEach { benefitId ->
			val benefit = validationService.validateAndGetBenefit(benefitId)
			benefit.status = dto.benefitStatus
			repo.save(benefit)
			val auditLog = MembershipAuditLogDTO(
				benefitId = benefit.id,
				reason = dto.reason,
				user = dto.updateBy,
				action = ChangeLogType.BENEFIT_STATUS_UPDATE.name,
				time = LocalDateTime.now(),
				type = ChangeLogType.BENEFIT_STATUS_UPDATE,
				organisation = benefit.category.policy.plan.name,
				data = dto.toJsonString()
			)
			auditLogService.benefitAuditLog(auditLog)
		}
		return ResultFactory.getSuccessResult(UPDATED_SUCCESSFULLY)
	}

	override suspend fun updateBeneficiariesStatus(dto: BeneficiaryStatusUpdateDTO): Result<Boolean> {
		dto.beneficiaryIds.forEach { beneficiaryId ->
			val beneficiary = validationService.validateAndGetBeneficiary(beneficiaryId)
			if(beneficiary.status != dto.status) {
				logBeneficiaryUpdate(dto,beneficiary)
			}

			if(dto.updateType == UpdateType.FAMILY){
				val dependants = beneficiaryRepo.findByPrincipal(beneficiary)
				dependants.forEach { dependant->
					if(dependant.status != dto.status) {
						logBeneficiaryUpdate(dto,dependant)
					}
				}
			}
		}
		return ResultFactory.getSuccessResult(UPDATED_SUCCESSFULLY)
	}

	@Transactional(readOnly = false, rollbackFor = [Exception::class])
	override suspend fun categoryChange(beneficiaryId: Long, dto: CategoryChangeDto): Result<Boolean> {
		val beneficiary = validationService.validateAndGetBeneficiary(beneficiaryId)
		val newCategory = validationService.validateAndGetCategory(dto.categoryId)
		val endDate = beneficiary.category.policy.endDate

		if (dateExpired(endDate)) {
			throw BadRequestException("Policy Expired on ${formatPolicyDate(endDate)}")
		}

		if(beneficiary.category.policy.id != newCategory.policy.id){
			throw BadRequestException("Category Policy mismatch. Please ensure the Categories are in the same Policy")
		}

		if(beneficiary.beneficiaryType == BeneficiaryType.PRINCIPAL){
			val allBeneficiaries = mutableListOf<Beneficiary>()
			allBeneficiaries.add(beneficiary)
			val dependants = beneficiaryRepo.findByPrincipal(beneficiary)
			allBeneficiaries.addAll(dependants)
			val parentBenefits = newCategory.benefits.stream().filter { it.parentBenefit == null }.collect(Collectors.toList())
			val trackers = mutableSetOf<SharedBenefitTracker>()
			parentBenefits.forEach { parentBenefit ->
				var aggregateId = UUID.randomUUID().toString()
					allBeneficiaries.forEach { member ->
						if(parentBenefit.sharing == BenefitDistribution.INDIVIDUAL){
							aggregateId = UUID.randomUUID().toString()
						}
						if(member.beneficiaryType == BeneficiaryType.PRINCIPAL && parentBenefit.sharing == BenefitDistribution.FAMILY){
							val tracker = SharedBenefitTracker(
								beneficiary = member,
								aggregateId = aggregateId,
								benefit = parentBenefit
							)
							trackers.add(tracker)
						}
						CoroutineScope(Dispatchers.IO).launch {
						if (benefitEligibilityPasses(benefit = parentBenefit, ben = member)) {
								val benefits = buildBenefitData(member, parentBenefit, aggregateId, beneficiary.category.id, dto.transferUtilization)
								claimService.categoryChange(benefits)
							}
						}
				}
			}

			if(beneficiary.category.id != newCategory.id) {
				val beneficiaryIds = allBeneficiaries.stream().map { it.id }.collect(Collectors.toSet())
				claimService.migrateBenefits(
					MigrateBenefitsDto(
						categoryId = beneficiary.category.id,
						beneficiaryIds = beneficiaryIds
					)
				)

				val auditLog = MembershipAuditLogDTO(
					beneficiaryId = beneficiary.id,
					reason = dto.reason,
					user = dto.updateBy,
					action = ChangeLogType.CATEGORY_CHANGE.name,
					time = LocalDateTime.now(),
					type = ChangeLogType.CATEGORY_CHANGE,
					organisation = newCategory.policy.plan.name,
					data = dto.toJsonString(),
					narration = "Changed from Category ${beneficiary.category.name} (${beneficiary.category.id}) to Category ${newCategory.name} (${newCategory.id})"
				)
				auditLogService.beneficiaryAuditLog(auditLog)

				beneficiary.category = newCategory
				beneficiaryRepo.save(beneficiary)

				beneficiary.dependants.forEach { dependant ->
					dependant.category = newCategory
					beneficiaryRepo.save(dependant)
				}
				trackers.forEach { tracker ->
					val sharedBenefitTrackerExists =
						trackerRepo.findByBeneficiaryAndBenefit(tracker.beneficiary, tracker.benefit)
					if (sharedBenefitTrackerExists == null) {
						trackerRepo.save(tracker)
					}
				}
			}
		}else{
			if(beneficiary.principal?.category?.id != newCategory.id){
				throw BadRequestException("Principal and Dependant category mismatch")
			}
		}
		return ResultFactory.getSuccessResult("Category change successful")
	}

	override suspend fun beneficiariesCategoryChange(dto: BeneficiariesCategoryChangeDto): Result<Boolean> {
		dto.beneficiaryIds.forEach { beneficiaryId->
			categoryChange(beneficiaryId,CategoryChangeDto(categoryId = dto.categoryId, updateBy = dto.updateBy, reason = dto.reason))
		}
		return ResultFactory.getSuccessResult("Category change successful")
	}

	override suspend fun processBenefitsInCategory(dto: ProcessCategoryDto): Result<Boolean> {
		val category = validationService.validateAndGetCategory(dto.categoryId)
		when{
			(dto.processBeneficiaries == BeneficiaryBenefitProcess.ALL && category.status == CategoryStatus.PROCESSED) -> throw BadRequestException("Failed to process. Category is already ${category.status}.")
			(category.status == CategoryStatus.SUSPENDED) -> throw BadRequestException("Failed to process. Category is ${category.status}.")
			else -> {
				val endDate = category.policy.endDate
				if (dateExpired(endDate)) {
					throw BadRequestException("Failed to process. Policy for category ${category.name} expired on ${formatPolicyDate(endDate)}.")
				}
			}
		}
		val parentBenefits =
			category.benefits.stream().filter { it.parentBenefit == null }.collect(Collectors.toList())
		val parentBenefitIds = parentBenefits.stream().map { it.id }.collect(Collectors.toSet())

		processSelectBenefitsInCategory(
				dto = ProcessSelectBenefitsDto(
					benefitIds = parentBenefitIds,
					processSubBenefits = true,
					processBeneficiaries = dto.processBeneficiaries
				),
				processingCategory = true
			)
		category.status = CategoryStatus.PROCESSED
		categoryRepo.save(category)
		return ResultFactory.getSuccessResult("Category processing initiated successfully")
	}

	override suspend fun processSelectBenefitsInCategory(dto: ProcessSelectBenefitsDto,
														 beneficiariesList:List<Beneficiary>,
														 processingCategory:Boolean,
														 skipValidation:Boolean): Result<Boolean> {
		if(!skipValidation){ validateSelectBenefits(dto) }
		CoroutineScope(Dispatchers.IO).launch {
			val processedBenefits = mutableListOf<Benefit>()
			val processedBeneficiaries = mutableListOf<Beneficiary>()
			val benefitList = dto.benefitIds.toList().sorted()
			benefitList.forEach { benefitId ->
				val benefit = validationService.validateAndGetBenefit(benefitId)
				val parentBenefit = findParentBenefitOfBenefit(benefit)
				if(processingCategory || (benefit.parentBenefit == null ||  parentBenefit.processed)) {
					val beneficiariesToProcess = when (dto.processBeneficiaries) {
						BeneficiaryBenefitProcess.ONLY_PROCESSED_BENEFICIARIES -> beneficiaryRepo.findByCategoryAndProcessed(
							benefit.category,
							true
						)

						BeneficiaryBenefitProcess.ONLY_UNPROCESSED_BENEFICIARIES -> beneficiaryRepo.findByCategoryAndProcessed(
							benefit.category,
							false
						)

						BeneficiaryBenefitProcess.ONLY_SELECTED_BENEFICIARIES -> beneficiariesList

						else -> beneficiaryRepo.findByCategory(benefit.category)
					}

					if(benefit.parentBenefit == null){
						val allPrincipals = mutableListOf<Beneficiary>()
						beneficiariesToProcess.forEach { beneficiary ->
							if(beneficiary.beneficiaryType == BeneficiaryType.PRINCIPAL){
								allPrincipals.add(beneficiary)
							}else{
								beneficiary.principal?.let { principal ->
									allPrincipals.add(principal)
								}
							}
						}
						val principalUnq = allPrincipals.distinctBy { principal -> principal.id }
						principalUnq.forEach { principal ->
							if (benefit.sharing == BenefitDistribution.FAMILY) {
								var tracker = trackerRepo.findByBeneficiaryAndBenefit(principal, parentBenefit)
								if (tracker == null) {
									tracker = SharedBenefitTracker(
										beneficiary = principal,
										aggregateId = UUID.randomUUID().toString(),
										benefit = parentBenefit
									)
									trackerRepo.save(tracker)
								}
							}
						}
					}
					val beneficiariesToProcessOrder= beneficiariesToProcess.sortedBy { it.id }
					beneficiariesToProcessOrder.forEach { beneficiary ->
						val principal = if (beneficiary.beneficiaryType == BeneficiaryType.PRINCIPAL) beneficiary else beneficiary.principal
						if(principal != null) {
							val aggregateId = if (benefit.sharing == BenefitDistribution.FAMILY) {
								val tracker = trackerRepo.findByBeneficiaryAndBenefit(principal, parentBenefit)
								tracker?.aggregateId!!
							} else {
								UUID.randomUUID().toString()
							}

							if (benefitEligibilityPasses(benefit = benefit, ben = beneficiary)) {
								val benefits =
									buildBenefitProcessData(beneficiary, benefit, aggregateId, dto.processSubBenefits)
								val response = claimService.processBenefits(benefits)
								if (response.success) {
									processedBeneficiaries.add(beneficiary)
									processedBenefits.add(benefit)
								}
							}
						}else{
							println("No Principal found for beneficiary id: ${beneficiary.id} and member number: ${beneficiary.memberNumber}")
						}
					}
				}

			}
			markBenefitsAsProcessed(processedBenefits)
			markBeneficaryAsProcessed(processedBeneficiaries)
		}
		return ResultFactory.getSuccessResult("Benefit processing initiated successfully")
	}

	override suspend fun processBeneficiaryBenefits(beneficiary: Beneficiary): Result<Boolean> {
		val category = beneficiary.category
		val endDate = category.policy.endDate
		if (dateExpired(endDate)) {
			throw BadRequestException("Failed to process. Policy for category ${category.name} expired on ${formatPolicyDate(endDate)}.")
		}

		val beneficiaryToProcess = mutableListOf<Beneficiary>()
		beneficiaryToProcess.add(beneficiary)
		if(beneficiary.beneficiaryType ==  BeneficiaryType.PRINCIPAL){
			val dependants = beneficiaryRepo.findByPrincipal(beneficiary)
			beneficiaryToProcess.addAll(dependants)
		}

		val findProcessedBeneficiaries = beneficiaryToProcess.stream().filter { it.processed }.map { it.id }.collect(Collectors.toSet())
		if(findProcessedBeneficiaries.size > 0) {
			claimService.migrateBenefits(
				MigrateBenefitsDto(
					categoryId = beneficiary.category.id,
					beneficiaryIds = findProcessedBeneficiaries
				)
			)
		}

		val parentBenefits =
			category.benefits.stream().filter { it.parentBenefit == null }.collect(Collectors.toList())
		val parentBenefitIds = parentBenefits.stream().map { it.id }.collect(Collectors.toSet())
		processSelectBenefitsInCategory(
			dto = ProcessSelectBenefitsDto(
				benefitIds = parentBenefitIds,
				processSubBenefits = true,
				processBeneficiaries = BeneficiaryBenefitProcess.ONLY_SELECTED_BENEFICIARIES
			),
			processingCategory = true,
			skipValidation = true,
			beneficiariesList = beneficiaryToProcess
		)
		return ResultFactory.getSuccessResult("Beneficiary benefit processing initiated successfully")
	}

	override suspend fun addBenefitPayerAdmin(dto: AddBenefitPayerAdminDto): Result<Boolean> {
		val benefit = validationService.validateAndGetBenefit(dto.benefitId)
		val payer = validationService.validateAndGetPayer(dto.payerId)

		val mapOpt = benefitPayerAdminRepository.findByPayerAndBenefit(payer, benefit)
		var action = "Benefit payer admin created"
		var type:ChangeLogType = ChangeLogType.CREATE
		var message ="Benefit admin added successfully"
		val mapping = if(mapOpt.isPresent){
			action = "Benefit payer admin enabled"
			type = ChangeLogType.UPDATE
			message = "Benefit admin updated successfully"
			val map = mapOpt.get()
			map.mappingEnabled = dto.mappingEnabled ?: true
			map
		}else{
			BenefitPayerAdmin(
				benefit = benefit,
				payer = payer,
				mappingEnabled = dto.mappingEnabled ?: true
			)
		}
		benefitPayerAdminRepository.save(mapping)
		val log = AuditLoggingDTO(
			action = action,
			benefitPayerAdmin = mapping,
			user = dto.actionedBy,
			type = type
		)
		auditLogService.log(log)
		return ResultFactory.getSuccessResult(message)
	}

	override suspend fun getSchemesAdministeredByPayerOrPayerAdmin(payerId: Long): Result<MutableList<BenefitAdminPlansProjectionDTO>> {
		val payer = validationService.validateAndGetPayer(payerId)
		val allEnabledMapping = benefitPayerAdminRepository.findSchemesAdministeredByPayerAndMappingEnabled(
			payerId = payer.id,
		)
		return ResultFactory.getSuccessResult(allEnabledMapping)
	}

	private fun markBenefitsAsProcessed(processedBenefits: List<Benefit>) {
		processedBenefits.forEach { benefit ->
			benefit.processed = true
			benefit.processedTime = LocalDateTime.now()
			repo.save(benefit)
			if(benefit.subBenefits.isNotEmpty()) {
				markBenefitsAsProcessed(benefit.subBenefits)
			}
		}
	}

	private fun markBeneficaryAsProcessed(processedBeneficiaries: MutableList<Beneficiary>) {
		processedBeneficiaries.forEach { beneficiary ->
			beneficiary.processed = true
			beneficiary.processedTime = LocalDateTime.now()
			beneficiaryRepo.save(beneficiary)
		}
	}

	private fun findParentBenefitOfBenefit(benefit: Benefit): Benefit {
			if(benefit.parentBenefit != null){
				val parentBenefit = benefitRepository.findById(benefit.parentBenefit!!.id).get()
				return findParentBenefitOfBenefit(parentBenefit)
			}
		return benefit
	}


//	override suspend fun processSelectBenefitsInCategory(dto: ProcessSelectBenefitsDto): Result<Boolean> {
//		validateSelectBenefits(dto)
//		CoroutineScope(Dispatchers.IO).launch {
//			dto.benefitIds.forEach { benefitId ->
//				val benefit = validationService.validateAndGetBenefit(benefitId)
//				val allPrincipals = when(dto.processBeneficiaries){
//					BeneficiaryBenefitProcess.ONLY_PROCESSED_BENEFICIARIES -> beneficiaryRepo.findByCategoryAndBeneficiaryTypeAndProcessed(
//						benefit.category,
//						BeneficiaryType.PRINCIPAL,
//						true
//					)
//					BeneficiaryBenefitProcess.ONLY_UNPROCESSED_BENEFICIARIES -> beneficiaryRepo.findByCategoryAndBeneficiaryTypeAndProcessed(
//						benefit.category,
//						BeneficiaryType.PRINCIPAL,
//						false
//					)
//					else -> beneficiaryRepo.findByCategoryAndBeneficiaryType(benefit.category, BeneficiaryType.PRINCIPAL)
//				}
//
//				allPrincipals.forEach { principal ->
//					val allBeneficiaries = mutableListOf<Beneficiary>()
//					allBeneficiaries.add(principal)
//					val dependants = beneficiaryRepo.findByPrincipal(principal)
//					allBeneficiaries.addAll(dependants)
//					val trackers = mutableSetOf<SharedBenefitTracker>()
//					var aggregateId = UUID.randomUUID().toString()
//					allBeneficiaries.forEach { member ->
//						if (member.beneficiaryType == BeneficiaryType.PRINCIPAL && benefit.sharing == BenefitDistribution.FAMILY && benefit.parentBenefit == null) {
//							val tracker = SharedBenefitTracker(
//								beneficiary = member,
//								aggregateId = aggregateId,
//								benefit = benefit
//							)
//							trackers.add(tracker)
//						} else if (benefit.sharing == BenefitDistribution.INDIVIDUAL) {
//							aggregateId = UUID.randomUUID().toString()
//						}
//						CoroutineScope(Dispatchers.IO).launch {
//							if (benefitEligibilityPasses(benefit = benefit, ben = member)) {
//								val benefits =
//									buildBenefitProcessData(member, benefit, aggregateId, dto.processSubBenefits)
//								val response = claimService.processBenefits(benefits)
//								if(response.success) {
//									benefit.processed = true
//									benefit.processedTime = LocalDateTime.now()
//									repo.save(benefit)
//									benefit.subBenefits.forEach {
//										it.processed = true
//										it.processedTime = LocalDateTime.now()
//										repo.save(it)
//									}
//									member.processed = true
//									member.processedTime = LocalDateTime.now()
//									beneficiaryRepo.save(member)
//								}
//							}
//						}
//					}
//				}
//			}
//		}
//		return ResultFactory.getSuccessResult("Benefit processing initiated successfully")
//	}

	private fun validateSelectBenefits(dto: ProcessSelectBenefitsDto) {
		dto.benefitIds.forEach { benefitId->
			val benefit = validationService.validateAndGetBenefit(benefitId)
			val endDate = benefit.category.policy.endDate
			if (dateExpired(endDate)) { throw BadRequestException("Failed to process. Policy for benefit ${benefit.name} expired on ${formatPolicyDate(endDate)}.") }
		}
	}

	private suspend fun buildBenefitData(member:Beneficiary, benefit:Benefit, aggregateId:String, migratedFromCategory:Long?, transferUtilization:Boolean) :BeneficiaryBenefitsDto{
		var startDate = if(member.toRenew == true){
			member.category.policy.startDate
		}else{
			member.category.policy.startDate.plus(benefit.waitingPeriod.period)
		}
		var endDate = member.category.policy.endDate

		if ((member.category.policy.plan.type == PlanType.RETAIL || member.category.policy.plan.planDate == PlanDate.MEMBER_JOIN_DATE) && member.joinDate != null) {
			val startDt = member.joinDate ?: LocalDate.now()
			startDate = if(member.toRenew == true){
				startDt
			}else{
				startDt.plus(benefit.waitingPeriod.period)
			}
			endDate = startDt.plusYears(1).minusDays(1)
		}

		val subBenefits = mutableSetOf<BeneficiaryBenefitsDto>()
		benefit.subBenefits.forEach { subBenefit ->
			if (benefitEligibilityPasses(benefit = subBenefit, ben = member)) {
				val benefits = buildBenefitData(member, subBenefit,aggregateId, migratedFromCategory, transferUtilization)
				subBenefits.add(benefits)
			}
		}
		return BeneficiaryBenefitsDto(
			aggregateId =  aggregateId,
			benefitId = benefit.id,
			parentBenefitId = benefit.parentBenefit?.id,
			beneficiaryId = member.id,
			memberName = member.name,
			memberNumber=member.memberNumber,
			benefitName=benefit.name,
			accessMode = if(benefit.preAuthType != PreAuthType.NONE) AccessModeType.PRE_AUTH_BASIS else AccessModeType.CREDIT_BASIS,
			suspensionThreshold = benefit.suspensionThreshold,
			preAuthThreshold = benefit.preAuthThreshold,
			thresholdAction = benefit.thresholdAction,
			sharing = benefit.sharing,
			initialLimit =benefit.limit,
			categoryId = benefit.category.id,
			payerId = benefit.payer.id,
			policyId = benefit.category.policy.id,
			startDate=startDate,
			endDate=endDate,
			joinDate=member.joinDate,
			gender=member.gender,
			memberType=member.beneficiaryType,
			catalogId=benefit.benefitRef.id ?:0,
			jicEntityId=member.jicEntityId,
			apaEntityId=member.apaEntityId,
			benefitType=benefit.benefitType,
			capitationType=benefit.capitationType,
			capitationPeriod=benefit.capitationPeriod,
			visitCountPeriod=benefit.visitCountPeriod,
			capitationMaxVisitCount=benefit.capitationMaxVisitCount,
			capitationFacilitiesCount=benefit.capitationFacilitiesCount,
			requireBeneficiaryToSelectProvider=benefit.requireBeneficiaryToSelectProvider,
			visitLimit=benefit.visitLimit,
			daysOfAdmissionLimit=benefit.daysOfAdmissionLimit,
			amountPerDayLimit=benefit.amountPerDayLimit,
			applicableMinAge=benefit.applicableMinAge,
			applicableMaxAge=benefit.applicableMaxAge,
			transferable=benefit.transferable,
			billable=benefit.billable,
			dob=member.dob,
			migratedFromCategoryId = migratedFromCategory,
			subBenefits = subBenefits,
			transferUtilization = transferUtilization
		)
	}

	private suspend fun buildBenefitProcessData(
		member: Beneficiary,
		benefit: Benefit,
		aggregateId: String,
		processSubBenefits: Boolean?
	) :BeneficiaryBenefitsDto{
		var startDate = member.category.policy.startDate.plus(benefit.waitingPeriod.period)
		var endDate = member.category.policy.endDate

		if ((member.category.policy.plan.type == PlanType.RETAIL || member.category.policy.plan.planDate == PlanDate.MEMBER_JOIN_DATE) && member.joinDate != null) {
			startDate = member.joinDate ?: LocalDate.now()
			endDate = startDate.plusYears(1).minusDays(1)
		}

		val subBenefits = mutableSetOf<BeneficiaryBenefitsDto>()
		if(processSubBenefits == true) {
			benefit.subBenefits.forEach { subBenefit ->
				if (benefitEligibilityPasses(benefit = subBenefit, ben = member)) {
					val benefits = buildBenefitProcessData(member, subBenefit, aggregateId, processSubBenefits)
					subBenefits.add(benefits)
				}
			}
		}
		val parentBeneficiaryId = if(member.principal == null) null else member.principal?.id
		return BeneficiaryBenefitsDto(
			aggregateId =  aggregateId,
			benefitId = benefit.id,
			parentBenefitId = benefit.parentBenefit?.id,
			parentBeneficiaryId = parentBeneficiaryId,
			beneficiaryId = member.id,
			memberName = member.name,
			memberNumber=member.memberNumber,
			benefitName=benefit.name,
			accessMode = if(benefit.preAuthType != PreAuthType.NONE) AccessModeType.PRE_AUTH_BASIS else AccessModeType.CREDIT_BASIS,
			suspensionThreshold = benefit.suspensionThreshold,
			preAuthThreshold = benefit.preAuthThreshold,
			thresholdAction = benefit.thresholdAction,
			sharing = benefit.sharing,
			initialLimit =benefit.limit,
			categoryId = benefit.category.id,
			payerId = benefit.payer.id,
			policyId = benefit.category.policy.id,
			startDate=startDate,
			endDate=endDate,
			joinDate=member.joinDate,
			gender=member.gender,
			memberType=member.beneficiaryType,
			catalogId=benefit.benefitRef.id ?:0,
			jicEntityId=member.jicEntityId,
			apaEntityId=member.apaEntityId,
			benefitType=benefit.benefitType,
			capitationType=benefit.capitationType,
			capitationPeriod=benefit.capitationPeriod,
			visitCountPeriod=benefit.visitCountPeriod,
			capitationMaxVisitCount=benefit.capitationMaxVisitCount,
			capitationFacilitiesCount=benefit.capitationFacilitiesCount,
			requireBeneficiaryToSelectProvider=benefit.requireBeneficiaryToSelectProvider,
			visitLimit=benefit.visitLimit,
			daysOfAdmissionLimit=benefit.daysOfAdmissionLimit,
			amountPerDayLimit=benefit.amountPerDayLimit,
			applicableMinAge=benefit.applicableMinAge,
			applicableMaxAge=benefit.applicableMaxAge,
			transferable=benefit.transferable,
			billable=benefit.billable,
			dob=member.dob,
			subBenefits = subBenefits,
		)
	}

	private suspend fun logBeneficiaryUpdate(dto: BeneficiaryStatusUpdateDTO, beneficiary: Beneficiary) {
		beneficiary.status = dto.status
		beneficiaryRepo.save(beneficiary)
		val action = when(dto.status){
				MemberStatus.ACTIVE -> "Member Activated"
				MemberStatus.SUSPENDED -> "Member Suspended"
				MemberStatus.DEACTIVATED -> "Member Deactivated"
		}
		val auditLog = AuditLoggingDTO(
			beneficiary = beneficiary,
			reason = dto.reason,
			user = dto.updateBy,
			action = action,
			type = ChangeLogType.MEMBERSTATUS_UPDATE,
			data = dto.toJsonString()
		)
		auditLogService.log(auditLog)
	}

	private fun suspendCurrentBenefits(beneficiaryId: Long, categoryId: Long):Boolean {

		val http = webClient(benefitUrl)
		var result = false


		 http.post()
			.uri(suspendBenefits)
			.header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
			//.body(Mono.just(mapper.writeValueAsString(dto)), String::class.java)
			.body(Mono.just(gson.toJson(DeactivateBenefitDTO(beneficiaryId,categoryId))), String::class.java)
			.exchange()
			.doOnSuccess { res ->
				println("-------------------------------")
				println(res)
				result = true
			}
			 .doOnError {
			 	result = false
			 }
			.block()!!.toString()

		return result


	}

	private fun filterProvidersByRestriction(providerIds: List<Long>, providerIdsInRestriction: List<Long>, restriction: RestrictionType?): List<Long> {
		return when (restriction) {
			RestrictionType.INCLUSIVE -> {
				if(providerIdsInRestriction.isEmpty()){
					throw NotFoundRequestException("Category has been marked as ${restriction.name} but no providers have been setup in restriction")
				}
				val providersNotInRestriction = providerIds.stream().filter { provider -> !providerIdsInRestriction.contains(provider) }.collect(Collectors.toList())
				if(providersNotInRestriction.size >0){
					throw BadRequestException("Provider with Ids ${providersNotInRestriction.map { "$it " }} are not in the Inclusive restriction")
				}
				providerIds.stream().filter { provider -> providerIdsInRestriction.contains(provider) }.collect(Collectors.toList())
			}

			RestrictionType.EXCLUSIVE -> {
				val providersInRestriction = providerIds.stream().filter { provider -> providerIdsInRestriction.contains(provider) }.collect(Collectors.toList())
				if(providersInRestriction.size >0){
					throw BadRequestException("Provider with Ids ${providersInRestriction.map { "$it " }} are in the Exclusive restriction")
				}
				providerIds.stream().filter { provider -> !providerIdsInRestriction.contains(provider) }.collect(Collectors.toList())
			}
			else -> {
				providerIds.toMutableList()
			}
		}
	}

	private fun mapBenefitToProvider(benefitId: Long, providerIds: List<Long>) {
		val benefit = validationService.validateAndGetBenefit(benefitId)
		val categoryRestriction = benefit.category.restrictionType

		val restrictions = categoryRestrictionMappingRepo.getProviderRestrictionsForACategory(benefit.category)

		val providerIdsInRestriction = restrictions.stream().map { entity -> entity.provider.id ?:0 }.collect(Collectors.toList())
		val allowedProviders = filterProvidersByRestriction(providerIds, providerIdsInRestriction, categoryRestriction)
		allowedProviders.forEach { providerId ->
			val provider = providerRepo.findById(providerId).orElseThrow{ NotFoundRequestException("Provider with Id $providerId doesn't exist") }

			payerProviderMappingRepo.findByPayerIdAndProviderId(
				payerId = benefit.payer.id,
				providerId = provider.id ?:0
			).orElseThrow{ NotFoundRequestException("Mapping for Payer and Provider with Id ${provider.id} not found") }

			val benefitPayerProviderMapping = benefitProviderMappingRepo.findByBenefitAndProvider(benefit,provider)
			val benefitPayerProviderMap: BenefitProviderMapping
			if(benefitPayerProviderMapping.isPresent){
				benefitPayerProviderMap = benefitPayerProviderMapping.get()
				benefitPayerProviderMap.mappingEnabled = true
			}else{
				benefitPayerProviderMap = BenefitProviderMapping(
					benefit=benefit,
					provider = provider,
					mappingEnabled = true
				)
			}
			benefitProviderMappingRepo.save(benefitPayerProviderMap)

		}
	}

}

