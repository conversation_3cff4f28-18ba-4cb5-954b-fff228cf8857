package net.lctafrica.membership.api.service

import javassist.NotFoundException
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.runBlocking
import net.lctafrica.membership.api.domain.PayerRepository
import net.lctafrica.membership.api.dtos.*
import net.lctafrica.membership.api.helper.NoRoleChangesException
import net.lctafrica.membership.api.util.KeycloakResponseUtil
import net.lctafrica.membership.api.util.Result
import net.lctafrica.membership.api.util.ResultFactory
import org.keycloak.OAuth2Constants
import org.keycloak.admin.client.Keycloak
import org.keycloak.admin.client.KeycloakBuilder
import org.keycloak.admin.client.resource.RealmResource
import org.keycloak.admin.client.resource.UserResource
import org.keycloak.admin.client.resource.UsersResource
import org.keycloak.representations.idm.CredentialRepresentation
import org.keycloak.representations.idm.GroupRepresentation
import org.keycloak.representations.idm.RoleRepresentation
import org.keycloak.representations.idm.UserRepresentation
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.security.SecureRandom
import java.time.LocalDateTime
import java.util.*
import java.util.function.Predicate
import java.util.stream.Collectors
import javax.annotation.PostConstruct
import javax.persistence.EntityNotFoundException
import javax.ws.rs.core.Response

@Service
@Transactional
class UserManagementService(
    @Value("\${lct-africa.keycloak.serverUrl}")
    val serverUrl: String,
    @Value("\${lct-africa.keycloak.keycloakRealm}")
    val keycloakRealm: String,
    @Value("\${lct-africa.keycloak.realm}")
    val realm: String,
    @Value("\${lct-africa.keycloak.keycloakClient}")
    val keycloakClient: String,
    @Value("\${lct-africa.keycloak.clientSecret}")
    val clientSecret: String,
    private val userRolesAuditLogService: UserRolesAuditLogService,
    private val payerRepository: PayerRepository,
    private val notificationService: INotificationService,
    private val denyPolicyService: DenyPolicyService
) : IUserManagementService {
    private val logger = LoggerFactory.getLogger(UserManagementService::class.java)
    private lateinit var keycloak: Keycloak
    val PASSWORD_EXPIRY_MINUTES = 15L

    @PostConstruct
    fun initKeycloak() {
        try {
            logger.info(
                "Initializing Keycloak connection with serverUrl: {}, realm: {}, clientId: {}",
                serverUrl, realm, keycloakClient
            )

            keycloak = KeycloakBuilder.builder()
                .serverUrl(serverUrl)
                .realm(realm)
                .grantType(OAuth2Constants.CLIENT_CREDENTIALS)
                .clientId(keycloakClient)
                .clientSecret(clientSecret)
                .build()

            // test connection
            try {
                val realmResource = keycloak.realm(realm)
                val count = realmResource.users().count()
                logger.info("Successfully connected to Keycloak. User count: {}", count)
            } catch (e: Exception) {
                logger.error("Failed to verify Keycloak connection: {}", e.message)
                logger.error("Keycloak connection error", e)
            }
        } catch (e: Exception) {
            logger.error("Failed to initialize Keycloak client: {}", e.message)
            logger.error("Keycloak initialization error", e)
        }
    }

    val allPayerModulesRoles = setOf(
        "ALL_CARE_ROLE",
        "ALL_CLAIMS_ROLE",
        "ALL_CLAIMS_ADJUDICATION_ROLE",
        "ALL_FINANCE_AND_ACCOUNTING_ROLE",
        "ALL_MEMBERSHIP_ROLE",
        "ALL_PROVIDERS_ROLE",
        "SCHEME_OVERVIEW_ROLE"
    )

    @Transactional(rollbackFor = [Exception::class])
    override fun addCashier(dto: CashierUserDTO): Result<String> {
        val credentials = CredentialRepresentation()
        credentials.type = CredentialRepresentation.PASSWORD
        credentials.value = dto.password
        credentials.isTemporary = true
        val userRepresentation = UserRepresentation()
        userRepresentation.username = dto.username
        userRepresentation.email = dto.email
        userRepresentation.firstName = dto.firstName
        userRepresentation.lastName = dto.lastName
        userRepresentation.credentials = listOf(credentials)
        userRepresentation.isEnabled = true
        val attributes: MutableMap<String, List<String>> = HashMap()
        attributes["providerId"] = listOf(dto.providerId.toString())
        attributes["providerName"] = listOf(dto.providerName.toString())
        userRepresentation.attributes = attributes
        val response: Response = keycloak.realm(keycloakRealm).users().create(userRepresentation);

        return if (HttpStatus.valueOf(response.status).is2xxSuccessful) {
            val locationHeader = response.getHeaderString("Location")
            val userResource: UserResource = getLctUsersResource()!!
                .get(
                    locationHeader
                        .replace(
                            ".*/(.*)$".toRegex(),
                            "$1"
                        )
                )

            if (dto.creditcontrol == true) {
                setRealmRole(userResource, "CREDIT_CONTROL")
            } else {
                setRealmRole(userResource, "Cashier")
            }

            ResultFactory.getSuccessResult(msg = "Cashier Created Successfully")
        } else {
            ResultFactory.getFailResult(msg = "Could not Create Cashier ")
        }

    }

    @Transactional(rollbackFor = [Exception::class])
    override fun addCreditControlCashier(dto: CashierUserDTO): Result<String> {
        val credentials = CredentialRepresentation()
        credentials.type = CredentialRepresentation.PASSWORD
        credentials.value = dto.password
        credentials.isTemporary = false
        val userRepresentation = UserRepresentation()
        userRepresentation.username = dto.username
        userRepresentation.email = dto.email
        userRepresentation.firstName = dto.firstName
        userRepresentation.lastName = dto.lastName
        userRepresentation.credentials = Arrays.asList(credentials)
        userRepresentation.isEnabled = true
        val attributes: MutableMap<String, List<String>> = HashMap()
        attributes["providerId"] = listOf(dto.providerId.toString())
        attributes["providerName"] = listOf(dto.providerName.toString())
        userRepresentation.attributes = attributes
        val response: Response = keycloak.realm(keycloakRealm).users().create(userRepresentation);

        return if (HttpStatus.valueOf(response.status).is2xxSuccessful) {
            val locationHeader = response.getHeaderString("Location")
            val userResource: UserResource = getLctUsersResource()!!
                .get(
                    locationHeader
                        .replace(
                            ".*/(.*)$".toRegex(),
                            "$1"
                        )
                )
            setRealmRole(userResource, "Cashier")
            setRealmRole(userResource, "CREDIT_CONTROL")
            ResultFactory.getSuccessResult(msg = "Credit Control Cashier Created Successfully")
        } else {
            ResultFactory.getFailResult(msg = "Could not Create Credit Control Cashier ")
        }
    }


    @Transactional(rollbackFor = [Exception::class])
    override fun addPayerAdmin(dto: PayerUserDTO): Result<PayerUserDTO> {
        val credentials = CredentialRepresentation()
        credentials.type = CredentialRepresentation.PASSWORD
        credentials.value = dto.password
        credentials.isTemporary = true
        val userRepresentation = UserRepresentation()
        userRepresentation.username = dto.username
        userRepresentation.email = dto.email
        userRepresentation.firstName = dto.firstName
        userRepresentation.lastName = dto.lastName
        userRepresentation.credentials = Arrays.asList(credentials)
        userRepresentation.isEnabled = true
        val attributes: MutableMap<String, List<String>> = HashMap()
        attributes["payerId"] = listOf(dto.payerId.toString())
        userRepresentation.attributes = attributes
        val response: Response = keycloak.realm(keycloakRealm).users().create(userRepresentation);

        return if (HttpStatus.valueOf(response.status).is2xxSuccessful) {
            val locationHeader = response.getHeaderString("Location")
            val userResource: UserResource = getLctUsersResource()!!
                .get(
                    locationHeader
                        .replace(
                            ".*/(.*)$".toRegex(),
                            "$1"
                        )
                )
            setRealmRole(userResource, "ADMIN")
            setRealmRole(userResource, "PAYER")
            ResultFactory.getSuccessResult(msg = "Payer Admin Created Successfully")
        } else {
            ResultFactory.getFailResult(msg = "Could not Create Payer Admin ")
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun addAdminUser(dto: AdminUserDTO): Result<AdminUserDTO> {
        TODO("Not yet implemented")
    }

    override fun getProviderUsers(providerId: Long): List<UserRepresentation?>? {
        var matchList: List<UserRepresentation?>? = mutableListOf()
        val list2 = getAllUsers()

        if (list2!!.isNotEmpty()) {
            try {
                val res = filterProviderUsers(list2, providerId)
                logger.debug("Provider Users Response: {}", res)
                return res
            } catch (e: Exception) {
                logger.error("Error filtering provider users: {}", e.message)
            }
        } else {
            return null
        }
        return matchList
    }

    override fun getPayerUsers(payerId: Long): List<UserRepresentation?>? {
        var matchList: List<UserRepresentation?>? = mutableListOf()
        val list2 = getAllUsers()

        if (list2!!.isNotEmpty()) {
            try {
                val res = filterPayerUsers(list2, payerId)
                return res
            } catch (e: Exception) {
                logger.error("Error getting payer users: {}", e.message)
            }
        } else {
            return null
        }
        return matchList
    }

    override fun resetPassword(username: String): List<UserRepresentation?>? {
        val list: List<UserRepresentation?>? = getUsersRepresentationByUsername(username)
        val user: UserRepresentation? = list!![0]
        val userResource: UserResource = keycloak.realm(realm).users().get(user!!.id)
        setPassword(userResource)
        return list

    }

    override fun addCreditControlRole(username: String): List<UserRepresentation?>? {
        val list: List<UserRepresentation?>? = getUsersRepresentationByUsername(username)
        val user: UserRepresentation? = list!![0]
        val userResource: UserResource = keycloak.realm(realm).users().get(user!!.id)
        setRealmRole(userResource, "CREDIT_CONTROL")
        return list
    }

    private fun setPassword(userResource: UserResource) {
        val cred = CredentialRepresentation()
        cred.type = CredentialRepresentation.PASSWORD
        cred.value = "1234"
        cred.isTemporary = true
        userResource.resetPassword(cred)
    }

    fun getAllUsers(): List<UserRepresentation?>? {
        logger.debug("Getting all users from Keycloak")
        return withKeycloakRetry { getUsersRepresentation(null) }
    }

    fun filterProviderUsers(users: List<UserRepresentation?>, providerId: Long): List<UserRepresentation?>? {
        val matchList: MutableList<UserRepresentation> = mutableListOf()
        for (user in users) {
            try {
                if (user == null) {
                    logger.debug("Skipping null user")
                    continue
                }

                val attributes = user.attributes
                if (attributes == null) {
                    logger.debug("User {} has null attributes", user.username ?: user.id)
                    continue
                }

                val providerIdList = attributes["providerId"]
                if (providerIdList == null || providerIdList.isEmpty()) {
                    logger.debug("User {} has no providerId attribute", user.username ?: user.id)
                    continue
                }

                val userProviderId = providerIdList[0]
                if (userProviderId != null && userProviderId.toLongOrNull() == providerId) {
                    matchList.add(user)
                    logger.debug("Added user {} to provider {} match list", user.username ?: user.id, providerId)
                }
            } catch (e: Exception) {
                logger.error("Error filtering provider users: {}", e.message)
            }
        }
        logger.debug("Matched users: {}", matchList)
        return matchList
    }

    fun filterPayerUsers(users: List<UserRepresentation?>, payerId: Long): List<UserRepresentation?>? {
        val matchList: MutableList<UserRepresentation> = mutableListOf()
        for (user in users) {
            try {
                if (user == null) {
                    logger.debug("Skipping null user")
                    continue
                }

                val attributes = user.attributes
                if (attributes == null) {
                    logger.debug("User {} has null attributes", user.username ?: user.id)
                    continue
                }

                val payerIdList = attributes["payerId"]
                if (payerIdList == null || payerIdList.isEmpty()) {
                    logger.debug("User {} has no payerId attribute", user.username ?: user.id)
                    continue
                }

                val userPayerId = payerIdList[0]
                if (userPayerId != null && userPayerId.toLongOrNull() == payerId) {
                    matchList.add(user)
                    logger.debug("Added user {} to payer {} match list", user.username ?: user.id, payerId)
                }
            } catch (e: Exception) {
                logger.error("Error filtering payer users: {}", e.message)
            }
        }

        return matchList
    }

    override fun getUserRoles(username: String): Result<MutableList<String>> {
        val usersResource: UsersResource? = getLctUsersResource()
        val foundUser = usersResource!!.search(username, true)
        val rolesList: MutableList<String> = mutableListOf()
        if (foundUser!!.isNotEmpty()) {
            for (user in foundUser) {
                if (username == user?.username.toString()) {
                    val userResource: UserResource = keycloak.realm(realm).users().get(user?.id)
                    userResource.roles().all.realmMappings.forEach {
                        rolesList.add(it.name)
                    }
                } else {
                    return ResultFactory.getSuccessResult(msg = "No such username found")
                }
            }
            return ResultFactory.getSuccessResult(data = rolesList)
        } else {
            return ResultFactory.getSuccessResult(msg = "No such username found")
        }
    }

    override fun addGroup(dto: AddKeycloakGroupDto): Result<String> {
        val kRealm = keycloak.realm(keycloakRealm)
        val grp = GroupRepresentation()

        // Include payer ID in the group name to ensure uniqueness across payers
        val groupName = if (dto.payerId != null) {
            "payer_${dto.payerId}_${dto.groupName}"
        } else {
            dto.groupName
        }

        grp.name = groupName
        val attributes: MutableMap<String, List<String>> = HashMap()
        dto.payerId?.let { payerId ->
            attributes["payerId"] = listOf(payerId.toString())
        }
        dto.providerId?.let { providerId ->
            attributes["providerId"] = listOf(providerId.toString())
        }
        dto.payerAdminId?.let { payerAdminId ->
            attributes["payerAdminId"] = listOf(payerAdminId.toString())
        }
        if (attributes.isNotEmpty()) {
            grp.attributes = attributes
        }
        val response: Response = kRealm.groups().add(grp)
        return if (HttpStatus.valueOf(response.status).is2xxSuccessful) {
            val createId = KeycloakResponseUtil.getCreatedId(response)
            dto.roles?.forEach { role ->
                val groupRole: RoleRepresentation = kRealm.roles().get(role).toRepresentation()
                val roles: MutableList<RoleRepresentation> = LinkedList()
                roles.add(groupRole)
                kRealm.groups().group(createId).roles().realmLevel().add(roles)
            }
            ResultFactory.getSuccessResult("Group created successfully")
        } else {
            val errorMessage = KeycloakResponseUtil.getErrorMessage(response, "Failed to create group")
            ResultFactory.getFailResult(errorMessage)
        }
    }

    private fun getUsersRepresentation(email: String?): List<UserRepresentation?>? {
        return withKeycloakRetry {
            val usersResource: UsersResource? = getLctUsersResource()
            usersResource!!.search(email, 0, usersResource.count())
        }
    }

    private fun getUsersRepresentationByUsername(username: String?): List<UserRepresentation?>? {
        return withKeycloakRetry {
            val usersResource: UsersResource? = getLctUsersResource()
            usersResource!!.search(username, 0, usersResource.count())
        }
    }

    private fun getLctRealmResource(): RealmResource? {
        return withKeycloakRetry { keycloak.realm(realm) }
    }

    private fun getLctUsersResource(): UsersResource? {
        return getLctRealmResource()!!.users()
    }

    private fun removeRealmRoles(userResource: UserResource, role: String) {
        val lctRealmRoles: List<RoleRepresentation> = getLctRealmResource()!!.roles().list()
        val filteredRole = lctRealmRoles.stream()
            .filter(isNotDefaultRealmRole("offline_access"))
            .filter(isNotDefaultRealmRole("uma_authorization"))
            .filter(isRealmRole(role))
            .collect(Collectors.toList())
        userResource.roles().realmLevel().remove(filteredRole)

    }

    private fun setRealmRole(userResource: UserResource, role: String) {
        val lctRealmRoles: List<RoleRepresentation> = getLctRealmResource()!!.roles().list()
        val newRole = lctRealmRoles.stream()
            .filter(isNotDefaultRealmRole("offline_access"))
            .filter(isNotDefaultRealmRole("uma_authorization"))
            .filter(isRealmRole(role))
            .collect(Collectors.toList())
        logger.debug("New role: {}", newRole)
        userResource.roles().realmLevel().add(newRole)
    }

    private fun isRealmRole(roleName: String): Predicate<RoleRepresentation>? {
        return Predicate { role: RoleRepresentation -> role.name == roleName }
    }

    private fun isNotDefaultRealmRole(realmRoleName: String): Predicate<RoleRepresentation>? {
        return Predicate { role: RoleRepresentation -> role.name != realmRoleName }
    }

    private fun isLoggedIn(usersResource: UsersResource): Predicate<UserRepresentation>? {
        return Predicate { u: UserRepresentation ->
            !usersResource[u.id].userSessions.isEmpty()
        }
    }

    override fun savePayerAdminRole(dto: UpdatedRolesDTO): Result<String?>? {
        if (dto.username.isNotEmpty()) {
            dto.disabledRoles?.let { disableUnselectedRoles(dto, it) }
            dto.enabledRoles?.let { enableSelectedRoles(dto, it) }
            return ResultFactory.getSuccessResult(msg = "Successfully Updated Roles")
        } else {
            return ResultFactory.getFailResult(msg = "Provide Username to update the role")
        }
    }

    private fun enableSelectedRoles(
        dto: UpdatedRolesDTO,
        enabledRoles: Set<String>
    ) {
        if (dto.enabledRoles?.isNotEmpty() == true) {
            val list: List<UserRepresentation?>? = getUsersRepresentationByUsername(dto.username)
            val user: UserRepresentation? = list!![0]
            val userResource: UserResource = keycloak.realm(realm).users().get(user!!.id)
            for (role in enabledRoles) {
                setRealmRole(userResource, role)
            }
        }
    }

    private fun disableUnselectedRoles(
        dto: UpdatedRolesDTO,
        disabledRoles: Set<String>
    ) {
        if (dto.disabledRoles?.isNotEmpty() == true) {
            val list: List<UserRepresentation?>? = getUsersRepresentationByUsername(dto.username)
            val user: UserRepresentation? = list!![0]
            val userResource: UserResource = keycloak.realm(realm).users().get(user!!.id)
            for (role in disabledRoles) {
                removeRealmRoles(userResource, role)
            }
        }
    }

    private fun addSelectedRoles(enabledRoles: Set<String>?, userResource: UserResource) {
        if (!enabledRoles.isNullOrEmpty()) {
            val validRoles = getLctRealmResource()?.roles()?.list()?.filter {
                it.name !in listOf("uma_authorization", "offline_access") && it.name in enabledRoles
            }
            userResource.roles().realmLevel().add(validRoles)
        }
    }

    private fun removeSelectedRoles(disabledRoles: Set<String>?, userResource: UserResource) {
        if (!disabledRoles.isNullOrEmpty()) {
            val realmResource = getLctRealmResource()
            val allRolesToRemove = mutableSetOf<String>()

            allRolesToRemove.addAll(disabledRoles)

            disabledRoles.forEach { roleName ->
                try {
                    val role = realmResource?.roles()?.get(roleName)?.toRepresentation()
                    if (role?.isComposite == true) {
                        val compositeRoles = realmResource.roles().get(roleName).roleComposites
                        compositeRoles.forEach { compositeRole ->
                            allRolesToRemove.add(compositeRole.name)
                        }
                    }
                } catch (e: Exception) {
                    // Role might not exist, just continue
                }
            }

            // remove all roles (original + composite)
            val validRoles = realmResource?.roles()?.list()?.filter {
                it.name !in listOf("uma_authorization", "offline_access") && it.name in allRolesToRemove
            }
            userResource.roles().realmLevel().remove(validRoles)
        }
    }

    @Transactional
    fun updateUserRoles(updatedRoles: UserRoleUpdateDTO, userId: String): Map<String, String> {
        try {
            val userResource: UserResource = keycloak.realm(realm).users().get(userId)
            addSelectedRoles(updatedRoles.addedRoles, userResource)
            removeSelectedRoles(updatedRoles.removedRoles, userResource)
            userRolesAuditLogService.logUserRoleChange(
                userId,
                updatedRoles.actionedBy,
                updatedRoles.addedRoles?.toList() ?: emptyList(),
                updatedRoles.removedRoles?.toList() ?: emptyList()
            )
            return mapOf("message" to "User roles updated successfully!")
        } catch (e: NotFoundException) {
            throw EntityNotFoundException("No user with the provided id exists!")
        } catch (e: NoRoleChangesException) {
            throw IllegalArgumentException("No role changes were provided.")
        } catch (e: Exception) {
            throw RuntimeException("Unexpected error occurred while updating user roles: ${e.message}", e)
        }
    }


    @Transactional
    fun updateMultipleUserRoles(batchUpdatedRoles: BatchUserRoleUpdateDTO): Map<String, Any> {
        if (batchUpdatedRoles.addedRoles.isNullOrEmpty() && batchUpdatedRoles.removedRoles.isNullOrEmpty()) {
            return mapOf("message" to "No role changes were provided.")
        }

        return updateMultipleUserRolesWithCoroutines(batchUpdatedRoles)
    }

    /**
     * Sequential implementation of updating multiple user roles.
     * Kept for reference and comparison purposes.
     */
    private fun updateMultipleUserRolesSequential(batchUpdatedRoles: BatchUserRoleUpdateDTO): Map<String, Any> {
        val results = mutableMapOf<String, String>()
        val failedUpdates = mutableListOf<Map<String, String>>()

        for (userId in batchUpdatedRoles.userIds) {
            try {
                val userResource: UserResource = keycloak.realm(realm).users().get(userId)
                addSelectedRoles(batchUpdatedRoles.addedRoles, userResource)
                removeSelectedRoles(batchUpdatedRoles.removedRoles, userResource)
                userRolesAuditLogService.logUserRoleChange(
                    userId,
                    batchUpdatedRoles.actionedBy,
                    batchUpdatedRoles.addedRoles?.toList() ?: emptyList(),
                    batchUpdatedRoles.removedRoles?.toList() ?: emptyList()
                )
                results[userId] = "User roles updated successfully!"
            } catch (e: NotFoundException) {
                failedUpdates.add(mapOf("userId" to userId, "error" to "No user with the provided id exists!"))
            } catch (e: Exception) {
                failedUpdates.add(mapOf("userId" to userId, "error" to "Error updating user roles: ${e.message}"))
            }
        }

        return mapOf(
            "message" to "Batch user role update completed",
            "successCount" to results.size,
            "failureCount" to failedUpdates.size,
            "successfulUpdates" to results,
            "failedUpdates" to failedUpdates
        )
    }


    private fun updateMultipleUserRolesWithCoroutines(batchUpdatedRoles: BatchUserRoleUpdateDTO): Map<String, Any> {
        val results = Collections.synchronizedMap(mutableMapOf<String, String>())
        val failedUpdates = Collections.synchronizedList(mutableListOf<Map<String, String>>())

        runBlocking {
            val deferredResults = batchUpdatedRoles.userIds.map { userId ->
                async(Dispatchers.Default) {
                    try {
                        val userResource = keycloak.realm(realm).users().get(userId)

                        addSelectedRoles(batchUpdatedRoles.addedRoles, userResource)

                        removeSelectedRoles(batchUpdatedRoles.removedRoles, userResource)

                        userRolesAuditLogService.logUserRoleChange(
                            userId,
                            batchUpdatedRoles.actionedBy,
                            batchUpdatedRoles.addedRoles?.toList() ?: emptyList(),
                            batchUpdatedRoles.removedRoles?.toList() ?: emptyList()
                        )

                        results[userId] = "User roles updated successfully!"
                    } catch (e: NotFoundException) {
                        failedUpdates.add(mapOf("userId" to userId, "error" to "No user with the provided id exists!"))
                    } catch (e: Exception) {
                        failedUpdates.add(
                            mapOf(
                                "userId" to userId,
                                "error" to "Error updating user roles: ${e.message}"
                            )
                        )
                    }
                }
            }

            deferredResults.forEach { it.await() }
        }

        return mapOf(
            "message" to "Batch user role update completed",
            "successCount" to results.size,
            "failureCount" to failedUpdates.size,
            "successfulUpdates" to results,
            "failedUpdates" to failedUpdates
        )
    }

    fun findUserById(userId: String): UserRepresentation? {
        try {
            val user = withKeycloakRetry {
                keycloak.realm(realm).users().get(userId).toRepresentation()
            }

            if (user.attributes == null) {
                logger.debug("User {} has null attributes, initializing empty map", user.username ?: userId)
                user.attributes = mutableMapOf()
            }

            return user
        } catch (e: Exception) {
            logger.warn("User with ID {} not found in Keycloak: {}", userId, e.message)
            throw EntityNotFoundException("No user with the provided id exists!")
        }
    }

    /**
     * Helper function to retry Keycloak operations with token refresh if needed
     */
    private fun <T> withKeycloakRetry(operation: () -> T): T {
        try {
            return operation()
        } catch (e: Exception) {
            if (e is javax.ws.rs.NotAuthorizedException) {
                logger.warn("Keycloak authentication failed, attempting to reinitialize connection")
                try {
                    // reinitialize Keycloak connection
                    initKeycloak()
                    // retry the operation
                    return operation()
                } catch (retryEx: Exception) {
                    logger.error("Failed to reinitialize Keycloak connection: {}", retryEx.message)
                    throw retryEx
                }
            } else {
                throw e
            }
        }
    }

    fun findEffectiveUserRealmRoles(userId: String): List<RoleRepresentation> {
        try {
            val userRepresentation = keycloak.realm(realm).users().get(userId)
            return userRepresentation.roles().realmLevel().listEffective()
        } catch (e: Exception) {
            throw EntityNotFoundException("No user with the provided id exists!")
        }
    }

    fun findDirectUserRealmRoles(userId: String): List<RoleRepresentation> {
        try {
            val userRepresentation = keycloak.realm(realm).users().get(userId)
            return userRepresentation.roles().realmLevel().listAll()
        } catch (e: Exception) {
            throw EntityNotFoundException("No user with the provided id exists!")
        }
    }

    fun getAllSystemRealmRoles(): List<RoleRepresentation> {
        return keycloak.realm(realm).roles().list()
    }

    fun findPayerUsers(payerId: Long, searchTerm: String? = null): List<UserRepresentation> {
        logger.info("Finding users for payer ID: {}, searchTerm: {}", payerId, searchTerm)
        val list2 = withKeycloakRetry { getAllUsers() }

        if (list2.isNullOrEmpty()) {
            logger.warn("No users found in Keycloak")
            return emptyList()
        }

        return try {
            logger.debug("Found {} total users, filtering for payer ID: {}", list2.size, payerId)
            val payerUsers = filterPayerUsers(list2, payerId)?.filterNotNull() ?: emptyList()
            logger.debug("Found {} users for payer ID: {}", payerUsers.size, payerId)

            if (searchTerm.isNullOrBlank()) {
                return payerUsers
            }

            val searchTermLower = searchTerm.lowercase()
            payerUsers.filter { user ->
                val fullName = "${user.firstName ?: ""} ${user.lastName ?: ""}".lowercase()
                val email = user.email?.lowercase() ?: ""
                val username = user.username?.lowercase() ?: ""

                fullName.contains(searchTermLower) ||
                        email.contains(searchTermLower) ||
                        username.contains(searchTermLower)
            }
        } catch (e: Exception) {
            logger.error("Error filtering payer users: {}", e.message, e)
            emptyList()
        }
    }


    @Transactional
    fun assignAllPayerAccountsAllModulesRoles(actionedBy: String): Map<String, Any> {
        val allUsers = getAllUsers()

        if (allUsers.isNullOrEmpty()) {
            return mapOf("message" to "No users found in the system")
        }

        val payerUsers = mutableListOf<UserRepresentation>()
        for (user in allUsers) {
            try {
                if (user == null) {
                    logger.debug("Skipping null user")
                    continue
                }

                val attributes = user.attributes
                if (attributes == null) {
                    logger.debug("User {} has null attributes", user.username ?: user.id)
                    continue
                }

                if (!attributes.containsKey("payerId")) {
                    logger.debug("User {} has no payerId attribute", user.username ?: user.id)
                    continue
                }

                // Exclude KTDA users (payerId = 14)
                val payerId = attributes["payerId"]?.firstOrNull()
                if (payerId == null) {
                    logger.debug("User {} has empty payerId list", user.username ?: user.id)
                    continue
                }

                if (payerId != "14") {
                    payerUsers.add(user)
                    logger.debug("Added user {} to payer users list", user.username ?: user.id)
                } else {
                    logger.debug("Skipping KTDA user {}", user.username ?: user.id)
                }
            } catch (e: Exception) {
                logger.error("Error checking user attributes: {}", e.message)
            }
        }

        if (payerUsers.isEmpty()) {
            return mapOf("message" to "No users with payerId attribute found")
        }

        val rolesToAdd = allPayerModulesRoles

        val batchUpdateDto = BatchUserRoleUpdateDTO(
            userIds = payerUsers.map { it.id },
            addedRoles = rolesToAdd,
            removedRoles = null,
            actionedBy = actionedBy
        )

        return updateMultipleUserRoles(batchUpdateDto)
    }


    @Transactional
    fun makeUserPayerSuperAdmin(userId: String, actionedBy: String): Map<String, String> {
        try {
            val userResource: UserResource = keycloak.realm(realm).users().get(userId)

            val user = findUserById(userId) ?: throw EntityNotFoundException("No user with the provided id exists!")

            val attributes = user.attributes
            if (attributes == null) {
                logger.error("User {} has null attributes", user.username ?: userId)
                throw IllegalStateException("User does not have any attributes")
            }

            if (!attributes.containsKey("payerId")) {
                logger.error("User {} has no payerId attribute", user.username ?: userId)
                throw IllegalStateException("User does not have a payerId attribute")
            }

            val payerIdList = attributes["payerId"]
            if (payerIdList == null || payerIdList.isEmpty()) {
                logger.error("User {} has empty payerId list", user.username ?: userId)
                throw IllegalStateException("User has an empty payerId attribute")
            }

            val payerId = payerIdList.firstOrNull()?.toLongOrNull()
                ?: throw IllegalStateException("User does not have a valid payerId attribute")

            val removeFromDenyPoliciesResult = denyPolicyService.removeUserFromAllDenyPolicies(userId)
            if (!removeFromDenyPoliciesResult.success) {
                throw RuntimeException("Failed to remove user from deny policies: ${removeFromDenyPoliciesResult.msg}")
            }

            val rolesToAdd = setOf("PAYER_SUPER_ADMIN")

            val updatedRoles = UserRoleUpdateDTO(
                addedRoles = rolesToAdd,
                removedRoles = null,
                actionedBy = actionedBy
            )

            addSelectedRoles(updatedRoles.addedRoles, userResource)

            userRolesAuditLogService.logUserRoleChange(
                userId,
                actionedBy,
                updatedRoles.addedRoles?.toList() ?: emptyList(),
                emptyList()
            )

            return mapOf("message" to "User successfully made a payer superadmin with all module roles")
        } catch (e: NotFoundException) {
            throw EntityNotFoundException("No user with the provided id exists!")
        } catch (e: Exception) {
            throw RuntimeException("Unexpected error occurred while making user a payer superadmin: ${e.message}", e)
        }
    }

    override fun updatePayerUserInfo(dto: PayerUserUpdateDTO): Result<UserRepresentation> {
        try {
            val userResource = keycloak.realm(realm).users().get(dto.userId)
            val user = userResource.toRepresentation()

            val attributes = user.attributes
            if (attributes == null) {
                logger.error("User {} has null attributes", user.username ?: dto.userId)
                user.attributes = mutableMapOf()
                logger.info("Initialized empty attributes map for user {}", user.username ?: dto.userId)
                return ResultFactory.getFailResult("User does not have any attributes")
            }

            if (!attributes.containsKey("payerId")) {
                logger.error("User {} has no payerId attribute", user.username ?: dto.userId)
                return ResultFactory.getFailResult("User is not associated with any payer account")
            }

            var updated = false

            if (dto.firstName != null) {
                user.firstName = dto.firstName
                updated = true
            }

            if (dto.lastName != null) {
                user.lastName = dto.lastName
                updated = true
            }

            if (dto.email != null) {
                user.email = dto.email
                updated = true
            }

            if (!updated) {
                return ResultFactory.getFailResult("No information provided for update")
            }

            userResource.update(user)

            return ResultFactory.getSuccessResult(user)
        } catch (e: Exception) {
            return ResultFactory.getFailResult("Failed to update user information: ${e.message}")
        }
    }


    override fun updateUserStatus(dto: UserStatusUpdateDTO): Result<UserRepresentation> {
        try {
            val userResource = keycloak.realm(realm).users().get(dto.userId)
            val user = userResource.toRepresentation()

            user.isEnabled = dto.enabled

            userResource.update(user)

            return ResultFactory.getSuccessResult(user)
        } catch (e: Exception) {
            return ResultFactory.getFailResult("Failed to update user status: ${e.message}")
        }
    }


    private fun generateSecurePassword(length: Int = 12): String {
        val chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()-_=+"
        val random = SecureRandom()
        return (1..length)
            .map { chars[random.nextInt(chars.length)] }
            .joinToString("")
    }


    override fun requestPasswordReset(dto: PasswordResetRequestDTO): Result<PasswordResetResponseDTO> {
        try {
            val list = getUsersRepresentationByUsername(dto.username)
            if (list.isNullOrEmpty()) {
                return ResultFactory.getFailResult("User not found")
            }

            val user = list[0]!!
            val userResource = keycloak.realm(realm).users().get(user.id)

            val attributes = user.attributes
            if (attributes == null) {
                logger.error("User {} has null attributes", user.username ?: user.id)
                user.attributes = mutableMapOf()
                logger.info("Initialized empty attributes map for user {}", user.username ?: user.id)
                return ResultFactory.getFailResult("User does not have any attributes")
            }

            if (!attributes.containsKey("payerId")) {
                logger.error("User {} has no payerId attribute", user.username ?: user.id)
                return ResultFactory.getFailResult("User is not associated with any payer account")
            }

            val payerIdList = attributes["payerId"]
            if (payerIdList == null || payerIdList.isEmpty()) {
                logger.error("User {} has empty payerId list", user.username ?: user.id)
                return ResultFactory.getFailResult("User has an invalid payer account association")
            }

            if (user.email.isNullOrEmpty()) {
                return ResultFactory.getFailResult(
                    "A user must have an email address to reset password. Contact your administrator for assistance."
                )
            }

            val payerId = attributes["payerId"]?.firstOrNull()?.toLongOrNull()
                ?: return ResultFactory.getFailResult("User has an invalid payer ID format")

            val payer = payerRepository.findById(payerId).orElse(null)
            val payerName = payer?.name ?: "Payer $payerId"

            val temporaryPassword = generateSecurePassword()

            val cred = CredentialRepresentation()
            cred.type = CredentialRepresentation.PASSWORD
            cred.value = temporaryPassword
            cred.isTemporary = true
            userResource.resetPassword(cred)

            val expiryTime = LocalDateTime.now().plusMinutes(PASSWORD_EXPIRY_MINUTES)

            val response = PasswordResetResponseDTO(
                userId = user.id,
                username = user.username,
                email = user.email,
                payerId = payerId,
                payerName = payerName,
                temporaryPassword = temporaryPassword,
                expiryTime = expiryTime
            )

            val emailSubject = "Password Reset for $payerName Account"
            val emailBody = """
                Hello ${user.firstName ?: user.username},

                You have requested a password reset for your account with $payerName.

                Your temporary password is: $temporaryPassword

                This password will expire in $PASSWORD_EXPIRY_MINUTES minutes. Please log in and change your password immediately.

                If you did not request this password reset, please contact your administrator.

                Regards,
                The System Administrator
            """.trimIndent()

            runBlocking {
                notificationService.sendEmail(emailSubject, emailBody, user.email)
            }

            return ResultFactory.getSuccessResult(response)
        } catch (e: Exception) {
            return ResultFactory.getFailResult("Failed to reset password: ${e.message}")
        }
    }
}
