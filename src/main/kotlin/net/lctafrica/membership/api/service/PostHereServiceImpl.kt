package net.lctafrica.membership.api.service

import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import org.springframework.web.reactive.function.BodyInserters
import org.springframework.web.reactive.function.client.WebClient
import org.springframework.web.reactive.function.client.awaitExchange
import org.springframework.web.reactive.function.client.createExceptionAndAwait

@Service
class PostHereServiceImpl(
    @Qualifier("postHereWebClientConfig") val webClient: WebClient
) : IPostHereService {
    val appLogger: Logger = LoggerFactory.getLogger(PostHereServiceImpl::class.java)
    override suspend fun post(data: String) {
        webClient.post()
            .uri("/7c49-4232-ac34")
            .headers { h -> h.setBearerAuth("no-auth") }
            .body(BodyInserters.fromValue(data))
            .awaitExchange { clientResponse ->
                return@awaitExchange if (clientResponse.statusCode() == HttpStatus.OK) {
                    appLogger.info("PostHereServiceImpl Ok")
                } else if (clientResponse.statusCode().is4xxClientError) {
                    throw Exception("PostHereServiceImpl Failed with Status: ${clientResponse.rawStatusCode()}")
                } else {
                    throw clientResponse.createExceptionAndAwait()
                }
            }
    }
}