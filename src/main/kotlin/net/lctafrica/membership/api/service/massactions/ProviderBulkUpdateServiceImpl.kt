package net.lctafrica.membership.api.service.massactions

import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import net.lctafrica.membership.api.domain.ProviderRepository
import net.lctafrica.membership.api.dtos.PayerProviderMappingDTO
import net.lctafrica.membership.api.dtos.PayerProviderMappingUpdateDTO
import net.lctafrica.membership.api.dtos.massactions.*
import net.lctafrica.membership.api.helper.ExcelHelper
import net.lctafrica.membership.api.service.IPayerService
import net.lctafrica.membership.api.service.IProviderService
import net.lctafrica.membership.api.service.IValidationService
import net.lctafrica.membership.api.util.*
import org.apache.poi.ss.usermodel.CellType
import org.apache.poi.ss.usermodel.DataFormatter
import org.apache.poi.ss.usermodel.Row
import org.apache.poi.ss.usermodel.Workbook
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.multipart.MultipartFile
import kotlin.math.min

@Service
class ProviderBulkUpdateServiceImpl(
    private val payerService: IPayerService,
    private val providerService: IProviderService,
    private val validationService: IValidationService,
    private val massActionsService: IMassActionsService,
    private val excelHelper: ExcelHelper,
    private val providerRepository: ProviderRepository,
    private val bulkOperationErrorHandler: BulkOperationErrorHandler,
    private val valueComparisonOptimizer: ValueComparisonOptimizer
) : IProviderBulkUpdateService {

    private val logger = LoggerFactory.getLogger(ProviderBulkUpdateServiceImpl::class.java)
    private val performanceLogger = PerformanceLogger()
    private val batchProcessor = BatchProcessor<ProviderBulkUpdateDto>()
    private val concurrentProcessor = ConcurrentProcessor()
    private val chunkedFileProcessor = ChunkedFileProcessor()

    companion object {
        const val DEFAULT_BATCH_SIZE = 100
        const val DEFAULT_CONCURRENCY_LEVEL = 10
        const val MEMORY_THRESHOLD_MB = 100L
    }

    override suspend fun processProviderUpdates(
        payerId: Long,
        file: MultipartFile
    ): ProviderUpdateSummary {
        return massActionsService.executeWithLogging("provider-bulk-update") {
            val monitor = performanceLogger.createPerformanceMonitor("provider-bulk-update")

            logger.info(
                "Starting optimized provider bulk update for payer: {}, file: {}",
                payerId,
                file.originalFilename
            )

            validationService.validateAndGetPayer(payerId)
            validateUploadedFile(file)

            // chunked file processing for memory efficiency
            val providerUpdates = parseExcelFileOptimized(file)
            logger.info("Parsed {} provider updates from Excel file using optimized processing", providerUpdates.size)

            monitor.logStart(
                providerUpdates.size, mapOf(
                    "payerId" to payerId,
                    "fileName" to (file.originalFilename ?: "unknown"),
                    "fileSize" to file.size
                )
            )

            // process with optimized concurrent and batch processing
            val result = processUpdatesOptimized(payerId, providerUpdates, monitor)

            // log final performance metrics with enhanced details
            val endTime = System.currentTimeMillis()
            val processingTime = endTime - System.currentTimeMillis() // Will be calculated properly in monitor

            monitor.logCompletion(
                PerformanceMetrics(
                    totalRecords = result.totalRows,
                    processingTimeMs = processingTime,
                    recordsPerSecond = if (processingTime > 0) (result.totalRows.toDouble() / processingTime * 1000) else 0.0,
                    memoryUsageMB = Runtime.getRuntime().let { (it.totalMemory() - it.freeMemory()) / (1024 * 1024) },
                    concurrencyLevel = DEFAULT_CONCURRENCY_LEVEL,
                    successRate = if (result.totalRows > 0) (result.successfulUpdates.toDouble() / result.totalRows * 100) else 0.0,
                    breakdown = mapOf(
                        "codeUpdates" to result.codeUpdates.toLong(),
                        "nameUpdates" to result.nameUpdates.toLong(),
                        "automaticMappings" to result.automaticMappings.toLong(),
                        "errors" to result.errors.size.toLong(),
                        "skippedRows" to result.skippedRows.toLong()
                    )
                )
            )

            // log memory metrics after processing
            performanceLogger.logMemoryMetrics()

            result
        }
    }


    private suspend fun parseExcelFileOptimized(file: MultipartFile): List<ProviderBulkUpdateDto> {
        logger.info("Starting optimized Excel file parsing for file: {}", file.originalFilename)

        val workbook: Workbook = try {
            excelHelper.parseWorkbookFromFile(file)
        } catch (e: Exception) {
            throw IllegalArgumentException("Failed to read file. File may be corrupted or in wrong format: ${e.message}")
        }

        return try {
            if (workbook.numberOfSheets == 0) {
                throw IllegalArgumentException("File contains no worksheets")
            }

            val sheet = workbook.getSheetAt(0)
            validateExcelColumns(sheet)

            // use chunked processing for memory efficiency
            val summary = chunkedFileProcessor.processExcelInChunks(workbook) { rows, metadata ->
                val chunkData = mutableListOf<ProviderBulkUpdateDto>()
                val formatter = DataFormatter()
                val columnMapping = getColumnMapping(sheet.getRow(0))

                rows.forEachIndexed { index, row ->
                    try {
                        val rowNumber = metadata.startRowIndex + index + 1 // +1 for Excel row numbering
                        val rowData = parseRowData(row, columnMapping, formatter, rowNumber)

                        if (rowData.providerId != null && (rowData.code != null || rowData.providerName != null)) {
                            chunkData.add(
                                ProviderBulkUpdateDto(
                                    rowData.providerId,
                                    rowData.code,
                                    rowData.providerName
                                )
                            )
                        }
                    } catch (e: Exception) {
                        logger.warn("Error parsing row in chunk {}: {}", metadata.chunkNumber, e.message)
                    }
                }

                ChunkResult(
                    chunkNumber = metadata.chunkNumber,
                    processedCount = rows.size,
                    successCount = chunkData.size,
                    errorCount = rows.size - chunkData.size,
                    data = chunkData
                )
            }

            val allUpdates = summary.chunkResults.flatMap { it.data }
            logger.info(
                "Optimized parsing completed: {} provider updates from {} chunks",
                allUpdates.size, summary.totalChunks
            )

            allUpdates
        } finally {
            workbook.close()
        }
    }

    private fun validateExcelColumns(sheet: org.apache.poi.ss.usermodel.Sheet) {
        if (sheet.physicalNumberOfRows == 0) {
            throw IllegalArgumentException("File is empty")
        }

        val headerRow = sheet.getRow(0) ?: throw IllegalArgumentException("File missing header row")

        val requiredColumns = listOf("Provider ID", "Code", "Provider Name")
        val actualColumns = mutableListOf<String>()

        // read actual column headers
        for (i in 0 until headerRow.physicalNumberOfCells) {
            val cell = headerRow.getCell(i)
            if (cell != null) {
                actualColumns.add(cell.stringCellValue.trim())
            }
        }

        if (actualColumns.isEmpty()) {
            throw IllegalArgumentException("File has no column headers")
        }

        val hasProviderId = actualColumns.any {
            it.equals("Provider ID", ignoreCase = true) ||
                    it.equals("Provider ID - required", ignoreCase = true) ||
                    it.startsWith("Provider ID", ignoreCase = true)
        }
        val hasCode = actualColumns.any {
            it.equals("Code", ignoreCase = true) ||
                    it.startsWith("Code", ignoreCase = true)
        }
        val hasProviderName = actualColumns.any {
            it.equals("Provider Name", ignoreCase = true) ||
                    it.startsWith("Provider Name", ignoreCase = true)
        }

        if (!hasProviderId) {
            throw IllegalArgumentException("File missing required column: 'Provider ID'")
        }

        if (!hasCode && !hasProviderName) {
            throw IllegalArgumentException("File must contain at least one of: 'Code' or 'Provider Name' columns")
        }

        logger.debug("File column validation passed. Found columns: {}", actualColumns)
    }

    private fun getColumnMapping(headerRow: Row): Map<String, Int> {
        val columnMapping = mutableMapOf<String, Int>()

        for (i in 0 until headerRow.physicalNumberOfCells) {
            val cell = headerRow.getCell(i)
            if (cell != null) {
                val columnName = cell.stringCellValue.trim()
                when {
                    columnName.equals("Provider ID", ignoreCase = true) ||
                            columnName.equals("Provider ID - required", ignoreCase = true) ||
                            columnName.startsWith("Provider ID", ignoreCase = true) -> columnMapping["providerId"] = i

                    columnName.equals("Code", ignoreCase = true) ||
                            columnName.startsWith("Code", ignoreCase = true) -> columnMapping["code"] = i

                    columnName.equals("Provider Name", ignoreCase = true) ||
                            columnName.startsWith("Provider Name", ignoreCase = true) -> columnMapping["providerName"] =
                        i
                }
            }
        }

        return columnMapping
    }


    private data class RowData(
        val providerId: Long?,
        val code: String?,
        val providerName: String?
    )

    private fun parseRowData(
        row: Row,
        columnMapping: Map<String, Int>,
        formatter: DataFormatter,
        rowNumber: Int
    ): RowData {
        var providerId: Long? = null
        var code: String? = null
        var providerName: String? = null

        try {
            columnMapping["providerId"]?.let { colIndex ->
                val cell = row.getCell(colIndex)
                if (cell != null) {
                    providerId = when (cell.cellType) {
                        CellType.NUMERIC -> cell.numericCellValue.toLong()
                        CellType.STRING -> cell.stringCellValue.toLongOrNull()
                        else -> null
                    }
                }
            }

            columnMapping["code"]?.let { colIndex ->
                val cell = row.getCell(colIndex)
                if (cell != null) {
                    code = when (cell.cellType) {
                        CellType.STRING -> cell.stringCellValue.takeIf { it.isNotBlank() }
                        CellType.NUMERIC -> formatter.formatCellValue(cell).takeIf { it.isNotBlank() }
                        else -> null
                    }
                }
            }

            columnMapping["providerName"]?.let { colIndex ->
                val cell = row.getCell(colIndex)
                if (cell != null) {
                    providerName = when (cell.cellType) {
                        CellType.STRING -> cell.stringCellValue.takeIf { it.isNotBlank() }
                        else -> null
                    }
                }
            }

        } catch (e: Exception) {
            logger.warn("Error parsing individual cells in row {}: {}", rowNumber, e.message)
        }

        return RowData(providerId, code, providerName)
    }

    internal fun validateUploadedFile(file: MultipartFile) {
        if (file.isEmpty) {
            throw IllegalArgumentException("Uploaded file is empty")
        }

        // limit to 10MB
        val maxFileSize = 10 * 1024 * 1024
        if (file.size > maxFileSize) {
            throw IllegalArgumentException("File size exceeds maximum limit of 10MB. Current size: ${file.size / (1024 * 1024)}MB")
        }

        if (!excelHelper.hasExcelFormat(file)) {
            val supportedExtensions = excelHelper.getSupportedExtensions().joinToString(", ")
            throw IllegalArgumentException(
                "Invalid file format. Expected Excel file with supported extensions: $supportedExtensions. " +
                        "Received: ${file.contentType} (${file.originalFilename})"
            )
        }

        val filename = file.originalFilename
        if (filename.isNullOrBlank()) {
            throw IllegalArgumentException("File must have a valid filename")
        }

        val supportedExtensions = excelHelper.getSupportedExtensions()
        val hasValidExtension = supportedExtensions.any { filename.lowercase().endsWith(it) }

        if (!hasValidExtension) {
            throw IllegalArgumentException(
                "File must have a supported extension: ${supportedExtensions.joinToString(", ")}. " +
                        "Received: $filename"
            )
        }

        logger.debug("File validation passed: {} ({}KB)", filename, file.size / 1024)
    }


    private suspend fun processUpdatesOptimized(
        payerId: Long,
        providerUpdates: List<ProviderBulkUpdateDto>,
        monitor: PerformanceMonitor
    ): ProviderUpdateSummary = coroutineScope {

        val startTime = System.currentTimeMillis()
        logger.info("Starting optimized processing of {} provider updates", providerUpdates.size)

        // calculate the optimal batch size based on data size and available resources
        val optimalBatchSize = calculateOptimalBatchSize(providerUpdates.size)
        val optimalConcurrency = calculateOptimalConcurrency(providerUpdates.size)

        logger.info("Using optimized parameters: batchSize={}, concurrency={}", optimalBatchSize, optimalConcurrency)

        // process updates using concurrent batch processing
        val batchResults = concurrentProcessor.processBatchesConcurrently(
            items = providerUpdates,
            batchSize = optimalBatchSize,
            concurrencyLevel = optimalConcurrency
        ) { batch ->
            processBatchOfUpdates(payerId, batch, monitor)
        }

        // aggregate all batch results with comprehensive error handling
        val summary = batchResults.aggregate()
        val processingTime = System.currentTimeMillis() - startTime

        // aggregate errors from all batches for comprehensive analysis
        val errorAggregation = bulkOperationErrorHandler.aggregateBatchErrors(
            batchResults = batchResults,
            operationContext = "provider-bulk-update"
        )

        // log error aggregation summary
        if (errorAggregation.errorAggregation.summary.totalErrors > 0) {
            logger.warn(
                "Error aggregation summary: {} total errors, recovery strategy: {}, " +
                        "critical errors: {}, recoverable errors: {}",
                errorAggregation.errorAggregation.summary.totalErrors,
                errorAggregation.recoveryStrategy.action,
                errorAggregation.errorAggregation.summary.criticalErrors,
                errorAggregation.errorAggregation.summary.recoverableErrors
            )
        }

        // collect detailed results from all batches
        val allResults = batchResults.flatMap { it.metadata["results"] as? List<ProviderUpdateResult> ?: emptyList() }

        var codeUpdates = 0
        var nameUpdates = 0
        var successfulUpdates = 0
        var automaticMappings = 0
        val errors = mutableListOf<ProviderUpdateError>()

        allResults.forEach { result ->
            if (result.success) {
                successfulUpdates++
                if (result.codeUpdated) codeUpdates++
                if (result.nameUpdated) nameUpdates++
                if (result.automaticMappingCreated) automaticMappings++
            } else {
                result.error?.let { errors.add(it) }
            }
        }

        val skippedRows = providerUpdates.size - successfulUpdates - errors.size

        logger.info(
            "Optimized provider bulk update completed - Total: {}, Successful: {}, Code updates: {}, " +
                    "Name updates: {}, Automatic mappings: {}, Errors: {}, Skipped: {}, Time: {}ms, " +
                    "Throughput: {:.2f} rec/sec. Performance optimizations applied for duplicate value detection.",
            providerUpdates.size, successfulUpdates, codeUpdates, nameUpdates,
            automaticMappings, errors.size, skippedRows, processingTime,
            if (processingTime > 0) (providerUpdates.size.toDouble() / processingTime * 1000) else 0.0
        )

        ProviderUpdateSummary(
            totalRows = providerUpdates.size,
            successfulUpdates = successfulUpdates,
            codeUpdates = codeUpdates,
            nameUpdates = nameUpdates,
            skippedRows = skippedRows,
            errors = errors,
            automaticMappings = automaticMappings
        )
    }

    @Transactional
    private suspend fun processBatchOfUpdates(
        payerId: Long,
        batch: List<ProviderBulkUpdateDto>,
        monitor: PerformanceMonitor
    ): BatchResult {
        val batchStartTime = System.currentTimeMillis()
        val results = mutableListOf<ProviderUpdateResult>()

        // enhanced logging with detailed batch context
        logger.info(
            "Starting batch processing: batchSize={}, payerId={}, operationId={}",
            batch.size, payerId, monitor.getOperationId()
        )

        performanceLogger.logDatabaseOperation(
            "batch-provider-update-start",
            batch.size,
            0,
            batch.size
        )

        // group updates by type for batch database operations
        val codeUpdates = mutableListOf<Pair<ProviderBulkUpdateDto, Int>>()
        val nameUpdates = mutableListOf<Pair<ProviderBulkUpdateDto, Int>>()
        val mappingCreations = mutableListOf<Pair<ProviderBulkUpdateDto, Int>>()

        // first pass: validate and categorize updates
        // process batch with comprehensive error handling and partial success
        val batchPartialResult = bulkOperationErrorHandler.processBatchWithPartialSuccess(
            items = batch.mapIndexed { index, update -> update to index },
            processor = { (update, index) ->
                val context = ProviderUpdateContext(
                    providerId = update.providerId,
                    rowNumber = index + 2,
                    payerId = payerId,
                    code = update.code,
                    providerName = update.providerName,
                    operationId = monitor.getOperationId()
                )

                // Validate provider with retry logic for transient failures
                val validationResult = bulkOperationErrorHandler.handleProviderUpdateWithRetry(
                    operation = { validationService.validateAndGetProvider(update.providerId) },
                    context = context,
                    maxRetries = 2
                )

                when (validationResult) {
                    is BulkOperationResult.Success -> {
                        // Categorize updates based on type and existing mappings
                        if (update.code != null) {
                            val mappingResult = payerService.findPayerProviderMappingEntity(payerId, update.providerId)
                            if (!mappingResult.success || mappingResult.data == null) {
                                mappingCreations.add(update to index)
                            } else {
                                codeUpdates.add(update to index)
                            }
                        }

                        if (update.providerName != null) {
                            nameUpdates.add(update to index)
                        }

                        ValidationSuccess(update, index)
                    }

                    is BulkOperationResult.Failure -> {
                        results.add(
                            ProviderUpdateResult(
                                success = false,
                                codeUpdated = false,
                                nameUpdated = false,
                                automaticMappingCreated = false,
                                error = validationResult.error as ProviderUpdateError
                            )
                        )
                        ValidationFailure(update, index, validationResult.error as ProviderUpdateError)
                    }
                }
            },
            errorContext = "provider-update-validation"
        )

        // log validation results
        logger.info(
            "Batch validation completed: {}/{} successful, {} errors, processing mode: {}",
            batchPartialResult.successfulResults.size, batch.size,
            batchPartialResult.errors.size, batchPartialResult.processingMode
        )

        // batch process automatic mappings
        if (mappingCreations.isNotEmpty()) {
            val mappingResults = processBatchMappingCreations(payerId, mappingCreations)
            results.addAll(mappingResults)
        }

        // batch process code updates
        if (codeUpdates.isNotEmpty()) {
            val codeUpdateResults = processBatchCodeUpdates(payerId, codeUpdates)
            results.addAll(codeUpdateResults)
        }

        // batch process name updates
        if (nameUpdates.isNotEmpty()) {
            val nameUpdateResults = processBatchNameUpdates(nameUpdates)
            results.addAll(nameUpdateResults)
        }

        val batchTime = System.currentTimeMillis() - batchStartTime
        val successCount = results.count { it.success }
        val errorCount = results.count { !it.success }
        val automaticMappingCount = results.count { it.automaticMappingCreated }
        val codeUpdateCount = results.count { it.codeUpdated }
        val nameUpdateCount = results.count { it.nameUpdated }

        // enhanced batch completion logging with detailed metrics
        logger.info(
            "Batch processing completed: batchSize={}, successCount={}, errorCount={}, " +
                    "automaticMappings={}, codeUpdates={}, nameUpdates={}, processingTime={}ms, " +
                    "throughput={:.2f} rec/sec, operationId={}",
            batch.size, successCount, errorCount, automaticMappingCount, codeUpdateCount,
            nameUpdateCount, batchTime,
            if (batchTime > 0) (batch.size.toDouble() / batchTime * 1000) else 0.0,
            monitor.getOperationId()
        )

        // Log detailed error information for failed records
        results.filter { !it.success }.forEach { result ->
            result.error?.let { error ->
                performanceLogger.logError(
                    ProcessingError(
                        type = when (error.errorType) {
                            ProviderUpdateErrorType.PROVIDER_NOT_FOUND -> ErrorType.PROVIDER_NOT_FOUND
                            ProviderUpdateErrorType.UPDATE_FAILED -> ErrorType.UPDATE_FAILED
                            else -> ErrorType.VALIDATION_ERROR
                        },
                        message = error.errorMessage,
                        providerId = error.providerId
                    ),
                    ErrorContext(
                        rowNumber = error.rowNumber,
                        operationId = monitor.getOperationId(),
                        additionalInfo = mapOf(
                            "payerId" to payerId,
                            "batchSize" to batch.size,
                            "errorType" to error.errorType.name
                        )
                    )
                )
            }
        }

        performanceLogger.logDatabaseOperation(
            "batch-provider-update-complete",
            batch.size,
            batchTime,
            batch.size
        )

        return BatchResult(
            processedCount = batch.size,
            successCount = successCount,
            errorCount = errorCount,
            processingTimeMs = batchTime,
            success = errorCount == 0,
            metadata = mapOf(
                "results" to results,
                "automaticMappings" to automaticMappingCount,
                "codeUpdates" to codeUpdateCount,
                "nameUpdates" to nameUpdateCount
            )
        )
    }

    private suspend fun processBatchMappingCreations(
        payerId: Long,
        mappingCreations: List<Pair<ProviderBulkUpdateDto, Int>>
    ): List<ProviderUpdateResult> {
        val results = mutableListOf<ProviderUpdateResult>()
        val batchStartTime = System.currentTimeMillis()

        logger.info(
            "Processing {} automatic mappings in batch for payerId={}",
            mappingCreations.size, payerId
        )

        // filter out mappings that already exist to avoid duplicate creation
        val mappingFilterResult = valueComparisonOptimizer.filterMappingCreationsForDuplicates(
            payerId, mappingCreations
        )

        // log optimization results
        if (mappingFilterResult.skippedExisting.isNotEmpty()) {
            logger.info(
                "Mapping optimization: {} mappings already exist and will be skipped ({}% optimization)",
                mappingFilterResult.skippedExisting.size,
                String.format("%.1f", mappingFilterResult.optimizationRatio)
            )
        }

        // handle skipped mappings as successful (no-op)
        mappingFilterResult.skippedExisting.forEach { (update, index) ->
            results.add(
                ProviderUpdateResult(
                    success = true,
                    codeUpdated = false, // No actual update performed
                    nameUpdated = false,
                    automaticMappingCreated = false, // Already existed
                    error = null
                )
            )
        }

        // handle filtering errors
        mappingFilterResult.errors.forEach { error ->
            results.add(
                ProviderUpdateResult(
                    success = false,
                    codeUpdated = false,
                    nameUpdated = false,
                    automaticMappingCreated = false,
                    error = ProviderUpdateError(
                        rowNumber = error.index + 2,
                        providerId = error.update.providerId,
                        errorMessage = "Mapping filter error: ${error.reason}",
                        errorType = ProviderUpdateErrorType.VALIDATION_ERROR
                    )
                )
            )
        }

        // only process mappings that actually need to be created
        val actualMappingsToCreate = mappingFilterResult.actualMappingsNeeded

        if (actualMappingsToCreate.isEmpty()) {
            logger.info("No new mappings need to be created after optimization")
            return results
        }

        logger.info(
            "Creating {} new mappings after filtering (reduced from {})",
            actualMappingsToCreate.size, mappingCreations.size
        )

        // process mappings with comprehensive error handling and retry logic
        val mappingPartialResult = bulkOperationErrorHandler.processBatchWithPartialSuccess(
            items = actualMappingsToCreate,
            processor = { (update, index) ->
                val context = ProviderMappingContext(
                    providerId = update.providerId,
                    payerId = payerId,
                    rowNumber = index + 2,
                    code = update.code
                )

                // Create mapping with retry logic for transient failures
                val mappingResult = bulkOperationErrorHandler.handleProviderMappingWithRetry(
                    operation = {
                        val mappingDto = PayerProviderMappingDTO(
                            payerId = payerId,
                            providerId = update.providerId,
                            code = update.code!!
                        )
                        payerService.addProviderMapping(mappingDto)
                    },
                    context = context,
                    maxRetries = 3
                )

                when (mappingResult) {
                    is BulkOperationResult.Success -> {
                        val result = mappingResult.result
                        if (result.success) {
                            performanceLogger.logAutomaticMapping(
                                update.providerId,
                                payerId,
                                update.code!!,
                                MappingContext(
                                    rowNumber = index + 2,
                                    additionalInfo = mapOf(
                                        "mappingId" to (result.data?.id ?: "unknown"),
                                        "batchOperation" to true,
                                        "processingTimeMs" to (System.currentTimeMillis() - batchStartTime),
                                        "retryUsed" to true
                                    )
                                )
                            )

                            ProviderUpdateResult(
                                success = true,
                                codeUpdated = true,
                                nameUpdated = false,
                                automaticMappingCreated = true,
                                error = null
                            )
                        } else {
                            ProviderUpdateResult(
                                success = false,
                                codeUpdated = false,
                                nameUpdated = false,
                                automaticMappingCreated = false,
                                error = ProviderUpdateError(
                                    rowNumber = index + 2,
                                    providerId = update.providerId,
                                    errorMessage = "Failed to create automatic mapping: ${result.msg}",
                                    errorType = ProviderUpdateErrorType.MAPPING_CREATION_FAILED
                                )
                            )
                        }
                    }

                    is BulkOperationResult.Failure -> {
                        ProviderUpdateResult(
                            success = false,
                            codeUpdated = false,
                            nameUpdated = false,
                            automaticMappingCreated = false,
                            error = mappingResult.error as ProviderUpdateError
                        )
                    }
                }
            },
            errorContext = "automatic-mapping-creation"
        )

        // convert partial success results to provider update results
        results.addAll(mappingPartialResult.successfulResults)

        // handle any processing errors
        mappingPartialResult.errors.forEach { processingError ->
            results.add(
                ProviderUpdateResult(
                    success = false,
                    codeUpdated = false,
                    nameUpdated = false,
                    automaticMappingCreated = false,
                    error = ProviderUpdateError(
                        rowNumber = 0, // Row number will be determined from error details
                        providerId = processingError.providerId,
                        errorMessage = processingError.message,
                        errorType = when (processingError.type) {
                            ErrorType.MAPPING_FAILED -> ProviderUpdateErrorType.MAPPING_CREATION_FAILED
                            ErrorType.DATABASE_ERROR -> ProviderUpdateErrorType.DATABASE_ERROR
                            ErrorType.TIMEOUT_ERROR -> ProviderUpdateErrorType.TIMEOUT_ERROR
                            else -> ProviderUpdateErrorType.SYSTEM_ERROR
                        }
                    )
                )
            )
        }

        val batchTime = System.currentTimeMillis() - batchStartTime
        val successCount = results.count { it.success }
        val errorCount = results.count { !it.success }

        logger.info(
            "Batch automatic mapping creation completed: total={}, successful={}, failed={}, " +
                    "processingTime={}ms, throughput={:.2f} mappings/sec",
            mappingCreations.size, successCount, errorCount, batchTime,
            if (batchTime > 0) (mappingCreations.size.toDouble() / batchTime * 1000) else 0.0
        )

        return results
    }

    private suspend fun processBatchCodeUpdates(
        payerId: Long,
        codeUpdates: List<Pair<ProviderBulkUpdateDto, Int>>
    ): List<ProviderUpdateResult> {
        val results = mutableListOf<ProviderUpdateResult>()
        val batchStartTime = System.currentTimeMillis()

        logger.info(
            "Processing {} code updates in batch for payerId={}",
            codeUpdates.size, payerId
        )

        // filter out code updates where values are already the same
        val codeFilterResult = valueComparisonOptimizer.filterCodeUpdatesForChanges(
            payerId, codeUpdates
        )

        // log optimization results
        if (codeFilterResult.skippedDuplicates.isNotEmpty()) {
            logger.info(
                "Code update optimization: {} updates have identical values and will be skipped ({}% optimization)",
                codeFilterResult.skippedDuplicates.size,
                String.format("%.1f", codeFilterResult.optimizationRatio)
            )
        }

        // handle skipped updates as successful (no-op)
        codeFilterResult.skippedDuplicates.forEach { (update, index) ->
            results.add(
                ProviderUpdateResult(
                    success = true,
                    codeUpdated = false,
                    nameUpdated = false,
                    automaticMappingCreated = false,
                    error = null
                )
            )
        }

        // handle filtering errors
        codeFilterResult.errors.forEach { error ->
            results.add(
                ProviderUpdateResult(
                    success = false,
                    codeUpdated = false,
                    nameUpdated = false,
                    automaticMappingCreated = false,
                    error = ProviderUpdateError(
                        rowNumber = error.index + 2,
                        providerId = error.update.providerId,
                        errorMessage = "Code comparison error: ${error.reason}",
                        errorType = ProviderUpdateErrorType.VALIDATION_ERROR
                    )
                )
            )
        }

        // only process updates that actually need to change values
        val actualCodeUpdates = codeFilterResult.actualUpdatesNeeded

        if (actualCodeUpdates.isEmpty()) {
            logger.info("No code updates needed after value comparison optimization")
            return results
        }

        logger.info(
            "Updating {} codes after filtering (reduced from {})",
            actualCodeUpdates.size, codeUpdates.size
        )

        // prepare batch update DTOs
        val updateDtos = mutableListOf<PayerProviderMappingUpdateDTO>()
        val updateMapping = mutableMapOf<Long, Pair<ProviderBulkUpdateDto, Int>>()

        actualCodeUpdates.forEach { (update, index) ->
            try {
                val mappingResult = payerService.findPayerProviderMappingEntity(payerId, update.providerId)
                if (mappingResult.success && mappingResult.data != null) {
                    val updateDto = PayerProviderMappingUpdateDTO(
                        mappingId = mappingResult.data.id,
                        status = null,
                        code = update.code!!
                    )
                    updateDtos.add(updateDto)
                    updateMapping[mappingResult.data.id] = update to index
                }
            } catch (e: Exception) {
                logger.error("Error preparing code update for provider {}: {}", update.providerId, e.message)
                results.add(
                    ProviderUpdateResult(
                        success = false,
                        codeUpdated = false,
                        nameUpdated = false,
                        automaticMappingCreated = false,
                        error = ProviderUpdateError(
                            rowNumber = index + 2,
                            providerId = update.providerId,
                            errorMessage = "Error preparing update: ${e.message}",
                            errorType = ProviderUpdateErrorType.UPDATE_FAILED
                        )
                    )
                )
            }
        }

        // execute batch update
        if (updateDtos.isNotEmpty()) {
            val dbOperationStart = System.currentTimeMillis()
            try {
                logger.info("Executing batch code update for {} mappings", updateDtos.size)
                val batchResult = payerService.updatePayerProviderMappingBatch(updateDtos)
                val dbOperationTime = System.currentTimeMillis() - dbOperationStart

                performanceLogger.logDatabaseOperation(
                    "batch-code-update",
                    updateDtos.size,
                    dbOperationTime,
                    updateDtos.size
                )

                if (batchResult.success) {
                    logger.info(
                        "Batch code update successful: {} mappings updated in {}ms",
                        updateDtos.size, dbOperationTime
                    )

                    updateMapping.values.forEach { (update, index) ->
                        results.add(
                            ProviderUpdateResult(
                                success = true,
                                codeUpdated = true,
                                nameUpdated = false,
                                automaticMappingCreated = false,
                                error = null
                            )
                        )
                    }
                } else {
                    logger.error(
                        "Batch code update failed: {} mappings affected, error: {}",
                        updateDtos.size, batchResult.msg
                    )

                    updateMapping.values.forEach { (update, index) ->
                        performanceLogger.logError(
                            ProcessingError(
                                type = ErrorType.UPDATE_FAILED,
                                message = "Batch code update failed: ${batchResult.msg}",
                                providerId = update.providerId
                            ),
                            ErrorContext(
                                rowNumber = index + 2,
                                additionalInfo = mapOf(
                                    "payerId" to payerId,
                                    "batchSize" to updateDtos.size,
                                    "operation" to "batch_code_update"
                                )
                            )
                        )

                        results.add(
                            ProviderUpdateResult(
                                success = false,
                                codeUpdated = false,
                                nameUpdated = false,
                                automaticMappingCreated = false,
                                error = ProviderUpdateError(
                                    rowNumber = index + 2,
                                    providerId = update.providerId,
                                    errorMessage = "Batch code update failed: ${batchResult.msg}",
                                    errorType = ProviderUpdateErrorType.UPDATE_FAILED
                                )
                            )
                        )
                    }
                }
            } catch (e: Exception) {
                val dbOperationTime = System.currentTimeMillis() - dbOperationStart
                logger.error("Exception executing batch code updates: {}", e.message, e)

                performanceLogger.logError(
                    ProcessingError(
                        type = ErrorType.DATABASE_ERROR,
                        message = "Batch update exception: ${e.message}",
                        exception = e
                    ),
                    ErrorContext(
                        additionalInfo = mapOf(
                            "payerId" to payerId,
                            "batchSize" to updateDtos.size,
                            "operation" to "batch_code_update",
                            "executionTimeMs" to dbOperationTime
                        )
                    )
                )

                updateMapping.values.forEach { (update, index) ->
                    results.add(
                        ProviderUpdateResult(
                            success = false,
                            codeUpdated = false,
                            nameUpdated = false,
                            automaticMappingCreated = false,
                            error = ProviderUpdateError(
                                rowNumber = index + 2,
                                providerId = update.providerId,
                                errorMessage = "Batch update exception: ${e.message}",
                                errorType = ProviderUpdateErrorType.UPDATE_FAILED
                            )
                        )
                    )
                }
            }
        }

        val batchTime = System.currentTimeMillis() - batchStartTime
        val successCount = results.count { it.success }
        val errorCount = results.count { !it.success }

        logger.info(
            "Batch code updates completed: total={}, successful={}, failed={}, " +
                    "processingTime={}ms, throughput={:.2f} updates/sec",
            actualCodeUpdates.size, successCount, errorCount, batchTime,
            if (batchTime > 0) (actualCodeUpdates.size.toDouble() / batchTime * 1000) else 0.0
        )

        return results
    }

    private suspend fun processBatchNameUpdates(
        nameUpdates: List<Pair<ProviderBulkUpdateDto, Int>>
    ): List<ProviderUpdateResult> {
        val results = mutableListOf<ProviderUpdateResult>()
        val batchStartTime = System.currentTimeMillis()

        logger.info("Processing {} name updates in batch", nameUpdates.size)

        // filter out name updates where values are already the same
        val nameFilterResult = valueComparisonOptimizer.filterNameUpdatesForChanges(nameUpdates)

        // log optimization results
        if (nameFilterResult.skippedDuplicates.isNotEmpty()) {
            logger.info(
                "Name update optimization: {} updates have identical values and will be skipped ({}% optimization)",
                nameFilterResult.skippedDuplicates.size,
                String.format("%.1f", nameFilterResult.optimizationRatio)
            )
        }

        // handle skipped updates as successful (no-op)
        nameFilterResult.skippedDuplicates.forEach { (update, index) ->
            results.add(
                ProviderUpdateResult(
                    success = true,
                    codeUpdated = false,
                    nameUpdated = false, // No actual update performed (value unchanged)
                    automaticMappingCreated = false,
                    error = null
                )
            )
        }

        // Handle filtering errors
        nameFilterResult.errors.forEach { error ->
            results.add(
                ProviderUpdateResult(
                    success = false,
                    codeUpdated = false,
                    nameUpdated = false,
                    automaticMappingCreated = false,
                    error = ProviderUpdateError(
                        rowNumber = error.index + 2,
                        providerId = error.update.providerId,
                        errorMessage = "Name comparison error: ${error.reason}",
                        errorType = ProviderUpdateErrorType.VALIDATION_ERROR
                    )
                )
            )
        }

        // only process updates that actually need to change values
        val actualNameUpdates = nameFilterResult.actualUpdatesNeeded

        if (actualNameUpdates.isEmpty()) {
            logger.info("No name updates needed after value comparison optimization")
            return results
        }

        logger.info(
            "Updating {} names after filtering (reduced from {})",
            actualNameUpdates.size, nameUpdates.size
        )

        // process name updates in smaller batches to avoid transaction timeout
        actualNameUpdates.chunked(50).forEachIndexed { chunkIndex, chunk ->
            val chunkStartTime = System.currentTimeMillis()
            logger.debug(
                "Processing name update chunk {} of {} (size: {})",
                chunkIndex + 1, (actualNameUpdates.size + 49) / 50, chunk.size
            )

            chunk.forEach { (update, index) ->
                try {
                    val success = updateProviderName(update.providerId, update.providerName!!)

                    if (success) {
                        logger.debug(
                            "Successfully updated provider name: providerId={}, newName='{}'",
                            update.providerId, update.providerName
                        )
                    } else {
                        logger.warn(
                            "Failed to update provider name: providerId={}, newName='{}'",
                            update.providerId, update.providerName
                        )
                    }

                    results.add(
                        ProviderUpdateResult(
                            success = success,
                            codeUpdated = false,
                            nameUpdated = success,
                            automaticMappingCreated = false,
                            error = if (!success) ProviderUpdateError(
                                rowNumber = index + 2,
                                providerId = update.providerId,
                                errorMessage = "Failed to update provider name",
                                errorType = ProviderUpdateErrorType.UPDATE_FAILED
                            ) else null
                        )
                    )
                } catch (e: Exception) {
                    logger.error(
                        "Exception updating provider name for {}: {}",
                        update.providerId, e.message, e
                    )

                    performanceLogger.logError(
                        ProcessingError(
                            type = ErrorType.UPDATE_FAILED,
                            message = "Exception updating name: ${e.message}",
                            providerId = update.providerId,
                            exception = e
                        ),
                        ErrorContext(
                            rowNumber = index + 2,
                            additionalInfo = mapOf(
                                "providerName" to (update.providerName ?: "null"),
                                "chunkIndex" to chunkIndex,
                                "operation" to "batch_name_update"
                            )
                        )
                    )

                    results.add(
                        ProviderUpdateResult(
                            success = false,
                            codeUpdated = false,
                            nameUpdated = false,
                            automaticMappingCreated = false,
                            error = ProviderUpdateError(
                                rowNumber = index + 2,
                                providerId = update.providerId,
                                errorMessage = "Exception updating name: ${e.message}",
                                errorType = ProviderUpdateErrorType.UPDATE_FAILED
                            )
                        )
                    )
                }
            }

            val chunkTime = System.currentTimeMillis() - chunkStartTime
            logger.debug(
                "Completed name update chunk {} in {}ms, throughput: {:.2f} updates/sec",
                chunkIndex + 1, chunkTime,
                if (chunkTime > 0) (chunk.size.toDouble() / chunkTime * 1000) else 0.0
            )
        }

        val batchTime = System.currentTimeMillis() - batchStartTime
        val successCount = results.count { it.success }
        val errorCount = results.count { !it.success }

        logger.info(
            "Batch name updates completed: total processed={}, successful={}, failed={}, " +
                    "processingTime={}ms, throughput={:.2f} updates/sec",
            actualNameUpdates.size, successCount, errorCount, batchTime,
            if (batchTime > 0) (actualNameUpdates.size.toDouble() / batchTime * 1000) else 0.0
        )

        return results
    }

    private fun calculateOptimalBatchSize(totalRecords: Int): Int {
        return when {
            totalRecords <= 100 -> 25
            totalRecords <= 500 -> 50
            totalRecords <= 1500 -> DEFAULT_BATCH_SIZE
            totalRecords <= 5000 -> 150
            else -> 200
        }
    }

    private fun calculateOptimalConcurrency(totalRecords: Int): Int {
        val availableProcessors = Runtime.getRuntime().availableProcessors()
        return when {
            totalRecords <= 100 -> min(2, availableProcessors)
            totalRecords <= 500 -> min(5, availableProcessors)
            totalRecords <= 1500 -> min(DEFAULT_CONCURRENCY_LEVEL, availableProcessors)
            totalRecords <= 5000 -> min(15, availableProcessors * 2)
            else -> min(20, availableProcessors * 2)
        }
    }

    private suspend fun processUpdatesWithCoroutines(
        payerId: Long,
        providerUpdates: List<ProviderBulkUpdateDto>
    ): ProviderUpdateSummary = coroutineScope {

        val errors = mutableListOf<ProviderUpdateError>()
        var codeUpdates = 0
        var nameUpdates = 0
        var successfulUpdates = 0
        var automaticMappings = 0

        val results = providerUpdates.mapIndexed { index, update ->
            async {
                try {
                    processProviderUpdate(payerId, update, index + 2) // +2 because row 1 is header, index starts at 0
                } catch (e: Exception) {
                    logger.error(
                        "Unexpected error processing provider update: providerId={}, error={}",
                        update.providerId,
                        e.message,
                        e
                    )
                    ProviderUpdateResult(
                        success = false,
                        codeUpdated = false,
                        nameUpdated = false,
                        automaticMappingCreated = false,
                        error = ProviderUpdateError(
                            rowNumber = index + 2,
                            providerId = update.providerId,
                            errorMessage = "Unexpected error: ${e.message}",
                            errorType = ProviderUpdateErrorType.UPDATE_FAILED
                        )
                    )
                }
            }
        }.awaitAll()

        // aggregate results
        results.forEach { result ->
            if (result.success) {
                successfulUpdates++
                if (result.codeUpdated) codeUpdates++
                if (result.nameUpdated) nameUpdates++
                if (result.automaticMappingCreated) automaticMappings++
            } else {
                result.error?.let { errors.add(it) }
            }
        }

        val skippedRows = providerUpdates.size - successfulUpdates - errors.size

        logger.info(
            "Provider bulk update summary - Total: {}, Successful: {}, Code updates: {}, Name updates: {}, Automatic mappings: {}, Errors: {}, Skipped: {}",
            providerUpdates.size,
            successfulUpdates,
            codeUpdates,
            nameUpdates,
            automaticMappings,
            errors.size,
            skippedRows
        )

        return@coroutineScope ProviderUpdateSummary(
            totalRows = providerUpdates.size,
            successfulUpdates = successfulUpdates,
            codeUpdates = codeUpdates,
            nameUpdates = nameUpdates,
            skippedRows = skippedRows,
            errors = errors,
            automaticMappings = automaticMappings
        )
    }


    private suspend fun processProviderUpdate(
        payerId: Long,
        update: ProviderBulkUpdateDto,
        rowNumber: Int
    ): ProviderUpdateResult {
        val operationStartTime = System.currentTimeMillis()

        logger.debug(
            "Processing provider update: providerId={}, payerId={}, code={}, name={}, rowNumber={}",
            update.providerId, payerId, update.code, update.providerName, rowNumber
        )

        try {
            val provider = try {
                validationService.validateAndGetProvider(update.providerId)
            } catch (e: Exception) {
                return ProviderUpdateResult(
                    success = false,
                    codeUpdated = false,
                    nameUpdated = false,
                    automaticMappingCreated = false,
                    error = ProviderUpdateError(
                        rowNumber = rowNumber,
                        providerId = update.providerId,
                        errorMessage = "Provider not found",
                        errorType = ProviderUpdateErrorType.PROVIDER_NOT_FOUND
                    )
                )
            }

            var codeUpdated = false
            var nameUpdated = false
            var automaticMappingCreated = false

            // update provider code in payer mapping if provided
            if (update.code != null) {
                // check if the provider is mapped to the payer
                val mappingResult = payerService.findPayerProviderMappingEntity(payerId, update.providerId)

                if (!mappingResult.success || mappingResult.data == null) {
                    // provider not mapped - attempt automatic mapping
                    logger.info(
                        "Provider {} not mapped to payer {} - attempting automatic mapping with code {} (row {})",
                        update.providerId, payerId, update.code, rowNumber
                    )

                    automaticMappingCreated = createAutomaticProviderMapping(
                        payerId,
                        update.providerId,
                        update.code,
                        rowNumber
                    )

                    if (!automaticMappingCreated) {
                        return ProviderUpdateResult(
                            success = false,
                            codeUpdated = false,
                            nameUpdated = false,
                            automaticMappingCreated = false,
                            error = ProviderUpdateError(
                                rowNumber = rowNumber,
                                providerId = update.providerId,
                                errorMessage = "Failed to create automatic provider mapping",
                                errorType = ProviderUpdateErrorType.UPDATE_FAILED
                            )
                        )
                    }

                    // mapping created successfully, code is already set
                    codeUpdated = true
                } else {
                    // provider already mapped - update the code
                    codeUpdated = updateProviderMappingCode(payerId, update.providerId, update.code, rowNumber)
                    if (!codeUpdated) {
                        return ProviderUpdateResult(
                            success = false,
                            codeUpdated = false,
                            nameUpdated = false,
                            automaticMappingCreated = false,
                            error = ProviderUpdateError(
                                rowNumber = rowNumber,
                                providerId = update.providerId,
                                errorMessage = "Failed to update provider code",
                                errorType = ProviderUpdateErrorType.UPDATE_FAILED
                            )
                        )
                    }
                }
            }

            // update provider name if provided
            if (update.providerName != null) {
                nameUpdated = updateProviderName(update.providerId, update.providerName)
                if (!nameUpdated) {
                    return ProviderUpdateResult(
                        success = false,
                        codeUpdated = codeUpdated,
                        nameUpdated = false,
                        automaticMappingCreated = automaticMappingCreated,
                        error = ProviderUpdateError(
                            rowNumber = rowNumber,
                            providerId = update.providerId,
                            errorMessage = "Failed to update provider name",
                            errorType = ProviderUpdateErrorType.UPDATE_FAILED
                        )
                    )
                }
            }

            val operationTime = System.currentTimeMillis() - operationStartTime
            logger.debug(
                "Successfully processed provider update: providerId={}, codeUpdated={}, " +
                        "nameUpdated={}, automaticMappingCreated={}, processingTime={}ms, rowNumber={}",
                update.providerId, codeUpdated, nameUpdated, automaticMappingCreated, operationTime, rowNumber
            )

            return ProviderUpdateResult(
                success = true,
                codeUpdated = codeUpdated,
                nameUpdated = nameUpdated,
                automaticMappingCreated = automaticMappingCreated,
                error = null
            )

        } catch (e: Exception) {
            val operationTime = System.currentTimeMillis() - operationStartTime
            logger.error(
                "Exception processing provider update for providerId={}: {} (processingTime={}ms, rowNumber={})",
                update.providerId, e.message, operationTime, rowNumber, e
            )

            performanceLogger.logError(
                ProcessingError(
                    type = ErrorType.UPDATE_FAILED,
                    message = "Processing error: ${e.message}",
                    providerId = update.providerId,
                    exception = e
                ),
                ErrorContext(
                    rowNumber = rowNumber,
                    additionalInfo = mapOf(
                        "payerId" to payerId,
                        "code" to (update.code ?: "null"),
                        "providerName" to (update.providerName ?: "null"),
                        "processingTimeMs" to operationTime,
                        "operation" to "individual_provider_update"
                    )
                )
            )

            return ProviderUpdateResult(
                success = false,
                codeUpdated = false,
                nameUpdated = false,
                automaticMappingCreated = false,
                error = ProviderUpdateError(
                    rowNumber = rowNumber,
                    providerId = update.providerId,
                    errorMessage = "Processing error: ${e.message}",
                    errorType = ProviderUpdateErrorType.UPDATE_FAILED
                )
            )
        }
    }


    @Transactional
    private suspend fun updateProviderName(providerId: Long, newName: String): Boolean {
        val updateStartTime = System.currentTimeMillis()
        return try {
            if (newName.isBlank()) {
                logger.debug("Skipping provider name update for providerId={} - empty name provided", providerId)
                return true
            }

            val providerResult = providerService.findById(providerId)
            if (!providerResult.success || providerResult.data == null) {
                logger.warn("Provider {} not found - cannot update name", providerId)
                return false
            }

            val provider = providerResult.data

            // update the provider name and save directly using repository
            provider.name = newName.trim()
            providerRepository.save(provider)

            val updateTime = System.currentTimeMillis() - updateStartTime
            logger.debug(
                "Successfully updated provider name: providerId={}, newName='{}', processingTime={}ms",
                providerId, newName, updateTime
            )

            performanceLogger.logDatabaseOperation(
                "provider-name-update",
                1,
                updateTime
            )

            true
        } catch (e: Exception) {
            val updateTime = System.currentTimeMillis() - updateStartTime
            logger.error(
                "Exception updating provider name: providerId={}, newName='{}', error={}, processingTime={}ms",
                providerId, newName, e.message, updateTime, e
            )

            performanceLogger.logError(
                ProcessingError(
                    type = ErrorType.UPDATE_FAILED,
                    message = "Exception updating provider name: ${e.message}",
                    providerId = providerId,
                    exception = e
                ),
                ErrorContext(
                    additionalInfo = mapOf(
                        "newName" to newName,
                        "processingTimeMs" to updateTime,
                        "operation" to "individual_name_update"
                    )
                )
            )

            false
        }
    }

    @Transactional
    private suspend fun updateProviderNamesBatch(nameUpdates: List<Pair<Long, String>>): Map<Long, Boolean> {
        val results = mutableMapOf<Long, Boolean>()

        try {
            // fetch all providers in a batch
            val providerIds = nameUpdates.map { it.first }
            val providers = providerRepository.findAllById(providerIds).associateBy { it.id }

            // update names and collect entities to save
            val providersToSave = mutableListOf<net.lctafrica.membership.api.domain.Provider>()

            nameUpdates.forEach { (providerId, newName) ->
                val provider = providers[providerId]
                if (provider != null && newName.isNotBlank()) {
                    provider.name = newName.trim()
                    providersToSave.add(provider)
                    results[providerId] = true
                } else {
                    results[providerId] = false
                    logger.warn("Provider {} not found or invalid name provided", providerId)
                }
            }

            // batch-save all updated providers
            if (providersToSave.isNotEmpty()) {
                providerRepository.saveAll(providersToSave)
                logger.debug("Batch updated {} provider names", providersToSave.size)
            }

        } catch (e: Exception) {
            logger.error("Error in batch provider name update: {}", e.message, e)
            //mark all as failed
            nameUpdates.forEach { (providerId, _) ->
                results[providerId] = false
            }
        }

        return results
    }

    private suspend fun updateProviderMappingCode(
        payerId: Long,
        providerId: Long,
        newCode: String,
        rowNumber: Int
    ): Boolean {
        val updateStartTime = System.currentTimeMillis()
        return try {
            val mappingResult = payerService.findPayerProviderMappingEntity(payerId, providerId)
            if (!mappingResult.success || mappingResult.data == null) {
                logger.warn(
                    "Provider {} not mapped to payer {} - cannot update code",
                    providerId,
                    payerId
                )
                return false
            }

            // update the mapping code using the existing batch update method
            val updateDto = PayerProviderMappingUpdateDTO(
                mappingId = mappingResult.data.id,
                status = null, // don't change provider status
                code = newCode
            )

            val updateResult = payerService.updatePayerProviderMappingBatch(listOf(updateDto))
            val updateTime = System.currentTimeMillis() - updateStartTime

            if (updateResult.success) {
                logger.debug(
                    "Successfully updated provider code: providerId={}, mappingId={}, newCode='{}', " +
                            "processingTime={}ms, row={}",
                    providerId, mappingResult.data.id, newCode, updateTime, rowNumber
                )

                performanceLogger.logDatabaseOperation(
                    "provider-code-update",
                    1,
                    updateTime
                )

                true
            } else {
                logger.error(
                    "Failed to update provider code: providerId={}, mappingId={}, newCode='{}', " +
                            "error={}, processingTime={}ms, row={}",
                    providerId, mappingResult.data.id, newCode, updateResult.msg, updateTime, rowNumber
                )
                false
            }
        } catch (e: Exception) {
            val updateTime = System.currentTimeMillis() - updateStartTime
            logger.error(
                "Exception updating provider code: providerId={}, payerId={}, newCode='{}', " +
                        "error={}, processingTime={}ms, row={}",
                providerId, payerId, newCode, e.message, updateTime, rowNumber, e
            )

            performanceLogger.logError(
                ProcessingError(
                    type = ErrorType.UPDATE_FAILED,
                    message = "Exception updating provider code: ${e.message}",
                    providerId = providerId,
                    payerId = payerId,
                    exception = e
                ),
                ErrorContext(
                    rowNumber = rowNumber,
                    additionalInfo = mapOf(
                        "newCode" to newCode,
                        "processingTimeMs" to updateTime,
                        "operation" to "individual_code_update"
                    )
                )
            )

            false
        }
    }

    private suspend fun createAutomaticProviderMapping(
        payerId: Long,
        providerId: Long,
        code: String,
        rowNumber: Int
    ): Boolean {
        val mappingStartTime = System.currentTimeMillis()
        return try {
            logger.info(
                "Creating automatic provider mapping: payerId={}, providerId={}, code={}, row={}",
                payerId, providerId, code, rowNumber
            )

            val mappingDto = PayerProviderMappingDTO(
                payerId = payerId,
                providerId = providerId,
                code = code
            )

            val result = payerService.addProviderMapping(mappingDto)

            val mappingTime = System.currentTimeMillis() - mappingStartTime

            if (result.success) {
                logger.info(
                    "Successfully created automatic provider mapping: payerId={}, providerId={}, code={}, " +
                            "mappingId={}, processingTime={}ms, row={}",
                    payerId, providerId, code, result.data?.id, mappingTime, rowNumber
                )

                performanceLogger.logAutomaticMapping(
                    providerId,
                    payerId,
                    code,
                    MappingContext(
                        rowNumber = rowNumber,
                        additionalInfo = mapOf(
                            "mappingId" to (result.data?.id ?: "unknown"),
                            "processingTimeMs" to mappingTime,
                            "individualOperation" to true
                        )
                    )
                )

                true
            } else {
                logger.error(
                    "Failed to create automatic provider mapping: payerId={}, providerId={}, error={}, " +
                            "processingTime={}ms, row={}",
                    payerId, providerId, result.msg, mappingTime, rowNumber
                )
                false
            }
        } catch (e: Exception) {
            val mappingTime = System.currentTimeMillis() - mappingStartTime
            logger.error(
                "Exception creating automatic provider mapping: payerId={}, providerId={}, error={}, " +
                        "processingTime={}ms, row={}",
                payerId, providerId, e.message, mappingTime, rowNumber, e
            )

            performanceLogger.logError(
                ProcessingError(
                    type = ErrorType.MAPPING_FAILED,
                    message = "Exception creating automatic mapping: ${e.message}",
                    providerId = providerId,
                    payerId = payerId,
                    exception = e
                ),
                ErrorContext(
                    rowNumber = rowNumber,
                    additionalInfo = mapOf(
                        "code" to code,
                        "processingTimeMs" to mappingTime,
                        "operation" to "individual_automatic_mapping"
                    )
                )
            )

            false
        }
    }

    override suspend fun processProviderMappings(
        payerId: Long,
        file: MultipartFile
    ): ProviderMappingSummary {
        return massActionsService.executeWithLogging("provider-bulk-mapping") {
            val monitor = performanceLogger.createPerformanceMonitor("provider-bulk-mapping")

            logger.info(
                "Starting provider bulk mapping for payer: {}, file: {}",
                payerId,
                file.originalFilename
            )

            validationService.validateAndGetPayer(payerId)
            validateUploadedFile(file)

            // parse Excel file for mapping data
            val providerMappings = parseMappingExcelFile(payerId, file)
            logger.info("Parsed {} provider mappings from Excel file", providerMappings.size)

            monitor.logStart(
                providerMappings.size, mapOf(
                    "payerId" to payerId,
                    "fileName" to (file.originalFilename ?: "unknown"),
                    "fileSize" to file.size
                )
            )

            // process mappings with optimized concurrent and batch processing
            val result = processMappingsOptimized(providerMappings, monitor)

            // log final performance metrics with enhanced details
            val endTime = System.currentTimeMillis()
            val processingTime = endTime - System.currentTimeMillis() // will be calculated properly in monitor

            monitor.logCompletion(
                PerformanceMetrics(
                    totalRecords = result.totalRows,
                    processingTimeMs = processingTime,
                    recordsPerSecond = if (processingTime > 0) (result.totalRows.toDouble() / processingTime * 1000) else 0.0,
                    memoryUsageMB = Runtime.getRuntime().let { (it.totalMemory() - it.freeMemory()) / (1024 * 1024) },
                    concurrencyLevel = DEFAULT_CONCURRENCY_LEVEL,
                    successRate = if (result.totalRows > 0) (result.successfulMappings.toDouble() / result.totalRows * 100) else 0.0,
                    breakdown = mapOf(
                        "successfulMappings" to result.successfulMappings.toLong(),
                        "skippedMappings" to result.skippedMappings.toLong(),
                        "errors" to result.errors.size.toLong(),
                        "processingTimeMs" to (result.processingTimeMs ?: 0L),
                        "recordsPerSecond" to (result.recordsPerSecond ?: 0.0).toLong()
                    )
                )
            )

            // log memory metrics after processing
            performanceLogger.logMemoryMetrics()

            result
        }
    }

    private suspend fun parseMappingExcelFile(payerId: Long, file: MultipartFile): List<ProviderBulkMappingDto> {
        logger.info("Starting Excel file parsing for mapping file: {}", file.originalFilename)

        val workbook: Workbook = try {
            excelHelper.parseWorkbookFromFile(file)
        } catch (e: Exception) {
            throw IllegalArgumentException("Failed to read file. File may be corrupted or in wrong format: ${e.message}")
        }

        return try {
            if (workbook.numberOfSheets == 0) {
                throw IllegalArgumentException("File contains no worksheets")
            }

            val sheet = workbook.getSheetAt(0)
            validateMappingExcelColumns(sheet)

            // chunked processing for memory efficiency
            val summary = chunkedFileProcessor.processExcelInChunks(workbook) { rows, metadata ->
                val chunkData = mutableListOf<ProviderBulkMappingDto>()
                val formatter = DataFormatter()
                val columnMapping = getMappingColumnMapping(sheet.getRow(0))

                rows.forEachIndexed { index, row ->
                    try {
                        val rowNumber = metadata.startRowIndex + index + 1 // +1 for Excel row numbering
                        val rowData = parseMappingRowData(row, columnMapping, formatter, rowNumber)

                        if (rowData.providerId != null && rowData.code != null) {
                            chunkData.add(
                                ProviderBulkMappingDto(
                                    providerId = rowData.providerId,
                                    payerId = payerId,
                                    code = rowData.code
                                )
                            )
                        }
                    } catch (e: Exception) {
                        logger.warn("Error parsing mapping row in chunk {}: {}", metadata.chunkNumber, e.message)
                    }
                }

                ChunkResult(
                    chunkNumber = metadata.chunkNumber,
                    processedCount = rows.size,
                    successCount = chunkData.size,
                    errorCount = rows.size - chunkData.size,
                    data = chunkData
                )
            }

            val allMappings = summary.chunkResults.flatMap { it.data }
            logger.info(
                "Mapping parsing completed: {} provider mappings from {} chunks",
                allMappings.size, summary.totalChunks
            )

            allMappings
        } finally {
            workbook.close()
        }
    }

    private fun validateMappingExcelColumns(sheet: org.apache.poi.ss.usermodel.Sheet) {
        if (sheet.physicalNumberOfRows == 0) {
            throw IllegalArgumentException("File is empty")
        }

        val headerRow = sheet.getRow(0) ?: throw IllegalArgumentException("File missing header row")

        val actualColumns = mutableListOf<String>()

        // read actual column headers
        for (i in 0 until headerRow.physicalNumberOfCells) {
            val cell = headerRow.getCell(i)
            if (cell != null) {
                actualColumns.add(cell.stringCellValue.trim())
            }
        }

        if (actualColumns.isEmpty()) {
            throw IllegalArgumentException("File has no column headers")
        }

        val hasProviderId = actualColumns.any {
            it.equals("Provider ID", ignoreCase = true) ||
                    it.equals("Provider ID - required", ignoreCase = true) ||
                    it.startsWith("Provider ID", ignoreCase = true)
        }
        val hasCode = actualColumns.any {
            it.equals("Code", ignoreCase = true) ||
                    it.equals("Code - required", ignoreCase = true) ||
                    it.startsWith("Code", ignoreCase = true)
        }

        if (!hasProviderId) {
            throw IllegalArgumentException("File missing required column: 'Provider ID'")
        }

        if (!hasCode) {
            throw IllegalArgumentException("File missing required column: 'Code'")
        }

        logger.debug("Mapping file column validation passed. Found columns: {}", actualColumns)
    }

    private fun getMappingColumnMapping(headerRow: Row): Map<String, Int> {
        val columnMapping = mutableMapOf<String, Int>()

        for (i in 0 until headerRow.physicalNumberOfCells) {
            val cell = headerRow.getCell(i)
            if (cell != null) {
                val columnName = cell.stringCellValue.trim()
                when {
                    columnName.equals("Provider ID", ignoreCase = true) ||
                            columnName.equals("Provider ID - required", ignoreCase = true) ||
                            columnName.startsWith("Provider ID", ignoreCase = true) -> columnMapping["providerId"] = i

                    columnName.equals("Code", ignoreCase = true) ||
                            columnName.equals("Code - required", ignoreCase = true) ||
                            columnName.startsWith("Code", ignoreCase = true) -> columnMapping["code"] = i
                }
            }
        }

        return columnMapping
    }

    private data class MappingRowData(
        val providerId: Long?,
        val code: String?
    )

    private fun parseMappingRowData(
        row: Row,
        columnMapping: Map<String, Int>,
        formatter: DataFormatter,
        rowNumber: Int
    ): MappingRowData {
        var providerId: Long? = null
        var code: String? = null

        try {
            columnMapping["providerId"]?.let { colIndex ->
                val cell = row.getCell(colIndex)
                if (cell != null) {
                    providerId = when (cell.cellType) {
                        CellType.NUMERIC -> cell.numericCellValue.toLong()
                        CellType.STRING -> cell.stringCellValue.toLongOrNull()
                        else -> null
                    }
                }
            }



            columnMapping["code"]?.let { colIndex ->
                val cell = row.getCell(colIndex)
                if (cell != null) {
                    code = when (cell.cellType) {
                        CellType.STRING -> cell.stringCellValue.takeIf { it.isNotBlank() }
                        CellType.NUMERIC -> formatter.formatCellValue(cell).takeIf { it.isNotBlank() }
                        else -> null
                    }
                }
            }

        } catch (e: Exception) {
            logger.warn("Error parsing individual cells in mapping row {}: {}", rowNumber, e.message)
        }

        return MappingRowData(providerId, code)
    }

    private suspend fun processMappingsOptimized(
        providerMappings: List<ProviderBulkMappingDto>,
        monitor: PerformanceMonitor
    ): ProviderMappingSummary = coroutineScope {

        val startTime = System.currentTimeMillis()
        logger.info("Starting optimized processing of {} provider mappings", providerMappings.size)

        // calculate optimal batch size and concurrency
        val optimalBatchSize = calculateOptimalBatchSize(providerMappings.size)
        val optimalConcurrency = calculateOptimalConcurrency(providerMappings.size)

        logger.info(
            "Using optimized parameters for mappings: batchSize={}, concurrency={}",
            optimalBatchSize,
            optimalConcurrency
        )

        // process mappings using concurrent batch processing
        val batchResults = concurrentProcessor.processBatchesConcurrently(
            items = providerMappings,
            batchSize = optimalBatchSize,
            concurrencyLevel = optimalConcurrency
        ) { batch ->
            processBatchOfMappings(batch, monitor)
        }

        // aggregate all batch results
        val summary = batchResults.aggregate()
        val processingTime = System.currentTimeMillis() - startTime

        // collect detailed results from all batches
        val allResults = batchResults.flatMap { it.metadata["results"] as? List<ProviderMappingResult> ?: emptyList() }

        var successfulMappings = 0
        var skippedMappings = 0
        val errors = mutableListOf<ProviderMappingError>()

        allResults.forEach { result ->
            when {
                result.success -> successfulMappings++
                result.skipped -> skippedMappings++
                else -> result.error?.let { errors.add(it) }
            }
        }

        logger.info(
            "Provider bulk mapping completed - Total: {}, Successful: {}, Skipped: {}, Errors: {}, Time: {}ms, " +
                    "Throughput: {:.2f} rec/sec",
            providerMappings.size, successfulMappings, skippedMappings, errors.size, processingTime,
            if (processingTime > 0) (providerMappings.size.toDouble() / processingTime * 1000) else 0.0
        )

        ProviderMappingSummary(
            totalRows = providerMappings.size,
            successfulMappings = successfulMappings,
            skippedMappings = skippedMappings,
            errors = errors,
            processingTimeMs = processingTime,
            recordsPerSecond = if (processingTime > 0) (providerMappings.size.toDouble() / processingTime * 1000) else 0.0
        )
    }

    @Transactional
    private suspend fun processBatchOfMappings(
        batch: List<ProviderBulkMappingDto>,
        monitor: PerformanceMonitor
    ): BatchResult {
        val batchStartTime = System.currentTimeMillis()
        val results = mutableListOf<ProviderMappingResult>()

        logger.info(
            "Processing batch of {} mappings, operationId={}",
            batch.size, monitor.getOperationId()
        )

        performanceLogger.logDatabaseOperation(
            "batch-provider-mapping-start",
            batch.size,
            0,
            batch.size
        )

        // process batch with comprehensive error handling and partial success
        val mappingPartialResult = bulkOperationErrorHandler.processBatchWithPartialSuccess(
            items = batch.mapIndexed { index, mapping -> mapping to index },
            processor = { (mapping, index) ->
                val context = ProviderMappingContext(
                    providerId = mapping.providerId,
                    payerId = mapping.payerId,
                    rowNumber = index + 2,
                    code = mapping.code,
                    operationId = monitor.getOperationId()
                )

                // validate provider and payer with retry logic
                val providerValidationResult = bulkOperationErrorHandler.handleProviderMappingWithRetry(
                    operation = { validationService.validateAndGetProvider(mapping.providerId) },
                    context = context,
                    maxRetries = 2
                )

                if (providerValidationResult is BulkOperationResult.Failure) {
                    ProviderMappingResult(
                        success = false,
                        skipped = false,
                        error = providerValidationResult.error as ProviderMappingError
                    )
                } else {
                    val payerValidationResult = bulkOperationErrorHandler.handleProviderMappingWithRetry(
                        operation = { validationService.validateAndGetPayer(mapping.payerId) },
                        context = context,
                        maxRetries = 2
                    )

                    if (payerValidationResult is BulkOperationResult.Failure) {
                        ProviderMappingResult(
                            success = false,
                            skipped = false,
                            error = payerValidationResult.error as ProviderMappingError
                        )
                    } else {
                        // check if mapping already exists
                        val existingMappingResult =
                            payerService.findPayerProviderMappingEntity(mapping.payerId, mapping.providerId)
                        if (existingMappingResult.success && existingMappingResult.data != null) {
                            logger.debug(
                                "Provider {} already mapped to payer {} - skipping",
                                mapping.providerId,
                                mapping.payerId
                            )
                            ProviderMappingResult(
                                success = false,
                                skipped = true,
                                error = ProviderMappingError(
                                    rowNumber = index + 2,
                                    providerId = mapping.providerId,
                                    payerId = mapping.payerId,
                                    errorMessage = "Provider already mapped to payer",
                                    errorType = ProviderMappingErrorType.ALREADY_MAPPED
                                )
                            )
                        } else {
                            // create the mapping with retry logic
                            val mappingCreationResult = bulkOperationErrorHandler.handleProviderMappingWithRetry(
                                operation = {
                                    val mappingDto = PayerProviderMappingDTO(
                                        payerId = mapping.payerId,
                                        providerId = mapping.providerId,
                                        code = mapping.code
                                    )
                                    payerService.addProviderMapping(mappingDto)
                                },
                                context = context,
                                maxRetries = 3
                            )

                            when (mappingCreationResult) {
                                is BulkOperationResult.Success -> {
                                    val result = mappingCreationResult.result
                                    if (result.success) {
                                        logger.info(
                                            "Successfully created provider mapping: providerId={}, payerId={}, code={}, mappingId={}",
                                            mapping.providerId, mapping.payerId, mapping.code, result.data?.id
                                        )

                                        // log the mapping creation with full context
                                        performanceLogger.logAutomaticMapping(
                                            mapping.providerId,
                                            mapping.payerId,
                                            mapping.code,
                                            MappingContext(
                                                rowNumber = index + 2,
                                                operationId = monitor.getOperationId(),
                                                additionalInfo = mapOf(
                                                    "mappingId" to (result.data?.id ?: "unknown"),
                                                    "bulkMappingOperation" to true,
                                                    "batchIndex" to index,
                                                    "retryUsed" to true
                                                )
                                            )
                                        )

                                        ProviderMappingResult(
                                            success = true,
                                            skipped = false,
                                            error = null
                                        )
                                    } else {
                                        ProviderMappingResult(
                                            success = false,
                                            skipped = false,
                                            error = ProviderMappingError(
                                                rowNumber = index + 2,
                                                providerId = mapping.providerId,
                                                payerId = mapping.payerId,
                                                errorMessage = "Failed to create mapping: ${result.msg}",
                                                errorType = ProviderMappingErrorType.MAPPING_FAILED
                                            )
                                        )
                                    }
                                }

                                is BulkOperationResult.Failure -> {
                                    ProviderMappingResult(
                                        success = false,
                                        skipped = false,
                                        error = mappingCreationResult.error as ProviderMappingError
                                    )
                                }
                            }
                        }
                    }
                }
            },
            errorContext = "bulk-mapping-creation"
        )

        // convert partial success results to provider mapping results
        results.addAll(mappingPartialResult.successfulResults)

        // handle any processing errors
        mappingPartialResult.errors.forEach { processingError ->
            results.add(
                ProviderMappingResult(
                    success = false,
                    skipped = false,
                    error = ProviderMappingError(
                        rowNumber = 0, // row number will be determined from error details
                        providerId = processingError.providerId,
                        payerId = processingError.payerId,
                        errorMessage = processingError.message,
                        errorType = when (processingError.type) {
                            ErrorType.PROVIDER_NOT_FOUND -> ProviderMappingErrorType.PROVIDER_NOT_FOUND
                            ErrorType.PAYER_NOT_FOUND -> ProviderMappingErrorType.PAYER_NOT_FOUND
                            ErrorType.MAPPING_FAILED -> ProviderMappingErrorType.MAPPING_FAILED
                            ErrorType.DATABASE_ERROR -> ProviderMappingErrorType.DATABASE_ERROR
                            ErrorType.TIMEOUT_ERROR -> ProviderMappingErrorType.TIMEOUT_ERROR
                            else -> ProviderMappingErrorType.SYSTEM_ERROR
                        }
                    )
                )
            )
        }

        val batchTime = System.currentTimeMillis() - batchStartTime
        val successCount = results.count { it.success }
        val errorCount = results.count { !it.success && !it.skipped }
        val skippedCount = results.count { it.skipped }

        // enhanced batch completion logging with detailed metrics
        logger.info(
            "Batch mapping processing completed: batchSize={}, successful={}, failed={}, " +
                    "skipped={}, processingTime={}ms, throughput={:.2f} mappings/sec, operationId={}",
            batch.size, successCount, errorCount, skippedCount, batchTime,
            if (batchTime > 0) (batch.size.toDouble() / batchTime * 1000) else 0.0,
            monitor.getOperationId()
        )

        // log detailed error information for failed mappings
        results.filter { !it.success && !it.skipped }.forEach { result ->
            result.error?.let { error ->
                performanceLogger.logError(
                    ProcessingError(
                        type = when (error.errorType) {
                            ProviderMappingErrorType.PROVIDER_NOT_FOUND -> ErrorType.PROVIDER_NOT_FOUND
                            ProviderMappingErrorType.PAYER_NOT_FOUND -> ErrorType.PAYER_NOT_FOUND
                            ProviderMappingErrorType.MAPPING_FAILED -> ErrorType.MAPPING_FAILED
                            else -> ErrorType.VALIDATION_ERROR
                        },
                        message = error.errorMessage,
                        providerId = error.providerId,
                        payerId = error.payerId
                    ),
                    ErrorContext(
                        rowNumber = error.rowNumber,
                        operationId = monitor.getOperationId(),
                        additionalInfo = mapOf(
                            "batchSize" to batch.size,
                            "errorType" to error.errorType.name,
                            "operation" to "bulk_mapping_batch"
                        )
                    )
                )
            }
        }

        performanceLogger.logDatabaseOperation(
            "batch-provider-mapping-complete",
            batch.size,
            batchTime,
            successCount
        )

        return BatchResult(
            processedCount = batch.size,
            successCount = successCount,
            errorCount = errorCount,
            processingTimeMs = batchTime,
            success = errorCount == 0,
            metadata = mapOf(
                "results" to results,
                "skippedCount" to skippedCount,
                "successfulMappings" to successCount,
                "failedMappings" to errorCount
            )
        )
    }

    private data class ProviderMappingResult(
        val success: Boolean,
        val skipped: Boolean,
        val error: ProviderMappingError?
    )

    private data class ProviderUpdateResult(
        val success: Boolean,
        val codeUpdated: Boolean,
        val nameUpdated: Boolean,
        val automaticMappingCreated: Boolean,
        val error: ProviderUpdateError?
    )

    private sealed class ValidationResult
    private data class ValidationSuccess(val update: ProviderBulkUpdateDto, val index: Int) : ValidationResult()
    private data class ValidationFailure(
        val update: ProviderBulkUpdateDto,
        val index: Int,
        val error: ProviderUpdateError
    ) : ValidationResult()
}