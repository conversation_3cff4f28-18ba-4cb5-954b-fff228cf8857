package net.lctafrica.membership.api.service

import net.lctafrica.membership.api.domain.OnboardBeneficiary
import net.lctafrica.membership.api.domain.OnboardBeneficiaryRepository
import net.lctafrica.membership.api.domain.OnboardPrincipal
import net.lctafrica.membership.api.dtos.OnboardBeneficiaryDTO
import net.lctafrica.membership.api.dtos.OnboardBeneficiaryResponseDTO
import net.lctafrica.membership.api.util.toResponseDTO
import org.springframework.stereotype.Service
import javax.persistence.EntityNotFoundException

@Service
class OnboardBeneficiaryService(private val onboardBeneficiaryRepository: OnboardBeneficiaryRepository) {
    fun createBeneficiary(request: OnboardBeneficiaryDTO, principal: OnboardPrincipal): OnboardBeneficiaryResponseDTO {
        val beneficiary = OnboardBeneficiary(
            firstName = request.firstName,
            middleName = request.middleName,
            surname = request.surname,
            gender = request.gender,
            idNumber = request.idNumber,
            dateOfBirth = request.dateOfBirth,
            mobileNumber = request.mobileNumber,
            principal = principal,
        )
        val savedBeneficiary = onboardBeneficiaryRepository.save(beneficiary)
        return savedBeneficiary.toResponseDTO()
    }

    fun getAllBeneficiaries(): List<OnboardBeneficiaryResponseDTO> {
        val beneficiaries = onboardBeneficiaryRepository.findAll().map { it.toResponseDTO() }
        return beneficiaries
    }

    fun getBeneficiaryById(id: Long): OnboardBeneficiaryResponseDTO {
        val beneficiary = onboardBeneficiaryRepository.findById(id).orElseThrow {
            throw EntityNotFoundException("Beneficiary not found!")
        }
        return beneficiary.toResponseDTO()
    }

    fun getBeneficiariesByPrincipalId(principalId: Long): List<OnboardBeneficiaryResponseDTO> {
        val beneficiaries = onboardBeneficiaryRepository.findByPrincipalId(principalId).map { it.toResponseDTO() }
        return beneficiaries
    }
}
