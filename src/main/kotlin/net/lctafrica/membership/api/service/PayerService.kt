package net.lctafrica.membership.api.service

import com.itextpdf.text.DocumentException
import com.itextpdf.text.PageSize
import com.itextpdf.text.pdf.PdfPCell
import com.itextpdf.text.pdf.PdfPTable
import com.itextpdf.text.pdf.PdfWriter
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.IOException
import java.util.*
import java.util.stream.Collectors
import net.lctafrica.membership.api.config.NotFoundRequestException
import net.lctafrica.membership.api.domain.*
import net.lctafrica.membership.api.dtos.*
import net.lctafrica.membership.api.util.Patterns
import net.lctafrica.membership.api.util.ReportUtils
import net.lctafrica.membership.api.util.Result
import net.lctafrica.membership.api.util.ResultFactory
import net.lctafrica.membership.api.util.addDefaultFormat
import net.sf.jasperreports.engine.JasperCompileManager
import net.sf.jasperreports.engine.JasperExportManager
import net.sf.jasperreports.engine.JasperFillManager
import net.sf.jasperreports.engine.JasperReport
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.springframework.core.io.InputStreamResource
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional


@Service("payerService")
@Transactional
class PayerService(
    private val repo: PayerRepository,
    private val benefitCatalogRepo: BenefitCatalogRepository,
    private val benefitMappingRepo: PayerBenefitMappingRepository,
    private val providerRepo: ProviderRepository,
    private val payerProviderMappingRepo: PayerProviderMappingRepository,
    private val benefitRepository: BenefitRepository,
    private val categoryRepository: CategoryRepository,
    private val payerRegionRepo: PayerRegionRepo,
    private val payerConfigRepository: PayerConfigRepository,
    private val validationService:IValidationService, private val policyRepository: PolicyRepository
) : IPayerService {


    @Transactional(readOnly = true)
    override fun findAll(): Result<MutableList<Payer>> {
        val allPayers = repo.findAll()
        return ResultFactory.getSuccessResult(allPayers)
    }

    @Transactional(readOnly = true)
    override fun findAdministrators(): Result<MutableList<Payer>> {
        val types: List<PayerType> = listOf(
            PayerType.INTERMEDIARY, PayerType.UNDERWRITER,
            PayerType.CORPORATE
        )
        val admins = repo.findAdmins(types)
        return ResultFactory.getSuccessResult(admins);
    }


    @Transactional(readOnly = false, rollbackFor = [Exception::class])
    override fun addPayer(dto: PayerDTO): Result<Payer> {
        var stored = repo.findByNameIgnoreCase(name = dto.name.trim())
        return if (stored.isPresent) {
            ResultFactory.getFailResult<Payer>("Payer ${dto.name} already exists")
        } else {

            val saved = repo.save(
                Payer(
                    name = dto.name.trim().replace(Patterns.doublespace, " "),
                    contact = dto.contact.trim().replace(Patterns.doublespace, " "),
                    email = dto.email?.trim()?.replace(Patterns.doublespace, ""),
                    website = dto.website?.trim()?.replace(Patterns.doublespace, ""),
                    streetAddress = dto.streetAddress?.trim()?.replace(Patterns.doublespace, ""),
                    postalAddress = dto.postalAddress?.trim()?.replace(Patterns.doublespace, ""),
                    logo = dto.logo?.trim()?.replace(Patterns.doublespace, ""),
                    type = dto.type,
                    //plans = mutableListOf(),
                    benefits = mutableListOf()
                )
            )
            ResultFactory.getSuccessResult(saved)
        }
    }

    override fun addPayerConfig(dto: PayerConfigDto): Result<PayerConfig> {
        val payer = validationService.validateAndGetPayer(dto.payerId)
        val configCatalog = validationService.validateAndGetConfig(dto.configId)
        val configOpt = payerConfigRepository.findByPayerAndConfig(payer, configCatalog)
        val config = if(configOpt.isPresent){
            configOpt.get().apply {
                value = dto.value
                if(!dto.description.isNullOrBlank()) {
                    description = dto.description
                }
            }
        }else{
            PayerConfig(
                payer = payer,
                config = configCatalog,
                value = dto.value,
                description = dto.description
            )
        }
        payerConfigRepository.save(config)
        return ResultFactory.getSuccessResult("Config has been successfully added")
    }

    @Transactional(readOnly = true)
    override fun findByType(type: PayerType): Result<MutableList<Payer>> {
        val payers = repo.findByType(type)
        return ResultFactory.getSuccessResult(payers)
    }

    override fun findById(payerId: Long): Result<Payer> {
        val payer = repo.findById(payerId).get();
        return ResultFactory.getSuccessResult(payer)
    }

    override fun findPayersByIds(dto: IdsListDto): Result<List<Payer>> {
        return ResultFactory.getSuccessResult(repo.findByIds(dto.ids))
    }

    override fun findByPolicyAdminId(policyAdminId: Long): Result<List<Payer>> {
       val benefits =  benefitRepository.findByPolicyAdminId(policyAdminId)
       val payerIdsAdmins = benefits.stream().map { it.payer.id }.collect(Collectors.toList())
       val allPayerIds = mutableListOf<Long>()
       allPayerIds.addAll(payerIdsAdmins)
       allPayerIds.add(policyAdminId)
       return ResultFactory.getSuccessResult(repo.findByIds(allPayerIds))
    }


    @Transactional(rollbackFor = [Exception::class])
    override fun addBenefitMapping(dto: PayerBenefitMappingDTO): Result<PayerBenefitMapping> {

        val payer = repo.findById(dto.payerId)
        if (payer.isEmpty) return ResultFactory.getFailResult("No payer with ID ${dto.payerId} was found")
        val benefit = benefitCatalogRepo.findById(dto.benefitCatalogId)
        if (benefit.isEmpty) return ResultFactory.getFailResult("No benefit catalog with ID ${dto.benefitCatalogId} was found")

        val benefitMap = benefitMappingRepo.findByPayerIdAndBenefitId(payerId = dto.payerId,
        benefitCatalogId = dto.benefitCatalogId)

        return if(benefitMap!== null){
            ResultFactory.getFailResult(msg = "Benefit Mapping already exists")
        }else{
            var mapping = PayerBenefitMapping(benefit = benefit.get(), payer = payer.get(), code = dto.code)
            benefitMappingRepo.save(mapping)
            ResultFactory.getSuccessResult(mapping)

        }

    }

    @Transactional(readOnly = true)
    override fun findBenefitMapping(payerId: Long): Result<MutableList<PayerBenefitMapping>> {
        val payer = repo.findById(payerId)
        if (payer.isEmpty) return ResultFactory.getFailResult("No payer with ID $payerId was found")
        val mappings = benefitMappingRepo.findByPayer(payer.get())
        return ResultFactory.getSuccessResult(mappings)
    }

    @Transactional(readOnly = true)
    override fun findBenefitCode(payerId: String, benefitId: String): Result<PayerBenefitCodeMappingDTO?> {

        val payer = repo.findById(java.lang.Long.valueOf(payerId))
        if (payer.isEmpty) return ResultFactory.getFailResult("No payer with ID ${payerId} was found")

        val byBenefitId = benefitRepository.findByBenefitId(benefitId)
        if (byBenefitId.isEmpty) return ResultFactory.getFailResult(
            "No Benefit with ID " +
                    "$benefitId was found"
        )

        val benefitRef = byBenefitId.get().benefitRef.id!!

        val mappings = benefitMappingRepo.findByPayerIdAndBenefitId(
            java.lang.Long.valueOf(payerId),
            java.lang.Long.valueOf(benefitRef)
        )

        return ResultFactory.getSuccessResult(mappings)

    }

    override fun findPayerProviderMapping(payerId: Long, providerId: Long): Result<PayerProviderMap> {
        val payer = repo.findById(payerId)
        if (payer.isEmpty) return ResultFactory.getFailResult("No payer with ID $payerId was found")
        val provider = providerRepo.findById(providerId)
        if (provider.isEmpty) return ResultFactory.getFailResult("No provider with ID $providerId was found")
        val mapping = payerProviderMappingRepo.findByPayerIdAndProviderId(
            payerId,
            providerId
        )

        return if (mapping.isPresent && !mapping.get().code.isNullOrEmpty()) {
            ResultFactory.getSuccessResult(
                PayerProviderMap(payerId = payerId, providerId = providerId, code = mapping.get().code!!)
            )
        } else {
            ResultFactory.getFailResult("No mapping for ${provider.get()} was found for ${payer.get()}")
        }

    }

    override fun findPayerProviderMappingByPayerRegion(payerRegionId: Long): Result<MutableList<PayerProviderMapping>> {
        val payerRegion = payerRegionRepo.findById(payerRegionId)
        if (payerRegion.isEmpty) return ResultFactory.getFailResult("No payer Region with ID $payerRegionId was found")
        val mapping = payerProviderMappingRepo.findByPayerRegionId(payerRegionId)

        return if (mapping.isNotEmpty()) {
            ResultFactory.getSuccessResult(data = mapping
            )
        } else {
            ResultFactory.getFailResult("No mappings for ${payerRegion.get()}")
        }

    }

    override fun findPayerProviderMappingEntity(
        payerId: Long,
        providerId: Long
    ): Result<PayerProviderMapping> {
        val payer = repo.findById(payerId)
        if (payer.isEmpty) return ResultFactory.getFailResult("No payer with ID $payerId was found")
        val provider = providerRepo.findById(providerId)
        if (provider.isEmpty) return ResultFactory.getFailResult("No provider with ID $providerId was found")
        val mapping = payerProviderMappingRepo.findByPayerIdAndProviderId(
            payerId,
            providerId
        )

        return if (mapping.isPresent) {
            ResultFactory.getSuccessResult(data = mapping.get()
            )
        } else {
            ResultFactory.getFailResult("No mapping for ${provider.get()} was found for ${payer.get()}")
        }

    }

    override fun findPayerProviderAndBenefitMappings(providerId: Long, payerId: Long, benefitId: Long, categoryId: Long ): Result<PayerMappings> {
        val provider = payerProviderMappingRepo.findByPayerIdAndProviderId(payerId, providerId)
        val category: Optional<Category> = categoryRepository.findById(categoryId)
        val service = benefitCatalogRepo.findByBenefitId(benefitId)
        val benefit = benefitMappingRepo.findByPayerIdAndBenefitId(payerId, service.serviceId)
        val payer = repo.findById(payerId)
        return if (provider.isEmpty) {
            ResultFactory.getFailResult("No mapping was found for (PROVIDER: $providerId, PAYER: $payerId). Please contact the benefit payer")
        } else {
            val mappings = PayerMappings(
                payerId = payerId,
                payerName = payer.get().name,
                benefitCode = benefit?.code,
                serviceId = service.serviceId,
                serviceGroup = service.serviceGroup,
                providerCode = provider.get().code,
                providerName = provider.get().provider?.name,
                schemeName = category.get().policy.plan.name,
                policyStartDate = category.get().policy.startDate,
                policyEndDate = category.get().policy.endDate
            )
            ResultFactory.getSuccessResult(mappings)
        }

    }

    @Transactional(rollbackFor = [Exception::class])
    override fun addProviderMapping(dto: PayerProviderMappingDTO): Result<PayerProviderMapping> {
        val payer = repo.findById(dto.payerId)
        if (payer.isEmpty) return ResultFactory.getFailResult("No payer with ID ${dto.payerId} was found")
        val provider = providerRepo.findById(dto.providerId)
        if (provider.isEmpty) return ResultFactory.getFailResult("No provider with ID ${dto.providerId} was found")

        return if(payerProviderMappingRepo.findByPayerIdAndProviderId(dto.payerId,dto.providerId).isPresent ){
            ResultFactory.getFailResult(msg = "Payer Provider Mapping already Exists")
        }else{
            val mapping = PayerProviderMapping(payer = payer.get(), provider = provider.get(), code = dto.code)
            payerProviderMappingRepo.save(mapping)
            ResultFactory.getSuccessResult(mapping)
        }


    }

    override fun updatePayerProviderMappingBatch(dto: List<PayerProviderMappingUpdateDTO>): Result<PayerProviderMapping> {
        dto.forEach { mapping ->
            val map = payerProviderMappingRepo.findById(mapping.mappingId).orElseThrow {
                NotFoundRequestException("No mapping found with Id ${mapping.mappingId}")
            }
            mapping.code?.let {
                map.code = mapping.code
            }
            mapping.status?.let {
                map.status = mapping.status
            }
            payerProviderMappingRepo.save(map)
        }
        return ResultFactory.getSuccessResult("Updated successfully")
    }

    override fun findPayerMapping(
        providerId: Long,
        query: String?,
        page: Int,
        size: Int
    ): Result<Page<PayerProviderMapping>> {
        val provider = providerRepo.findById(providerId)
        if(provider.isEmpty)return ResultFactory.getFailResult("No Provider with ID $providerId " +
                "was found")
        val request = PageRequest.of(page - 1, size)
        val mappings = if (query.isNullOrBlank()) {
            payerProviderMappingRepo.findByProviderId(provider.get().id!!, request)
        } else {
            payerProviderMappingRepo.filterProviderPayers(
                providerId = provider.get().id!!,
                status = PayerProviderMapping.PayerProviderMappingStatus.ACTIVE,
                query = query,
                pageable = request
            )
        }
        return if (mappings.hasContent() && mappings.content.isNotEmpty()) {
            ResultFactory.getSuccessResult(
                data = mappings, msg = "Payers " +
                        "Successfully found"
            )
        } else {
            ResultFactory.getFailResult(msg = "No  mapping exists")
        }
    }

    @Transactional(readOnly = true)
    override fun findProviderMapping(payerId: Long, page: Int, size: Int): Result<Page<PayerProviderMapping>> {
        val payer = repo.findById(payerId)
        if (payer.isEmpty) return ResultFactory.getFailResult("No payer with ID $payerId was found")
        val request = PageRequest.of(page - 1, size)
        val mappings = payerProviderMappingRepo.findByPayerId(payer.get().id, request)


        return if (mappings.hasContent() && mappings.content.isNotEmpty()) {

            ResultFactory.getSuccessResult(
                data = mappings, msg = "Mapping " +
                        "Successfully found"
            )
        } else {
            ResultFactory.getFailResult(msg = "No  mapping exists")
        }
    }

    @Transactional(readOnly = true)
    override fun findPayerProviders(payerId: Long, filters: FilterPayerProvidersDto, page: Int, size: Int): Result<Page<Provider>> {
        val pageable = PageRequest.of(page - 1, size)
        val providers = filterPayerProviders(payerId, filters, pageable)
        return ResultFactory.getSuccessResult(data = providers)
    }

    override fun exportPayerProvidersPDF(payerId: Long, filters: FilterPayerProvidersDto): ResponseEntity<InputStreamResource> {
        val document = com.itextpdf.text.Document(PageSize.A4.rotate(), 0f, 0f, 50f, 60f)
        val out = ByteArrayOutputStream()
        try {
            PdfWriter.getInstance(document, out)
            document.open()
            ReportUtils.reportAddLogoAndTitle("PAYER PROVIDERS", document)
            val tableInfo = PdfPTable(2)
            tableInfo.totalWidth = 200f
            tableInfo.setWidths(floatArrayOf(27f, 173f))
            document.add(tableInfo)
            val reportRows = arrayOf(
                "#",
                "LCT Code",
                "Payer Code",
                "Provider Name",
                "Tier",
                "Region",
            )
            val tableRows = PdfPTable(floatArrayOf(1f, 2f, 2f, 5f, 2f, 3f))
            ReportUtils.addReportHeader(reportRows, tableRows)
            val pageable = Pageable.unpaged()
            val providers = payerProviderMappingRepo.filterPayerProviders(
                payerId=payerId,
                status = PayerProviderMapping.PayerProviderMappingStatus.ACTIVE,
                query=filters.query,
                countryId = filters.countryId,
                regionId = filters.regionId,
                providerType = filters.providerType,
                tier = filters.tier,
                mainFacilityId = filters.mainFacilityId,
                pageable = pageable
            )
            var tableCount = 1
            providers.forEach {
                val idCell = PdfPCell()
                tableRows.addCell(idCell.addDefaultFormat("$tableCount"))
                tableRows.addCell(idCell.addDefaultFormat("${it.id}"))
                tableRows.addCell(idCell.addDefaultFormat(it.code))
                tableRows.addCell(idCell.addDefaultFormat(it.provider?.name))
                tableRows.addCell(idCell.addDefaultFormat(it.provider?.tier?.name))
                tableRows.addCell(idCell.addDefaultFormat(it.provider?.region?.name))
                tableCount++
            }
            document.add(tableRows)
            document.close()
        } catch (e: DocumentException) {
            println(e.message)
        }
        val fileName = "Report_Payer_Provider_Mapping_${System.currentTimeMillis()}.pdf"
        val reportData = InputStreamResource(ByteArrayInputStream(out.toByteArray()))
        return ResponseEntity.ok()
            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=$fileName")
            .contentType(MediaType.parseMediaType(MediaType.APPLICATION_PDF_VALUE))
            .body(reportData)
    }

    override fun exportPayerProvidersXLSX(
        payerId: Long,
        filters: FilterPayerProvidersDto
    ): ResponseEntity<ByteArray?>? {
        val pageable = Pageable.unpaged()
        val providers = payerProviderMappingRepo.filterPayerProviders(
            payerId=payerId,
            status = PayerProviderMapping.PayerProviderMappingStatus.ACTIVE,
            query=filters.query,
            countryId = filters.countryId,
            regionId = filters.regionId,
            providerType = filters.providerType,
            tier = filters.tier,
            mainFacilityId = filters.mainFacilityId,
            pageable = pageable
        )
        return exportProvidersXLSX(providers.content)
    }

    fun filterPayerProviders(payerId: Long, filters: FilterPayerProvidersDto, pageable: Pageable): Page<Provider> {
        val providers = providerRepo.searchPayerProviders(
                payerId=payerId,
                query=filters.query,
                countryId = filters.countryId,
                regionId = filters.regionId,
                providerType = filters.providerType,
                mainFacilityId = filters.mainFacilityId,
                tier=filters.tier,
                pageable = pageable
        )
        return providers
    }

    @Transactional(readOnly = true)
    override fun findBenefitMapping(payerId: Long, page: Int, size: Int): Result<Page<PayerBenefitMapping>> {
        val payer = repo.findById(payerId)
        if (payer.isEmpty) return ResultFactory.getFailResult("No payer with ID $payerId was found")
        val request = PageRequest.of(page - 1, size)
        val mappings = benefitMappingRepo.findByPayerId(payer.get().id, request)


        return if (mappings.hasContent() && mappings.content.isNotEmpty()) {

            ResultFactory.getSuccessResult(
                data = mappings, msg = "Mapping " +
                        "Successfully found"
            )
        } else {
            ResultFactory.getFailResult(msg = "No  mapping exists")
        }
    }

    fun exportProvidersPDF(providers: List<Provider>?): ResponseEntity<ByteArray?>? {
        val dataSource = JRBeanCollectionDataSource(providers)

        val input = this.javaClass.getResourceAsStream("/templates/payer-providers.jrxml");
        val jasperReport: JasperReport = JasperCompileManager.compileReport(input)

        val jasperPrint = JasperFillManager.fillReport(jasperReport, null, dataSource)
        val data = JasperExportManager.exportReportToPdf(jasperPrint)

        val headers = HttpHeaders()
        headers.add("Content-Disposition", "inline; filename=Providers.pdf")

        return ResponseEntity.ok().headers(headers).contentType(MediaType.APPLICATION_PDF).body<ByteArray>(data)
    }

    fun exportProvidersXLSX(providers: List<PayerProviderMapping>?): ResponseEntity<ByteArray?> {
        val headers = listOf("LCT Code", "Payer Code", "Provider Name", "Tier", "Region", "Main Facility", "Latitude", "Longitude")

        try {
            XSSFWorkbook().use { workbook ->
                ByteArrayOutputStream().use { out ->
                    val sheet = workbook.createSheet("Providers")

                    val headerRow = sheet.createRow(0)
                    for (idx in headers.indices) {
                        val cell = headerRow.createCell(idx)
                        cell.setCellValue(headers[idx])
                    }

                    providers?.forEachIndexed { index, map ->
                        val row = sheet.createRow(index+1)
                        val values: List<String> = listOf(
                                "${map.id}",
                                "${map.code}",
                                map.provider?.name ?:"",
                                map.provider?.tier.toString(),
                                map.provider?.region?.name ?: "",
                                map.provider?.mainFacility?.name ?: "",
                                "%,.3f".format(map.provider?.latitude),
                                "%,.3f".format(map.provider?.longitude)
                        )

                        for (i in headers.indices) {
                            row.createCell(i).setCellValue(values[i])
                        }
                    }

                    for (idx in headers.indices) {
                        sheet.autoSizeColumn(idx)
                    }

                    workbook.write(out)
                    val filename = "Providers.xlsx"
                    val file = out.toByteArray()

                    return ResponseEntity.ok()
                            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=$filename")
                            .contentType(MediaType.parseMediaType("application/vnd.ms-excel"))
                            .body(file)
                }
            }
        } catch (e: IOException) {
            throw RuntimeException("fail to export data to Excel file: " + e.message)
        }
    }
}
