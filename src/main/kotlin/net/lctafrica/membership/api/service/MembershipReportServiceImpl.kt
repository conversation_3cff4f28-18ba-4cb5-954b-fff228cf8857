package net.lctafrica.membership.api.service

import com.itextpdf.text.BaseColor
import com.itextpdf.text.Document
import com.itextpdf.text.DocumentException
import com.itextpdf.text.Element
import com.itextpdf.text.FontFactory
import com.itextpdf.text.Image
import com.itextpdf.text.PageSize
import com.itextpdf.text.Paragraph
import com.itextpdf.text.Phrase
import com.itextpdf.text.Rectangle
import com.itextpdf.text.html.WebColors
import com.itextpdf.text.pdf.PdfPCell
import com.itextpdf.text.pdf.PdfPTable
import com.itextpdf.text.pdf.PdfWriter
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.IOException
import java.io.OutputStreamWriter
import java.net.URL
import java.time.LocalDate
import java.util.stream.Collectors
import net.lctafrica.membership.api.domain.AuditLog
import net.lctafrica.membership.api.domain.Beneficiary
import net.lctafrica.membership.api.domain.BeneficiaryRepository
import net.lctafrica.membership.api.domain.BeneficiaryType
import net.lctafrica.membership.api.domain.ExportFileType
import net.lctafrica.membership.api.domain.MemberStatus
import net.lctafrica.membership.api.domain.Payer
import net.lctafrica.membership.api.domain.PlanDate
import net.lctafrica.membership.api.domain.PlanType
import net.lctafrica.membership.api.domain.ResolveDataRepository
import net.lctafrica.membership.api.domain.VerificationMode
import net.lctafrica.membership.api.dtos.FilterBeneficiariesDto
import net.lctafrica.membership.api.dtos.ReportType
import net.lctafrica.membership.api.util.AppConstants
import net.lctafrica.membership.api.util.FooterPageEvent
import net.lctafrica.membership.api.util.Result
import net.lctafrica.membership.api.util.ResultFactory
import net.lctafrica.membership.api.util.addDefaultFormat
import net.lctafrica.membership.api.util.dateNow
import net.lctafrica.membership.api.util.dbDateFormat
import net.lctafrica.membership.api.util.displayNullCheck
import net.lctafrica.membership.api.util.formatDate
import net.lctafrica.membership.api.util.getReportHeader
import net.lctafrica.membership.api.util.getReportType
import net.lctafrica.membership.api.util.getReportTypePDF
import org.apache.commons.csv.CSVFormat
import org.apache.commons.csv.CSVPrinter
import org.apache.poi.xssf.usermodel.XSSFSheet
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.springframework.core.io.InputStreamResource
import org.springframework.core.io.Resource
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional


@Service("membershipReportServiceImpl")
class MembershipReportServiceImpl(
    private val beneficiaryRepository: BeneficiaryRepository,
    private val validationService: IValidationService,
    private val profileService: IProfileService,
    private val resolveDataRepository: ResolveDataRepository,
) : IMembershipReportService {

    @Transactional(readOnly = true)
    override suspend fun filterBeneficiaries(
        dto: FilterBeneficiariesDto,
        page: Int,
        size: Int
    ): Result<Page<Beneficiary>> {
        val beneficiaries = runFilter(dto, page, size)
        return ResultFactory.getSuccessResult(beneficiaries)
    }

    @Transactional(readOnly = true)
    override suspend fun exportFilteredBeneficiariesToExcel(
        dto: FilterBeneficiariesDto,
        fileType: ExportFileType,
        page: Int,
        size: Int
    ): ResponseEntity<Resource> {
        val reportType = getReportType(dto)
        val reportRows = getReportHeader(reportType)
        try {
            val beneficiaries = runFilter(dto, page, size, true)
            XSSFWorkbook().use { workbook ->
                ByteArrayOutputStream().use { out ->
                    val sheet = workbook.createSheet("Sheet1")
                    val rowIdx = 0
                    val headerRow = sheet.createRow(rowIdx)
                    for (col in reportRows.indices) {
                        val cell = headerRow.createCell(col)
                        cell.setCellValue(reportRows[col])
                        sheet.autoSizeColumn(col)
                    }
                    when {
                        reportType == ReportType.BIOMETRIC_CAPTURE -> {
                            generateBiometricCaptureReport(sheet, beneficiaries)
                        }
                        reportType == ReportType.MEMBERSHIP_EDITS -> {
                            generateMembershipEditReport(sheet, beneficiaries)
                        }
                        reportType == ReportType.MEMBERSHIP_STATUS -> {
                            generateMembershipStatusReport(sheet, beneficiaries)
                        }
                        reportType == ReportType.CATEGORY_CHANGE -> {
                            generateCategoryChangeReport(sheet, beneficiaries)
                        }
                        else -> {
                            generateMembershipReport(sheet, beneficiaries)
                        }
                    }
                    workbook.write(out)
                    val filename = "Membership_Report_${System.currentTimeMillis()}.${fileType.name.lowercase()}"
                    val file = InputStreamResource(ByteArrayInputStream(out.toByteArray()))

                    return ResponseEntity.ok()
                        .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=$filename")
                        .contentType(MediaType.parseMediaType("application/vnd.ms-excel"))
                        .body(file)
                }
            }
        } catch (e: IOException) {
            throw RuntimeException("fail to export data to file type $fileType: " + e.message)
        }
    }

    private fun generateMembershipReport(sheet: XSSFSheet, beneficiaries: Page<Beneficiary>) {
        var rowIdx = 1
        for (beneficiary in beneficiaries) {
            var deactivateDate: LocalDate? = null
            if(beneficiary.status == MemberStatus.DEACTIVATED) {
                beneficiary.changeLog?.let { logs ->
                    val deactivated = logs.stream().filter { it.data?.contains(MemberStatus.DEACTIVATED.name) == true }
                        .findFirst().orElse(null)
                    deactivateDate = deactivated?.time?.toLocalDate()
                }
            }
            val row = sheet.createRow(rowIdx++)
            var columnIndex = 0
            row.createCell(columnIndex++).setCellValue(beneficiary?.memberNumber)
            row.createCell(columnIndex++).setCellValue(beneficiary?.name)
            row.createCell(columnIndex++).setCellValue(beneficiary?.beneficiaryType?.name)
            row.createCell(columnIndex++).setCellValue(beneficiary?.gender?.name)
            row.createCell(columnIndex++).setCellValue("")
            row.createCell(columnIndex++).setCellValue(formatDate(beneficiary.dob))
            row.createCell(columnIndex++).setCellValue(displayNullCheck(beneficiary.email))
            row.createCell(columnIndex++).setCellValue(displayNullCheck(beneficiary.familySize))
            row.createCell(columnIndex++).setCellValue(displayNullCheck(beneficiary.phoneNumber))
            row.createCell(columnIndex++).setCellValue(beneficiary?.category?.name)
            row.createCell(columnIndex++).setCellValue(beneficiary?.category?.description)
            row.createCell(columnIndex++).setCellValue(beneficiary?.category?.policy?.plan?.name)
            row.createCell(columnIndex++).setCellValue(formatDate(beneficiary?.joinDate))
            row.createCell(columnIndex++).setCellValue(formatDate(deactivateDate))
            row.createCell(columnIndex++).setCellValue(beneficiary.idNumber)
            row.createCell(columnIndex++).setCellValue(beneficiary?.status?.name)

            var policyStartDate = beneficiary?.category?.policy?.startDate
            var policyEndDate = beneficiary?.category?.policy?.endDate
            if((beneficiary.category.policy.plan.type == PlanType.RETAIL || beneficiary.category.policy.plan.planDate == PlanDate.MEMBER_JOIN_DATE) && beneficiary.joinDate !=null){
                policyStartDate = beneficiary.joinDate!!
                policyEndDate = policyStartDate.plusYears(1).minusDays(1)
            }

            row.createCell(columnIndex++).setCellValue(formatDate(policyStartDate))
            row.createCell(columnIndex++).setCellValue(formatDate(policyEndDate))
            row.createCell(columnIndex++).setCellValue(formatDate(beneficiary.biometricCaptureDate?.toLocalDate()))
            row.createCell(columnIndex++).setCellValue(beneficiary?.biometricStatus?.name)
        }
    }

    private fun generateBiometricCaptureReport(sheet: XSSFSheet, beneficiaries: Page<Beneficiary>) {
        var rowIdx = 1
        for (beneficiary in beneficiaries) {
            val row = sheet.createRow(rowIdx++)
            var columnIndex = 0
            row.createCell(columnIndex++).setCellValue(beneficiary?.memberNumber)
            row.createCell(columnIndex++).setCellValue(beneficiary?.name)
            row.createCell(columnIndex++).setCellValue(beneficiary?.beneficiaryType?.name)
            row.createCell(columnIndex++).setCellValue(beneficiary?.gender?.name)
            row.createCell(columnIndex++).setCellValue("")
            row.createCell(columnIndex++).setCellValue(formatDate(beneficiary.dob))
            row.createCell(columnIndex++).setCellValue(beneficiary?.category?.policy?.plan?.name)
            row.createCell(columnIndex++).setCellValue(formatDate(beneficiary.biometricCaptureDate?.toLocalDate()))
            row.createCell(columnIndex++).setCellValue(beneficiary?.biometricStatus?.name)
        }
    }

    private fun generateMembershipEditReport(sheet: XSSFSheet, beneficiaries: Page<Beneficiary>) {
        var rowIdx = 1
        for (beneficiary in beneficiaries) {
            beneficiary.changeLog?.let {
                if(it.isNotEmpty()){
                    val changeLog = it[0]
                    val row = sheet.createRow(rowIdx++)
                    var columnIndex = 0
                    row.createCell(columnIndex++).setCellValue(beneficiary?.memberNumber)
                    row.createCell(columnIndex++).setCellValue(beneficiary?.otherNumber)
                    row.createCell(columnIndex++).setCellValue(beneficiary?.name)
                    row.createCell(columnIndex++).setCellValue(beneficiary?.beneficiaryType?.name)
                    row.createCell(columnIndex++).setCellValue(beneficiary?.gender?.name)
                    row.createCell(columnIndex++).setCellValue(formatDate(beneficiary.dob))
                    row.createCell(columnIndex++).setCellValue(displayNullCheck(beneficiary.phoneNumber))
                    row.createCell(columnIndex++).setCellValue(beneficiary?.category?.policy?.plan?.name)
                    row.createCell(columnIndex++).setCellValue(beneficiary?.status?.name)
                    row.createCell(columnIndex++).setCellValue(changeLog.changeLogType.name)
                    row.createCell(columnIndex++).setCellValue(formatDate(changeLog.time))
                    row.createCell(columnIndex++).setCellValue(changeLog.reason)
                    row.createCell(columnIndex++).setCellValue(changeLog.user)
                }
            }
        }
    }

    private fun generateMembershipStatusReport(sheet: XSSFSheet, beneficiaries: Page<Beneficiary>) {
        var rowIdx = 1
        for (beneficiary in beneficiaries) {

            var deactivateDate: LocalDate? = null
            if(beneficiary.status == MemberStatus.DEACTIVATED) {
                beneficiary.changeLog?.let { logs ->
                    val deactivated = logs.stream().filter { it.data?.contains(MemberStatus.DEACTIVATED.name) == true }
                        .findFirst().orElse(null)
                    deactivateDate = deactivated?.time?.toLocalDate()
                }
            }

            val row = sheet.createRow(rowIdx++)
            var columnIndex = 0
            row.createCell(columnIndex++).setCellValue(beneficiary?.memberNumber)
            row.createCell(columnIndex++).setCellValue(beneficiary?.name)
            row.createCell(columnIndex++).setCellValue(beneficiary?.beneficiaryType?.name)
            row.createCell(columnIndex++).setCellValue(beneficiary?.gender?.name)
            row.createCell(columnIndex++).setCellValue(formatDate(beneficiary.dob))
            row.createCell(columnIndex++).setCellValue(beneficiary?.category?.name)
            row.createCell(columnIndex++).setCellValue(beneficiary?.category?.policy?.plan?.name)
            row.createCell(columnIndex++).setCellValue(formatDate(beneficiary?.joinDate))
            row.createCell(columnIndex++).setCellValue(formatDate(deactivateDate))
            row.createCell(columnIndex++).setCellValue(beneficiary?.status?.name)

            var policyStartDate = beneficiary?.category?.policy?.startDate
            var policyEndDate = beneficiary?.category?.policy?.endDate
            if((beneficiary.category.policy.plan.type == PlanType.RETAIL || beneficiary.category.policy.plan.planDate == PlanDate.MEMBER_JOIN_DATE) && beneficiary.joinDate !=null){
                policyStartDate = beneficiary.joinDate!!
                policyEndDate = policyStartDate.plusYears(1).minusDays(1)
            }

            row.createCell(columnIndex++).setCellValue(formatDate(policyStartDate))
            row.createCell(columnIndex++).setCellValue(formatDate(policyEndDate))
            var changeLog:AuditLog? = null
            beneficiary.changeLog?.let {
                if(it.isNotEmpty()){
                    changeLog = it[0]
                }
            }
            row.createCell(columnIndex++).setCellValue(formatDate(changeLog?.time))
            row.createCell(columnIndex++).setCellValue(changeLog?.reason)
            row.createCell(columnIndex++).setCellValue(changeLog?.user)
        }
    }

    private fun generateCategoryChangeReport(sheet: XSSFSheet, beneficiaries: Page<Beneficiary>) {
        var rowIdx = 1
        for (beneficiary in beneficiaries) {
            val row = sheet.createRow(rowIdx++)
            var columnIndex = 0
            row.createCell(columnIndex++).setCellValue(beneficiary?.memberNumber)
            row.createCell(columnIndex++).setCellValue(beneficiary?.name)
            row.createCell(columnIndex++).setCellValue(beneficiary?.beneficiaryType?.name)
            row.createCell(columnIndex++).setCellValue(beneficiary?.gender?.name)
            row.createCell(columnIndex++).setCellValue("")
            row.createCell(columnIndex++).setCellValue(formatDate(beneficiary.dob))
            row.createCell(columnIndex++).setCellValue("")
            row.createCell(columnIndex++).setCellValue(displayNullCheck(beneficiary.email))
            row.createCell(columnIndex++).setCellValue(displayNullCheck(beneficiary.familySize))
            row.createCell(columnIndex++).setCellValue(displayNullCheck(beneficiary.phoneNumber))
            row.createCell(columnIndex++).setCellValue(beneficiary?.category?.policy?.plan?.name)
            var changeLog:AuditLog? = null
            beneficiary.changeLog?.let {
                if(it.isNotEmpty()){
                    changeLog = it[0]
                }
            }
            row.createCell(columnIndex++).setCellValue(changeLog?.changeLogType?.name)
            row.createCell(columnIndex++).setCellValue("")
            row.createCell(columnIndex++).setCellValue(beneficiary.category.name)
            row.createCell(columnIndex++).setCellValue(changeLog?.user)
        }
    }

    @Transactional(readOnly = true)
    override suspend fun exportFilteredBeneficiariesToCSV(
        dto: FilterBeneficiariesDto,
        fileType: ExportFileType,
        page: Int,
        size: Int
    ): ResponseEntity<Resource> {
        val reportType = getReportType(dto)
        val reportRows = getReportHeader(reportType)
        val output = ByteArrayOutputStream()
        output.use { outputStream ->
            val printer = CSVPrinter(OutputStreamWriter(outputStream), CSVFormat.DEFAULT)
            printer.printRecord(reportRows.asIterable())
            val beneficiaries = runFilter(dto, page, size, true)
            beneficiaries.forEach { beneficiary ->
                when {
                    reportType == ReportType.BIOMETRIC_CAPTURE -> {
                        printer.printRecord(
                            beneficiary.memberNumber, beneficiary.name, beneficiary.beneficiaryType.name, beneficiary.gender.name,
                            "", formatDate(beneficiary.dob),beneficiary?.category?.policy?.plan?.name,
                            formatDate(beneficiary.biometricCaptureDate?.toLocalDate()),
                            displayNullCheck(beneficiary.biometricStatus?.name)
                        )
                    }
                    reportType == ReportType.MEMBERSHIP_EDITS -> {
                        beneficiary.changeLog?.let {
                            if(it.isNotEmpty()){
                                val changeLog = it[0]
                                printer.printRecord(
                                    beneficiary.memberNumber,
                                    beneficiary.otherNumber,
                                    beneficiary.name,
                                    beneficiary.beneficiaryType.name,
                                    beneficiary.gender.name,
                                    formatDate(beneficiary.dob),
                                    beneficiary.phoneNumber,
                                    beneficiary?.category?.policy?.plan?.name,
                                    beneficiary.status?.name,
                                    changeLog.changeLogType.name,
                                    formatDate(changeLog.time),
                                    changeLog.reason,displayNullCheck(changeLog.user)
                                )
                            }
                        }
                    }
                    reportType == ReportType.MEMBERSHIP_STATUS -> {
                        var deactivateDate: LocalDate? = null
                        var changeLog:AuditLog? = null
                        beneficiary.changeLog?.let { logs ->
                            if(logs.isNotEmpty()){
                                changeLog = logs[0]
                            }
                            if(beneficiary.status == MemberStatus.DEACTIVATED) {
                                val deactivated =
                                    logs.stream().filter { it.data?.contains(MemberStatus.DEACTIVATED.name) == true }
                                        .findFirst().orElse(null)
                                deactivateDate = deactivated?.time?.toLocalDate()
                            }
                        }
                        var policyStartDate = beneficiary?.category?.policy?.startDate
                        var policyEndDate = beneficiary?.category?.policy?.endDate
                        if((beneficiary.category.policy.plan.type == PlanType.RETAIL || beneficiary.category.policy.plan.planDate == PlanDate.MEMBER_JOIN_DATE) && beneficiary.joinDate !=null){
                            policyStartDate = beneficiary.joinDate!!
                            policyEndDate = policyStartDate.plusYears(1).minusDays(1)
                        }
                        printer.printRecord(
                            beneficiary.memberNumber, beneficiary.name, beneficiary.beneficiaryType.name, beneficiary.gender.name,
                            formatDate(beneficiary.dob), beneficiary?.category?.name,beneficiary?.category?.policy?.plan?.name,
                            formatDate(beneficiary.joinDate), formatDate(deactivateDate),beneficiary.status?.name,
                            formatDate(policyStartDate),formatDate(policyEndDate),
                            formatDate(changeLog?.time), changeLog?.reason,displayNullCheck(changeLog?.user)
                        )
                    }
                    reportType == ReportType.CATEGORY_CHANGE -> {
                        var changeLog:AuditLog? = null
                        beneficiary.changeLog?.let {
                            if(it.isNotEmpty()){
                                changeLog = it[0]
                            }
                        }
                        printer.printRecord(
                            beneficiary.memberNumber, beneficiary.name, beneficiary.beneficiaryType.name, beneficiary.gender.name,
                            "", formatDate(beneficiary.dob),"",beneficiary.email,displayNullCheck(beneficiary.familySize),
                            beneficiary.phoneNumber, beneficiary?.category?.policy?.plan?.name,
                            changeLog?.changeLogType?.name,"", beneficiary.category.name,displayNullCheck(changeLog?.user))
                    }
                    else -> {
                        var policyStartDate = beneficiary?.category?.policy?.startDate
                        var policyEndDate = beneficiary?.category?.policy?.endDate
                        if((beneficiary.category.policy.plan.type == PlanType.RETAIL || beneficiary.category.policy.plan.planDate == PlanDate.MEMBER_JOIN_DATE) && beneficiary.joinDate !=null){
                            policyStartDate = beneficiary.joinDate!!
                            policyEndDate = policyStartDate.plusYears(1).minusDays(1)
                        }
                        var deactivateDate: LocalDate? = null
                        if(beneficiary.status == MemberStatus.DEACTIVATED) {
                            beneficiary.changeLog?.let { logs ->
                                val deactivated = logs.stream().filter { it.data?.contains(MemberStatus.DEACTIVATED.name) == true }
                                        .findFirst().orElse(null)
                                deactivateDate = deactivated?.time?.toLocalDate()
                            }
                        }
                        printer.printRecord(
                            beneficiary.memberNumber, beneficiary.name, beneficiary.beneficiaryType.name, beneficiary.gender.name,
                            "", formatDate(beneficiary.dob),beneficiary.email,displayNullCheck(beneficiary.familySize),
                            beneficiary.phoneNumber, beneficiary?.category?.name,
                            beneficiary?.category?.description,beneficiary?.category?.policy?.plan?.name,
                            formatDate(beneficiary.joinDate), formatDate(deactivateDate), beneficiary.idNumber,beneficiary.status?.name,
                            formatDate(policyStartDate),formatDate(policyEndDate),
                            formatDate(beneficiary.biometricCaptureDate?.toLocalDate()), displayNullCheck(beneficiary.biometricStatus?.name)
                        )
                    }
                }
            }
            printer.flush()
            val file = InputStreamResource(ByteArrayInputStream(output.toByteArray()))
            val filename = "Membership_Report_${System.currentTimeMillis()}.${fileType.name.lowercase()}"
            return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=$filename")
                .body(file)
        }
    }

    @Transactional(readOnly = true)
    override suspend fun exportFilteredBeneficiariesToPDF(
        dto: FilterBeneficiariesDto,
        fileType: ExportFileType,
        page: Int,
        size: Int
    ) : ResponseEntity<InputStreamResource> {
        val beneficiaries = runFilter(dto, page, size, true)
        val bis: ByteArrayInputStream = generateMembershipReportPDF(dto, beneficiaries)
        val fileName = "Membership_Report_${System.currentTimeMillis()}.pdf"
        val headers = HttpHeaders()
        headers.add("Content-Disposition", "inline; filename=$fileName");
        return ResponseEntity.ok()
            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=$fileName")
            .contentType(MediaType.parseMediaType(MediaType.APPLICATION_PDF_VALUE))
            .body(InputStreamResource(bis))
    }

    private fun generateMembershipReportPDF(dto: FilterBeneficiariesDto, beneficiaries: Page<Beneficiary>): ByteArrayInputStream{
        val document = Document(PageSize.A4.rotate(), 0f, 0f, 50f, 60f)
        val out = ByteArrayOutputStream()
        try {
            val reportType = getReportTypePDF(dto)
            val writer = PdfWriter.getInstance(document, out)
            val event = FooterPageEvent()
            writer.pageEvent = event
            document.open()
            reportAddLogoAndTitle("Membership Report", document)

            val filterFromDate = dbDateFormat(dto.fromDate)
            val filterToDate = dbDateFormat(dto.toDate)

            val tableInfo = PdfPTable(2)
            tableInfo.totalWidth = 200f
            tableInfo.setWidths(floatArrayOf(27f, 173f))

            dto.payerId?.let {
                val payer = validationService.validateAndGetPayer(it)
                addTableInfo(tableInfo, "Payer Name:", WebColors.getRGBColor("#61758A"))
                addTableInfo(tableInfo, payer.name, WebColors.getRGBColor("#304254"))
            }
            addTableInfo(tableInfo, "Statement Date:", WebColors.getRGBColor("#61758A"))
            addTableInfo(tableInfo, dateNow(), WebColors.getRGBColor("#304254"))

            if (filterFromDate !=null && filterToDate !=null) {
                addTableInfo(tableInfo, "Statement Period:", WebColors.getRGBColor("#61758A"))
                addTableInfo(tableInfo, "$filterFromDate - $filterToDate", WebColors.getRGBColor("#304254"))
            }
            document.add(Paragraph("\n"))
            document.add(tableInfo)
            document.add(Paragraph("\n"))

            val table:PdfPTable
            val reportRows:Array<String>
            if(reportType ==ReportType.MEMBERSHIP_EDITS){
                table = PdfPTable(floatArrayOf(0.5f, 2f, 2f, 3f, 2f, 2f, 3f))
                reportRows = arrayOf(
                    "MEMBER NUMBER", "OTHER NUMBER", "MEMBER NAME", "RELATIONSHIP", "EDIT DATE", "REASON"
                )
            }else if(reportType ==ReportType.BIOMETRIC_CAPTURE){
                table = PdfPTable(floatArrayOf(0.5f, 2f, 3f, 2f, 1.5f, 3f))
                reportRows = arrayOf(
                    "MEMBER NUMBER", "MEMBER NAME", "RELATIONSHIP","BIO CAPTURE DATE", "STATUS"
                )
            }else{
                table = PdfPTable(10)
                table.totalWidth = 800f
                table.setWidths(floatArrayOf(15f, 25f,40f,40f,25f,25f,20f,25f,20f,20f))
                reportRows = arrayOf(
                    "MEMBER NUMBER", "MEMBER NAME", "SCHEME NAME","CATEGORY", "RELATIONSHIP","GENDER",
                    "PHONE NUMBER","JOIN DATE", "DOB"
                )
            }
            table.defaultCell.border = Rectangle.NO_BORDER
            addReportHeader(arrayOf("#"),table)
            addReportHeader(reportRows,table)

            var tableCount = 1
            beneficiaries.forEach { beneficiary ->
                if(reportType ==ReportType.MEMBERSHIP_EDITS){
                    beneficiary.changeLog?.let {
                        if(it.isNotEmpty()){
                            val changeLog = it[0]
                            val idCell = PdfPCell()
                            table.addCell(idCell.addDefaultFormat("$tableCount"))
                            table.addCell(idCell.addDefaultFormat(beneficiary.memberNumber))
                            table.addCell(idCell.addDefaultFormat(beneficiary.otherNumber))
                            table.addCell(idCell.addDefaultFormat(beneficiary.name))
                            table.addCell(idCell.addDefaultFormat(beneficiary.beneficiaryType.name))
                            table.addCell(idCell.addDefaultFormat(formatDate(changeLog.time)))
                            table.addCell(idCell.addDefaultFormat(changeLog.reason))
                            tableCount++
                        }
                    }
                }else if(reportType ==ReportType.BIOMETRIC_CAPTURE){
                    val idCell = PdfPCell()
                    table.addCell(idCell.addDefaultFormat("$tableCount"))
                    table.addCell(idCell.addDefaultFormat(beneficiary.memberNumber))
                    table.addCell(idCell.addDefaultFormat(beneficiary.name))
                    table.addCell(idCell.addDefaultFormat(beneficiary.beneficiaryType.name))
                    table.addCell(idCell.addDefaultFormat(formatDate(beneficiary?.biometricCaptureDate?.toLocalDate())))
                    table.addCell(idCell.addDefaultFormat(beneficiary.biometricStatus?.name))
                    tableCount++
                }else{
                    val idCell = PdfPCell()
                    table.addCell(idCell.addDefaultFormat("$tableCount"))
                    table.addCell(idCell.addDefaultFormat(beneficiary.memberNumber))
                    table.addCell(idCell.addDefaultFormat(beneficiary.name))
                    table.addCell(idCell.addDefaultFormat(beneficiary.category.policy.plan.name))
                    table.addCell(idCell.addDefaultFormat(beneficiary.category.name))
                    table.addCell(idCell.addDefaultFormat(beneficiary.beneficiaryType.name))
                    table.addCell(idCell.addDefaultFormat(beneficiary.gender.name))
                    table.addCell(idCell.addDefaultFormat(beneficiary.phoneNumber))
                    table.addCell(idCell.addDefaultFormat(formatDate(beneficiary?.joinDate)))
                    table.addCell(idCell.addDefaultFormat(formatDate(beneficiary?.dob)))
                    tableCount++
                }
            }
            document.add(table)
            document.close()
        } catch (e: DocumentException) {
            println(e.message)
        }
        return ByteArrayInputStream(out.toByteArray())
    }

    private fun addReportHeader(reportRows: Array<String>, table: PdfPTable) {
        reportRows.forEach { headerTitle ->
            val header = PdfPCell()
            val headFont = FontFactory.getFont(FontFactory.HELVETICA, 8f, WebColors.getRGBColor("#304254"))
            header.backgroundColor = BaseColor.WHITE
            header.horizontalAlignment = Element.ALIGN_LEFT
            header.paddingTop = 5f
            header.paddingBottom = 5f
            header.borderWidth = 1f
            header.phrase = Phrase(headerTitle, headFont)
            header.border = Rectangle.NO_BORDER
            table.addCell(header)
        }
    }

    private fun reportAddLogoAndTitle(reportTitle: String, document: Document) {
        val tableHeader = PdfPTable(floatArrayOf(2f, 5f))
        val image: Image = Image.getInstance(URL(AppConstants.LCT_LOGO_URL))
        image.scaleToFit(200f, 150f)
        val imgCell = PdfPCell(image, true)
        imgCell.border = Rectangle.NO_BORDER
        tableHeader.addCell(imgCell)

        val title = PdfPCell()
        val font = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 15f, WebColors.getRGBColor("#1A2853"))
        title.phrase = Phrase(reportTitle.uppercase(), font)
        title.horizontalAlignment = Element.ALIGN_LEFT
        title.verticalAlignment = Element.ALIGN_CENTER
        title.border = Rectangle.NO_BORDER
        title.paddingTop = 30f
        title.paddingLeft = 30f
        tableHeader.addCell(title)
        document.add(tableHeader)
    }

    private fun addTableInfo(tableInfo: PdfPTable, info: String, color: BaseColor) {
        val infoCell = PdfPCell()
        val font = FontFactory.getFont(FontFactory.HELVETICA, 10f, color)
        infoCell.phrase = Phrase(info, font)
        infoCell.horizontalAlignment = Element.ALIGN_LEFT
        infoCell.verticalAlignment = Element.ALIGN_CENTER
        infoCell.border = Rectangle.NO_BORDER
        infoCell.paddingBottom = 5f
        tableInfo.addCell(infoCell)
    }

    private suspend fun runFilter(
        dto: FilterBeneficiariesDto,
        page: Int,
        size: Int,
        exportAll: Boolean = false
    ): Page<Beneficiary> {
        var payer: Payer? = null
        dto.payerId?.let {
            payer = validationService.validateAndGetPayer(it)
        }

        val pageable : Pageable = if(exportAll){
            Pageable.unpaged()
        }else{
            PageRequest.of(page, size)
        }

        var canUseBiometrics: Boolean? = null
        if (dto.verificationMode == VerificationMode.OTP) {
            canUseBiometrics = false
        }
        val filterFromDate = dbDateFormat(dto.fromDate)
        val filterToDate = dbDateFormat(dto.toDate)

        val beneficiaries = when {
            dto.changeLogType != null -> {
                val list = beneficiaryRepository.filterBeneficiaries(
                    payer = payer,
                    policyIds = dto.policyIds,
                    idNumbers = dto.idNumbers,
                    phoneNumbers = dto.phoneNumbers,
                    planIds = dto.planIds,
                    categoryIds = dto.categoryIds,
                    statuses = dto.statuses,
                    beneficiaryTypes = dto.beneficiaryTypes,
                    verificationMode = canUseBiometrics,
                    changeLogType = dto.changeLogType,
                    fromDate = filterFromDate,
                    toDate = filterToDate,
                    pageable = pageable
                )
                list.forEach {
                    val changeLogs = it.changeLog ?: emptyList()
                    val logs = changeLogs.stream().filter { log -> log.changeLogType == dto.changeLogType }
                        .sorted(Comparator.comparing(AuditLog::time, Comparator.nullsLast(Comparator.reverseOrder())))
                        .collect(Collectors.toList())
                    it.changeLog = logs
                }
                list
            }

            else -> {
                beneficiaryRepository.filterBeneficiaries(
                    payer = payer,
                    policyIds = dto.policyIds,
                    idNumbers = dto.idNumbers,
                    phoneNumbers = dto.phoneNumbers,
                    planIds = dto.planIds,
                    categoryIds = dto.categoryIds,
                    statuses = dto.statuses,
                    beneficiaryTypes = dto.beneficiaryTypes,
                    verificationMode = canUseBiometrics,
                    fromDate = filterFromDate,
                    toDate = filterToDate,
                    pageable = pageable
                )
            }
        }

        val beneficiaryIds = beneficiaries.stream().map {
            it.id
        }.collect(Collectors.toList())

        val memberNumbers = beneficiaries.stream().map {
            it.memberNumber
        }.collect(Collectors.toList())

        val bios = resolveDataRepository.findBiometricsForBeneficiaries(beneficiaryIds, memberNumbers)

        beneficiaries.forEach { beneficiary ->
            val benId = beneficiary.id
            val memberNo = beneficiary.memberNumber
            val benType = beneficiary.beneficiaryType
            if (benType == BeneficiaryType.PRINCIPAL) {
                val numDependants = beneficiary.dependants.size
                beneficiary.familySize = if(numDependants > 0) "M+$numDependants" else "M"
            }

            val fingerPrint =
                bios.stream().filter { (it.beneficiaryId == benId || it.memberNumber == memberNo) }.findFirst()
                    .orElse(null)
            if (fingerPrint != null) {
                beneficiary.biometricStatus = fingerPrint.biometricStatus
                beneficiary.biometricCaptureDate = fingerPrint.captureDate
            }

            beneficiary.joinDate?.let {
                val plan = beneficiary.category.policy.plan
                if(plan.type == PlanType.RETAIL || plan.planDate == PlanDate.MEMBER_JOIN_DATE){
                    beneficiary.category.policy.startDate = it
                    beneficiary.category.policy.endDate = it.plusYears(1).minusDays(1)
                }
            }
        }
        return beneficiaries
    }

}