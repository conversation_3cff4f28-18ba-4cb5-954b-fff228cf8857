package net.lctafrica.membership.api.service

import net.lctafrica.membership.api.domain.OnboardBenefitType
import net.lctafrica.membership.api.domain.OnboardDependantBeneficiaryType
import net.lctafrica.membership.api.domain.OnboardPrincipal
import net.lctafrica.membership.api.domain.OnboardReportType
import net.lctafrica.membership.api.dtos.OnboardBenefitDTO
import net.lctafrica.membership.api.dtos.OnboardDependantResponseDTO
import net.lctafrica.membership.api.dtos.OnboardMemberRequestDTO
import net.lctafrica.membership.api.dtos.OnboardPrincipalResponseDTO
import net.lctafrica.membership.api.util.OnboardReportUser
import net.lctafrica.membership.api.util.calculateOnboardInpatientLimit
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.springframework.core.io.InputStreamResource
import org.springframework.core.io.Resource
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Service
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.IOException
import java.math.BigDecimal
import java.time.LocalDate
import javax.transaction.Transactional

@Service
class OnboardService(
    private val onboardPrincipalService: OnboardPrincipalService,
    private val onboardDependantService: OnboardDependantService,
    private val onboardBeneficiaryService: OnboardBeneficiaryService,
    private val onboardDocumentService: OnboardDocumentService,
    private val onboardBenefitService: OnboardBenefitService,
    private val onboardAgentService: OnboardAgentService,
) {
    var fileHeaderItems = arrayOf(
        "EMPLOYEE NAME",
        "GROWER NUMBER",
        "MEMBER NUMBER",
        "ID NUMBER",
        "GENDER",
        "TOWN",
        "COUNTY",
        "RELATIONSHIP",
        "MARITAL STATUS",
        "MOBILE NUMBER",
        "DATE OF BIRTH",
        "AGE",
        "OP CATEGORY",
        "OP AMOUNT",
        "IP CATEGORY",
        "IP AMOUNT",
        "PROVIDER NAME",
        "AGENT NAME",
        "AGENT MOBILE NUMBER",
        "AGENT NUMBER",
        "AGENT FACTORY",
        "FAMILY SIZE",
        "STAFF NAME",
        "STAFF USERNAME",
        "BENEFICIARY NAME",
        "BENEFICIARY ID",
        "BENEFICIARY PHONE",
        "APPLICATION DATE",
        "APPLICATION FORM CONTEXT ID",
        "APPLICATION FORM INFO ID",
        "HOSPITAL FORM CONTEXT ID",
        "HOSPITAL FORM INFO ID",
        "STUDENT ID CONTEXT ID",
        "STUDENT ID INFO ID",

    )

    @Transactional
    fun onboardPrincipal(onboardMemberRequestDTO: OnboardMemberRequestDTO) {
        val agent = if (onboardMemberRequestDTO.principal.agent.id != null) {
            onboardAgentService.findOnboardAgentById(onboardMemberRequestDTO.principal.agent.id)
        } else {
            val agentRequest = onboardMemberRequestDTO.principal.agent
            val res = onboardAgentService.createAgent(agentRequest)
            res[true]
        }

        val savedPrincipal = onboardPrincipalService.createPrincipal(onboardMemberRequestDTO.principal, agent!!)
        val onboardPrincipal = OnboardPrincipal(
            id = savedPrincipal.id,
            firstName = onboardMemberRequestDTO.principal.firstName,
            middleName = onboardMemberRequestDTO.principal.middleName,
            surname = onboardMemberRequestDTO.principal.surname,
            dateOfBirth = savedPrincipal.dateOfBirth,
            idNumber = savedPrincipal.idNumber,
            mobileNumber = savedPrincipal.mobileNumber,
            gender = savedPrincipal.gender,
            maritalStatus = savedPrincipal.maritalStatus,
            town = savedPrincipal.town,
            county = savedPrincipal.county,
            hospital = savedPrincipal.hospital,
            inpatientPremium = savedPrincipal.inpatientPremium,
            outpatientPremium = savedPrincipal.outpatientPremium,
            growerNumber = savedPrincipal.growerNumber,
            paymentMode = savedPrincipal.paymentMode,
            category = savedPrincipal.category,
            agent = agent,
            createdByName = savedPrincipal.createdByName,
            createdByUsername = savedPrincipal.createdByUsername,
            applicationDate = savedPrincipal.applicationDate,
        )
        onboardMemberRequestDTO.dependants?.size?.let { dependantCount ->
            if (dependantCount > 0) {
                val sortedDependants = onboardMemberRequestDTO.dependants.sortedBy { dependantRequest ->
                    when (dependantRequest.beneficiaryType) {
                        OnboardDependantBeneficiaryType.SPOUSE -> 0
                        OnboardDependantBeneficiaryType.CHILD -> 1
                    }
                }

                sortedDependants.forEach { dependantRequest ->
                    onboardDependantService.createDependant(
                        dependantRequest, onboardPrincipal
                    )
                }

                val totalMonthlyOutpatientAmount =
                    onboardPrincipal.outpatientPremium.plus(
                        onboardMemberRequestDTO.dependants
                            .sumOf { it.outpatientPremium })

                val totalMonthlyInpatientAmount =
                    onboardPrincipal.inpatientPremium.plus(
                        onboardMemberRequestDTO.dependants
                            .sumOf { it.inpatientPremium })

                val inpatientLimit = calculateOnboardInpatientLimit(savedPrincipal.inpatientPremium)
                val outpatientLimit = BigDecimal(20_000)

                val benefits = listOf(
                    OnboardBenefitDTO(
                        benefitType = OnboardBenefitType.INPATIENT,
                        benefitLimit = inpatientLimit,
                        benefitMonthlyAmount = totalMonthlyInpatientAmount
                    ), OnboardBenefitDTO(
                        benefitType = OnboardBenefitType.OUTPATIENT,
                        benefitLimit = outpatientLimit,
                        benefitMonthlyAmount = totalMonthlyOutpatientAmount,
                    )
                )

                benefits.forEach { benefit ->
                    onboardBenefitService.createBenefit(onboardPrincipal, benefit)
                }
            } else {
                val totalMonthlyOutpatientAmount =
                    onboardPrincipal.outpatientPremium

                val totalMonthlyInpatientAmount =
                    onboardPrincipal.inpatientPremium

                val inpatientLimit = calculateOnboardInpatientLimit(savedPrincipal.inpatientPremium)
                val outpatientLimit = BigDecimal(20_000)

                val benefits = listOf(
                    OnboardBenefitDTO(
                        benefitType = OnboardBenefitType.INPATIENT,
                        benefitLimit = inpatientLimit,
                        benefitMonthlyAmount = totalMonthlyInpatientAmount
                    ), OnboardBenefitDTO(
                        benefitType = OnboardBenefitType.OUTPATIENT,
                        benefitLimit = outpatientLimit,
                        benefitMonthlyAmount = totalMonthlyOutpatientAmount,
                    )
                )

                benefits.forEach { benefit ->
                    onboardBenefitService.createBenefit(onboardPrincipal, benefit)
                }
            }
        }

        onboardMemberRequestDTO.beneficiaries.forEach { beneficiaryRequest ->
            onboardBeneficiaryService.createBeneficiary(
                beneficiaryRequest, onboardPrincipal
            )
        }

        onboardMemberRequestDTO.documents?.forEach { documentRequest ->
            onboardDocumentService.createOnboardDocument(onboardPrincipal, documentRequest)
        }
    }

    fun getAllMembers(fromDate: LocalDate?, toDate: LocalDate?): MutableList<Any> {
        val page = 0
        val size = Int.MAX_VALUE
        val principals = onboardPrincipalService.filterPrincipals(fromDate, toDate, page, size)
        val dependants = onboardDependantService.filterDependants(fromDate, toDate, page, size)
        val allMembers = mutableListOf<Any>()
        principals.forEach { principal ->
            allMembers.add(OnboardReportUser.OnboardPrincipalType(principal).principal)
        }
        dependants.forEach { dependant ->
            allMembers.add(OnboardReportUser.OnboardDependantType(dependant).dependant)
        }
        return allMembers
    }

    fun filterAllMembers(
        fromDate: LocalDate?,
        toDate: LocalDate?,
        page: Int = 0,
        size: Int = 10,
        beneficiaryType: OnboardReportType? = OnboardReportType.PRINCIPALS_AND_DEPENDANTS
    ): Page<Any> {
        val largePageSize = Int.MAX_VALUE
        val principals =
            if (beneficiaryType == OnboardReportType.PRINCIPALS_AND_DEPENDANTS
                || beneficiaryType == OnboardReportType.PRINCIPALS
            ) {
                onboardPrincipalService.filterPrincipals(
                    fromDate = fromDate, toDate = toDate, size = largePageSize
                ).content
            } else {
                emptyList()
            }

        val dependants = if (
            beneficiaryType == OnboardReportType.PRINCIPALS_AND_DEPENDANTS
            || beneficiaryType == OnboardReportType.DEPENDANTS
        ) {
            onboardDependantService.filterDependants(
                fromDate = fromDate, toDate = toDate, size = largePageSize
            ).content
        } else {
            emptyList()
        }

        val allMembers = mutableListOf<Any>()
        principals.forEach { principal ->
            allMembers.add(OnboardReportUser.OnboardPrincipalType(principal).principal)
        }
        dependants.forEach { dependant ->
            allMembers.add(OnboardReportUser.OnboardDependantType(dependant).dependant)
        }

        val startIndex = page * size
        val endIndex = minOf(startIndex + size, allMembers.size)
        if (startIndex >= allMembers.size) {
            return Page.empty(PageRequest.of(page, size))
        }

        val paginatedContent = allMembers.subList(startIndex, endIndex)
        return PageImpl(paginatedContent, PageRequest.of(page, size), allMembers.size.toLong())
    }

    fun exportMembersData(
        fromDate: LocalDate?,
        toDate: LocalDate?,
        reportType: OnboardReportType?,
    ): ResponseEntity<Resource>? {
        var members = mutableListOf<Any>()
        val size = Int.MAX_VALUE
        val page = 0

        when (reportType) {
            OnboardReportType.DEPENDANTS -> {
                members.clear()
                onboardDependantService.filterDependants(fromDate, toDate, page, size)
                    .forEach { dependant ->
                        members.add(dependant)
                    }
            }

            OnboardReportType.PRINCIPALS -> {
                members.clear()
                onboardPrincipalService.filterPrincipals(fromDate, toDate, page, size)
                    .forEach { principal ->
                        members.add(principal)
                    }
            }

            else -> {
                members = getAllMembers(fromDate, toDate)
            }
        }

        if (members.isNotEmpty()) {
            try {
                XSSFWorkbook().use { workbook ->
                    ByteArrayOutputStream().use { out ->
                        val sheet = workbook.createSheet("Beneficiaries")
                        val boldFont = workbook.createFont()
                        boldFont.bold = true
                        val boldStyle = workbook.createCellStyle()
                        boldStyle.setFont(boldFont)

                        val headerRow = sheet.createRow(0)
                        for (col in fileHeaderItems.indices) {
                            val cell = headerRow.createCell(col)
                            cell.setCellValue(fileHeaderItems[col])
                            cell.cellStyle = boldStyle
                            sheet.autoSizeColumn(col)
                        }

                        var rowIndex = 1
                        for (member in members) {
                            val row = sheet.createRow(rowIndex++)
                            when (member) {
                                is OnboardPrincipalResponseDTO -> {
                                    row.createCell(0).setCellValue(member.name.uppercase())
                                    row.createCell(1).setCellValue(
                                        member.growerNumber.uppercase()
                                    )
                                    row.createCell(2).setCellValue(
                                        member.memberNumber.uppercase()
                                    )
                                    row.createCell(3).setCellValue(
                                        member.idNumber.uppercase()
                                    )
                                    row.createCell(4).setCellValue(member.gender.toString().uppercase())
                                    row.createCell(5).setCellValue(member.town.uppercase())
                                    row.createCell(6)
                                        .setCellValue(member.county.uppercase())
                                    row.createCell(7).setCellValue(member.beneficiaryType.toString().uppercase())
                                    row.createCell(8).setCellValue(member.maritalStatus.toString().uppercase())
                                    row.createCell(9).setCellValue(member.mobileNumber.uppercase())
                                    row.createCell(10).setCellValue(member.dateOfBirth.toString().uppercase())
                                    row.createCell(11).setCellValue(member.age.toString().uppercase())
                                    row.createCell(12).setCellValue(member.outpatientCategory.uppercase())
                                    row.createCell(13).setCellValue(member.outpatientPremium.toString().uppercase())
                                    row.createCell(14).setCellValue(member.inpatientCategory.uppercase())
                                    row.createCell(15).setCellValue(member.inpatientPremium.toString().uppercase())
                                    row.createCell(16).setCellValue(member.hospital.uppercase())
                                    row.createCell(17).setCellValue(member.agentName.uppercase())
                                    row.createCell(18).setCellValue(member.agentMobileNumber.uppercase())
                                    row.createCell(19).setCellValue(member.agentNumber.uppercase())
                                    row.createCell(20).setCellValue(member.agentFactory.uppercase())
                                    row.createCell(21).setCellValue(member.familySize.toString().uppercase())
                                    row.createCell(22).setCellValue(member.createdByName.uppercase())
                                    row.createCell(23).setCellValue(member.createdByUsername.uppercase())
                                    row.createCell(24).setCellValue(member.beneficiaryName.uppercase())
                                    row.createCell(25).setCellValue(member.beneficiaryIdNumber.uppercase())
                                    row.createCell(26).setCellValue(member.beneficiaryMobileNumber.uppercase())
                                    row.createCell(27).setCellValue(member.applicationDate.uppercase())
                                    row.createCell(28).setCellValue(member.applicationFormContextIds?.joinToString(","))
                                    row.createCell(29).setCellValue(member.applicationFormInfoIds?.joinToString(","))
                                    row.createCell(30).setCellValue(member.hospitalFormContextIds?.joinToString(","))
                                    row.createCell(31).setCellValue(member.hospitalFormInfoIds?.joinToString(","))
                                    row.createCell(32).setCellValue(member.studentIdContextIds?.joinToString(","))
                                    row.createCell(33).setCellValue(member.studentIdInfoIds?.joinToString(","))
                                }

                                is OnboardDependantResponseDTO -> {
                                    row.createCell(0).setCellValue(member.name.uppercase())
                                    row.createCell(1).setCellValue(
                                        member.principalGrowerNumber.uppercase()
                                    )
                                    row.createCell(2).setCellValue(
                                        member.memberNumber.uppercase()
                                    )
                                    row.createCell(3).setCellValue(
                                        member.idNumber?.uppercase()
                                    )
                                    row.createCell(4).setCellValue(member.gender.toString().uppercase())
                                    row.createCell(5).setCellValue(member.town.uppercase())
                                    row.createCell(6)
                                        .setCellValue(member.county.uppercase())
                                    row.createCell(7).setCellValue(member.beneficiaryType.toString().uppercase())
                                    row.createCell(8).setCellValue(member.maritalStatus.toString().uppercase())
                                    row.createCell(9).setCellValue(member.mobileNumber?.uppercase())
                                    row.createCell(10).setCellValue(member.dateOfBirth.toString().uppercase())
                                    row.createCell(11).setCellValue(member.age.toString().uppercase())
                                    row.createCell(12).setCellValue(member.outpatientCategory.uppercase())
                                    row.createCell(13).setCellValue(member.outpatientPremium.toString().uppercase())
                                    row.createCell(14).setCellValue(member.inpatientCategory.uppercase())
                                    row.createCell(15).setCellValue(member.inpatientPremium.toString().uppercase())
                                    row.createCell(16).setCellValue(member.hospital.uppercase())
                                    row.createCell(17).setCellValue(member.agentName.uppercase())
                                    row.createCell(18).setCellValue(member.agentMobileNumber.uppercase())
                                    row.createCell(19).setCellValue(member.agentNumber.uppercase())
                                    row.createCell(20).setCellValue(member.agentFactory.uppercase())
                                    row.createCell(21).setCellValue(member.familySize.toString().uppercase())
                                    row.createCell(22).setCellValue(member.createdByName.uppercase())
                                    row.createCell(23).setCellValue(member.createdByUsername.uppercase())
                                    row.createCell(24).setCellValue("")
                                    row.createCell(25).setCellValue("")
                                    row.createCell(26).setCellValue("")
                                    row.createCell(27).setCellValue(member.applicationDate.uppercase())
                                    row.createCell(28).setCellValue("")
                                    row.createCell(29).setCellValue("")
                                    row.createCell(30).setCellValue("")
                                    row.createCell(31).setCellValue("")
                                    row.createCell(32).setCellValue("")
                                    row.createCell(33).setCellValue("")
                                }
                            }
                        }
                        workbook.write(out)
                        val reportName = when (reportType) {
                            OnboardReportType.DEPENDANTS -> "Dependants"
                            OnboardReportType.PRINCIPALS -> "Principals"
                            else -> "PrincipalsAndDependants"
                        }
                        val filename = "$reportName.xlsx"
                        val file = InputStreamResource(ByteArrayInputStream(out.toByteArray()))

                        return ResponseEntity.ok()
                            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=$filename")
                            .contentType(MediaType.parseMediaType("application/vnd.ms-excel")).body(file)
                    }
                }
            } catch (e: IOException) {
                throw RuntimeException("Error exporting data to Excel: " + e.message)
            }
        }
        return ResponseEntity.ok().build()
    }
}
