package net.lctafrica.membership.api.service

import java.util.stream.Collectors
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import net.lctafrica.membership.api.config.BadRequestException
import net.lctafrica.membership.api.domain.AllocationStatus
import net.lctafrica.membership.api.domain.CheckerStatus
import net.lctafrica.membership.api.domain.DeviceAccessory
import net.lctafrica.membership.api.domain.DeviceAccessoryRepo
import net.lctafrica.membership.api.domain.DeviceAllocation
import net.lctafrica.membership.api.domain.DeviceAllocationRepo
import net.lctafrica.membership.api.domain.DeviceCatalog
import net.lctafrica.membership.api.domain.DeviceCatalogRepo
import net.lctafrica.membership.api.domain.DeviceDocument
import net.lctafrica.membership.api.domain.DeviceDocumentRepo
import net.lctafrica.membership.api.domain.DeviceImei
import net.lctafrica.membership.api.domain.DeviceImeiRepo
import net.lctafrica.membership.api.domain.DeviceManagementAuditLog
import net.lctafrica.membership.api.domain.DeviceManagementAuditLogRepo
import net.lctafrica.membership.api.domain.DeviceModel
import net.lctafrica.membership.api.domain.DeviceModelRepo
import net.lctafrica.membership.api.domain.DeviceSim
import net.lctafrica.membership.api.domain.DeviceSimRepo
import net.lctafrica.membership.api.domain.DeviceStatus
import net.lctafrica.membership.api.domain.DeviceUploadError
import net.lctafrica.membership.api.domain.DeviceUploadErrorRepo
import net.lctafrica.membership.api.domain.LogAction
import net.lctafrica.membership.api.domain.ProviderRepository
import net.lctafrica.membership.api.domain.SimStatus
import net.lctafrica.membership.api.domain.SimUploadError
import net.lctafrica.membership.api.domain.SimUploadErrorRepo
import net.lctafrica.membership.api.dtos.Accessory
import net.lctafrica.membership.api.dtos.AddDeviceModel
import net.lctafrica.membership.api.dtos.AddSIM
import net.lctafrica.membership.api.dtos.AllocateDeviceAndSIMRequestDTO
import net.lctafrica.membership.api.dtos.AllocateDeviceRequestDTO
import net.lctafrica.membership.api.dtos.AllocateProviderDeviceSimAndAccessory
import net.lctafrica.membership.api.dtos.AllocationStatusUpdate
import net.lctafrica.membership.api.dtos.DeviceAllocationResponseDto
import net.lctafrica.membership.api.dtos.DeviceCatalogResponseDto
import net.lctafrica.membership.api.dtos.DeviceRegistrationBatchInput
import net.lctafrica.membership.api.dtos.DeviceRegistrationRequestDTO
import net.lctafrica.membership.api.dtos.DeviceStatusUpdate
import net.lctafrica.membership.api.dtos.ProviderDeviceAllocation
import net.lctafrica.membership.api.dtos.SIMStatusUpdate
import net.lctafrica.membership.api.dtos.SimBatchInput
import net.lctafrica.membership.api.dtos.SupportingDocumentRequest
import net.lctafrica.membership.api.util.AppConstants
import net.lctafrica.membership.api.util.AppConstants.ADDED_SUCCESSFULLY
import net.lctafrica.membership.api.util.AppConstants.ALLOCATED_SUCCESSFULLY
import net.lctafrica.membership.api.util.AppConstants.REGISTERED_SUCCESSFULLY
import net.lctafrica.membership.api.util.AppConstants.UPLOADED_SUCCESSFULLY
import net.lctafrica.membership.api.util.ReadExcelFile
import net.lctafrica.membership.api.util.Result
import net.lctafrica.membership.api.util.ResultFactory
import net.lctafrica.membership.api.util.toJsonString
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.multipart.MultipartFile


@Service("deviceManagementService")
class DeviceManagementService(
    private val deviceCatalogRepo: DeviceCatalogRepo,
    private val deviceImeiRepo: DeviceImeiRepo,
    private val deviceSimRepo: DeviceSimRepo,
    private val deviceModelRepo: DeviceModelRepo,
    private val deviceAccessoryRepo: DeviceAccessoryRepo,
    private val deviceAllocationRepo: DeviceAllocationRepo,
    private val providerRepo: ProviderRepository,
    private val deviceUploadErrorRepo: DeviceUploadErrorRepo,
    private val simUploadErrorRepo: SimUploadErrorRepo,
    private val deviceManagementAuditLogRepo: DeviceManagementAuditLogRepo,
    private val deviceDocumentRepo: DeviceDocumentRepo,
    private val validationService: IValidationService,
    private val notificationService: INotificationService,
    private val xlFileReader: ReadExcelFile
) : IDeviceManagementService {
    override suspend fun searchDevice(
        deviceId: String?,
        imei: String?,
        deviceStatus: DeviceStatus?,
        page: Int,
        size: Int
    ): Result<Page<DeviceCatalogResponseDto>> {
        val pageable = PageRequest.of(page - 1, size)
        val devices = deviceCatalogRepo.searchCatalog(deviceId, deviceStatus, imei, pageable)
        
        if (!imei.isNullOrBlank()) {
            devices.forEach { device ->
                val imeis =
                    device.imei.stream().filter { (it.imei ?: "").contains(imei) }.collect(Collectors.toList())
                device.imei = imeis
            }
        }

        val devicesWithAllocations =
            devices.map { device ->
                val allocations = device.allocations
                    .sortedByDescending { allocation -> allocation.id }
                    .map { allocation ->
                        DeviceAllocationResponseDto(
                            id = allocation.id,
                            provider = allocation.provider,
                            deviceSim = allocation.deviceSim,
                            enableGeofence = allocation.enableGeofence,
                            radius = allocation.radius,
                            status = allocation.status,
                            createdBy = allocation.createdBy,
                            allocatedBy = allocation.allocatedBy,
                            checkedBy = allocation.checkedBy,
                            checkerStatus = allocation.checkerStatus,
                            note = allocation.note,
                            dateCreated = allocation.dateCreated,
                            accessories = allocation.accessories,
                            documents = allocation.documents,
                            logs = allocation.logs
                        )
                    }

                DeviceCatalogResponseDto(
                        id = device.id,
                        deviceId = device.deviceId,
                        status = device.status,
                        description = device.description,
                        deviceModel = device.deviceModel,
                        registeredOn = device.registeredOn,
                        imei = device.imei,
                        allocations = allocations,
                        accessories = device.accessories,
                        logs = device.logs
                )
            }

        return ResultFactory.getSuccessResult(devicesWithAllocations)
    }

    override suspend fun searchSIM(
        simNumber: String?,
        simStatus: SimStatus?,
        page: Int,
        size: Int
    ): Result<Page<DeviceSim>> {
        val pageable = PageRequest.of(page - 1, size)
        return ResultFactory.getSuccessResult(deviceSimRepo.searchSIM(simNumber, simStatus, pageable))
    }

    override suspend fun getDeviceModels(page: Int, size: Int): Result<Page<DeviceModel>> {
        val pageable = PageRequest.of(page - 1, size)
        return ResultFactory.getSuccessResult(deviceModelRepo.findAll(pageable))
    }

    override suspend fun addDeviceModel(dto: AddDeviceModel): Result<Boolean> {
        val model = DeviceModel(
            model = dto.model,
            description = dto.description
        )
        deviceModelRepo.save(model)
        deviceAuditTrail(
            DeviceManagementAuditLog(
                model = model,
                actionByUser = dto.addedBy,
                action = LogAction.ADDED_MODEL,
                payload = dto.toJsonString()
            )
        )
        return ResultFactory.getSuccessResult(ADDED_SUCCESSFULLY)
    }

    override suspend fun addSIM(dto: AddSIM): Result<Boolean> {
        val sim = DeviceSim(
            simNumber = dto.simNumber
        )
        deviceSimRepo.save(sim)
        deviceAuditTrail(
            DeviceManagementAuditLog(
                sim = sim,
                actionByUser = dto.registeredByUser,
                action = LogAction.ADDED_SIM,
                payload = dto.toJsonString()
            )
        )
        return ResultFactory.getSuccessResult(ADDED_SUCCESSFULLY)
    }

    override suspend fun getAllSim(page: Int, size: Int): Result<Page<DeviceSim>> {
        val pageable = PageRequest.of(page - 1, size)
        return ResultFactory.getSuccessResult(deviceSimRepo.findAll(pageable))
    }

    override suspend fun getAllDevicesInCatalog(page: Int, size: Int): Result<Page<DeviceCatalogResponseDto>> {
        val pageable = PageRequest.of(page - 1, size)
        val catalogs = deviceCatalogRepo.findAllDeviceCatalog(pageable)


        val data = catalogs.map { catalog ->
            catalog.allocations = catalog.allocations.stream().sorted(
                Comparator.comparing(
                    DeviceAllocation::id, Comparator.nullsLast(Comparator.reverseOrder())
                )
            ).collect(Collectors.toList())
            val allocations = catalog.allocations.stream().map {
                DeviceAllocationResponseDto(
                    id = it.id,
                    provider = it.provider,
                    deviceSim = it.deviceSim,
                    enableGeofence = it.enableGeofence,
                    radius = it.radius,
                    status = it.status,
                    createdBy = it.createdBy,
                    allocatedBy = it.allocatedBy,
                    checkedBy = it.checkedBy,
                    checkerStatus = it.checkerStatus,
                    note = it.note,
                    dateCreated = it.dateCreated,
                    accessories = it.accessories,
                    documents = it.documents,
                    logs = it.logs
                )
            }.collect(Collectors.toList())

            DeviceCatalogResponseDto(
                id = catalog.id,
                deviceId = catalog.deviceId,
                status = catalog.status,
                description = catalog.description,
                deviceModel = catalog.deviceModel,
                registeredOn = catalog.registeredOn,
                imei = catalog.imei,
                allocations = allocations,
                accessories = catalog.accessories,
                logs = catalog.logs
            )
        }
        return ResultFactory.getSuccessResult(data)
    }

    override suspend fun getUnallocatedDevices(page: Int, size: Int): Result<Page<DeviceCatalog>> {
        val pageable = PageRequest.of(page - 1, size)
        return ResultFactory.getSuccessResult(
            deviceCatalogRepo.getUnallocatedDevices(
                AllocationStatus.ALLOCATED,
                pageable
            )
        )
    }

    @Transactional
    override suspend fun registerDevice(dto: DeviceRegistrationRequestDTO): Result<Boolean> {
        val deviceCheck = deviceCatalogRepo.findByDeviceId(
            deviceId = dto.deviceId
        )
        if (deviceCheck.isPresent) {
            throw BadRequestException("Device is already registered")
        }

        val model = deviceModelRepo.findById(dto.modelId)
        if (!model.isPresent) {
            throw BadRequestException("Invalid device model")
        }

        val catalog = DeviceCatalog(
            deviceId = dto.deviceId,
            description = dto.description,
            deviceModel = model.get()
        )
        deviceCatalogRepo.save(catalog)
        dto.imei?.let { imei ->
            imei.forEach {
                val imeiCheck = deviceImeiRepo.findByImei(it)
                if (imeiCheck.isPresent) {
                    throw BadRequestException("Imei is already registered")
                }
                deviceImeiRepo.save(DeviceImei(imei = it, deviceCatalog = catalog))
            }
        }

        dto.accessories?.forEach { devAccessory ->
            deviceAccessoryRepo.save(
                DeviceAccessory(
                    accessory = devAccessory.accessory,
                    note = devAccessory.note,
                    deviceCatalog = catalog
                )
            )
        }

        deviceAuditTrail(
            DeviceManagementAuditLog(
                catalog = catalog,
                actionByUser = dto.registeredByUser,
                action = LogAction.ADDED_DEVICE,
                payload = dto.toJsonString()
            )
        )
        return ResultFactory.getSuccessResult(REGISTERED_SUCCESSFULLY)
    }

    @Transactional
    override suspend fun allocateBatch(dto: AllocateDeviceRequestDTO): Result<Boolean> {
        val provider = validationService.validateAndGetProvider(dto.providerId)
        dto.deviceAndAccessories.forEach { deviceAndAccessory ->
            val catalog = validationService.validateAndGetDeviceCatalog(deviceAndAccessory.deviceCatalogId)
            val sim = validationService.validateAndGetDeviceSIM(deviceAndAccessory.simId)
            val allocationCheck = deviceAllocationRepo.findByDeviceCatalogAndStatus(
                deviceCatalog = catalog,
                AllocationStatus.ALLOCATED
            )
            if (allocationCheck.isPresent) {
                throw BadRequestException("Device is already allocated")
            }
            if (deviceAndAccessory.enableGeofence && (deviceAndAccessory.radius == null || deviceAndAccessory.radius!! <= 0.00)) {
                throw BadRequestException("Geofence radius (in metres) is required and should be greater than zero")
            }
            val allocation = DeviceAllocation(
                deviceCatalog = catalog,
                provider = provider,
                deviceSim = sim,
                createdBy = dto.createdBy,
                allocatedBy = dto.createdBy,
                checkerStatus = CheckerStatus.PENDING,
                status = AllocationStatus.ALLOCATED,
                enableGeofence = deviceAndAccessory.enableGeofence,
                radius = deviceAndAccessory.radius
            )
            deviceAllocationRepo.save(allocation)
            catalog.status = DeviceStatus.ALLOCATED
            deviceCatalogRepo.save(catalog)
            sim.status = SimStatus.ASSIGNED
            deviceSimRepo.save(sim)
            deviceAndAccessory.accessories?.forEach { devAccessory ->
                deviceAccessoryRepo.save(
                    DeviceAccessory(
                        accessory = devAccessory.accessory,
                        note = devAccessory.note,
                        deviceAllocation = allocation
                    )
                )
            }
            deviceAuditTrail(
                DeviceManagementAuditLog(
                    allocation = allocation,
                    actionByUser = dto.createdBy,
                    action = LogAction.CREATED_AND_ALLOCATED_DEVICE,
                    payload = deviceAndAccessory.toJsonString(), note = "Batch"
                )
            )
        }
        return ResultFactory.getSuccessResult(ALLOCATED_SUCCESSFULLY)
    }

    override suspend fun allocateDevice(dto: AllocateProviderDeviceSimAndAccessory): Result<Boolean> {
        val provider = validationService.validateAndGetProvider(dto.providerId)
        val catalog = validationService.validateAndGetDeviceCatalog(dto.deviceCatalogId)
        val sim = validationService.validateAndGetDeviceSIM(dto.simId)
        val allocationCheck = deviceAllocationRepo.findByDeviceCatalogAndStatus(
            deviceCatalog = catalog,
            AllocationStatus.ALLOCATED
        )
        if (allocationCheck.isPresent) {
            throw BadRequestException("Device is already allocated")
        }
        if (dto.enableGeofence && (dto.radius == null || dto.radius!! <= 0.00)) {
            throw BadRequestException("Geofence radius (in metres) is required and should be greater than zero")
        }
        val allocation = DeviceAllocation(
            deviceCatalog = catalog,
            provider = provider,
            deviceSim = sim,
            status = AllocationStatus.ALLOCATED,
            enableGeofence = dto.enableGeofence,
            radius = dto.radius,
            createdBy = dto.createdBy,
            allocatedBy = dto.createdBy,
            checkerStatus = CheckerStatus.PENDING
        )
        deviceAllocationRepo.save(allocation)
        catalog.status = DeviceStatus.ALLOCATED
        deviceCatalogRepo.save(catalog)
        sim.status = SimStatus.ASSIGNED
        deviceSimRepo.save(sim)
        dto.accessories?.forEach { devAccessory ->
            deviceAccessoryRepo.save(
                DeviceAccessory(
                    accessory = devAccessory.accessory,
                    note = devAccessory.note,
                    deviceAllocation = allocation
                )
            )
        }
        deviceAuditTrail(
            DeviceManagementAuditLog(
                allocation = allocation,
                actionByUser = dto.createdBy,
                action = LogAction.CREATED_AND_ALLOCATED_DEVICE,
                payload = dto.toJsonString()
            )
        )

        return ResultFactory.getSuccessResult(ALLOCATED_SUCCESSFULLY)
    }

    @Transactional(readOnly = true)
    override suspend fun getAllocation(
        providerId: Long,
        deviceId: String
    ): Result<DeviceAllocation> {
        val deviceOpt = deviceCatalogRepo.findByDeviceId(deviceId)
        if (deviceOpt.isPresent) {
            val deviceCatalog = deviceOpt.get()
            if (deviceCatalog.status == DeviceStatus.AVAILABLE) {
                val deviceAllocation =
                    deviceAllocationRepo.findByDeviceCatalogAndStatus(deviceCatalog, AllocationStatus.ALLOCATED)
                return if (deviceAllocation.isPresent) {
                    val allocation = deviceAllocation.get()
                    val cashierProviderOpt = providerRepo.findById(providerId)
                    if (!cashierProviderOpt.isPresent) {
                        return ResultFactory.getFailResult("Provider Id doesn't exist")
                    }
                    val cashierProvider = cashierProviderOpt.get()
                    val deviceProvider = providerRepo.findById(allocation.provider?.id ?: 0).get()

                    val deviceMainFacilityId = deviceProvider.mainFacility?.id ?: -1
                    val cashierMainFacilityId = cashierProvider.mainFacility?.id ?: -2

                    if (providerId == deviceProvider.id || providerId == deviceMainFacilityId ||
                        cashierMainFacilityId == deviceProvider.id ||
                        cashierMainFacilityId == deviceMainFacilityId
                    ) {
                        ResultFactory.getSuccessResult(allocation)
                    } else {
                        ResultFactory.getFailResult("Provider Id and Device mismatch")
                    }
                } else {
                    ResultFactory.getFailResult("Device has not been allocated")
                }
            } else {
                return ResultFactory.getFailResult("Device status is ${deviceCatalog.status}")
            }
        }
        return ResultFactory.getFailResult("Provided Device Id is not yet registered")
    }

    override suspend fun allocateDeviceAndSim(dto: AllocateDeviceAndSIMRequestDTO): Result<DeviceAllocation> {
        val catalog = validationService.validateAndGetDeviceCatalog(dto.deviceCatalogId)
        val sim = validationService.validateAndGetDeviceSIM(dto.simId)
        val allocationCheck = deviceAllocationRepo.findByDeviceCatalogAndStatus(
            deviceCatalog = catalog,
            AllocationStatus.ALLOCATED
        )
        if (allocationCheck.isPresent) {
            throw BadRequestException("Device is already allocated")
        }

        val allocation = DeviceAllocation(
            deviceCatalog = catalog,
            deviceSim = sim,
            status = AllocationStatus.UNALLOCATED,
            createdBy = dto.createdBy,
            checkerStatus = CheckerStatus.PENDING
        )
        deviceAllocationRepo.save(allocation)
        dto.accessories?.forEach { devAccessory ->
            deviceAccessoryRepo.save(
                DeviceAccessory(
                    accessory = devAccessory.accessory,
                    note = devAccessory.note,
                    deviceAllocation = allocation
                )
            )
        }
        deviceAuditTrail(
            DeviceManagementAuditLog(
                allocation = allocation,
                actionByUser = dto.createdBy,
                action = LogAction.CREATED_ALLOCATION,
                payload = dto.toJsonString()
            )
        )

        return ResultFactory.getSuccessResult(allocation)
    }

    override suspend fun uploadDocuments(dto: SupportingDocumentRequest): Result<Boolean> {
        val allocation = validationService.validateAndGetDeviceAllocation(dto.allocationId)
        dto.supportingDocuments.forEach { supportingDocument ->
            val document = DeviceDocument(
                deviceAllocation = allocation,
                documentUrl = supportingDocument
            )
            deviceDocumentRepo.save(document)
        }
        deviceAuditTrail(
            DeviceManagementAuditLog(
                allocation = allocation,
                actionByUser = dto.uploadedBy,
                action = LogAction.UPLOADED_SUPPORTING_DOCUMENTS,
                payload = dto.toJsonString()
            )
        )
        return ResultFactory.getSuccessResult(UPLOADED_SUCCESSFULLY)
    }

    override suspend fun batchUploadDevice(file: MultipartFile, emailCallBack: String?): Result<Boolean> {
        if (xlFileReader.isExcelFormat(file)) {
            val input = file.inputStream
            val workBook = XSSFWorkbook(input)
            workBook.map { sheet ->
                val data = mutableListOf<DeviceRegistrationBatchInput>()
                sheet.rowIterator().forEach { record ->
                    if (record.rowNum != 0) {
                        if (record.getCell(0) != null && !record.getCell(0).stringCellValue.isNullOrBlank()) {
                            val deviceId = record.getCell(0).stringCellValue ?: ""
                            val imei = record.getCell(1).stringCellValue ?: ""
                            val model = record.getCell(2).stringCellValue ?: ""
                            val registeredBy = record.getCell(3).stringCellValue ?: ""
                            val description = record.getCell(4).stringCellValue ?: ""
                            val accessories = record.getCell(5).stringCellValue ?: ""
                            if (imei.isNotBlank() && model.isNotBlank() && registeredBy.isNotBlank()) {
                                val imeis = imei.split(",")
                                val accessoryAndNoteItems = accessories.split(",")

                                val accessoryItems = mutableSetOf<Accessory>()
                                accessoryAndNoteItems.forEach {
                                    val accessoryAndNoteItem = it.split("-")
                                    val accessoryName =
                                        if (accessoryAndNoteItem.isNotEmpty()) accessoryAndNoteItem[0] else ""
                                    val note = if (accessoryAndNoteItem.size > 1) accessoryAndNoteItem[1] else null
                                    val accessory = Accessory(accessory = accessoryName, note = note)
                                    accessoryItems.add(accessory)
                                }

                                val batchInput = DeviceRegistrationBatchInput(
                                    deviceId = deviceId,
                                    registeredByUser = registeredBy,
                                    description = description,
                                    model = model,
                                    imei = imeis.toSet(),
                                    accessories = accessoryItems.toSet(),
                                )
                                data.add(batchInput)
                            } else {
                                throw BadRequestException(
                                    "Invalid excel format (DEVICE_ID - required, IMEI (Comma separated) - required, MODEL -required, " +
                                            "REGISTERED_BY - required, DESCRIPTION - optional, ACCESSORIES - optional)"
                                )
                            }
                        }
                    }
                }

                CoroutineScope(Dispatchers.IO).launch {
                    addDevices(data)
                    val msg = "Device batch upload complete"
                    println(msg)
                    emailCallBack?.let { email ->
                        notificationService.sendEmail("Upload Process Complete", msg, email)
                    }
                }
            }
            return ResultFactory.getSuccessResult(msg = AppConstants.UPLOADED_SUCCESSFULLY_P)
        } else {
            return ResultFactory.getFailResult("File is not a valid excel format, ${file.contentType}")
        }
    }

    override suspend fun batchUploadSim(file: MultipartFile, emailCallBack: String?): Result<Boolean> {
        if (xlFileReader.isExcelFormat(file)) {
            val input = file.inputStream
            val workBook = XSSFWorkbook(input)
            workBook.map { sheet ->
                val data = mutableListOf<SimBatchInput>()
                sheet.rowIterator().forEach { record ->
                    if (record.rowNum != 0) {
                        if (record.getCell(0) != null && !record.getCell(0).stringCellValue.isNullOrBlank()) {
                            val simNumber = record.getCell(0).stringCellValue ?: ""
                            val registeredBy = record.getCell(1).stringCellValue ?: ""
                            if (registeredBy.isNotBlank()) {
                                val batchInput = SimBatchInput(simNumber = simNumber, registeredByUser = registeredBy)
                                data.add(batchInput)
                            } else {
                                throw BadRequestException("Invalid excel format (SIM_NUMBER - required, REGISTERED_BY - required)")
                            }
                        }
                    }
                }

                CoroutineScope(Dispatchers.IO).launch {
                    registerSIMs(data)
                    val msg = "SIM batch upload complete"
                    println(msg)
                    emailCallBack?.let { email ->
                        notificationService.sendEmail("Upload Process Complete", msg, email)
                    }
                }
            }
            return ResultFactory.getSuccessResult(msg = AppConstants.UPLOADED_SUCCESSFULLY_P)
        } else {
            return ResultFactory.getFailResult("File is not a valid excel format, ${file.contentType}")
        }
    }

    override suspend fun deviceAuditTrail(log: DeviceManagementAuditLog) {
        deviceManagementAuditLogRepo.save(log)
    }

    override suspend fun getDevicesAllocatedToAProvider(
        providerId: Long,
        page: Int,
        size: Int
    ): Result<Page<DeviceAllocation>> {
        validationService.validateAndGetProvider(providerId)
        val pageable = PageRequest.of(page - 1, size)
        return ResultFactory.getSuccessResult(
            deviceAllocationRepo.findProviderAllocatedDevices(
                providerId, CheckerStatus.APPROVED, pageable
            )
        )
    }

    override suspend fun getDevicesAllocations(page: Int, size: Int): Result<Page<DeviceAllocation>> {
        val pageable = PageRequest.of(page - 1, size)
        return ResultFactory.getSuccessResult(
            deviceAllocationRepo.findAllocations(pageable)
        )
    }

    override suspend fun updateDeviceStatus(deviceId: Long, dto: DeviceStatusUpdate): Result<Boolean> {
        val catalog = validationService.validateAndGetDeviceCatalog(deviceId)
        if (catalog.status != dto.deviceStatus) {
            catalog.status = dto.deviceStatus
            deviceCatalogRepo.save(catalog)
            deviceAuditTrail(
                DeviceManagementAuditLog(
                    catalog = catalog,
                    actionByUser = dto.updatedBy,
                    action = LogAction.UPDATED_DEVICE,
                    previousValues = catalog.status.name,
                    payload = dto.toJsonString(),
                    reason = dto.reason
                )
            )
        }
        return ResultFactory.getSuccessResult(msg = AppConstants.UPDATED_SUCCESSFULLY)
    }

    override suspend fun updateSIMStatus(simCatalogId: Long, dto: SIMStatusUpdate): Result<Boolean> {
        val sim = validationService.validateAndGetDeviceSIM(simCatalogId)
        if (sim.status != dto.simStatus) {
            sim.status = dto.simStatus
            deviceSimRepo.save(sim)
            deviceAuditTrail(
                DeviceManagementAuditLog(
                    sim = sim,
                    actionByUser = dto.updatedBy,
                    action = LogAction.UPDATED_SIM,
                    previousValues = sim.status.name,
                    payload = dto.toJsonString(),
                    reason = dto.reason
                )
            )
        }
        return ResultFactory.getSuccessResult(msg = AppConstants.UPDATED_SUCCESSFULLY)
    }

    override suspend fun updateAllocationStatus(allocationId: Long, dto: AllocationStatusUpdate): Result<Boolean> {
        val allocation = validationService.validateAndGetDeviceAllocation(allocationId)
        if (allocation.status != dto.allocationStatus) {
            if (dto.allocationStatus != AllocationStatus.ALLOCATED) {
                val catalog = allocation.deviceCatalog
                catalog.status = DeviceStatus.AVAILABLE
                deviceCatalogRepo.save(catalog)

                val sim = allocation.deviceSim
                sim?.let {
                    it.status = SimStatus.UNASSIGNED
                    deviceSimRepo.save(sim)
                }
            }

            allocation.status = dto.allocationStatus
            deviceAllocationRepo.save(allocation)

            deviceAuditTrail(
                DeviceManagementAuditLog(
                    allocation = allocation,
                    actionByUser = dto.updatedBy,
                    action = LogAction.UPDATED_ALLOCATION,
                    previousValues = allocation.status.name,
                    payload = dto.toJsonString(),
                    reason = dto.reason
                )
            )
        }
        return ResultFactory.getSuccessResult(msg = AppConstants.UPDATED_SUCCESSFULLY)
    }

    override suspend fun allocateDeviceToProvider(dto: ProviderDeviceAllocation): Result<Boolean> {
        val provider = validationService.validateAndGetProvider(dto.providerId)
        if (dto.enableGeofence && (dto.radius == null || dto.radius!! <= 0.00)) {
            throw BadRequestException("Geofence radius (in metres) is required and should be greater than zero")
        }
        dto.allocationIds.forEach { allocationId ->
            val allocation = validationService.validateAndGetDeviceAllocation(allocationId)
            allocation.status = AllocationStatus.ALLOCATED
            allocation.allocatedBy = dto.allocatedBy
            allocation.provider = provider
            allocation.enableGeofence = dto.enableGeofence
            allocation.radius = dto.radius
            allocation.checkerStatus = CheckerStatus.PENDING
            deviceAllocationRepo.save(allocation)

            val catalog = allocation.deviceCatalog
            catalog.status = DeviceStatus.ALLOCATED
            deviceCatalogRepo.save(catalog)
            val sim = allocation.deviceSim
            sim?.let {
                it.status = SimStatus.ASSIGNED
                deviceSimRepo.save(it)
            }

            deviceAuditTrail(
                DeviceManagementAuditLog(
                    allocation = allocation,
                    actionByUser = dto.allocatedBy,
                    action = LogAction.ALLOCATED_DEVICE,
                    payload = dto.toJsonString()
                )
            )
        }
        return ResultFactory.getSuccessResult(msg = AppConstants.UPDATED_SUCCESSFULLY)
    }

    private fun registerSIMs(data: MutableList<SimBatchInput>) {
        for (record in data) {
            val simCheck = deviceSimRepo.findBySimNumber(record.simNumber)
            if (simCheck.isPresent) {
                val error = SimUploadError(
                    simNumber = record.simNumber,
                    registeredByUser = record.registeredByUser,
                    error = "SIM already registered"
                )
                simUploadErrorRepo.save(error)
                continue
            }
            val deviceSim = DeviceSim(
                simNumber = record.simNumber
            )
            deviceSimRepo.save(deviceSim)
            val log = DeviceManagementAuditLog(
                sim = deviceSim,
                actionByUser = record.registeredByUser,
                action = LogAction.ADDED_SIM,
                payload = record.toJsonString(),
                note = "Batch Upload"
            )
            deviceManagementAuditLogRepo.save(log)
        }
    }

    private fun addDevices(data: MutableList<DeviceRegistrationBatchInput>) {
        for (device in data) {
            val deviceCheck = deviceCatalogRepo.findByDeviceId(
                deviceId = device.deviceId
            )
            if (deviceCheck.isPresent) {
                logDeviceUploadError(device, "Device is already registered")
                continue
            }

            val modelCheck = deviceModelRepo.findByModel(device.model)
            if (!modelCheck.isPresent) {
                logDeviceUploadError(device, "Invalid device model")
                continue
            }

            val catalog = DeviceCatalog(
                deviceId = device.deviceId,
                description = device.description,
                deviceModel = modelCheck.get(),
            )
            deviceCatalogRepo.save(catalog)
            device.imei?.let { imeis ->
                for (imei in imeis) {
                    val imeiCheck = deviceImeiRepo.findByImei(imei)
                    if (imeiCheck.isPresent) {
                        logDeviceUploadError(device, "Imei is already registered")
                        continue
                    }
                    deviceImeiRepo.save(DeviceImei(imei = imei, deviceCatalog = catalog))
                }
            }
            device.accessories?.forEach { devAccessory ->
                deviceAccessoryRepo.save(
                    DeviceAccessory(
                        accessory = devAccessory.accessory,
                        note = devAccessory.note,
                        deviceCatalog = catalog
                    )
                )
            }
            val log = DeviceManagementAuditLog(
                catalog = catalog,
                actionByUser = device.registeredByUser,
                action = LogAction.ADDED_DEVICE,
                payload = device.toJsonString(), note = "Batch Upload"
            )
            deviceManagementAuditLogRepo.save(log)
        }
    }

    private fun logDeviceUploadError(device: DeviceRegistrationBatchInput, errorMsg: String) {
        val error = DeviceUploadError(
            deviceId = device.deviceId,
            imei = device.imei?.joinToString(","),
            description = device.description, registeredByUser = device.registeredByUser,
            model = device.model, error = errorMsg
        )
        deviceUploadErrorRepo.save(error)
    }

}
