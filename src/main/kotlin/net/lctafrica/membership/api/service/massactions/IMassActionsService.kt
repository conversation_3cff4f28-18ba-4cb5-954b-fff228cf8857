package net.lctafrica.membership.api.service.massactions

import net.lctafrica.membership.api.dtos.massactions.MassActionResult


interface IMassActionsService {
    suspend fun <T> executeWithLogging(
        operation: String,
        // operation to execute
        block: suspend () -> T
    ): T


    fun createSummary(
        totalProcessed: Int,
        successCount: Int,
        errors: List<String>,
        processingTimeMs: Long,
        details: Any? = null
    ): MassActionResult
}