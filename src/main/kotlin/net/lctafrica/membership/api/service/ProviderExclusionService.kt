package net.lctafrica.membership.api.service

import java.time.LocalDateTime
import java.util.stream.Collectors
import net.lctafrica.membership.api.domain.Category
import net.lctafrica.membership.api.domain.CategoryRepository
import net.lctafrica.membership.api.domain.CategoryRestriction
import net.lctafrica.membership.api.domain.CategoryRestrictionMappingRepo
import net.lctafrica.membership.api.domain.PayerProviderMappingRepository
import net.lctafrica.membership.api.domain.PayerRepository
import net.lctafrica.membership.api.domain.ProviderExclusion
import net.lctafrica.membership.api.domain.ProviderExclusionRepository
import net.lctafrica.membership.api.domain.ProviderRepository
import net.lctafrica.membership.api.domain.RestrictionStatus
import net.lctafrica.membership.api.domain.RestrictionType
import net.lctafrica.membership.api.domain.Status
import net.lctafrica.membership.api.dtos.ExclusionDTO
import net.lctafrica.membership.api.dtos.RemoveRestrictionDTO
import net.lctafrica.membership.api.dtos.RestrictionDTO
import net.lctafrica.membership.api.util.Result
import net.lctafrica.membership.api.util.ResultFactory
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service("providerRestrictionService")
@Transactional
class ProviderRestrictionService(
	private val repo: ProviderExclusionRepository,
	private val providerRepo: ProviderRepository,
	private val payerRepository: PayerRepository,
	private val categoryRepo: CategoryRepository,
	private val categoryRestrictionMappingRepo: CategoryRestrictionMappingRepo,
	private val payerProviderMappingRepository: PayerProviderMappingRepository
) : IProviderRestrictionService {

	@Transactional(rollbackFor = [Exception::class])
	override fun addExclusion(dto: ExclusionDTO): Result<ProviderExclusion> {
		val category = categoryRepo.findById(dto.categoryId)
		val provider = providerRepo.findById(dto.providerId)

		if (category.isEmpty) return ResultFactory.getFailResult("No category with ID ${dto.categoryId} was found")
		if (provider.isEmpty) return ResultFactory.getFailResult("No provider with ID ${dto.providerId} was found")

		val optional = repo.findByCategoryAndProvider(category.get(), provider.get())

		if (optional.isPresent) {
			return ResultFactory.getFailResult(
				data = optional.get(),
				msg = "This exclusion for ${provider.get().name} has already been set up"
			)
		}

		var exclusion =
			ProviderExclusion(
				id = null,
				provider = provider.get(),
				category = category.get(),
				status = Status.ACTIVE
			)
		repo.save(exclusion)
		return ResultFactory.getSuccessResult(exclusion)
	}

	override fun addRestriction(dto: RestrictionDTO): Result<CategoryRestriction> {
		val category = categoryRepo.findById(dto.categoryId)
		val payerProviderMap = payerProviderMappingRepository.findById(dto.payerProviderId)
		val provider = providerRepo.findById(payerProviderMap.get().provider!!.id!!)
		val payer = payerRepository.findById(payerProviderMap.get().payer!!.id)

		if (category.isEmpty) return ResultFactory.getFailResult("No category with ID ${dto.categoryId} was found")
		if (payerProviderMap.isEmpty) return ResultFactory.getFailResult(
			"No Provider Mapping with ID ${
				dto
					.payerProviderId
			} was found"
		)
		val cat = category.get()
		if (cat.restrictionType == null) {
			val theCategory = category.get().apply {
				this.restrictionType = RestrictionType.valueOf(dto.restrictionType.toString())
			}
			categoryRepo.save(theCategory)
		}

		val optionalRestriction =
			categoryRestrictionMappingRepo.findByCategoryAndProvider(
				category.get(),
				provider.get()
			)

		if (optionalRestriction.isPresent) {
			if (optionalRestriction.get().restrictionStatus == RestrictionStatus.ACTIVE) {
				return ResultFactory.getFailResult(msg = "Restriction Already Active")
			} else {
				optionalRestriction.map {
					it.apply {
						it.restrictionStatus = RestrictionStatus.ACTIVE
					}
					categoryRestrictionMappingRepo.save(it)
				}
				return ResultFactory.getSuccessResult(optionalRestriction.get())
			}
//
		} else {
			var restriction =
				CategoryRestriction(
					id = null,
					provider = provider.get(),
					category = category.get(),
					payer = payer.get()
				)
			categoryRestrictionMappingRepo.save(restriction)
			return ResultFactory.getSuccessResult(restriction)
		}
	}

	override fun findRestrictionByCategory(categoryId: Long): Result<MutableList<CategoryRestriction>> {
		val category = categoryRepo.findById(categoryId)
		if (category.isPresent) {
			val results =
				categoryRestrictionMappingRepo.getProviderRestrictionsForACategory(category.get())
			return ResultFactory.getSuccessResult(results)
		}
		return ResultFactory.getFailResult("No category with ID $categoryId was found")
	}

	@Transactional(rollbackFor = [Exception::class])
	override fun deactivate(restrictionDTO: RemoveRestrictionDTO): Result<CategoryRestriction> {
		var providerRestriction =
			categoryRestrictionMappingRepo.findById(restrictionDTO.restrictionId)
		providerRestriction.ifPresent {
			it.apply {
				restrictionStatus = RestrictionStatus.DELETED
			}
			categoryRestrictionMappingRepo.save(it)
		}
		return ResultFactory.getSuccessResult(providerRestriction.get())
	}

	@Transactional(rollbackFor = [Exception::class])
	override fun process(category: Category): Result<MutableList<ProviderExclusion>> {
		val exclusions = repo.findByCategory(category)
		for (exclusion in exclusions) {
			exclusion.apply { processed = true; processedTime = LocalDateTime.now() }
		}
		//TODO send to provider exclusion kafka topic
		repo.saveAll(exclusions)
		return ResultFactory.getSuccessResult("Successfully processed the exclusions")
	}

	override fun findExclusionsByProvider(providerId: Long): Result<MutableList<ProviderExclusion>> {
		val provider = providerRepo.findById(providerId)
		if (provider.isPresent) {
			val results = repo.findByProvider(provider.get())
			return ResultFactory.getSuccessResult(results)
		}
		return ResultFactory.getFailResult("No provider with ID $providerId was found")

	}

	override fun findExclusionsByCategory(
		categoryId: Long,
		page: Int,
		size: Int
	): Result<Page<ProviderExclusion>> {
		val category = categoryRepo.findById(categoryId)
		if (category.isPresent) {
			val request = PageRequest.of(page - 1, size)
			val results = repo.findByCategory(category.get(), request)
			return ResultFactory.getSuccessResult(results)
		}

		return ResultFactory.getFailResult("No category with ID $categoryId was found")
	}

	override fun findRestrictionByCategoryAndProviderId(
		categoryId: Long,
		providerId: Long
	): Result<Boolean?> {
		val category = categoryRepo.findById(categoryId)
		val provider = providerRepo.findById(providerId)
		if (category.isEmpty) return ResultFactory.getFailResult("No category with ID $categoryId was found")
		if (category.isEmpty) return ResultFactory.getFailResult(
			data = false, msg = "No " +
					"Restriction"
		)
		if (category.get().restrictionType !== null) {
			if (category.get().restrictionType!! == RestrictionType.INCLUSIVE) {
				ResultFactory.getFailResult(data = false, msg = "Restriction is inclusive")
				val providerRestriction = categoryRestrictionMappingRepo
					.getProviderRestrictionsForACategory(category =category.get())

				val filterRes = providerRestriction.stream().filter {
					it.provider.id === provider.get().id
				}.collect(Collectors.toList())
				return if(filterRes.size>0){
					ResultFactory.getFailResult(
						data = false, msg = "No Inclusive restriction " +
								"has been set"
					)
				}else{
					ResultFactory.getSuccessResult(data = true)
				}

			} else if (category.get().restrictionType!! == RestrictionType.EXCLUSIVE) {
				val providerRestriction =
					categoryRestrictionMappingRepo.findByCategoryAndProviderAndRestrictionStatus(
						category.get(),
						provider.get(), RestrictionStatus.ACTIVE
					)
				return if (providerRestriction.isPresent) {
					ResultFactory.getSuccessResult(data = true)
				} else {
					ResultFactory.getFailResult(
						data = false, msg = "No Exclusive restriction " +
								"has been set"
					)
				}

			}
		}
		return ResultFactory.getFailResult(data = false, msg = "No Restriction")
	}

	override fun findExclusionsByCategoryAndProvider(
		categoryId: Long,
		providerId: Long
	): Result<Boolean> {
		val category = categoryRepo.findById(categoryId)
		val provider = providerRepo.findById(providerId)

		if (category.isEmpty) return ResultFactory.getFailResult("No category with ID $categoryId was found")
		if (provider.isEmpty) return ResultFactory.getFailResult("No provider with ID $providerId was found")

		val optional = repo.findByCategoryAndProvider(category.get(), provider.get())
		return if (optional.isPresent) {
			ResultFactory.getSuccessResult(
				data = true,
				msg = "An exclusion exists at ${provider.get().name} for category ${
					category.get
						().name
				}"
			)
		} else {
			ResultFactory.getFailResult(
				data = false,
				msg = "No Exclusion exists at ${provider.get().name} for category ${
					category.get
						().name
				}"
			)
		}

	}
}