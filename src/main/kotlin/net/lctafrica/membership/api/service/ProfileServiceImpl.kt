package net.lctafrica.membership.api.service

import net.lctafrica.membership.api.dtos.BeneficiariesDto
import net.lctafrica.membership.api.dtos.FingerPrintDto
import net.lctafrica.membership.api.dtos.UpdateProfileDto
import net.lctafrica.membership.api.util.ApiResult
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import org.springframework.web.reactive.function.BodyInserters
import org.springframework.web.reactive.function.client.WebClient
import org.springframework.web.reactive.function.client.awaitBody
import org.springframework.web.reactive.function.client.awaitExchange
import org.springframework.web.reactive.function.client.createExceptionAndAwait

@Service("profileServiceImpl")
class ProfileServiceImpl(
    @Qualifier("profileWebClientConfig")
    val webClient: WebClient
) : IProfileService {
    override suspend fun findBiometricsForBeneficiaries(
        beneficiaryIds: List<Long>,
        memberNumbers: List<String>
    ): ApiResult<List<FingerPrintDto>> {
        val request= BeneficiariesDto(beneficiaryIds, memberNumbers)
        return webClient.post()
            .uri("/api/v1/biometric/findByBeneficiaries")
            .body(BodyInserters.fromValue(request))
            .awaitExchange { clientResponse ->
                return@awaitExchange if (clientResponse.statusCode() == HttpStatus.OK) {
                    clientResponse.awaitBody<ApiResult<List<FingerPrintDto>>>()
                } else if (clientResponse.statusCode().is4xxClientError) {
                    throw Exception("findBiometricsForBeneficiaries => Failed with Status: ${clientResponse.rawStatusCode()}")
                } else {
                    throw clientResponse.createExceptionAndAwait()
                }
            }
    }

    override suspend fun updatePhoneNumber(dto: UpdateProfileDto): ApiResult<Boolean> {
        return webClient.post()
            .uri("/api/v1/profile/updatePhoneNumber")
            .body(BodyInserters.fromValue(dto))
            .awaitExchange { clientResponse ->
                return@awaitExchange if (clientResponse.statusCode() == HttpStatus.OK) {
                    clientResponse.awaitBody<ApiResult<Boolean>>()
                } else if (clientResponse.statusCode().is4xxClientError) {
                    throw Exception("updatePhoneNumber => Failed with Status: ${clientResponse.rawStatusCode()}")
                } else {
                    throw clientResponse.createExceptionAndAwait()
                }
            }
    }
}