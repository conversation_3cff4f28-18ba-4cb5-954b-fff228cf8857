package net.lctafrica.membership.api.service

import net.lctafrica.membership.api.config.NotFoundRequestException
import net.lctafrica.membership.api.domain.BenefitCatalog
import net.lctafrica.membership.api.domain.RequestService
import net.lctafrica.membership.api.domain.RequestServiceRepository
import net.lctafrica.membership.api.domain.RequestType
import net.lctafrica.membership.api.domain.RequestTypeRepository
import net.lctafrica.membership.api.domain.ServiceBenefitMapping
import net.lctafrica.membership.api.domain.ServiceBenefitMappingRepository
import net.lctafrica.membership.api.domain.ServiceCatalog
import net.lctafrica.membership.api.domain.ServiceCatalogRepository
import net.lctafrica.membership.api.domain.ServiceProviderMapping
import net.lctafrica.membership.api.domain.ServiceProviderMappingRepository
import net.lctafrica.membership.api.dtos.AddRequestTypeServiceDto
import net.lctafrica.membership.api.dtos.BatchMapServiceProvidersDto
import net.lctafrica.membership.api.dtos.CreateServiceCatalogDto
import net.lctafrica.membership.api.dtos.IdsListDto
import net.lctafrica.membership.api.dtos.MapServiceAndBenefitCatalogBatchDto
import net.lctafrica.membership.api.dtos.RequestServiceProjectionDto
import net.lctafrica.membership.api.dtos.RequestTypeProjectionDto
import net.lctafrica.membership.api.dtos.UnmapServiceAndBenefitCatalogBatchDto
import net.lctafrica.membership.api.util.AppConstants.ADDED_SUCCESSFULLY
import net.lctafrica.membership.api.util.AppConstants.REMOVED_SUCCESSFULLY
import net.lctafrica.membership.api.util.AppConstants.SUCCESSFULLY_MAPPED
import net.lctafrica.membership.api.util.AppConstants.SUCCESSFULLY_UNMAPPED
import net.lctafrica.membership.api.util.Result
import net.lctafrica.membership.api.util.ResultFactory
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional
class ManageProviderBenefitImplService(
    private val serviceCatalogRepository: ServiceCatalogRepository,
    private val serviceBenefitMappingRepository: ServiceBenefitMappingRepository,
    private val serviceProviderMappingRepository: ServiceProviderMappingRepository,
    private val requestTypeRepository: RequestTypeRepository,
    private val requestServiceRepository: RequestServiceRepository,
    private val validationService: IValidationService
) :
    IManageProviderBenefitService {
    override fun getServiceCatalog(page: Int, size: Int): Result<Page<ServiceCatalog>> {
        val request = PageRequest.of(page - 1, size)
        val pages = serviceCatalogRepository.findByIsEnabled(true,request)
        return ResultFactory.getSuccessResult(pages)
    }

    override fun addToServiceCatalog(dto: CreateServiceCatalogDto): Result<Boolean> {
        val check = serviceCatalogRepository.findByName(dto.name)
        val catalog = if (check.isPresent) {
            check.get().apply {
                isEnabled = true
            }
        } else {
            ServiceCatalog(
                name = dto.name,
                description = dto.description,
                isEnabled = true
            )

        }
        serviceCatalogRepository.save(catalog)
        return ResultFactory.getSuccessResult(ADDED_SUCCESSFULLY)
    }

    override fun removeFromServiceCatalog(catalogId: Long): Result<Boolean> {
        val catalog = validationService.validateAndGetServiceCatalog(catalogId)
        catalog.isEnabled = false
        serviceCatalogRepository.save(catalog)
        return ResultFactory.getSuccessResult(REMOVED_SUCCESSFULLY)
    }

    override fun batchMapServiceAndBenefitCatalog(dto: MapServiceAndBenefitCatalogBatchDto): Result<Boolean> {
        val serviceCatalog = validationService.validateAndGetServiceCatalog(dto.serviceCatalogId)
        dto.benefitCatalogIds.forEach { benefitCatalogId ->
            val benefitCatalog = validationService.validateAndGetBenefitCatalog(benefitCatalogId)
            mapServiceToBenefitCatlog(serviceCatalog, benefitCatalog)
        }
        return ResultFactory.getSuccessResult(SUCCESSFULLY_MAPPED)
    }

    override fun batchUnmapServiceBenefits(
        serviceCatalogId: Long,
        dto: UnmapServiceAndBenefitCatalogBatchDto
    ): Result<Boolean> {
        val serviceCatalog = validationService.validateAndGetServiceCatalog(serviceCatalogId)
        serviceBenefitMappingRepository.disableMappedServiceBenefits(serviceCatalog, dto.benefitCatalogIds)
        return ResultFactory.getSuccessResult(SUCCESSFULLY_UNMAPPED)
    }

    override fun getMappedBenefits(
        serviceCatalogId: Long,
        page: Int,
        size: Int
    ): Result<Page<ServiceBenefitMapping>> {
        val pageable = PageRequest.of(page - 1, size)
        val serviceCatalog = validationService.validateAndGetServiceCatalog(serviceCatalogId)
        val mapList = serviceBenefitMappingRepository.findByServiceCatalogAndMappingEnabled(
            serviceCatalog = serviceCatalog,
            pageable = pageable
        )
        return ResultFactory.getSuccessResult(mapList)
    }

    override fun batchMapServiceProviders(dto: BatchMapServiceProvidersDto): Result<Boolean> {
        val serviceCatalog = validationService.validateAndGetServiceCatalog(dto.serviceCatalogId)
        dto.providerIds.forEach { providerId ->
            val provider = validationService.validateAndGetProvider(providerId)
            val check = serviceProviderMappingRepository.findByServiceCatalogAndProvider(
                serviceCatalog = serviceCatalog,
                provider = provider
            )
            val map = if (check.isPresent) {
                check.get().apply {
                    mappingEnabled = true
                }
            } else {
                ServiceProviderMapping(
                    serviceCatalog = serviceCatalog,
                    provider = provider,
                    mappingEnabled = true
                )
            }
            serviceProviderMappingRepository.save(map)
        }
        return ResultFactory.getSuccessResult(SUCCESSFULLY_MAPPED)
    }

    override fun batchUnmapServiceProviders(
        serviceCatalogId: Long,
        dto: IdsListDto
    ): Result<Boolean> {
        val serviceCatalog = validationService.validateAndGetServiceCatalog(serviceCatalogId)
        serviceProviderMappingRepository.disableMappedServiceProviders(serviceCatalog, dto.ids)
        return ResultFactory.getSuccessResult(SUCCESSFULLY_UNMAPPED)
    }

    override fun getMappedProviders(
        serviceCatalogId: Long,
        page: Int,
        size: Int
    ): Result<Page<ServiceProviderMapping>> {
        val pageable = PageRequest.of(page - 1, size)
        val serviceCatalog = validationService.validateAndGetServiceCatalog(serviceCatalogId)
        val mapList = serviceProviderMappingRepository.findByServiceCatalogAndMappingEnabled(
            serviceCatalog = serviceCatalog,
            pageable = pageable
        )
        return ResultFactory.getSuccessResult(mapList)
    }

    override fun getRequestTypes(providerId: Long): Result<List<RequestTypeProjectionDto>> {
        return ResultFactory.getSuccessResult(requestTypeRepository.getAll())
    }

    override fun getRequestService(providerId: Long, requestName: String): Result<List<RequestServiceProjectionDto>> {
        val requestType = requestTypeRepository.findByName(requestName).orElseThrow {
            NotFoundRequestException("No request named $requestName")
        }
        return ResultFactory.getSuccessResult(requestServiceRepository.findByRequestType(requestType))
    }

    override fun addRequestTypesAndServices(dto: AddRequestTypeServiceDto): Result<Boolean> {
        val requestTypeOpt = requestTypeRepository.findByName(dto.requestName)
        val requestType =if(requestTypeOpt.isPresent){
            requestTypeOpt.get()
        }else{
            val data =RequestType(name = dto.requestName)
            requestTypeRepository.save(data)
        }
        dto.serviceName.forEach {
            val optCheck = requestServiceRepository.findByRequestTypeAndName(requestType,it)
            if(!optCheck.isPresent) {
                val data = RequestService(name = it, requestType = requestType)
                requestServiceRepository.save(data)
            }
        }
        return ResultFactory.getSuccessResult(ADDED_SUCCESSFULLY)
    }

    private fun mapServiceToBenefitCatlog(serviceCatalog: ServiceCatalog, benefitCatalog: BenefitCatalog) {
        val check = serviceBenefitMappingRepository.findByServiceCatalogAndBenefitCatalog(
            serviceCatalog = serviceCatalog,
            benefitCatalog = benefitCatalog
        )
        val map = if (check.isPresent) {
            check.get().apply {
                mappingEnabled = true
            }
        } else {
            ServiceBenefitMapping(
                serviceCatalog = serviceCatalog,
                benefitCatalog = benefitCatalog,
                mappingEnabled = true
            )
        }
        serviceBenefitMappingRepository.save(map)
    }
}