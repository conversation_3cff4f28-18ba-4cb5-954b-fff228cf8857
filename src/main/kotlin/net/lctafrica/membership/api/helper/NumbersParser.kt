package net.lctafrica.membership.api.helper

import org.apache.poi.ss.usermodel.Workbook
import org.springframework.stereotype.Component
import org.springframework.web.multipart.MultipartFile

@Component
class NumbersParser : FileFormatParser {

    companion object {
        private val SUPPORTED_EXTENSIONS = listOf(".numbers")
        private val SUPPORTED_MIME_TYPES = listOf(
            "application/x-iwork-numbers-sffnumbers",
            "application/vnd.apple.numbers"
        )
    }

    override fun canParse(file: MultipartFile): Boolean {
        val filename = file.originalFilename?.lowercase() ?: return false
        val mimeType = file.contentType

        return SUPPORTED_EXTENSIONS.any { filename.endsWith(it) } ||
                SUPPORTED_MIME_TYPES.any { it == mimeType }
    }

    override fun parse(file: MultipartFile): Workbook {
        throw IllegalArgumentException(
            "Apple Numbers (.numbers) files are not currently supported. " +
                    "Please convert your file to Excel format (.xlsx or .xls) and try again."
        )
    }

    override fun getSupportedExtensions(): List<String> = SUPPORTED_EXTENSIONS

    override fun getSupportedMimeTypes(): List<String> = SUPPORTED_MIME_TYPES
}