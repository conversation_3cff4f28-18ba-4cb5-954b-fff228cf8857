package net.lctafrica.membership.api.helper

import org.apache.poi.hssf.usermodel.HSSFWorkbook
import org.apache.poi.ss.usermodel.Workbook
import org.springframework.stereotype.Component
import org.springframework.web.multipart.MultipartFile
import java.io.IOException


@Component
class ExcelXlsParser : FileFormatParser {
    companion object {
        private val SUPPORTED_EXTENSIONS = listOf(".xls")
        private val SUPPORTED_MIME_TYPES = listOf(
            "application/vnd.ms-excel",
            "application/excel"
        )
    }

    override fun canParse(file: MultipartFile): Boolean {
        val filename = file.originalFilename?.lowercase() ?: return false
        val mimeType = file.contentType

        return SUPPORTED_EXTENSIONS.any { filename.endsWith(it) } ||
                SUPPORTED_MIME_TYPES.any { it == mimeType }
    }

    override fun parse(file: MultipartFile): Workbook {
        return try {
            file.inputStream.use { inputStream ->
                HSSFWorkbook(inputStream)
            }
        } catch (e: IOException) {
            throw IllegalArgumentException("Failed to parse .xls file: ${e.message}", e)
        } catch (e: Exception) {
            throw IllegalArgumentException("Invalid .xls file format: ${e.message}", e)
        }
    }

    override fun getSupportedExtensions(): List<String> = SUPPORTED_EXTENSIONS

    override fun getSupportedMimeTypes(): List<String> = SUPPORTED_MIME_TYPES
}