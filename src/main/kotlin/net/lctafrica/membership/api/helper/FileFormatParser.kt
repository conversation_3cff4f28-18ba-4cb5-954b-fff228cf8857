package net.lctafrica.membership.api.helper

import org.apache.poi.ss.usermodel.Workbook
import org.springframework.web.multipart.MultipartFile


interface FileFormatParser {
    fun canParse(file: MultipartFile): Boolean
    fun parse(file: MultipartFile): Workbook
    fun getSupportedExtensions(): List<String>
    fun getSupportedMimeTypes(): List<String>
}

data class FileParseResult(
    val workbook: Workbook,
    val format: String,
    val originalFilename: String?
)