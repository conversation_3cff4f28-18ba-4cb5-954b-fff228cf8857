package net.lctafrica.membership.api.helper

import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

object DateTimeUtil {
    private val formatter = DateTimeFormatter.ofPattern("d MMMM, yyyy 'at' HH:mm:ss")

    fun formatWithOrdinalSuffix(dateTime: ZonedDateTime, useSuperscript: Boolean = false): String {
        val formattedDate = dateTime.format(formatter)

        val dayMatch = Regex("\\b(\\d{1,2})\\b").find(formattedDate)
        val day = dayMatch?.value?.toIntOrNull() ?: return formattedDate

        val suffix = getOrdinalSuffix(day, useSuperscript)
        val formattedDay = "$day$suffix"

        return formattedDate.replaceFirst(Regex("\\b(\\d{1,2})\\b"), formattedDay)
    }

    private fun getOrdinalSuffix(day: Int, useSuperscript: Boolean): String {
        val suffix = when {
            day in 11..13 -> "th"
            day % 10 == 1 -> "st"
            day % 10 == 2 -> "nd"
            day % 10 == 3 -> "rd"
            else -> "th"
        }
        return if (useSuperscript) suffix.toSuperscript() else suffix
    }

    private fun String.toSuperscript(): String {
        return this.replace("st", "ˢᵗ")
            .replace("nd", "ⁿᵈ")
            .replace("rd", "ʳᵈ")
            .replace("th", "ᵗʰ")
    }
}
