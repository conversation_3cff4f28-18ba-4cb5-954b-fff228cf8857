package net.lctafrica.membership.api.helper

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import net.lctafrica.membership.api.domain.*
import net.lctafrica.membership.api.gson
import net.lctafrica.membership.api.service.IPayerService
import net.lctafrica.membership.api.service.IProviderService
import org.apache.poi.ss.usermodel.*
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import org.springframework.web.multipart.MultipartFile
import org.springframework.web.reactive.function.client.WebClient
import java.io.IOException
import java.io.InputStream

@Component
class ExcelHelper(
    val payerService: IPayerService,
    val providerService: IProviderService,
    val fileFormatParserFactory: FileFormatParserFactory
) {
    var TYPE = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    var HEADERs = arrayOf("Code", "Title")
    var SHEET = "ICD10CODES"
    var PROVIDERS = "PROVIDERS"
    var MAPPING = "MAPPING"
    var SHEET_DRUGS = "medical_drugs"
    var SHEET_LAB = "lab"
    var SHEET_RADIOLOGY = "radiology"
    var SHEET_PROCEDURES = "medical_procedures"
    var SHEET_OTHER_BENEFIT_DETAIL = "OTHER"

    @Value("\${lct-africa.member.url}")
    lateinit var memberUrl: String

    fun hasExcelFormat(file: MultipartFile): Boolean {
        return fileFormatParserFactory.isSupported(file)
    }

    fun getSupportedExtensions(): List<String> {
        return fileFormatParserFactory.getSupportedExtensions()
    }

    fun getSupportedMimeTypes(): List<String> {
        return fileFormatParserFactory.getSupportedMimeTypes()
    }

    fun parseWorkbookFromFile(file: MultipartFile): Workbook {
        return try {
            val parseResult = fileFormatParserFactory.parseFile(file)
            parseResult.workbook
        } catch (e: Exception) {
            throw RuntimeException("Failed to parse file: ${e.message}", e)
        }
    }


    fun excelToproviders(`is`: InputStream?): List<Provider>? {
        return try {
            // Note: This method uses InputStream directly, so we still use XSSFWorkbook
            // For MultipartFile support, use the new parseWorkbookFromFile method
            val workbook: Workbook = XSSFWorkbook(`is`)
            var row: Row
            var cell: Cell?
            val sheet = workbook.getSheet(PROVIDERS)
            val rows: Iterator<Row> = sheet.iterator()
            val providers: MutableList<Provider> = ArrayList<Provider>()
            var rowNumber = 0
            val formatter = DataFormatter()
            var style: CellStyle = workbook.createCellStyle();
            val format = workbook.createDataFormat()
            style = workbook.createCellStyle();
            style.setDataFormat(format.getFormat("0.0"));
            //precision only up to 15 significant
            // digits

            while (rows.hasNext()) {
                val currentRow: Row = rows.next()

                // skip header
                if (rowNumber == 0) {
                    rowNumber++
                    continue
                }
                val cellsInRow: Iterator<Cell> = currentRow.iterator()
                val exprovider = Provider()
                var cellIdx = 0
                while (cellsInRow.hasNext()) {
                    val currentCell: Cell = cellsInRow.next()



                    when (cellIdx) {
                        0 -> exprovider.name = currentCell.stringCellValue
                        1 -> exprovider.tier = ProviderTier.valueOf(currentCell.stringCellValue)
                        2 -> exprovider.region = findRegionById(currentCell.numericCellValue.toLong())

                        else -> {}
                    }
                    cellIdx++
                }
                providers.add(exprovider)
            }
            workbook.close()
            providers
        } catch (e: IOException) {
            throw RuntimeException("fail to parse Excel file: " + e.message)
        }
    }

    fun findRegionById(regionId: Long): Region {
        val memberClient = WebClient.builder()
            .baseUrl(memberUrl).build()

        val regionResponse = memberClient
            .get()
            .uri { u ->
                u
                    .path("/api/v1/country/region/$regionId")
                    .build()
            }
            .retrieve()
            .bodyToMono(String::class.java)
            .block()
        val regionRemoteResponse = gson.fromJson(regionResponse, RegionResponse::class.java)
        return regionRemoteResponse.data
    }

    fun findProviderById(providerId: Long): Provider? {
        println(providerId)
        return providerService.findById(providerId).data
    }

    fun findBritamPayerProviderMappingByProviderId(providerId: Long): Long {
        println(providerId)
        return payerService.findPayerProviderMappingEntity(2, providerId).data!!.id
    }

    fun findPayerById(payerId: Long): Payer? {
        println(payerId)
        return if (payerService.findById(payerId).data !== null)
            payerService.findById(payerId).data
        else {
            null
        }
    }

    fun excelToPayerProviderMapping(`is`: InputStream?): List<PayerProviderMapping>? {
        return try {
            // Note: This method uses InputStream directly, so we still use XSSFWorkbook
            // For MultipartFile support, use the new parseWorkbookFromFile method
            val workbook: Workbook = XSSFWorkbook(`is`)
            var row: Row
            var cell: Cell?
            val sheet = workbook.getSheet(MAPPING)
//			val sheet = workbook.getSheet(DEVICE)
            val rows: Iterator<Row> = sheet.iterator()
            val payerProviderMappingList: MutableList<PayerProviderMapping> =
                ArrayList<PayerProviderMapping>()
            var rowNumber = 0
            val formatter = DataFormatter()

            //precision only up to 15 significant
            // digits

            while (rows.hasNext()) {
                val currentRow: Row = rows.next()

                // skip header
                if (rowNumber == 0) {
                    rowNumber++
                    continue
                }
                val cellsInRow: Iterator<Cell> = currentRow.iterator()
                val payerProviderMapping = PayerProviderMapping()
                var cellIdx = 0
                while (cellsInRow.hasNext()) {
                    val currentCell: Cell = cellsInRow.next()

                    when (cellIdx) {
//						KENGEN
                        0 -> payerProviderMapping.provider = findProviderById(currentCell.numericCellValue.toLong())!!
                        1 -> payerProviderMapping.payer = findPayerById(currentCell.numericCellValue.toLong())!!
                        2 -> payerProviderMapping.code = currentCell.toString()
                        else -> {}
                    }
                    cellIdx++
                }
                payerProviderMappingList.add(payerProviderMapping)
            }
            workbook.close()
            payerProviderMappingList
        } catch (e: IOException) {
            throw RuntimeException("fail to parse Excel file: " + e.message)
        }
    }

    fun updateBritamPayerProviderMapping(`is`: InputStream?): List<PayerProviderMapping>? {
        return try {
            // Note: This method uses InputStream directly, so we still use XSSFWorkbook
            // For MultipartFile support, use the new parseWorkbookFromFile method
            val workbook: Workbook = XSSFWorkbook(`is`)
            var row: Row
            var cell: Cell?
            val sheet = workbook.getSheet(MAPPING)
//			val sheet = workbook.getSheet(DEVICE)
            val rows: Iterator<Row> = sheet.iterator()
            val payerProviderMappingList: MutableList<PayerProviderMapping> =
                ArrayList<PayerProviderMapping>()
            var rowNumber = 0
            val formatter = DataFormatter()

            //precision only up to 15 significant
            // digits

            while (rows.hasNext()) {
                val currentRow: Row = rows.next()

                // skip header
                if (rowNumber == 0) {
                    rowNumber++
                    continue
                }
                val cellsInRow: Iterator<Cell> = currentRow.iterator()
                val payerProviderMapping = PayerProviderMapping()
                var cellIdx = 0
                while (cellsInRow.hasNext()) {
                    val currentCell: Cell = cellsInRow.next()

                    when (cellIdx) {
                        1 -> payerProviderMapping.payer = findPayerById(currentCell.numericCellValue.toLong())!!
                        2 -> payerProviderMapping.code = currentCell.numericCellValue.toString()
                            .substring(0, currentCell.numericCellValue.toString().length - 2)

                        3 -> payerProviderMapping.id =
                            findBritamPayerProviderMappingByProviderId(currentCell.numericCellValue.toLong())

                        else -> {}
                    }
                    cellIdx++
                }
                payerProviderMappingList.add(payerProviderMapping)
            }
            workbook.close()
//			payerProviderMappingList.forEach {
//				println(it.code)
//			}
            payerProviderMappingList
        } catch (e: IOException) {
            throw RuntimeException("fail to parse Excel file: " + e.message)
        }
    }

}

@JsonIgnoreProperties(ignoreUnknown = true)
data class RegionResponse(
    @JsonProperty("data")
    val data: Region,

    @JsonProperty("success")
    val success: Boolean,

    @JsonProperty("msg")
    val msg: String?
)