package net.lctafrica.membership.api.helper

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import org.springframework.web.multipart.MultipartFile


@Component
class FileFormatParserFactory(
    private val xlsxParser: ExcelXlsxParser,
    private val xlsParser: ExcelXlsParser,
    private val numbersParser: NumbersParser
) {

    private val logger = LoggerFactory.getLogger(FileFormatParserFactory::class.java)

    private val parsers = listOf(xlsxParser, xlsParser, numbersParser)

    fun getParser(file: MultipartFile): FileFormatParser {
        val filename = file.originalFilename ?: "unknown"
        val mimeType = file.contentType ?: "unknown"

        logger.debug("Finding parser for file: {} (MIME: {})", filename, mimeType)

        val parser = parsers.firstOrNull { it.canParse(file) }

        if (parser != null) {
            logger.debug("Selected parser: {} for file: {}", parser::class.simpleName, filename)
            return parser
        }

        // provide detailed error message with supported formats
        val supportedExtensions = parsers.flatMap { it.getSupportedExtensions() }.distinct()
        val supportedMimeTypes = parsers.flatMap { it.getSupportedMimeTypes() }.distinct()

        throw IllegalArgumentException(
            "Unsupported file format. File: '$filename' (MIME: '$mimeType'). " +
                    "Supported extensions: ${supportedExtensions.joinToString(", ")}. " +
                    "Supported MIME types: ${supportedMimeTypes.joinToString(", ")}"
        )
    }

    fun isSupported(file: MultipartFile): Boolean {
        return parsers.any { it.canParse(file) }
    }

    fun getSupportedExtensions(): List<String> {
        return parsers.flatMap { it.getSupportedExtensions() }.distinct()
    }


    fun getSupportedMimeTypes(): List<String> {
        return parsers.flatMap { it.getSupportedMimeTypes() }.distinct()
    }

    fun parseFile(file: MultipartFile): FileParseResult {
        val parser = getParser(file)
        val workbook = parser.parse(file)

        val format = when (parser) {
            is ExcelXlsxParser -> "xlsx"
            is ExcelXlsParser -> "xls"
            is NumbersParser -> "numbers"
            else -> "unknown"
        }

        return FileParseResult(
            workbook = workbook,
            format = format,
            originalFilename = file.originalFilename
        )
    }
}