package net.lctafrica.membership.api.helper

import org.apache.poi.ss.usermodel.Workbook
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.springframework.stereotype.Component
import org.springframework.web.multipart.MultipartFile
import java.io.IOException

@Component
class ExcelXlsxParser : FileFormatParser {
    companion object {
        private val SUPPORTED_EXTENSIONS = listOf(".xlsx")
        private val SUPPORTED_MIME_TYPES = listOf(
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
    }

    override fun canParse(file: MultipartFile): Boolean {
        val filename = file.originalFilename?.lowercase() ?: return false
        val mimeType = file.contentType

        return SUPPORTED_EXTENSIONS.any { filename.endsWith(it) } ||
                SUPPORTED_MIME_TYPES.any { it == mimeType }
    }

    override fun parse(file: MultipartFile): Workbook {
        return try {
            file.inputStream.use { inputStream ->
                XSSFWorkbook(inputStream)
            }
        } catch (e: IOException) {
            throw IllegalArgumentException("Failed to parse .xlsx file: ${e.message}", e)
        } catch (e: Exception) {
            throw IllegalArgumentException("Invalid .xlsx file format: ${e.message}", e)
        }
    }

    override fun getSupportedExtensions(): List<String> = SUPPORTED_EXTENSIONS

    override fun getSupportedMimeTypes(): List<String> = SUPPORTED_MIME_TYPES
}