package net.lctafrica.membership.api.util

import java.io.InputStream
import java.time.LocalDate
import java.util.Date
import net.lctafrica.membership.api.config.BadRequestException
import net.lctafrica.membership.api.domain.BeneficiaryType
import net.lctafrica.membership.api.domain.Gender
import net.lctafrica.membership.api.domain.StagingStatus
import net.lctafrica.membership.api.util.AppConstants.REQUIRED_FIELDS
import org.apache.poi.openxml4j.opc.OPCPackage
import org.apache.poi.ss.usermodel.DataFormatter
import org.apache.poi.ss.usermodel.Row
import org.apache.poi.xssf.eventusermodel.XSSFReader
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.springframework.stereotype.Component
import org.springframework.web.multipart.MultipartFile

@Component
class ReadExcelFile {

	val fileType: String = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
	val headers =
		arrayListOf("FLAG", "NAME", "DOB", "GENDER", "TITLE", "MEMBER NUMBER", "EMAIL", "PHONE")

	fun isExcelFormat(file: MultipartFile): Boolean {
		return file.contentType.equals(fileType)
	}
	fun validateExcelFile(file: MultipartFile) {
		if(!file.contentType.equals(fileType)){
			throw BadRequestException("File ${file.contentType} is not a valid excel format")
		}
	}

	fun read(input: InputStream): MutableMap<String, MutableList<MemberInput>> {
		val map = mutableMapOf<String, MutableList<MemberInput>>()
		val workBook = XSSFWorkbook(input)
		//val shIterator = workBook.spliterator()
		val formatter = DataFormatter()
		workBook.map {
			val cat = it.sheetName
			val members = mutableListOf<MemberInput>()
			System.out.println(it.rowIterator().hasNext())

			it.rowIterator().forEach { r ->
				if (r.rowNum != 0) {
					if (r.getCell(0) !== null && r.getCell(1) !== null && r.getCell(2).dateCellValue !== null && r.getCell(3) !== null
						&& r.getCell(4) !== null && r.getCell(5) !== null && r.getCell(7) !== null
					) {
						val tracker = formatter.formatCellValue(r.getCell(0))
						val name = r.getCell(1).stringCellValue
						val dob = r.getCell(2).dateCellValue
						val gender = r.getCell(3).stringCellValue
						val beneficiaryType = r.getCell(4).stringCellValue
						val memberNumber = r.getCell(5).stringCellValue
						val phoneNo = r.getCell(7).stringCellValue
						if(tracker.isNullOrBlank() || name.isNullOrBlank() || dob == null || gender.isNullOrBlank()
							|| beneficiaryType.isNullOrBlank() || memberNumber.isNullOrBlank() || phoneNo.isNullOrBlank()){
							throw BadRequestException(REQUIRED_FIELDS)
						}
						val member = MemberInput(
							tracker = tracker,
							name = name,
							dob = dob,
							gender = Gender.valueOf(gender.uppercase().trim()),
							title = BeneficiaryType.valueOf(beneficiaryType.uppercase().trim()),
							memberNumber = formatter.formatCellValue(r.getCell(5)),
							email = r.getCell(6, Row.MissingCellPolicy.RETURN_NULL_AND_BLANK)
								?.stringCellValue,
							phone = formatter.formatCellValue(
								r.getCell(
									7, Row
										.MissingCellPolicy.RETURN_NULL_AND_BLANK
								)
							),
							nhif = formatter.formatCellValue(
								r.getCell(
									8, Row
										.MissingCellPolicy.RETURN_NULL_AND_BLANK
								)
							),
							id = formatter.formatCellValue(
								r.getCell(
									9, Row.MissingCellPolicy
										.RETURN_NULL_AND_BLANK
								)
							),
							joinDate = getDateFromExcelRow(r,10)
							)
						members.add(member)
					}else{
						throw BadRequestException(REQUIRED_FIELDS)
					}

				}
			}
			map[cat.uppercase().trim()] = members
		}
		return map
	}

	private fun getDateFromExcelRow(row: Row?, index:Int): Date? {
		try {
			return row?.getCell(index)?.dateCellValue
		} catch (_: Exception) { }
		return null
	}

	fun getSheets(input: InputStream): MutableList<String> {
		val sheetNames = mutableListOf<String>()
		val workBook = OPCPackage.open(input)
		val reader = XSSFReader(workBook)
		val sheets = reader.sheetsData
		println(sheets.toString())
		if (sheets is XSSFReader.SheetIterator) {
			val sheetIterator: XSSFReader.SheetIterator = sheets
			while (sheetIterator.hasNext()) {
//                val next = sheetIterator.next()
				println("****************")
				println(sheetIterator.sheetName)
				sheetNames.add(sheetIterator.sheetName)
//                val sheet = reader.getSheet(sheetIterator.sheetName)
			}
		}
		return sheetNames
	}


}

data class MemberInput(
	val tracker: String,
	val name: String,
	val dob: Date,
	val gender: Gender,
	val title: BeneficiaryType,
	val memberNumber: String,
	var email: String?,
	var phone: String?,
	var nhif: String?,
	var id: String?,
	var jicEntityId: Int? = null,
	var apaEntityId: Int? = null,
	var joinDate: Date? = null

)

data class UploadBeneficiaryDto(
	val batchNo:String?=null,
	val familyNumber:String,
	val name:String,
	val dob:LocalDate?,
	val gender:Gender,
	val memberType:BeneficiaryType,
	val memberNumber:String,
	val email:String?=null,
	val phone:String?=null,
	val nhifNumber:String?=null,
	val idNumber:String?=null,
	var joinDate: LocalDate? = null,
	var policyId: Long?=null,
	var category: String?=null,
	var status: StagingStatus,
	var renewal: String?=null,
	var autoProcessBenefits: Boolean,
	var error: String? = null,
)