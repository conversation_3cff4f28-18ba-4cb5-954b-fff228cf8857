package net.lctafrica.membership.api.util

import com.itextpdf.text.BaseColor
import com.itextpdf.text.Document
import com.itextpdf.text.DocumentException
import com.itextpdf.text.Element
import com.itextpdf.text.ExceptionConverter
import com.itextpdf.text.FontFactory
import com.itextpdf.text.Image
import com.itextpdf.text.Phrase
import com.itextpdf.text.Rectangle
import com.itextpdf.text.html.WebColors
import com.itextpdf.text.pdf.ColumnText
import com.itextpdf.text.pdf.PdfPCell
import com.itextpdf.text.pdf.PdfPTable
import com.itextpdf.text.pdf.PdfPageEventHelper
import com.itextpdf.text.pdf.PdfTemplate
import com.itextpdf.text.pdf.PdfWriter

class HeaderFooterPageEvent : PdfPageEventHelper() {
    override fun onStartPage(writer: PdfWriter, document: Document?) {
        val rect: Rectangle = writer.getBoxSize("art")
        ColumnText.showTextAligned(
            writer.directContent,
            Element.ALIGN_CENTER,
            Phrase("Top Left"),
            rect.left,
            rect.top,
            0f
        )
        ColumnText.showTextAligned(
            writer.directContent,
            Element.ALIGN_CENTER,
            Phrase("Top Right"),
            rect.right,
            rect.top,
            0f
        )
    }

    override fun onEndPage(writer: PdfWriter, document: Document?) {
        val rect: Rectangle = writer.getBoxSize("art")
        ColumnText.showTextAligned(
            writer.directContent,
            Element.ALIGN_CENTER,
            Phrase("Bottom Left"),
            rect.left,
            rect.bottom,
            0f
        )
        ColumnText.showTextAligned(
            writer.directContent,
            Element.ALIGN_CENTER,
            Phrase("Bottom Right"),
            rect.right,
            rect.bottom,
            0f
        )
    }
}

class FooterPageEvent : PdfPageEventHelper() {
    var total: PdfTemplate?=null

    override fun onOpenDocument(writer: PdfWriter, document: Document?) {
        total=writer.directContent.createTemplate(30f,16f)
    }

    override fun onEndPage(writer: PdfWriter, document: Document) {
        onEndPageFooter(writer, total)
    }

    override fun onCloseDocument(writer: PdfWriter?, document: Document?) {
        val font = FontFactory.getFont(FontFactory.HELVETICA, 8f,WebColors.getRGBColor("#304254"))
        ColumnText.showTextAligned(total,Element.ALIGN_LEFT,
            Phrase(" / ${writer?.pageNumber}",font),0f,3f,0f)
    }
}

fun onEndPageFooter(writer: PdfWriter, total: PdfTemplate?) {
    val font = FontFactory.getFont(FontFactory.HELVETICA, 8f,WebColors.getRGBColor("#304254"))
    val table = PdfPTable(3)
    try {
        table.setWidths(intArrayOf(240, 200,30))
        table.totalWidth = 780.0f
        table.isLockedWidth = true
        table.horizontalAlignment = Element.ALIGN_CENTER
        table.defaultCell.fixedHeight = 50f
        table.defaultCell.border = Rectangle.NO_BORDER
        table.defaultCell.paddingTop = 5f
        table.defaultCell.borderColor = BaseColor.GRAY
        table.addCell(Phrase(printDateTime(), font))

        table.defaultCell.horizontalAlignment = Element.ALIGN_RIGHT
        table.addCell(Phrase(String.format("Page %d", writer.pageNumber), font))

        val cellPages = PdfPCell(Image.getInstance(total))
        cellPages.border = Rectangle.BOTTOM
        table.addCell(cellPages)
        table.writeSelectedRows(0, -1, 34f, 50f, writer.directContent)
    } catch (de: DocumentException) {
        throw ExceptionConverter(de)
    }
}
