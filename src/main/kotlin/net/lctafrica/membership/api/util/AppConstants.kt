package net.lctafrica.membership.api.util

object AppConstants {
    const val SAVED_SUCCESSFULLY="Saved successfully"
    const val REGISTERED_SUCCESSFULLY="Successfully registered"
    const val ALLOCATED_SUCCESSFULLY="Successfully allocated"
    const val UPLOADED_SUCCESSFULLY="Uploaded successfully"
    const val UPDATED_SUCCESSFULLY="Updated successfully"
    const val ADDED_SUCCESSFULLY="Added successfully"
    const val CREATED_SUCCESSFULLY="Created successfully"
    const val SUCCESSFULLY_MAPPED="Successfully mapped"
    const val SUCCESSFULLY_UNMAPPED="Successfully unmapped"
    const val REMOVED_SUCCESSFULLY="Removed successfully"
    const val SUCCESS="Success"
    const val REQUEST_PROCESSING="Request accepted for processing"
    const val UPLOADED_SUCCESSFULLY_P="Uploaded successfully. Processing..."
    const val REQUIRED_FIELDS="NB: Family number, Name, Date of Birth, Gender, Member Type," +
            " Member number and Phone number are required fields"
    const val LCT_LOGO_URL="https://user-images.githubusercontent.com/60912349/188652731-63ff53de-1e18-4e64-9cce-9963dce9a9d5.png"

    ///////////// Configs ///////////////
    const val REMOVE_CHILD_FROM_COVER_IF_ABOVE_AGE="REMOVE_CHILD_FROM_COVER_IF_ABOVE_AGE"
    const val REMOVE_PRINCIPAL_AND_FAMILY_FROM_COVER_IF_ABOVE_AGE="REMOVE_PRINCIPAL_AND_FAMILY_FROM_COVER_IF_ABOVE_AGE"
}