package net.lctafrica.membership.api.util

import org.slf4j.LoggerFactory
import org.slf4j.MDC
import java.time.Duration
import java.time.Instant
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong

/**
 * Performance logger for detailed operation metrics and monitoring.
 * Provides comprehensive logging for bulk operations, performance tracking, and system monitoring.
 */
class PerformanceLogger {
    private val logger = LoggerFactory.getLogger(PerformanceLogger::class.java)
    private val operationStartTimes = ConcurrentHashMap<String, Instant>()
    private val operationCounters = ConcurrentHashMap<String, AtomicLong>()

    fun logOperationStart(
        operation: String,
        recordCount: Int,
        additionalContext: Map<String, Any> = emptyMap()
    ) {
        val operationId = generateOperationId(operation)
        operationStartTimes[operationId] = Instant.now()

        // set MDC context for structured logging
        MDC.put("operation", operation)
        MDC.put("operationId", operationId)
        MDC.put("recordCount", recordCount.toString())

        additionalContext.forEach { (key, value) ->
            MDC.put(key, value.toString())
        }

        logger.info(
            "Operation started: {} with {} records. Context: {}",
            operation, recordCount, additionalContext
        )

        // Initialize counter
        operationCounters.computeIfAbsent(operationId) { AtomicLong(0) }
    }

    fun logBatchProgress(
        operationId: String,
        processed: Int,
        total: Int,
        elapsed: Duration,
        batchMetrics: BatchMetrics = BatchMetrics()
    ) {
        val progressPercent = if (total > 0) (processed.toDouble() / total * 100) else 0.0
        val throughput = if (elapsed.toMillis() > 0) (processed.toDouble() / elapsed.toSeconds()) else 0.0

        MDC.put("operationId", operationId)
        MDC.put("processed", processed.toString())
        MDC.put("total", total.toString())
        MDC.put("progressPercent", String.format("%.2f", progressPercent))
        MDC.put("throughputPerSecond", String.format("%.2f", throughput))

        logger.info(
            "Batch progress: {}/{} records ({:.2f}%) in {}ms, throughput: {:.2f} records/sec. Metrics: {}",
            processed, total, progressPercent, elapsed.toMillis(), throughput, batchMetrics
        )

        // update counter
        operationCounters[operationId]?.set(processed.toLong())
    }

    fun logPerformanceMetrics(operationId: String, summary: PerformanceMetrics) {
        val startTime = operationStartTimes[operationId]
        val actualDuration = if (startTime != null) {
            Duration.between(startTime, Instant.now())
        } else {
            Duration.ofMillis(summary.processingTimeMs)
        }

        MDC.put("operationId", operationId)
        MDC.put("totalRecords", summary.totalRecords.toString())
        MDC.put("processingTimeMs", summary.processingTimeMs.toString())
        MDC.put("recordsPerSecond", String.format("%.2f", summary.recordsPerSecond))
        MDC.put("memoryUsageMB", summary.memoryUsageMB.toString())
        MDC.put("concurrencyLevel", summary.concurrencyLevel.toString())

        logger.info(
            "Performance metrics - Records: {}, Time: {}ms, Throughput: {:.2f} rec/sec, " +
                    "Memory: {}MB, Concurrency: {}, Success Rate: {:.2f}%",
            summary.totalRecords, summary.processingTimeMs, summary.recordsPerSecond,
            summary.memoryUsageMB, summary.concurrencyLevel, summary.successRate
        )

        // log detailed breakdown if available
        if (summary.breakdown.isNotEmpty()) {
            logger.info("Performance breakdown: {}", summary.breakdown)
        }

        // clean up
        operationStartTimes.remove(operationId)
        operationCounters.remove(operationId)
        MDC.clear()
    }

    fun logAutomaticMapping(
        providerId: Long,
        payerId: Long,
        code: String,
        operationContext: MappingContext = MappingContext()
    ) {
        MDC.put("providerId", providerId.toString())
        MDC.put("payerId", payerId.toString())
        MDC.put("providerCode", code)
        MDC.put("mappingType", "automatic")

        logger.info(
            "Automatic provider mapping created: providerId={}, payerId={}, code='{}', " +
                    "rowNumber={}, batchId={}, timestamp={}",
            providerId, payerId, code, operationContext.rowNumber,
            operationContext.batchId, Instant.now()
        )

        // log additional context if provided
        if (operationContext.additionalInfo.isNotEmpty()) {
            logger.debug("Mapping context: {}", operationContext.additionalInfo)
        }

        MDC.clear()
    }

    fun logError(error: ProcessingError, context: ErrorContext = ErrorContext()) {
        MDC.put("errorType", error.type.name)
        MDC.put("rowNumber", context.rowNumber.toString())
        MDC.put("operationId", context.operationId ?: "unknown")

        if (error.providerId != null) {
            MDC.put("providerId", error.providerId.toString())
        }

        logger.error(
            "Processing error: type={}, message='{}', rowNumber={}, providerId={}, context={}",
            error.type, error.message, context.rowNumber, error.providerId, context.additionalInfo
        )

        MDC.clear()
    }

    fun logDatabaseOperation(
        operation: String,
        recordCount: Int,
        executionTimeMs: Long,
        batchSize: Int? = null
    ) {
        MDC.put("dbOperation", operation)
        MDC.put("recordCount", recordCount.toString())
        MDC.put("executionTimeMs", executionTimeMs.toString())

        if (batchSize != null) {
            MDC.put("batchSize", batchSize.toString())
        }

        val throughput = if (executionTimeMs > 0) (recordCount.toDouble() / executionTimeMs * 1000) else 0.0

        logger.info(
            "Database operation: {} - {} records in {}ms, throughput: {:.2f} rec/sec{}",
            operation, recordCount, executionTimeMs, throughput,
            if (batchSize != null) ", batch size: $batchSize" else ""
        )

        MDC.clear()
    }

    fun logMemoryMetrics() {
        val runtime = Runtime.getRuntime()
        val totalMemory = runtime.totalMemory() / (1024 * 1024)
        val freeMemory = runtime.freeMemory() / (1024 * 1024)
        val usedMemory = totalMemory - freeMemory
        val maxMemory = runtime.maxMemory() / (1024 * 1024)

        logger.info(
            "Memory metrics - Used: {}MB, Free: {}MB, Total: {}MB, Max: {}MB",
            usedMemory, freeMemory, totalMemory, maxMemory
        )
    }

    fun createPerformanceMonitor(operation: String): PerformanceMonitor {
        return PerformanceMonitor(operation, this)
    }

    private fun generateOperationId(operation: String): String {
        return "${operation}_${System.currentTimeMillis()}_${Thread.currentThread().id}"
    }
}

data class PerformanceMetrics(
    val totalRecords: Int,
    val processingTimeMs: Long,
    val recordsPerSecond: Double,
    val memoryUsageMB: Long,
    val concurrencyLevel: Int,
    val successRate: Double = 100.0,
    val breakdown: Map<String, Long> = emptyMap()
)

data class BatchMetrics(
    val batchSize: Int = 0,
    val batchNumber: Int = 0,
    val totalBatches: Int = 0,
    val memoryUsageMB: Long = 0,
    val dbConnectionsUsed: Int = 0
)

data class MappingContext(
    val rowNumber: Int = 0,
    val batchId: String? = null,
    val operationId: String? = null,
    val additionalInfo: Map<String, Any> = emptyMap()
)

data class ErrorContext(
    val rowNumber: Int = 0,
    val operationId: String? = null,
    val batchId: String? = null,
    val additionalInfo: Map<String, Any> = emptyMap()
)

data class ProcessingError(
    val type: ErrorType,
    val message: String,
    val providerId: Long? = null,
    val payerId: Long? = null,
    val exception: Throwable? = null
)

enum class ErrorType {
    PROVIDER_NOT_FOUND,
    PAYER_NOT_FOUND,
    MAPPING_FAILED,
    UPDATE_FAILED,
    VALIDATION_ERROR,
    DATABASE_ERROR,
    FILE_PROCESSING_ERROR,
    MEMORY_ERROR,
    TIMEOUT_ERROR
}

class PerformanceMonitor(
    private val operation: String,
    private val logger: PerformanceLogger
) {
    private val startTime = Instant.now()
    private val operationId = "${operation}_${System.currentTimeMillis()}"

    fun logStart(recordCount: Int, context: Map<String, Any> = emptyMap()) {
        logger.logOperationStart(operation, recordCount, context)
    }

    fun logProgress(processed: Int, total: Int, batchMetrics: BatchMetrics = BatchMetrics()) {
        val elapsed = Duration.between(startTime, Instant.now())
        logger.logBatchProgress(operationId, processed, total, elapsed, batchMetrics)
    }

    fun logCompletion(summary: PerformanceMetrics) {
        logger.logPerformanceMetrics(operationId, summary)
    }

    fun getOperationId(): String = operationId
}