package net.lctafrica.membership.api.util

import com.itextpdf.text.BaseColor
import com.itextpdf.text.Chunk
import com.itextpdf.text.Document
import com.itextpdf.text.Element
import com.itextpdf.text.FontFactory
import com.itextpdf.text.Image
import com.itextpdf.text.Paragraph
import com.itextpdf.text.Phrase
import com.itextpdf.text.Rectangle
import com.itextpdf.text.html.WebColors
import com.itextpdf.text.pdf.PdfPCell
import com.itextpdf.text.pdf.PdfPTable
import java.net.URL

object ReportUtils {
    fun reportAddLogoAndTitle(reportTitle: String, document: Document) {
        val tableHeader = PdfPTable(floatArrayOf(2f, 5f))
        val image: Image = Image.getInstance(URL(AppConstants.LCT_LOGO_URL))
        image.scaleToFit(200f, 150f)
        val imgCell = PdfPCell(image, true)
        imgCell.border = Rectangle.NO_BORDER
        tableHeader.addCell(imgCell)

        val title = PdfPCell()
        val font = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 17f, WebColors.getRGBColor("#1A2853"))
        title.phrase = Phrase(reportTitle.uppercase(), font)
        title.horizontalAlignment = Element.ALIGN_LEFT
        title.verticalAlignment = Element.ALIGN_CENTER
        title.border = Rectangle.NO_BORDER
        title.paddingTop = 30f
        title.paddingLeft = 30f
        tableHeader.addCell(title)
        document.add(tableHeader)
    }

    fun addTableInfo(tableInfo: PdfPTable, info: String, color: BaseColor,
                     horizontalAlignment:Int =Element.ALIGN_LEFT,
                     verticalAlignment:Int =Element.ALIGN_CENTER,
                     font:String=FontFactory.HELVETICA,
                     size:Float =10f,
                     paddingBottom:Float =2.5f,
                     paddingTop:Float =2.5f) {
        val infoCell = PdfPCell()
        val font = FontFactory.getFont(font, size, color)
        infoCell.phrase = Phrase(info, font)
        infoCell.horizontalAlignment = horizontalAlignment
        infoCell.verticalAlignment = verticalAlignment
        infoCell.border = Rectangle.NO_BORDER
        infoCell.paddingBottom = paddingBottom
        infoCell.paddingTop = paddingTop
        tableInfo.addCell(infoCell)
    }

    fun addTableHeaderAndInfoH(tableInfo: PdfPTable, headerText: String,info: String, colorHeader: String, colorInfo: String,
                     horizontalAlignment:Int =Element.ALIGN_LEFT,
                     verticalAlignment:Int =Element.ALIGN_CENTER,
                     fontHeader:String=FontFactory.HELVETICA,
                     fontInfo:String=FontFactory.HELVETICA,
                     size:Float =10f,
                     paddingBottom:Float =2.5f,
                     paddingTop:Float =2.5f) {

        val infoCell = PdfPCell()
        val headerFont = FontFactory.getFont(fontHeader, size, colorHeader.toItextBaseColor())
        val infoFont = FontFactory.getFont(fontInfo, size, colorInfo.toItextBaseColor())

        val p = Paragraph()
        p.add(Chunk(headerText, headerFont))
        p.add(Chunk(info, infoFont))
        infoCell.phrase = p
        infoCell.horizontalAlignment = horizontalAlignment
        infoCell.verticalAlignment = verticalAlignment
        infoCell.border = Rectangle.NO_BORDER
        infoCell.paddingBottom = paddingBottom
        infoCell.paddingTop = paddingTop
        tableInfo.addCell(infoCell)
    }

    fun addReportHeader(reportRows: Array<String>, table: PdfPTable, border:Int = Rectangle.NO_BORDER) {
        reportRows.forEach { headerTitle ->
            val header = PdfPCell()
            val headFont =
                FontFactory.getFont(FontFactory.HELVETICA, 8f, WebColors.getRGBColor("#304254"))
            header.backgroundColor = WebColors.getRGBColor("#F2F2F2") // BaseColor.WHITE
            header.horizontalAlignment = Element.ALIGN_LEFT
            header.paddingTop = 5f
            header.paddingBottom = 5f
            header.borderWidth = 1f
            header.phrase = Phrase(headerTitle, headFont)
            header.border = border
            table.addCell(header)
        }
    }

    fun reportSpacer(document: Document, space:String){
        document.add(Paragraph(space))
    }

    fun addEmptyCell(tableInfo: PdfPTable){
        val emptyCell = PdfPCell()
        emptyCell.border = Rectangle.NO_BORDER
        tableInfo.addCell(emptyCell)
    }
}