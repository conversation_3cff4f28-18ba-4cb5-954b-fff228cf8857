package net.lctafrica.membership.api.util

import net.lctafrica.membership.api.domain.*
import net.lctafrica.membership.api.dtos.*
import net.lctafrica.membership.api.helper.DateTimeUtil
import org.apache.poi.ss.usermodel.Cell
import org.apache.poi.ss.usermodel.DataFormatter
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import java.math.BigDecimal
import java.time.LocalDate
import java.time.ZoneId
import java.time.format.DateTimeFormatter


fun String?.sanitizeValue(): String? {
    val trimmed = this?.trim()
    if (trimmed.isNullOrBlank()) {
        return null
    }
    return this
}

fun Cell?.sanitizeStringCellValue(): String? {
    val cellValue = this?.stringCellValue?.trim()
    if (cellValue.isNullOrBlank()) {
        return null
    }
    return cellValue
}

fun Cell?.coerceToString(): String? {
    val formatter = DataFormatter()
    val strValue = formatter.formatCellValue(this)?.trim()

    if (strValue.isNullOrBlank()) {
        return null
    }
    return strValue
}

fun Cell?.sanitizeDateCellValue(): LocalDate? {
    return this?.dateCellValue?.toInstant()?.atZone(ZoneId.systemDefault())?.toLocalDate()
}

fun String?.sanitizeStringValue(): String? {
    val cellValue = this?.trim()
    if (cellValue.isNullOrBlank()) {
        return null
    }
    return cellValue
}

fun String?.toRenew(): Boolean? {
    if (!this.isNullOrBlank()) {
        if (this.trim().uppercase().startsWith("REN")) {
            return true
        }
    }
    return null
}

fun OnboardPrincipal.toResponseDTO(): OnboardPrincipalResponseDTO {
    val calculatedAge = getAge(this.dateOfBirth)
    val name = getFullName(this.firstName, this.middleName, this.surname)
    val inpatientCategory = getInpatientOptionFromAmount(this.inpatientPremium, isAdult = true)
    val outpatientCategory = getOutpatientOptionFromAmount(this.outpatientPremium)
    val familySize = StringBuilder().append("M + ").append(this.dependants.size).toString()

    val memberNumber = StringBuilder()
    memberNumber.append(this.growerNumber)
    memberNumber.append("-00")

    val beneficiaryName = StringBuilder()
    beneficiaryName.append(this.beneficiaries.firstOrNull()?.firstName)
    this.beneficiaries.firstOrNull()?.middleName?.let { beneficiaryName.append(" $it") }
    beneficiaryName.append(" ${this.beneficiaries.firstOrNull()?.surname}")

    val beneficiaryIdNumber = this.beneficiaries.firstOrNull()?.idNumber
    val beneficiaryMobileNumber = this.beneficiaries.firstOrNull()?.mobileNumber

    val applicationFormContextIds = this.applicationFormContextIds.map { it.value }
    val applicationFormInfoIds = this.applicationFormInfoIds.map { it.value }
    val hospitalFormContextIds = this.hospitalFormContextIds.map { it.value }
    val hospitalFormInfoIds = this.hospitalFormInfoIds.map { it.value }
    val studentIdContextIds = this.studentIdContextIds.map { it.value }
    val studentIdInfoIds = this.studentIdInfoIds.map { it.value }

    return OnboardPrincipalResponseDTO(
        id = this.id,
        name = name,
        dateOfBirth = this.dateOfBirth,
        applicationDate = this.applicationDate,
        age = calculatedAge,
        idNumber = this.idNumber,
        gender = this.gender,
        maritalStatus = this.maritalStatus,
        category = this.category,
        mobileNumber = this.mobileNumber,
        town = this.town ?: "",
        county = this.county,
        hospital = this.hospital,
        inpatientPremium = this.inpatientPremium,
        outpatientPremium = this.outpatientPremium,
        outpatientCategory = outpatientCategory,
        inpatientCategory = inpatientCategory,
        familySize = familySize,
        paymentMode = this.paymentMode,
        beneficiaryType = this.beneficiaryType,
        growerNumber = this.growerNumber,
        memberNumber = memberNumber.toString(),
        agentName = this.agent.name,
        agentNumber = this.agent.agentNumber,
        /**
         * some agents created before the system was live do not have mobile numbers
         *  however all new agents should have a mobile number
         * */
        agentMobileNumber = this.agent.mobileNumber ?: "",
        agentFactory = this.agent.factory,
        agentZone = this.agent.zone,
        beneficiaryName = beneficiaryName.toString(),
        beneficiaryIdNumber = beneficiaryIdNumber ?: "",
        beneficiaryMobileNumber = beneficiaryMobileNumber ?: "",
        createdByName = this.createdByName,
        createdByUsername = this.createdByUsername,
        premiumPayerGrowerNumber = this.premiumPayerGrowerNumber ?: "",
        applicationFormContextIds = applicationFormContextIds,
        applicationFormInfoIds = applicationFormInfoIds,
        hospitalFormContextIds = hospitalFormContextIds,
        hospitalFormInfoIds = hospitalFormInfoIds,
        studentIdContextIds = studentIdContextIds,
        studentIdInfoIds = studentIdInfoIds,
        createdAt = this.createdAt!!
    )
}

fun OnboardDependant.toResponseDTO(): OnboardDependantResponseDTO {
    val calculatedAge = getAge(this.dateOfBirth)
    val name = getFullName(this.firstName, this.middleName, this.surname)
    val inpatientCategory = getInpatientOptionFromAmount(this.inpatientPremium, isAdult = calculatedAge >= 18)
    val outpatientCategory = getOutpatientOptionFromAmount(this.outpatientPremium)
    val maritalStatus = getMaritalStatus(this.beneficiaryType)
    val familySize = StringBuilder().append("M + ").append(this.principal.dependants.size).toString()
    val mobileNumber = when (this.mobileNumber) {
        null -> this.principal.mobileNumber
        "" -> this.principal.mobileNumber
        else -> this.mobileNumber
    }

    return OnboardDependantResponseDTO(
        id = this.id,
        name = name,
        principalGrowerNumber = this.principal.growerNumber,
        memberNumber = this.memberNumber ?: "",
        dateOfBirth = this.dateOfBirth,
        applicationDate = this.principal.applicationDate,
        age = calculatedAge,
        familySize = familySize,
        idNumber = this.idNumber ?: "",
        gender = this.gender,
        mobileNumber = mobileNumber,
        hospital = this.hospital ?: this.principal.hospital,
        inpatientPremium = this.inpatientPremium,
        outpatientPremium = this.outpatientPremium,
        inpatientCategory = inpatientCategory,
        outpatientCategory = outpatientCategory,
        beneficiaryType = this.beneficiaryType,
        maritalStatus = maritalStatus,
        town = this.principal.town ?: "",
        county = this.principal.county,
        agentNumber = this.principal.agent.agentNumber,
        agentName = this.principal.agent.name,
        /**
         * some agents created before the system was live do not have mobile numbers
         *  however all new agents should have a mobile number
         * */
        agentMobileNumber = this.principal.agent.mobileNumber ?: "",
        agentFactory = this.principal.agent.factory,
        agentZone = this.principal.agent.zone,
        createdByName = this.principal.createdByName,
        createdByUsername = this.principal.createdByUsername,
        createdAt = this.createdAt!!
    )
}

fun OnboardBeneficiary.toResponseDTO(): OnboardBeneficiaryResponseDTO {
    val calculatedAge = getAge(this.dateOfBirth)
    val name = getFullName(this.firstName, this.middleName, this.surname)
    return OnboardBeneficiaryResponseDTO(
        id = this.id,
        name = name,
        dateOfBirth = this.dateOfBirth,
        age = calculatedAge,
        idNumber = this.idNumber,
        gender = this.gender,
        mobileNumber = this.mobileNumber,
        createdAt = this.createdAt!!
    )
}

fun OnboardDocument.toResponseDTO(): OnboardDocumentResponseDTO {
    return OnboardDocumentResponseDTO(
        id = this.id,
        type = this.type,
        url = this.url
    )
}

fun OnboardBenefit.toResponseDTO(): OnboardBenefitResponseDTO {
    return OnboardBenefitResponseDTO(
        id = this.id,
        benefitType = this.benefitType,
        benefitLimit = this.benefitLimit,
        benefitUtilization = this.benefitUtilization!!,
        balance = this.benefitLimit - this.benefitUtilization,
        benefitMonthlyAmount = this.benefitMonthlyAmount!!,
        createdAt = this.createdAt!!,
    )
}

fun OnboardAgent.toResponseDTO(): OnboardAgentResponseDTO {
    return OnboardAgentResponseDTO(
        id = this.id,
        name = name,
        agentNumber = this.agentNumber,
        mobileNumber = this.mobileNumber,
        idNumber = this.idNumber ?: "",
        factory = this.factory,
        emailAddress = this.emailAddress ?: "",
        dateOfBirth = this.dateOfBirth ?: "",
        registrationDate = this.registrationDate ?: "",
        zone = this.zone,
        createdAt = this.createdAt!!,
    )
}

fun Int.toBigDecimal(): BigDecimal = BigDecimal(this)

fun <T> T.toResponse(status: HttpStatus): ResponseEntity<T> =
    ResponseEntity(this, status)

fun UserRolesAuditLog.toResponseDTO(): UserRolesAuditLogResponse {
    val createdAtDateFormatter = DateTimeFormatter.ofPattern("MMMM-yyyy")

    return UserRolesAuditLogResponse(
        id = this.id,
        userId = this.userId,
        actionedBy = this.actionedBy,
        eventTimestamp = DateTimeUtil.formatWithOrdinalSuffix(this.eventTimestamp, useSuperscript = true),
        addedRoles = this.addedRoles.toSet(),
        removedRoles = this.removedRoles.toSet(),
        createdAt = this.createdAt.format(createdAtDateFormatter)
    )
}
