package net.lctafrica.membership.api.util

import kotlinx.coroutines.coroutineScope
import org.slf4j.LoggerFactory

/**
 * Generic batch processor for chunked database operations to optimize performance
 * and reduce memory usage when processing large datasets.
 */
class BatchProcessor<T> {
    private val logger = LoggerFactory.getLogger(BatchProcessor::class.java)

    suspend fun processBatch(
        items: List<T>,
        batchSize: Int = 100,
        processor: suspend (List<T>) -> BatchResult
    ): List<BatchResult> = coroutineScope {

        if (items.isEmpty()) {
            logger.debug("No items to process in batch")
            return@coroutineScope emptyList()
        }

        val totalItems = items.size
        val totalBatches = (totalItems + batchSize - 1) / batchSize

        logger.info(
            "Starting batch processing: {} items in {} batches of size {}",
            totalItems, totalBatches, batchSize
        )

        val results = mutableListOf<BatchResult>()
        var processedItems = 0

        items.chunked(batchSize).forEachIndexed { batchIndex, batch ->
            try {
                logger.debug(
                    "Processing batch {}/{} with {} items",
                    batchIndex + 1, totalBatches, batch.size
                )

                val startTime = System.currentTimeMillis()
                val result = processor(batch)
                val processingTime = System.currentTimeMillis() - startTime

                processedItems += batch.size

                logger.debug(
                    "Batch {}/{} completed in {}ms. Progress: {}/{} items",
                    batchIndex + 1, totalBatches, processingTime, processedItems, totalItems
                )

                results.add(
                    result.copy(
                        batchNumber = batchIndex + 1,
                        processingTimeMs = processingTime
                    )
                )

            } catch (e: Exception) {
                logger.error(
                    "Error processing batch {}/{}: {}",
                    batchIndex + 1, totalBatches, e.message, e
                )

                results.add(
                    BatchResult(
                        batchNumber = batchIndex + 1,
                        processedCount = 0,
                        successCount = 0,
                        errorCount = batch.size,
                        processingTimeMs = 0,
                        success = false,
                        errorMessage = e.message
                    )
                )
            }
        }

        val totalProcessed = results.sumOf { it.processedCount }
        val totalSuccess = results.sumOf { it.successCount }
        val totalErrors = results.sumOf { it.errorCount }
        val totalTime = results.sumOf { it.processingTimeMs }

        logger.info(
            "Batch processing completed: {}/{} items processed, {} successful, {} errors in {}ms",
            totalProcessed, totalItems, totalSuccess, totalErrors, totalTime
        )

        results
    }

    suspend fun processBatchWithDynamicSize(
        items: List<T>,
        batchSizeCalculator: (List<T>) -> Int,
        processor: suspend (List<T>) -> BatchResult
    ): List<BatchResult> {
        val optimalBatchSize = batchSizeCalculator(items)
        logger.debug("Calculated optimal batch size: {} for {} items", optimalBatchSize, items.size)
        return processBatch(items, optimalBatchSize, processor)
    }
}

data class BatchResult(
    val batchNumber: Int = 0,
    val processedCount: Int,
    val successCount: Int,
    val errorCount: Int,
    val processingTimeMs: Long,
    val success: Boolean = true,
    val errorMessage: String? = null,
    val metadata: Map<String, Any> = emptyMap()
)

fun List<BatchResult>.aggregate(): BatchSummary {
    return BatchSummary(
        totalBatches = this.size,
        totalProcessed = this.sumOf { it.processedCount },
        totalSuccess = this.sumOf { it.successCount },
        totalErrors = this.sumOf { it.errorCount },
        totalProcessingTimeMs = this.sumOf { it.processingTimeMs },
        averageBatchTimeMs = if (this.isNotEmpty()) this.map { it.processingTimeMs }.average() else 0.0,
        successfulBatches = this.count { it.success },
        failedBatches = this.count { !it.success }
    )
}

data class BatchSummary(
    val totalBatches: Int,
    val totalProcessed: Int,
    val totalSuccess: Int,
    val totalErrors: Int,
    val totalProcessingTimeMs: Long,
    val averageBatchTimeMs: Double,
    val successfulBatches: Int,
    val failedBatches: Int
) {
    val successRate: Double = if (totalProcessed > 0) (totalSuccess.toDouble() / totalProcessed) * 100 else 0.0
    val throughputPerSecond: Double =
        if (totalProcessingTimeMs > 0) (totalProcessed.toDouble() / totalProcessingTimeMs) * 1000 else 0.0
}