package net.lctafrica.membership.api.util

import org.keycloak.representations.idm.UserRepresentation
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest

fun paginateList(data: List<UserRepresentation>, page: Int, size: Int): Page<UserRepresentation> {
    val adjustedPage = if (page > 0) page - 1 else 0
    val start = adjustedPage * size
    val end = minOf(start + size, data.size)

    val pagedData = if (start < data.size) data.subList(start, end) else emptyList()

    return PageImpl(pagedData, PageRequest.of(adjustedPage, size), data.size.toLong())
}
