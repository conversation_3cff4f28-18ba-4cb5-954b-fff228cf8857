package net.lctafrica.membership.api.util

import net.lctafrica.membership.api.dtos.massactions.ProviderBulkUpdateDto
import net.lctafrica.membership.api.service.IPayerService
import net.lctafrica.membership.api.service.IValidationService
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

/**
 * Performance optimizer that checks for duplicate values before performing database updates.
 * This significantly improves performance by skipping unnecessary database operations.
 */
@Component
class ValueComparisonOptimizer(
    private val payerService: IPayerService,
    private val validationService: IValidationService
) {
    private val logger = LoggerFactory.getLogger(ValueComparisonOptimizer::class.java)

    suspend fun filterCodeUpdatesForChanges(
        payerId: Long,
        codeUpdates: List<Pair<ProviderBulkUpdateDto, Int>>
    ): CodeUpdateFilterResult {
        val startTime = System.currentTimeMillis()
        val actualUpdatesNeeded = mutableListOf<Pair<ProviderBulkUpdateDto, Int>>()
        val skippedDuplicates = mutableListOf<Pair<ProviderBulkUpdateDto, Int>>()
        val errors = mutableListOf<CodeUpdateError>()

        logger.info("Filtering {} code updates for actual changes", codeUpdates.size)

        codeUpdates.forEach { (update, index) ->
            try {
                val mappingResult = payerService.findPayerProviderMappingEntity(payerId, update.providerId)

                if (mappingResult.success && mappingResult.data != null) {
                    val existingCode = mappingResult.data.code
                    val newCode = update.code!!

                    if (existingCode == newCode) {
                        logger.debug(
                            "Skipping code update for provider {} - value unchanged: '{}'",
                            update.providerId, existingCode
                        )
                        skippedDuplicates.add(update to index)
                    } else {
                        logger.debug(
                            "Code update needed for provider {}: '{}' -> '{}'",
                            update.providerId, existingCode, newCode
                        )
                        actualUpdatesNeeded.add(update to index)
                    }
                } else {
                    // mapping not found, this shouldn't happen at this stage but handle gracefully
                    logger.warn(
                        "Mapping not found for provider {} and payer {} during code comparison",
                        update.providerId, payerId
                    )
                    errors.add(
                        CodeUpdateError(
                            update = update,
                            index = index,
                            reason = "Mapping not found during comparison"
                        )
                    )
                }
            } catch (e: Exception) {
                logger.error(
                    "Error comparing code values for provider {}: {}",
                    update.providerId, e.message, e
                )
                errors.add(
                    CodeUpdateError(
                        update = update,
                        index = index,
                        reason = "Error during comparison: ${e.message}"
                    )
                )
            }
        }

        val processingTime = System.currentTimeMillis() - startTime
        val optimizationRatio = if (codeUpdates.isNotEmpty()) {
            (skippedDuplicates.size.toDouble() / codeUpdates.size) * 100
        } else 0.0

        logger.info(
            "Code update filtering completed: {} total, {} need updates, {} skipped ({}% optimization), " +
                    "{} errors in {}ms",
            codeUpdates.size, actualUpdatesNeeded.size, skippedDuplicates.size,
            String.format("%.1f", optimizationRatio), errors.size, processingTime
        )

        return CodeUpdateFilterResult(
            actualUpdatesNeeded = actualUpdatesNeeded,
            skippedDuplicates = skippedDuplicates,
            errors = errors,
            processingTimeMs = processingTime,
            optimizationRatio = optimizationRatio
        )
    }

    suspend fun filterNameUpdatesForChanges(
        nameUpdates: List<Pair<ProviderBulkUpdateDto, Int>>
    ): NameUpdateFilterResult {
        val startTime = System.currentTimeMillis()
        val actualUpdatesNeeded = mutableListOf<Pair<ProviderBulkUpdateDto, Int>>()
        val skippedDuplicates = mutableListOf<Pair<ProviderBulkUpdateDto, Int>>()
        val errors = mutableListOf<NameUpdateError>()

        logger.info("Filtering {} name updates for actual changes", nameUpdates.size)

        nameUpdates.forEach { (update, index) ->
            try {
                val provider = validationService.validateAndGetProvider(update.providerId)

                if (provider != null) {
                    val existingName = provider.name
                    val newName = update.providerName!!

                    if (existingName == newName) {
                        logger.debug(
                            "Skipping name update for provider {} - value unchanged: '{}'",
                            update.providerId, existingName
                        )
                        skippedDuplicates.add(update to index)
                    } else {
                        logger.debug(
                            "Name update needed for provider {}: '{}' -> '{}'",
                            update.providerId, existingName, newName
                        )
                        actualUpdatesNeeded.add(update to index)
                    }
                } else {
                    logger.warn(
                        "Provider {} not found during name comparison",
                        update.providerId
                    )
                    errors.add(
                        NameUpdateError(
                            update = update,
                            index = index,
                            reason = "Provider not found during comparison"
                        )
                    )
                }
            } catch (e: Exception) {
                logger.error(
                    "Error comparing name values for provider {}: {}",
                    update.providerId, e.message, e
                )
                errors.add(
                    NameUpdateError(
                        update = update,
                        index = index,
                        reason = "Error during comparison: ${e.message}"
                    )
                )
            }
        }

        val processingTime = System.currentTimeMillis() - startTime
        val optimizationRatio = if (nameUpdates.isNotEmpty()) {
            (skippedDuplicates.size.toDouble() / nameUpdates.size) * 100
        } else 0.0

        logger.info(
            "Name update filtering completed: {} total, {} need updates, {} skipped ({}% optimization), " +
                    "{} errors in {}ms",
            nameUpdates.size, actualUpdatesNeeded.size, skippedDuplicates.size,
            String.format("%.1f", optimizationRatio), errors.size, processingTime
        )

        return NameUpdateFilterResult(
            actualUpdatesNeeded = actualUpdatesNeeded,
            skippedDuplicates = skippedDuplicates,
            errors = errors,
            processingTimeMs = processingTime,
            optimizationRatio = optimizationRatio
        )
    }

    /**
     * Check if a provider is already mapped to payer to skip duplicate mapping creation
     */
    suspend fun filterMappingCreationsForDuplicates(
        payerId: Long,
        mappingCreations: List<Pair<ProviderBulkUpdateDto, Int>>
    ): MappingFilterResult {
        val startTime = System.currentTimeMillis()
        val actualMappingsNeeded = mutableListOf<Pair<ProviderBulkUpdateDto, Int>>()
        val skippedExisting = mutableListOf<Pair<ProviderBulkUpdateDto, Int>>()
        val errors = mutableListOf<MappingFilterError>()

        logger.info("Filtering {} mapping creations for duplicates", mappingCreations.size)

        mappingCreations.forEach { (update, index) ->
            try {
                val existingMappingResult = payerService.findPayerProviderMappingEntity(payerId, update.providerId)

                if (existingMappingResult.success && existingMappingResult.data != null) {
                    val existingCode = existingMappingResult.data.code
                    logger.debug(
                        "Skipping mapping creation for provider {} - already mapped with code: '{}'",
                        update.providerId, existingCode
                    )
                    skippedExisting.add(update to index)
                } else {
                    logger.debug(
                        "Mapping creation needed for provider {} with code: '{}'",
                        update.providerId, update.code
                    )
                    actualMappingsNeeded.add(update to index)
                }
            } catch (e: Exception) {
                logger.error(
                    "Error checking existing mapping for provider {}: {}",
                    update.providerId, e.message, e
                )
                errors.add(
                    MappingFilterError(
                        update = update,
                        index = index,
                        reason = "Error during mapping check: ${e.message}"
                    )
                )
            }
        }

        val processingTime = System.currentTimeMillis() - startTime
        val optimizationRatio = if (mappingCreations.isNotEmpty()) {
            (skippedExisting.size.toDouble() / mappingCreations.size) * 100
        } else 0.0

        logger.info(
            "Mapping creation filtering completed: {} total, {} need creation, {} skipped ({}% optimization), " +
                    "{} errors in {}ms",
            mappingCreations.size, actualMappingsNeeded.size, skippedExisting.size,
            String.format("%.1f", optimizationRatio), errors.size, processingTime
        )

        return MappingFilterResult(
            actualMappingsNeeded = actualMappingsNeeded,
            skippedExisting = skippedExisting,
            errors = errors,
            processingTimeMs = processingTime,
            optimizationRatio = optimizationRatio
        )
    }


    suspend fun batchCompareCodeValues(
        payerId: Long,
        codeUpdates: List<Pair<ProviderBulkUpdateDto, Int>>,
        batchSize: Int = 100
    ): CodeUpdateFilterResult {
        val startTime = System.currentTimeMillis()
        val allActualUpdates = mutableListOf<Pair<ProviderBulkUpdateDto, Int>>()
        val allSkippedDuplicates = mutableListOf<Pair<ProviderBulkUpdateDto, Int>>()
        val allErrors = mutableListOf<CodeUpdateError>()

        logger.info(
            "Starting batch code comparison for {} updates in batches of {}",
            codeUpdates.size, batchSize
        )

        codeUpdates.chunked(batchSize).forEachIndexed { batchIndex, batch ->
            val batchResult = filterCodeUpdatesForChanges(payerId, batch)

            allActualUpdates.addAll(batchResult.actualUpdatesNeeded)
            allSkippedDuplicates.addAll(batchResult.skippedDuplicates)
            allErrors.addAll(batchResult.errors)

            logger.debug(
                "Batch {} completed: {} updates needed, {} skipped, {} errors",
                batchIndex + 1, batchResult.actualUpdatesNeeded.size,
                batchResult.skippedDuplicates.size, batchResult.errors.size
            )
        }

        val totalProcessingTime = System.currentTimeMillis() - startTime
        val overallOptimizationRatio = if (codeUpdates.isNotEmpty()) {
            (allSkippedDuplicates.size.toDouble() / codeUpdates.size) * 100
        } else 0.0

        logger.info(
            "Batch code comparison completed: {} total processed, {} updates needed, " +
                    "{} skipped ({}% optimization) in {}ms",
            codeUpdates.size, allActualUpdates.size, allSkippedDuplicates.size,
            String.format("%.1f", overallOptimizationRatio), totalProcessingTime
        )

        return CodeUpdateFilterResult(
            actualUpdatesNeeded = allActualUpdates,
            skippedDuplicates = allSkippedDuplicates,
            errors = allErrors,
            processingTimeMs = totalProcessingTime,
            optimizationRatio = overallOptimizationRatio
        )
    }
}


data class CodeUpdateFilterResult(
    val actualUpdatesNeeded: List<Pair<ProviderBulkUpdateDto, Int>>,
    val skippedDuplicates: List<Pair<ProviderBulkUpdateDto, Int>>,
    val errors: List<CodeUpdateError>,
    val processingTimeMs: Long,
    val optimizationRatio: Double
)

data class NameUpdateFilterResult(
    val actualUpdatesNeeded: List<Pair<ProviderBulkUpdateDto, Int>>,
    val skippedDuplicates: List<Pair<ProviderBulkUpdateDto, Int>>,
    val errors: List<NameUpdateError>,
    val processingTimeMs: Long,
    val optimizationRatio: Double
)

data class MappingFilterResult(
    val actualMappingsNeeded: List<Pair<ProviderBulkUpdateDto, Int>>,
    val skippedExisting: List<Pair<ProviderBulkUpdateDto, Int>>,
    val errors: List<MappingFilterError>,
    val processingTimeMs: Long,
    val optimizationRatio: Double
)

data class CodeUpdateError(
    val update: ProviderBulkUpdateDto,
    val index: Int,
    val reason: String
)

data class NameUpdateError(
    val update: ProviderBulkUpdateDto,
    val index: Int,
    val reason: String
)

data class MappingFilterError(
    val update: ProviderBulkUpdateDto,
    val index: Int,
    val reason: String
)