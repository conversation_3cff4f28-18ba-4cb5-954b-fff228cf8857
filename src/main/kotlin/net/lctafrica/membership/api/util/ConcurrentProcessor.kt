package net.lctafrica.membership.api.util

import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.sync.Semaphore
import kotlinx.coroutines.sync.withPermit
import org.slf4j.LoggerFactory
import kotlin.math.min

class ConcurrentProcessor {

    private val logger = LoggerFactory.getLogger(ConcurrentProcessor::class.java)

    suspend fun <T, R> processWithConcurrency(
        items: List<T>,
        concurrencyLevel: Int = 10,
        processor: suspend (T) -> ProcessResult<R>
    ): List<ProcessResult<R>> = coroutineScope {

        if (items.isEmpty()) {
            logger.debug("No items to process concurrently")
            return@coroutineScope emptyList()
        }

        val actualConcurrency = min(concurrencyLevel, items.size)
        logger.info(
            "Starting concurrent processing: {} items with concurrency level {}",
            items.size, actualConcurrency
        )

        val startTime = System.currentTimeMillis()

        // create a semaphore to limit concurrency
        val semaphore = Semaphore(actualConcurrency)

        val results = items.mapIndexed { index, item ->
            async {
                semaphore.withPermit {
                    try {
                        logger.debug("Processing item {} of {}", index + 1, items.size)
                        val itemStartTime = System.currentTimeMillis()
                        val result = processor(item)
                        val itemProcessingTime = System.currentTimeMillis() - itemStartTime

                        result.copy(
                            itemIndex = index,
                            processingTimeMs = itemProcessingTime
                        )
                    } catch (e: Exception) {
                        logger.error(
                            "Error processing item {} of {}: {}",
                            index + 1, items.size, e.message, e
                        )

                        ProcessResult<R>(
                            itemIndex = index,
                            success = false,
                            data = null,
                            errorMessage = e.message,
                            processingTimeMs = 0
                        )
                    }
                }
            }
        }.awaitAll()

        val totalTime = System.currentTimeMillis() - startTime
        val successCount = results.count { result -> result.success }
        val errorCount = results.count { result -> !result.success }

        logger.info(
            "Concurrent processing completed: {}/{} items successful, {} errors in {}ms",
            successCount, items.size, errorCount, totalTime
        )

        results
    }

    suspend fun <T> processBatchesConcurrently(
        items: List<T>,
        batchSize: Int = 100,
        concurrencyLevel: Int = 5,
        processor: suspend (List<T>) -> BatchResult
    ): List<BatchResult> = coroutineScope {

        if (items.isEmpty()) {
            logger.debug("No items to process in concurrent batches")
            return@coroutineScope emptyList()
        }

        val batches = items.chunked(batchSize)
        val actualConcurrency = min(concurrencyLevel, batches.size)

        logger.info(
            "Starting concurrent batch processing: {} items in {} batches with concurrency {}",
            items.size, batches.size, actualConcurrency
        )

        val semaphore = Semaphore(actualConcurrency)
        val startTime = System.currentTimeMillis()

        val results = batches.mapIndexed { batchIndex, batch ->
            async {
                semaphore.withPermit {
                    try {
                        logger.debug(
                            "Processing batch {} of {} with {} items",
                            batchIndex + 1, batches.size, batch.size
                        )

                        val batchStartTime = System.currentTimeMillis()
                        val result = processor(batch)
                        val batchProcessingTime = System.currentTimeMillis() - batchStartTime

                        result.copy(
                            batchNumber = batchIndex + 1,
                            processingTimeMs = batchProcessingTime
                        )
                    } catch (e: Exception) {
                        logger.error(
                            "Error processing batch {} of {}: {}",
                            batchIndex + 1, batches.size, e.message, e
                        )

                        BatchResult(
                            batchNumber = batchIndex + 1,
                            processedCount = 0,
                            successCount = 0,
                            errorCount = batch.size,
                            processingTimeMs = 0,
                            success = false,
                            errorMessage = e.message
                        )
                    }
                }
            }
        }.awaitAll()

        val totalTime = System.currentTimeMillis() - startTime
        val summary = results.aggregate()

        logger.info(
            "Concurrent batch processing completed: {} batches, {}/{} items successful in {}ms",
            results.size, summary.totalSuccess, summary.totalProcessed, totalTime
        )

        results
    }

    suspend fun <T, R> processWithAdaptiveConcurrency(
        items: List<T>,
        initialConcurrency: Int = 5,
        maxConcurrency: Int = 20,
        processor: suspend (T) -> ProcessResult<R>
    ): List<ProcessResult<R>> {

        val availableProcessors = Runtime.getRuntime().availableProcessors()
        val optimalConcurrency = min(
            maxConcurrency,
            min(initialConcurrency * availableProcessors, items.size)
        )

        logger.info(
            "Using adaptive concurrency: {} (available processors: {}, max: {})",
            optimalConcurrency, availableProcessors, maxConcurrency
        )

        return processWithConcurrency(items, optimalConcurrency, processor)
    }
}

data class ProcessResult<T>(
    val itemIndex: Int = 0,
    val success: Boolean,
    val data: T? = null,
    val errorMessage: String? = null,
    val processingTimeMs: Long = 0,
    val metadata: Map<String, Any> = emptyMap()
)


fun <T> List<ProcessResult<T>>.aggregateResults(): ProcessSummary {
    return ProcessSummary(
        totalItems = this.size,
        successfulItems = this.count { it.success },
        failedItems = this.count { !it.success },
        totalProcessingTimeMs = this.sumOf { it.processingTimeMs },
        averageItemTimeMs = if (this.isNotEmpty()) this.map { it.processingTimeMs }.average() else 0.0
    )
}

data class ProcessSummary(
    val totalItems: Int,
    val successfulItems: Int,
    val failedItems: Int,
    val totalProcessingTimeMs: Long,
    val averageItemTimeMs: Double
) {
    val successRate: Double = if (totalItems > 0) (successfulItems.toDouble() / totalItems) * 100 else 0.0
    val throughputPerSecond: Double =
        if (totalProcessingTimeMs > 0) (totalItems.toDouble() / totalProcessingTimeMs) * 1000 else 0.0
}