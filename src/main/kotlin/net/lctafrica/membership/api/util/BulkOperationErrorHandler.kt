package net.lctafrica.membership.api.util

import net.lctafrica.membership.api.dtos.massactions.ProviderMappingError
import net.lctafrica.membership.api.dtos.massactions.ProviderMappingErrorType
import net.lctafrica.membership.api.dtos.massactions.ProviderUpdateError
import net.lctafrica.membership.api.dtos.massactions.ProviderUpdateErrorType
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

/**
 * Specialized error handler for bulk operations with comprehensive error management,
 * partial success handling, and recovery strategies.
 */
@Component
class BulkOperationErrorHandler {
    private val logger = LoggerFactory.getLogger(BulkOperationErrorHandler::class.java)
    private val errorRecoveryHandler = ErrorRecoveryHandler()

    suspend fun <T> handleProviderUpdateWithRetry(
        operation: suspend () -> T,
        context: ProviderUpdateContext,
        maxRetries: Int = 3
    ): BulkOperationResult<T> {
        return try {
            val recoveryResult = errorRecoveryHandler.executeWithRetry(
                operation = operation,
                maxRetries = maxRetries,
                retryPredicate = { exception -> isRetryableProviderError(exception, context) }
            )

            when (recoveryResult) {
                is RecoveryResult.Success -> {
                    if (recoveryResult.attemptsUsed > 0) {
                        logger.info(
                            "Provider update succeeded after {} retries for providerId={}",
                            recoveryResult.attemptsUsed, context.providerId
                        )
                    }
                    BulkOperationResult.success(recoveryResult.result)
                }

                is RecoveryResult.Failure -> {
                    val error = createProviderUpdateError(recoveryResult.exception, context)
                    logger.error(
                        "Provider update failed after {} attempts for providerId={}: {}",
                        recoveryResult.attemptsUsed, context.providerId, recoveryResult.reason
                    )
                    BulkOperationResult.failure(error)
                }
            }
        } catch (e: Exception) {
            val error = createProviderUpdateError(e, context)
            logger.error("Unexpected error in provider update for providerId={}: {}", context.providerId, e.message, e)
            BulkOperationResult.failure(error)
        }
    }


    suspend fun <T> handleProviderMappingWithRetry(
        operation: suspend () -> T,
        context: ProviderMappingContext,
        maxRetries: Int = 3
    ): BulkOperationResult<T> {
        return try {
            val recoveryResult = errorRecoveryHandler.executeWithRetry(
                operation = operation,
                maxRetries = maxRetries,
                retryPredicate = { exception -> isRetryableMappingError(exception, context) }
            )

            when (recoveryResult) {
                is RecoveryResult.Success -> {
                    if (recoveryResult.attemptsUsed > 0) {
                        logger.info(
                            "Provider mapping succeeded after {} retries for providerId={}, payerId={}",
                            recoveryResult.attemptsUsed, context.providerId, context.payerId
                        )
                    }
                    BulkOperationResult.success(recoveryResult.result)
                }

                is RecoveryResult.Failure -> {
                    val error = createProviderMappingError(recoveryResult.exception, context)
                    logger.error(
                        "Provider mapping failed after {} attempts for providerId={}, payerId={}: {}",
                        recoveryResult.attemptsUsed, context.providerId, context.payerId, recoveryResult.reason
                    )
                    BulkOperationResult.failure(error)
                }
            }
        } catch (e: Exception) {
            val error = createProviderMappingError(e, context)
            logger.error(
                "Unexpected error in provider mapping for providerId={}, payerId={}: {}",
                context.providerId, context.payerId, e.message, e
            )
            BulkOperationResult.failure(error)
        }
    }


    suspend fun <T, R> processBatchWithPartialSuccess(
        items: List<T>,
        processor: suspend (T) -> R,
        errorContext: String = "batch-operation"
    ): BatchPartialSuccessResult<R> {
        val partialResult = errorRecoveryHandler.processWithPartialSuccess(
            items = items,
            processor = processor,
            errorHandler = { item, exception ->
                logger.warn("Error processing item in {}: {}", errorContext, exception.message)

                // determine action based on the error type
                when {
                    isCriticalError(exception) -> PartialFailureAction.FAIL_FAST
                    isSystemError(exception) -> PartialFailureAction.SKIP_REMAINING
                    else -> PartialFailureAction.CONTINUE
                }
            }
        )

        return BatchPartialSuccessResult(
            successfulResults = partialResult.successfulResults,
            errors = partialResult.errors,
            skippedCount = partialResult.skippedCount,
            totalProcessed = partialResult.totalProcessed,
            successRate = partialResult.successRate,
            processingMode = partialResult.processingMode
        )
    }

    /**
     * Aggregate errors from multiple batch operations
     */
    fun aggregateBatchErrors(
        batchResults: List<BatchResult>,
        operationContext: String
    ): BulkErrorAggregation {
        val errorSources = batchResults.mapIndexed { index, result ->
            val errors = extractErrorsFromBatchResult(result, index)
            ErrorSource(
                sourceName = "batch-$index",
                errors = errors,
                context = mapOf(
                    "batchNumber" to index,
                    "operation" to operationContext,
                    "processedCount" to result.processedCount,
                    "successCount" to result.successCount
                )
            )
        }.filter { it.errors.isNotEmpty() }

        val aggregation = errorRecoveryHandler.aggregateErrors(errorSources)
        val strategy = errorRecoveryHandler.createRecoveryStrategy(aggregation)

        logger.info(
            "Aggregated errors from {} batches in {}: {} total errors, strategy: {}",
            batchResults.size, operationContext, aggregation.summary.totalErrors, strategy.action
        )

        return BulkErrorAggregation(
            errorAggregation = aggregation,
            recoveryStrategy = strategy,
            operationContext = operationContext
        )
    }


    private fun createProviderUpdateError(
        exception: Throwable,
        context: ProviderUpdateContext
    ): ProviderUpdateError {
        val errorType = classifyProviderUpdateError(exception)
        val errorMessage = buildDetailedErrorMessage(exception, context)

        return ProviderUpdateError(
            rowNumber = context.rowNumber,
            providerId = context.providerId,
            errorMessage = errorMessage,
            errorType = errorType
        )
    }


    private fun createProviderMappingError(
        exception: Throwable,
        context: ProviderMappingContext
    ): ProviderMappingError {
        val errorType = classifyProviderMappingError(exception)
        val errorMessage = buildDetailedErrorMessage(exception, context)

        return ProviderMappingError(
            rowNumber = context.rowNumber,
            providerId = context.providerId,
            payerId = context.payerId,
            errorMessage = errorMessage,
            errorType = errorType
        )
    }


    private fun classifyProviderUpdateError(exception: Throwable): ProviderUpdateErrorType {
        return when {
            exception.message?.contains("provider not found", ignoreCase = true) == true ->
                ProviderUpdateErrorType.PROVIDER_NOT_FOUND

            exception.message?.contains("not mapped", ignoreCase = true) == true ->
                ProviderUpdateErrorType.PROVIDER_NOT_MAPPED_TO_PAYER

            exception.message?.contains("validation", ignoreCase = true) == true ->
                ProviderUpdateErrorType.VALIDATION_ERROR

            exception.message?.contains("timeout", ignoreCase = true) == true ->
                ProviderUpdateErrorType.TIMEOUT_ERROR

            exception.message?.contains("permission", ignoreCase = true) == true ->
                ProviderUpdateErrorType.INSUFFICIENT_PERMISSIONS

            exception.message?.contains("concurrent", ignoreCase = true) == true ->
                ProviderUpdateErrorType.CONCURRENT_MODIFICATION

            exception is java.sql.SQLException -> ProviderUpdateErrorType.DATABASE_ERROR
            exception is org.springframework.dao.DataAccessException -> ProviderUpdateErrorType.DATABASE_ERROR
            exception is IllegalArgumentException -> ProviderUpdateErrorType.INVALID_DATA
            exception is java.net.SocketTimeoutException -> ProviderUpdateErrorType.TIMEOUT_ERROR
            else -> ProviderUpdateErrorType.UPDATE_FAILED
        }
    }

    private fun classifyProviderMappingError(exception: Throwable): ProviderMappingErrorType {
        return when {
            exception.message?.contains("provider not found", ignoreCase = true) == true ->
                ProviderMappingErrorType.PROVIDER_NOT_FOUND

            exception.message?.contains("payer not found", ignoreCase = true) == true ->
                ProviderMappingErrorType.PAYER_NOT_FOUND

            exception.message?.contains("already mapped", ignoreCase = true) == true ->
                ProviderMappingErrorType.ALREADY_MAPPED

            exception.message?.contains("duplicate", ignoreCase = true) == true ->
                ProviderMappingErrorType.DUPLICATE_MAPPING

            exception.message?.contains("validation", ignoreCase = true) == true ->
                ProviderMappingErrorType.VALIDATION_ERROR

            exception.message?.contains("timeout", ignoreCase = true) == true ->
                ProviderMappingErrorType.TIMEOUT_ERROR

            exception.message?.contains("permission", ignoreCase = true) == true ->
                ProviderMappingErrorType.INSUFFICIENT_PERMISSIONS

            exception.message?.contains("concurrent", ignoreCase = true) == true ->
                ProviderMappingErrorType.CONCURRENT_MODIFICATION

            exception is java.sql.SQLException -> ProviderMappingErrorType.DATABASE_ERROR
            exception is org.springframework.dao.DataAccessException -> ProviderMappingErrorType.DATABASE_ERROR
            exception is IllegalArgumentException -> ProviderMappingErrorType.INVALID_DATA
            exception is java.net.SocketTimeoutException -> ProviderMappingErrorType.TIMEOUT_ERROR
            else -> ProviderMappingErrorType.MAPPING_FAILED
        }
    }

    private fun buildDetailedErrorMessage(exception: Throwable, context: Any): String {
        val baseMessage = exception.message ?: "Unknown error occurred"
        val contextInfo = when (context) {
            is ProviderUpdateContext -> "providerId=${context.providerId}, row=${context.rowNumber}"
            is ProviderMappingContext -> "providerId=${context.providerId}, payerId=${context.payerId}, row=${context.rowNumber}"
            else -> "context=$context"
        }

        return "$baseMessage ($contextInfo)"
    }


    private fun isRetryableProviderError(exception: Throwable, context: ProviderUpdateContext): Boolean {
        return when {
            exception is java.sql.SQLException -> true
            exception is java.net.SocketTimeoutException -> true
            exception is org.springframework.dao.DataAccessException -> true
            exception.message?.contains("timeout", ignoreCase = true) == true -> true
            exception.message?.contains("connection", ignoreCase = true) == true -> true
            exception.message?.contains("temporary", ignoreCase = true) == true -> true
            else -> false
        }
    }

    private fun isRetryableMappingError(exception: Throwable, context: ProviderMappingContext): Boolean {
        return when {
            exception is java.sql.SQLException -> true
            exception is java.net.SocketTimeoutException -> true
            exception is org.springframework.dao.DataAccessException -> true
            exception.message?.contains("timeout", ignoreCase = true) == true -> true
            exception.message?.contains("connection", ignoreCase = true) == true -> true
            exception.message?.contains("temporary", ignoreCase = true) == true -> true
            // don't retry if already mapped - that's a business logic issue
            exception.message?.contains("already mapped", ignoreCase = true) == true -> false
            else -> false
        }
    }


    private fun isCriticalError(exception: Throwable): Boolean {
        return when {
            exception is OutOfMemoryError -> true
            exception is StackOverflowError -> true
            exception.message?.contains("system error", ignoreCase = true) == true -> true
            exception.message?.contains("fatal", ignoreCase = true) == true -> true
            else -> false
        }
    }

    private fun isSystemError(exception: Throwable): Boolean {
        return when {
            exception is java.sql.SQLException && exception.message?.contains(
                "connection",
                ignoreCase = true
            ) == true -> true

            exception.message?.contains("database unavailable", ignoreCase = true) == true -> true
            exception.message?.contains("service unavailable", ignoreCase = true) == true -> true
            else -> false
        }
    }

    private fun extractErrorsFromBatchResult(result: BatchResult, batchIndex: Int): List<ProcessingError> {
        val errors = mutableListOf<ProcessingError>()

        if (!result.success && result.errorMessage != null) {
            errors.add(
                ProcessingError(
                    type = ErrorType.UPDATE_FAILED,
                    message = result.errorMessage
                )
            )
        }

        // extract additional errors from metadata if available
        val metadataErrors = result.metadata["errors"] as? List<*>
        metadataErrors?.forEach { error ->
            when (error) {
                is ProviderUpdateError -> {
                    errors.add(
                        ProcessingError(
                            type = mapProviderUpdateErrorType(error.errorType),
                            message = error.errorMessage,
                            providerId = error.providerId
                        )
                    )
                }

                is ProviderMappingError -> {
                    errors.add(
                        ProcessingError(
                            type = mapProviderMappingErrorType(error.errorType),
                            message = error.errorMessage,
                            providerId = error.providerId,
                            payerId = error.payerId
                        )
                    )
                }
            }
        }

        return errors
    }


    private fun mapProviderUpdateErrorType(errorType: ProviderUpdateErrorType): ErrorType {
        return when (errorType) {
            ProviderUpdateErrorType.PROVIDER_NOT_FOUND -> ErrorType.PROVIDER_NOT_FOUND
            ProviderUpdateErrorType.DATABASE_ERROR -> ErrorType.DATABASE_ERROR
            ProviderUpdateErrorType.TIMEOUT_ERROR -> ErrorType.TIMEOUT_ERROR
            ProviderUpdateErrorType.VALIDATION_ERROR -> ErrorType.VALIDATION_ERROR
            else -> ErrorType.UPDATE_FAILED
        }
    }

    private fun mapProviderMappingErrorType(errorType: ProviderMappingErrorType): ErrorType {
        return when (errorType) {
            ProviderMappingErrorType.PROVIDER_NOT_FOUND -> ErrorType.PROVIDER_NOT_FOUND
            ProviderMappingErrorType.PAYER_NOT_FOUND -> ErrorType.PAYER_NOT_FOUND
            ProviderMappingErrorType.DATABASE_ERROR -> ErrorType.DATABASE_ERROR
            ProviderMappingErrorType.TIMEOUT_ERROR -> ErrorType.TIMEOUT_ERROR
            ProviderMappingErrorType.VALIDATION_ERROR -> ErrorType.VALIDATION_ERROR
            else -> ErrorType.MAPPING_FAILED
        }
    }
}

data class ProviderUpdateContext(
    val providerId: Long,
    val rowNumber: Int,
    val payerId: Long? = null,
    val code: String? = null,
    val providerName: String? = null,
    val operationId: String? = null
)

data class ProviderMappingContext(
    val providerId: Long,
    val payerId: Long,
    val rowNumber: Int,
    val code: String? = null,
    val operationId: String? = null
)

sealed class BulkOperationResult<T> {
    data class Success<T>(val result: T) : BulkOperationResult<T>()
    data class Failure<T>(val error: Any) : BulkOperationResult<T>()

    companion object {
        fun <T> success(result: T) = Success(result)
        fun <T> failure(error: Any) = Failure<T>(error)
    }
}

data class BatchPartialSuccessResult<T>(
    val successfulResults: List<T>,
    val errors: List<ProcessingError>,
    val skippedCount: Int,
    val totalProcessed: Int,
    val successRate: Double,
    val processingMode: ProcessingMode
)

data class BulkErrorAggregation(
    val errorAggregation: ErrorAggregation,
    val recoveryStrategy: RecoveryStrategy,
    val operationContext: String
)
