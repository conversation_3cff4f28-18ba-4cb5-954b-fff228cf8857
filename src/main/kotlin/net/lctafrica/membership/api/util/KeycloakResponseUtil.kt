package net.lctafrica.membership.api.util

import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import javax.ws.rs.WebApplicationException
import javax.ws.rs.core.Response

object KeycloakResponseUtil {
    /**
     * Reads the Response object, confirms that it returns a 201 created and parses the ID from the location
     * It always assumes the ID is the last segment of the URI
     *
     * @param response The JAX-RS Response received
     * @return The String ID portion of the URI
     * @throws WebApplicationException if the response is not a 201 Created
     */
    @Throws(WebApplicationException::class)
    fun getCreatedId(response: Response): String? {
        val location = response.location
        if (response.statusInfo != Response.Status.CREATED) {
            val statusInfo = response.statusInfo
            val contentType = response.getHeaderString(HttpHeaders.CONTENT_TYPE)
            var errorMessage = "Create method returned status " +
                    statusInfo.reasonPhrase + " (Code: " + statusInfo.statusCode + "); " +
                    "expected status: Created (201)."
            if (MediaType.APPLICATION_JSON.equals(contentType)) {
                // try to add actual server error message to the exception message
                try {
                    val responseBody = response.readEntity(Map::class.java)
                    if (responseBody != null && responseBody.containsKey("errorMessage")) {
                        errorMessage += " errorMessage: " + responseBody.get("errorMessage")
                    }
                } catch (ignored: Exception) {
                    // ignore if we couldn't parse
                }
            }

            throw WebApplicationException(errorMessage, response)
        }
        if (location == null) {
            return null
        }
        val path = location.path
        return path.substring(path.lastIndexOf('/') + 1)
    }

    fun getErrorMessage(response: Response, myErrorMsg: String? = "Unknown Error"): String? {
        val responseBody = response.readEntity(Map::class.java)
        return if (responseBody != null && responseBody.containsKey("errorMessage")) {
            "${responseBody.get("errorMessage")}"
        } else {
            myErrorMsg
        }
    }

    /**
     * Parses detailed error information from Keycloak response for user creation errors
     */
    fun parseUserCreationError(response: Response, username: String, email: String): String {
        return try {
            val responseBody = response.readEntity(Map::class.java)

            when (response.status) {
                409 -> {
                    val errorMessage = responseBody?.get("errorMessage")?.toString()?.lowercase()
                    when {
                        errorMessage?.contains("username") == true -> "Username '$username' already exists"
                        errorMessage?.contains("email") == true -> "Email '$email' already exists"
                        errorMessage?.contains("user exists") == true -> {
                            val error = responseBody.get("error")?.toString()?.lowercase()
                            when {
                                error?.contains("username") == true -> "Username '$username' already exists"
                                error?.contains("email") == true -> "Email '$email' already exists"
                                else -> "User with username '$username' or email '$email' already exists"
                            }
                        }

                        else -> "User with username '$username' or email '$email' already exists"
                    }
                }

                400 -> {
                    val errorMessage = responseBody?.get("errorMessage")?.toString()
                    val error = responseBody?.get("error")?.toString()
                    val errorDescription = responseBody?.get("error_description")?.toString()

                    val fullErrorText =
                        listOfNotNull(errorMessage, error, errorDescription)
                            .joinToString(" ").lowercase()

                    when {
                        fullErrorText.contains("email") && fullErrorText.contains("invalid") ->
                            "Invalid email format: '$email'"

                        fullErrorText.contains("username") && fullErrorText.contains("invalid") ->
                            "Invalid username format: '$username'"

                        fullErrorText.contains("password") ->
                            "Password does not meet requirements"

                        fullErrorText.contains("firstname") || fullErrorText.contains("first name") ->
                            "First name is missing or invalid"

                        fullErrorText.contains("lastname") || fullErrorText.contains("last name") ->
                            "Last name is missing or invalid"

                        errorMessage != null -> errorMessage
                        else -> "Invalid user data provided. Please check all required fields."
                    }
                }

                else -> {
                    val errorMessage = responseBody?.get("errorMessage")?.toString()
                    errorMessage ?: "Failed to create user. HTTP status: ${response.status}"
                }
            }
        } catch (e: Exception) {
            // fallback to basic error messages if parsing fails
            when (response.status) {
                409 -> "User with username '$username' or email '$email' already exists"
                400 -> "Invalid user data provided. Please check username, email format, and password requirements"
                else -> "Failed to create user."
            }
        }
    }
}