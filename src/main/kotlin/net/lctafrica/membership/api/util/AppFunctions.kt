package net.lctafrica.membership.api.util

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.itextpdf.text.BaseColor
import com.itextpdf.text.Element
import com.itextpdf.text.FontFactory
import com.itextpdf.text.Phrase
import com.itextpdf.text.Rectangle
import com.itextpdf.text.html.WebColors
import com.itextpdf.text.pdf.PdfPCell
import io.netty.handler.ssl.SslContextBuilder
import io.netty.handler.ssl.util.InsecureTrustManagerFactory
import java.math.BigDecimal
import java.text.DateFormat
import java.text.ParseException
import java.text.SimpleDateFormat
import java.time.Duration
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.Period
import java.time.format.DateTimeFormatter
import java.util.Date
import java.util.regex.Pattern
import net.lctafrica.membership.api.domain.ChangeLogType
import net.lctafrica.membership.api.domain.OnboardDependantBeneficiaryType
import net.lctafrica.membership.api.dtos.CustomPaging
import net.lctafrica.membership.api.dtos.FilterBeneficiariesDto
import net.lctafrica.membership.api.dtos.ReportType
import org.springframework.data.domain.Page
import org.springframework.http.client.reactive.ReactorClientHttpConnector
import reactor.netty.http.client.HttpClient


fun <T, D> customPaging(list: List<T>, page: Page<D>): CustomPaging<T> {
    return CustomPaging(
        content = list,
        pageable = page.pageable,
        empty = page.isEmpty,
        first = page.isFirst,
        last = page.isLast,
        number = page.number,
        numberOfElements = page.numberOfElements,
        size = page.size,
        sort = page.sort,
        totalElements = page.totalElements,
        totalPages = page.totalPages
    )
}

private val FORMATS = arrayOf(
    "yyyy-MM-dd'T'HH:mm:ss'Z'", "yyyy-MM-dd'T'HH:mm:ssZ",
    "yyyy-MM-dd'T'HH:mm:ss", "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
    "yyyy-MM-dd'T'HH:mm:ss.SSSZ", "yyyy-MM-dd HH:mm:ss",
    "MM/dd/yyyy HH:mm:ss", "MM/dd/yyyy'T'HH:mm:ss.SSS'Z'",
    "MM/dd/yyyy'T'HH:mm:ss.SSSZ", "MM/dd/yyyy'T'HH:mm:ss.SSS",
    "MM/dd/yyyy'T'HH:mm:ssZ", "MM/dd/yyyy'T'HH:mm:ss",
    "yyyy:MM:dd HH:mm:ss", "yyyyMMdd", "d-MMM-yyyy"
)

fun convertToDateFormat(dateStr: String?, toFormat: String): String {
    dateStr?.let { dt ->
        for (format in FORMATS) {
            try {
                val originalFormat: DateFormat = SimpleDateFormat(format)
                val targetFormat: DateFormat = SimpleDateFormat(toFormat)
                val date: Date = originalFormat.parse(dt)
                return targetFormat.format(date)
            } catch (e: ParseException) {
                //do nada
            }
        }
    }
    return ""
}

fun dbDateFormat(dateStr: String?): LocalDate? {
    val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
    return if (dateStr.isNullOrBlank()) null else LocalDate.parse(dateStr, formatter)
}

fun dbDateFormat(dateStr: String?, format: String): LocalDate? {
    try {
        val formatter = DateTimeFormatter.ofPattern(format)
        return if (dateStr.isNullOrBlank()) null else LocalDate.parse(dateStr, formatter)
    } catch (e: Exception) {
        return null
    }
}

fun dateNow(): String {
    val formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy")
    val dateTimeNow = LocalDateTime.now()
    return dateTimeNow.format(formatter)
}

fun displayNullCheck(string: String?): String {
    return string ?: " "
}

val membershipReportHeader = arrayOf(
    "MEMBER NUMBER",
    "MEMBER NAME",
    "RELATIONSHIP",
    "GENDER",
    "OTHER NUMBER",
    "DOB",
    "EMAIL",
    "FAMILY SIZE",
    "PHONE NUMBER",
    "CATEGORY",
    "CATEGORY DESCRIPTION",
    "SCHEME NAME",
    "DATE ADDED",
    "EXIT DATE",
    "ID NUMBER",
    "MEMBER STATUS",
    "POLICY START DATE",
    "POLICY END DATE",
    "BIO CAPTURE DATE",
    "MEMBER BIOMETRIC STATUS"
)

val membershipEditReportHeader = arrayOf(
    "MEMBER NUMBER",
    "OTHER NUMBER",
    "MEMBER NAME",
    "RELATIONSHIP",
    "GENDER",
    "DOB",
    "PHONE NUMBER",
    "SCHEME NAME",
    "MEMBER STATUS",
    "ACTION",
    "EDIT DATE",
    "REASON FOR CHANGE",
    "USER"
)

val membershipStatusReportHeader = arrayOf(
    "MEMBER NUMBER",
    "MEMBER NAME",
    "RELATIONSHIP",
    "GENDER",
    "DOB",
    "CATEGORY",
    "SCHEME NAME",
    "JOIN DATE",
    "EXIT DATE",
    "MEMBER STATUS",
    "POLICY START DATE",
    "POLICY END DATE",
    "STATUS DATE CHANGE",
    "STATUS CHANGE REASON",
    "USER"
)

val membershipCategoryChangeReportHeader = arrayOf(
    "MEMBER NUMBER",
    "MEMBER NAME",
    "RELATIONSHIP",
    "GENDER",
    "OTHER NUMBER",
    "DOB",
    "ID NUMBER",
    "EMAIL",
    "FAMILY SIZE",
    "PHONE NUMBER",
    "SCHEME NAME",
    "ACTION",
    "PREVIOUS CATEGORY",
    "CURRENT CATEGORY",
    "USER"
)

val membershipBiometricReportHeader = arrayOf(
    "MEMBER NUMBER", "MEMBER NAME", "RELATIONSHIP", "GENDER", "OTHER NUMBER", "DOB",
    "SCHEME NAME", "BIO CAPTURE DATE", "BIOMETRIC STATUS"
)


fun getReportHeader(reportType: ReportType): Array<String> {
    return when (reportType) {
        ReportType.MEMBERSHIP_STATUS -> {
            membershipStatusReportHeader
        }

        ReportType.MEMBERSHIP_EDITS -> {
            membershipEditReportHeader
        }

        ReportType.CATEGORY_CHANGE -> {
            membershipCategoryChangeReportHeader
        }

        ReportType.BIOMETRIC_CAPTURE -> {
            membershipBiometricReportHeader
        }

        else -> {
            membershipReportHeader
        }
    }
}

fun getReportType(dto: FilterBeneficiariesDto): ReportType {
    return dto.reportType
        ?: when {
            dto.changeLogType == ChangeLogType.BIOMETRICS_UPDATE -> {
                ReportType.BIOMETRIC_CAPTURE
            }

            dto.changeLogType == ChangeLogType.CATEGORY_UPDATE -> {
                ReportType.CATEGORY_CHANGE
            }

            dto.changeLogType == ChangeLogType.MEMBER_UPDATE -> {
                ReportType.MEMBERSHIP_EDITS
            }

            dto.changeLogType == ChangeLogType.MEMBERSTATUS_UPDATE -> {
                ReportType.MEMBERSHIP_STATUS
            }

            else -> {
                ReportType.MEMBERSHIP_REPORT
            }
        }
}

fun getReportTypePDF(dto: FilterBeneficiariesDto): ReportType {
    return dto.reportType
        ?: when {
            dto.changeLogType == ChangeLogType.BIOMETRICS_UPDATE -> {
                ReportType.BIOMETRIC_CAPTURE
            }

            (dto.changeLogType == ChangeLogType.MEMBER_UPDATE)
                    || (dto.changeLogType == ChangeLogType.CATEGORY_UPDATE)
                    || (dto.changeLogType == ChangeLogType.MEMBERSTATUS_UPDATE) -> {
                ReportType.MEMBERSHIP_EDITS
            }

            else -> {
                ReportType.MEMBERSHIP_REPORT
            }
        }
}

val dateFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy")
val dateFormatter2 = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")
fun formatDate(date: LocalDate?): String? {
    var dateStr: String? = null
    date?.let {
        dateStr = it.format(dateFormatter)
    }
    return dateStr
}

fun formatDate(date: LocalDateTime?): String? {
    var dateStr: String? = null
    date?.let {
        dateStr = it.format(dateFormatter2)
    }
    return dateStr
}

fun printDateTime(): String {
    val dateTimeNow = LocalDateTime.now()
    return "Printed At: ${dateTimeNow.format(dateFormatter2)}"
}

fun getAge(dob: LocalDate): Int {
    return Period.between(
        dob,
        LocalDate.now()
    ).years
}

fun dateExpired(endDate: LocalDate?): Boolean {
    if (endDate != null) {
        if (LocalDate.now().compareTo(endDate) <= 0) {
            return false
        }
    }
    return true
}

fun dateCommenced(startDate: LocalDate?): Boolean {
    if (startDate != null) {
        if (LocalDate.now().compareTo(startDate) >= 0) {
            return true
        }
    }
    return false
}

val localDateFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("dd/MMMM/yyyy")
fun formatPolicyDate(localDate: LocalDate): String {
    return localDate.format(localDateFormatter)
}

fun PdfPCell.addDefaultFormat(value: String?): PdfPCell {
    val font = FontFactory.getFont(FontFactory.HELVETICA, 8f, WebColors.getRGBColor("#61758A"))
    this.phrase = Phrase(value, font)
    this.phrase.font = font
    this.paddingBottom = 5f
    this.paddingTop = 5f
    this.verticalAlignment = Element.ALIGN_MIDDLE
    this.horizontalAlignment = Element.ALIGN_LEFT
    this.border = Rectangle.NO_BORDER or Rectangle.TOP
    this.borderColor = BaseColor.GRAY
    return this
}


fun String.toItextBaseColor(): BaseColor {
    return WebColors.getRGBColor(this)
}

fun Any.toJsonString(): String? {
    val mapper = ObjectMapper()
    mapper.registerModule(JavaTimeModule())
    return mapper.writeValueAsString(this)
}

fun phoneValidation(phoneNo: String): Boolean {
    val regexPattern = "^[0-9+]{9,13}\$"
    return Pattern.compile(regexPattern)
        .matcher(phoneNo)
        .matches()
}

fun emailValidation(emailAddress: String): Boolean {
    val regexPattern = "^(.+)@(\\S+)$"
    return Pattern.compile(regexPattern)
        .matcher(emailAddress)
        .matches()
}

fun findFamilyNumber(memberNo: String): String {
    val regex = "[-/\\\\]".toRegex()
    val splitter = memberNo.split(regex)
    return if (splitter.size > 1) {
        splitter.first()
    } else {
        memberNo.substring(0, memberNo.lastIndex - 2)
    }
}

fun unifyPhoneNumber(phoneNo: String?): String {
    if (phoneNo.isNullOrBlank()) return ""
    return phoneNo.substring(phoneNo.length - 9, phoneNo.length)
}

fun getFileNameWithNoExtension(fileName: String?): String {
    return fileName?.substring(0, fileName.lastIndexOf(".")) ?: ""
}

fun getFileExtension(fileName: String?): String {
    return fileName?.substring(fileName.lastIndexOf(".")) ?: ""
}

fun validateFullName(fullName: String?): Boolean {
    if (fullName.isNullOrBlank()) {
        return false
    }
    val splitter = fullName.trim().split(" ")
    return splitter.size > 1
}

fun validateDateNotAfter(date: LocalDate?): Boolean {
    if (date != null) {
        return !date.isAfter(LocalDate.now())
    }
    return false
}

fun calculateOnboardInpatientLimit(premiumPaid: BigDecimal): BigDecimal {
    val daysMultiplier = 30.toBigDecimal()
    return when (premiumPaid) {
        55.toBigDecimal() -> 1000.toBigDecimal().times(daysMultiplier)
        75.toBigDecimal() -> 1500.toBigDecimal().times(daysMultiplier)
        95.toBigDecimal() -> 2000.toBigDecimal().times(daysMultiplier)
        115.toBigDecimal() -> 2500.toBigDecimal().times(daysMultiplier)
        130.toBigDecimal() -> 3000.toBigDecimal().times(daysMultiplier)
        150.toBigDecimal() -> 4000.toBigDecimal().times(daysMultiplier)
        180.toBigDecimal() -> 5000.toBigDecimal().times(daysMultiplier)
        270.toBigDecimal() -> 7500.toBigDecimal().times(daysMultiplier)
        350.toBigDecimal() -> 10000.toBigDecimal().times(daysMultiplier)
        else -> premiumPaid * 10.toBigDecimal()
    }
}


fun getInpatientOptionFromAmount(amount: BigDecimal, isAdult: Boolean): String {
    return if (isAdult) {
        when {
            amount.compareTo(BigDecimal(55)) == 0 -> "I"
            amount.compareTo(BigDecimal(75)) == 0 -> "II"
            amount.compareTo(BigDecimal(95)) == 0 -> "III"
            amount.compareTo(BigDecimal(115)) == 0 -> "IV"
            amount.compareTo(BigDecimal(130)) == 0 -> "V"
            amount.compareTo(BigDecimal(150)) == 0 -> "VI"
            amount.compareTo(BigDecimal(180)) == 0 -> "VII"
            amount.compareTo(BigDecimal(270)) == 0 -> "VIII"
            amount.compareTo(BigDecimal(350)) == 0 -> "IX"
            else -> "Option X"
        }
    } else {
        when {
            amount.compareTo(BigDecimal(40)) == 0 -> "I"
            amount.compareTo(BigDecimal(50)) == 0 -> "II"
            amount.compareTo(BigDecimal(65)) == 0 -> "III"
            amount.compareTo(BigDecimal(80)) == 0 -> "IV"
            amount.compareTo(BigDecimal(90)) == 0 -> "V"
            amount.compareTo(BigDecimal(105)) == 0 -> "VI"
            amount.compareTo(BigDecimal(130)) == 0 -> "VII"
            amount.compareTo(BigDecimal(195)) == 0 -> "VIII"
            amount.compareTo(BigDecimal(260)) == 0 -> "IX"
            else -> "Option X"
        }
    }
}

fun getOutpatientOptionFromAmount(amount: BigDecimal): String {
    return when {
        amount.compareTo(BigDecimal(210)) == 0 -> "BIRTH TO 74 YEARS"
        else -> "EXISTING OVER 75 YEARS"
    }
}

fun getMaritalStatus(beneficiaryType: OnboardDependantBeneficiaryType): String {
    return if (beneficiaryType == OnboardDependantBeneficiaryType.SPOUSE) {
        "MARRIED"
    } else {
        "SINGLE"
    }
}

fun getFullName(firstName: String, middleName: String? = null, surname: String): String {
    val fullName = StringBuilder()
    fullName.append(firstName)
    middleName?.let {
        fullName.append(" $it")
    }
    fullName.append(" $surname")
    return fullName.toString()
}

fun getClientHttpConnector() : ReactorClientHttpConnector {
    val sslContext = SslContextBuilder
        .forClient()
        .trustManager(InsecureTrustManagerFactory.INSTANCE)
        .build()
    val httpClient = HttpClient.create().secure { t -> t.sslContext(sslContext) }.responseTimeout(Duration.ofMillis(600000))
    return ReactorClientHttpConnector(httpClient)
}