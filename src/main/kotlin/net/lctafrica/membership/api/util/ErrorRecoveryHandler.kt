package net.lctafrica.membership.api.util

import kotlinx.coroutines.delay
import org.slf4j.LoggerFactory
import kotlin.math.min
import kotlin.math.pow
import kotlin.random.Random

/**
 * Comprehensive error recovery handler with retry logic, partial success handling,
 * and error aggregation for bulk operations.
 */
class ErrorRecoveryHandler {
    private val logger = LoggerFactory.getLogger(ErrorRecoveryHandler::class.java)

    companion object {
        const val DEFAULT_MAX_RETRIES = 3
        const val DEFAULT_BASE_DELAY_MS = 1000L
        const val DEFAULT_MAX_DELAY_MS = 30000L
        const val DEFAULT_BACKOFF_MULTIPLIER = 2.0
        const val DEFAULT_JITTER_FACTOR = 0.1
    }

    suspend fun <T> executeWithRetry(
        operation: suspend () -> T,
        maxRetries: Int = DEFAULT_MAX_RETRIES,
        baseDelayMs: Long = DEFAULT_BASE_DELAY_MS,
        maxDelayMs: Long = DEFAULT_MAX_DELAY_MS,
        backoffMultiplier: Double = DEFAULT_BACKOFF_MULTIPLIER,
        jitterFactor: Double = DEFAULT_JITTER_FACTOR,
        retryPredicate: (Throwable) -> Boolean = ::isRetryableError
    ): RecoveryResult<T> {
        var lastException: Throwable? = null
        var attempt = 0

        while (attempt <= maxRetries) {
            try {
                val result = operation()

                if (attempt > 0) {
                    logger.info(
                        "Operation succeeded after {} retries. Total attempts: {}",
                        attempt, attempt + 1
                    )
                }

                return RecoveryResult.success(result, attempt)

            } catch (e: Throwable) {
                lastException = e
                attempt++

                if (attempt > maxRetries) {
                    logger.error(
                        "Operation failed after {} attempts. Final error: {}",
                        attempt, e.message, e
                    )
                    break
                }

                if (!retryPredicate(e)) {
                    logger.error(
                        "Non-retryable error encountered on attempt {}: {}",
                        attempt, e.message, e
                    )
                    return RecoveryResult.failure(e, attempt - 1, "Non-retryable error")
                }

                val delayMs = calculateBackoffDelay(
                    attempt - 1, baseDelayMs, maxDelayMs, backoffMultiplier, jitterFactor
                )

                logger.warn(
                    "Operation failed on attempt {} ({}), retrying in {}ms. Error: {}",
                    attempt, maxRetries + 1, delayMs, e.message
                )

                delay(delayMs)
            }
        }

        return RecoveryResult.failure(
            lastException ?: RuntimeException("Unknown error"),
            maxRetries,
            "Max retries exceeded"
        )
    }

    suspend fun <T, R> processWithPartialSuccess(
        items: List<T>,
        processor: suspend (T) -> R,
        errorHandler: (T, Throwable) -> PartialFailureAction = { _, _ -> PartialFailureAction.CONTINUE }
    ): PartialSuccessResult<R> {
        val results = mutableListOf<R>()
        val errors = mutableListOf<ProcessingError>()
        val skipped = mutableListOf<T>()
        var stopProcessing = false

        logger.info("Starting partial success processing for {} items", items.size)

        items.forEachIndexed { index, item ->
            if (stopProcessing) {
                skipped.add(item)
                return@forEachIndexed
            }

            try {
                val result = processor(item)
                results.add(result)

                logger.debug("Successfully processed item {} of {}", index + 1, items.size)

            } catch (e: Throwable) {
                logger.warn(
                    "Error processing item {} of {}: {}",
                    index + 1, items.size, e.message
                )

                val error = ProcessingError(
                    type = classifyError(e),
                    message = e.message ?: "Unknown error",
                    exception = e
                )
                errors.add(error)

                when (errorHandler(item, e)) {
                    PartialFailureAction.CONTINUE -> {
                        logger.debug("Continuing processing after error on item {}", index + 1)
                    }

                    PartialFailureAction.SKIP_REMAINING -> {
                        logger.warn("Skipping remaining items after error on item {}", index + 1)
                        stopProcessing = true
                    }

                    PartialFailureAction.FAIL_FAST -> {
                        logger.error("Failing fast after error on item {}", index + 1)
                        return PartialSuccessResult(
                            successfulResults = results,
                            errors = errors,
                            skippedItems = items.drop(index + 1),
                            totalProcessed = index + 1,
                            processingMode = ProcessingMode.FAIL_FAST
                        )
                    }
                }
            }
        }

        val summary = PartialSuccessResult(
            successfulResults = results,
            errors = errors,
            skippedItems = skipped,
            totalProcessed = items.size - skipped.size,
            processingMode = if (stopProcessing) ProcessingMode.SKIP_REMAINING else ProcessingMode.CONTINUE_ON_ERROR
        )

        logger.info(
            "Partial success processing completed: {}/{} successful, {} errors, {} skipped",
            results.size, items.size, errors.size, skipped.size
        )

        return summary
    }

    fun aggregateErrors(
        errorSources: List<ErrorSource>,
        maxErrorsToReport: Int = 100
    ): ErrorAggregation {
        val allErrors = mutableListOf<AggregatedError>()
        val errorsByType = mutableMapOf<ErrorType, MutableList<AggregatedError>>()
        val errorsBySource = mutableMapOf<String, MutableList<AggregatedError>>()

        errorSources.forEach { source ->
            source.errors.forEach { error ->
                val aggregatedError = AggregatedError(
                    originalError = error,
                    source = source.sourceName,
                    timestamp = System.currentTimeMillis(),
                    context = source.context
                )

                allErrors.add(aggregatedError)

                errorsByType.computeIfAbsent(error.type) { mutableListOf() }.add(aggregatedError)
                errorsBySource.computeIfAbsent(source.sourceName) { mutableListOf() }.add(aggregatedError)
            }
        }

        // sort errors by timestamp (the most recent first)
        val sortedErrors = allErrors.sortedByDescending { it.timestamp }

        val errorSummary = ErrorSummary(
            totalErrors = allErrors.size,
            errorsByType = errorsByType.mapValues { it.value.size },
            errorsBySource = errorsBySource.mapValues { it.value.size },
            mostCommonErrorType = errorsByType.maxByOrNull { it.value.size }?.key,
            criticalErrors = allErrors.count { isCriticalErrorType(it.originalError.type) },
            recoverableErrors = allErrors.count { isRetryableError(it.originalError.exception) }
        )

        logger.info(
            "Error aggregation completed: {} total errors from {} sources. " +
                    "Critical: {}, Recoverable: {}, Most common: {}",
            errorSummary.totalErrors, errorSources.size, errorSummary.criticalErrors,
            errorSummary.recoverableErrors, errorSummary.mostCommonErrorType
        )

        return ErrorAggregation(
            allErrors = sortedErrors.take(maxErrorsToReport),
            summary = errorSummary,
            truncated = sortedErrors.size > maxErrorsToReport
        )
    }


    fun createRecoveryStrategy(errorAggregation: ErrorAggregation): RecoveryStrategy {
        val summary = errorAggregation.summary

        return when {
            summary.criticalErrors > summary.totalErrors * 0.5 -> {
                RecoveryStrategy(
                    action = RecoveryAction.ABORT_OPERATION,
                    reason = "Too many critical errors (${summary.criticalErrors}/${summary.totalErrors})",
                    retryRecommended = false
                )
            }

            summary.recoverableErrors > summary.totalErrors * 0.8 -> {
                RecoveryStrategy(
                    action = RecoveryAction.RETRY_WITH_BACKOFF,
                    reason = "Most errors are recoverable (${summary.recoverableErrors}/${summary.totalErrors})",
                    retryRecommended = true,
                    suggestedRetryDelay = calculateAdaptiveDelay(summary.totalErrors)
                )
            }

            summary.totalErrors < 10 -> {
                RecoveryStrategy(
                    action = RecoveryAction.CONTINUE_WITH_LOGGING,
                    reason = "Low error count (${summary.totalErrors})",
                    retryRecommended = false
                )
            }

            else -> {
                RecoveryStrategy(
                    action = RecoveryAction.PARTIAL_RETRY,
                    reason = "Mixed error types, partial retry recommended",
                    retryRecommended = true,
                    suggestedRetryDelay = DEFAULT_BASE_DELAY_MS
                )
            }
        }
    }

    private fun calculateBackoffDelay(
        attempt: Int,
        baseDelayMs: Long,
        maxDelayMs: Long,
        multiplier: Double,
        jitterFactor: Double
    ): Long {
        val exponentialDelay = (baseDelayMs * multiplier.pow(attempt)).toLong()
        val cappedDelay = min(exponentialDelay, maxDelayMs)

        // add jitter to prevent thundering herd
        val jitter = (cappedDelay * jitterFactor * Random.nextDouble()).toLong()

        return cappedDelay + jitter
    }

    private fun calculateAdaptiveDelay(errorCount: Int): Long {
        return when {
            errorCount < 5 -> 1000L
            errorCount < 20 -> 2000L
            errorCount < 50 -> 5000L
            else -> 10000L
        }
    }

    private fun isRetryableError(exception: Throwable?): Boolean {
        return when (exception) {
            is java.sql.SQLException -> true
            is java.net.SocketTimeoutException -> true
            is java.net.ConnectException -> true
            is org.springframework.dao.DataAccessException -> true
            is org.springframework.transaction.TransactionException -> true
            is kotlinx.coroutines.TimeoutCancellationException -> true
            else -> false
        }
    }

    private fun classifyError(exception: Throwable): ErrorType {
        return when (exception) {
            is IllegalArgumentException -> ErrorType.VALIDATION_ERROR
            is java.sql.SQLException -> ErrorType.DATABASE_ERROR
            is java.net.SocketTimeoutException -> ErrorType.TIMEOUT_ERROR
            is OutOfMemoryError -> ErrorType.MEMORY_ERROR
            is org.springframework.dao.DataAccessException -> ErrorType.DATABASE_ERROR
            else -> ErrorType.UPDATE_FAILED
        }
    }

    private fun isCriticalErrorType(errorType: ErrorType): Boolean {
        return when (errorType) {
            ErrorType.MEMORY_ERROR -> true
            ErrorType.DATABASE_ERROR -> true
            else -> false
        }
    }
}

sealed class RecoveryResult<T> {
    data class Success<T>(
        val result: T,
        val attemptsUsed: Int
    ) : RecoveryResult<T>()

    data class Failure<T>(
        val exception: Throwable,
        val attemptsUsed: Int,
        val reason: String
    ) : RecoveryResult<T>()

    companion object {
        fun <T> success(result: T, attempts: Int) = Success(result, attempts)
        fun <T> failure(exception: Throwable, attempts: Int, reason: String) =
            Failure<T>(exception, attempts, reason)
    }
}

data class PartialSuccessResult<T>(
    val successfulResults: List<T>,
    val errors: List<ProcessingError>,
    val skippedItems: List<*>,
    val totalProcessed: Int,
    val processingMode: ProcessingMode
) {
    val successCount: Int = successfulResults.size
    val errorCount: Int = errors.size
    val skippedCount: Int = skippedItems.size
    val successRate: Double = if (totalProcessed > 0) (successCount.toDouble() / totalProcessed) * 100 else 0.0
}


enum class PartialFailureAction {
    CONTINUE,
    SKIP_REMAINING,
    FAIL_FAST
}

enum class ProcessingMode {
    CONTINUE_ON_ERROR,
    SKIP_REMAINING,
    FAIL_FAST
}

data class ErrorSource(
    val sourceName: String,
    val errors: List<ProcessingError>,
    val context: Map<String, Any> = emptyMap()
)

data class AggregatedError(
    val originalError: ProcessingError,
    val source: String,
    val timestamp: Long,
    val context: Map<String, Any>
)

data class ErrorSummary(
    val totalErrors: Int,
    val errorsByType: Map<ErrorType, Int>,
    val errorsBySource: Map<String, Int>,
    val mostCommonErrorType: ErrorType?,
    val criticalErrors: Int,
    val recoverableErrors: Int
)

data class ErrorAggregation(
    val allErrors: List<AggregatedError>,
    val summary: ErrorSummary,
    val truncated: Boolean
)

data class RecoveryStrategy(
    val action: RecoveryAction,
    val reason: String,
    val retryRecommended: Boolean,
    val suggestedRetryDelay: Long? = null
)

enum class RecoveryAction {
    CONTINUE_WITH_LOGGING,
    RETRY_WITH_BACKOFF,
    PARTIAL_RETRY,
    ABORT_OPERATION
}

