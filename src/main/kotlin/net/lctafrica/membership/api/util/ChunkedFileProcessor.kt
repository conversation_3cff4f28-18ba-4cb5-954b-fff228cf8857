package net.lctafrica.membership.api.util

import kotlinx.coroutines.coroutineScope
import org.apache.poi.ss.usermodel.Row
import org.apache.poi.ss.usermodel.Sheet
import org.apache.poi.ss.usermodel.Workbook
import org.slf4j.LoggerFactory
import org.springframework.web.multipart.MultipartFile

/**
 * Memory-efficient file processor that handles large files by processing them in chunks
 * to prevent memory exhaustion and improve performance.
 */
class ChunkedFileProcessor {
    private val logger = LoggerFactory.getLogger(ChunkedFileProcessor::class.java)

    companion object {
        const val DEFAULT_CHUNK_SIZE = 1000
        const val DEFAULT_MEMORY_THRESHOLD_MB = 100
    }

    suspend fun <T> processExcelInChunks(
        workbook: Workbook,
        chunkSize: Int = DEFAULT_CHUNK_SIZE,
        processor: suspend (List<Row>, ChunkMetadata) -> ChunkResult<T>
    ): ChunkedProcessingSummary<T> = coroutineScope {

        if (workbook.numberOfSheets == 0) {
            throw IllegalArgumentException("Workbook contains no sheets")
        }

        val sheet = workbook.getSheetAt(0)
        val totalRows = sheet.physicalNumberOfRows

        if (totalRows <= 1) { // only header or empty
            logger.info("Sheet has no data rows to process")
            return@coroutineScope ChunkedProcessingSummary(
                totalRows = 0,
                totalChunks = 0,
                processedRows = 0,
                successfulRows = 0,
                chunkResults = emptyList(),
                processingTimeMs = 0
            )
        }

        logger.info("Starting chunked processing: {} rows in chunks of {}", totalRows - 1, chunkSize)

        val startTime = System.currentTimeMillis()
        val chunkResults = mutableListOf<ChunkResult<T>>()
        var processedRows = 0
        var chunkNumber = 1

        // skip the header row (index 0) and process data rows
        val dataRows = (1 until totalRows).toList()
        val chunks = dataRows.chunked(chunkSize)

        for (rowIndices in chunks) {
            try {
                val chunkStartTime = System.currentTimeMillis()

                // extract rows for this chunk
                val chunkRows = rowIndices.mapNotNull { rowIndex ->
                    sheet.getRow(rowIndex)
                }.filter { row ->
                    // filter out completely empty rows
                    row.physicalNumberOfCells > 0
                }

                if (chunkRows.isEmpty()) {
                    logger.debug("Chunk {} contains no valid rows, skipping", chunkNumber)
                    chunkNumber++
                    continue
                }

                val metadata = ChunkMetadata(
                    chunkNumber = chunkNumber,
                    totalChunks = chunks.size,
                    chunkSize = chunkRows.size,
                    startRowIndex = rowIndices.first(),
                    endRowIndex = rowIndices.last(),
                    memoryUsageMB = getMemoryUsageMB()
                )

                logger.debug(
                    "Processing chunk {}/{}: rows {}-{} ({} rows)",
                    chunkNumber, chunks.size,
                    rowIndices.first() + 1, rowIndices.last() + 1, chunkRows.size
                )

                val result = processor(chunkRows, metadata)
                val chunkProcessingTime = System.currentTimeMillis() - chunkStartTime

                chunkResults.add(
                    result.copy(
                        chunkNumber = chunkNumber,
                        processingTimeMs = chunkProcessingTime
                    )
                )

                processedRows += chunkRows.size

                logger.debug(
                    "Chunk {}/{} completed in {}ms. Progress: {}/{} rows",
                    chunkNumber, chunks.size, chunkProcessingTime, processedRows, totalRows - 1
                )

                // memory management: suggest GC if memory usage is high
                if (metadata.memoryUsageMB > DEFAULT_MEMORY_THRESHOLD_MB) {
                    logger.debug(
                        "High memory usage detected ({}MB), suggesting garbage collection",
                        metadata.memoryUsageMB
                    )
                    System.gc()
                }

            } catch (e: Exception) {
                logger.error("Error processing chunk {}/{}: {}", chunkNumber, chunks.size, e.message, e)

                chunkResults.add(
                    ChunkResult(
                        chunkNumber = chunkNumber,
                        processedCount = 0,
                        successCount = 0,
                        errorCount = rowIndices.size,
                        data = emptyList(),
                        success = false,
                        errorMessage = e.message,
                        processingTimeMs = 0
                    )
                )
            }

            chunkNumber++
        }

        val totalTime = System.currentTimeMillis() - startTime
        val successfulRows = chunkResults.sumOf { it.successCount }

        logger.info(
            "Chunked processing completed: {}/{} rows processed successfully in {}ms",
            successfulRows, processedRows, totalTime
        )

        ChunkedProcessingSummary(
            totalRows = totalRows - 1, // Exclude header
            totalChunks = chunkResults.size,
            processedRows = processedRows,
            successfulRows = successfulRows,
            chunkResults = chunkResults,
            processingTimeMs = totalTime
        )
    }

    suspend fun <T> processFileWithAdaptiveChunking(
        file: MultipartFile,
        workbookProvider: (MultipartFile) -> Workbook,
        processor: suspend (List<Row>, ChunkMetadata) -> ChunkResult<T>
    ): ChunkedProcessingSummary<T> {

        val fileSizeMB = file.size / (1024 * 1024)
        val availableMemoryMB = getAvailableMemoryMB()

        // calculate the optimal chunk size based on file size and available memory
        val optimalChunkSize = calculateOptimalChunkSize(fileSizeMB, availableMemoryMB)

        logger.info(
            "Using adaptive chunking: file size {}MB, available memory {}MB, chunk size {}",
            fileSizeMB, availableMemoryMB, optimalChunkSize
        )

        val workbook = workbookProvider(file)
        return try {
            processExcelInChunks(workbook, optimalChunkSize, processor)
        } finally {
            workbook.close()
        }
    }

    suspend fun <T> processStreamingMode(
        sheet: Sheet,
        maxMemoryUsageMB: Long = DEFAULT_MEMORY_THRESHOLD_MB.toLong(),
        processor: suspend (Row, Int) -> ProcessResult<T>
    ): StreamingProcessingSummary<T> = coroutineScope {

        val totalRows = sheet.physicalNumberOfRows
        logger.info(
            "Starting streaming processing: {} rows with memory limit {}MB",
            totalRows - 1, maxMemoryUsageMB
        )

        val startTime = System.currentTimeMillis()
        val results = mutableListOf<ProcessResult<T>>()
        var processedRows = 0

        // process rows one by one, starting from row 1 (skip header)
        for (rowIndex in 1 until totalRows) {
            val row = sheet.getRow(rowIndex) ?: continue

            if (row.physicalNumberOfCells == 0) continue // skip empty rows

            try {
                val result = processor(row, rowIndex)
                results.add(result)
                processedRows++

                // check memory usage periodically
                if (processedRows % 100 == 0) {
                    val currentMemoryMB = getMemoryUsageMB()
                    if (currentMemoryMB > maxMemoryUsageMB) {
                        logger.warn(
                            "Memory usage ({}MB) exceeded threshold ({}MB), forcing garbage collection",
                            currentMemoryMB, maxMemoryUsageMB
                        )
                        System.gc()
                    }

                    logger.debug(
                        "Streaming progress: {}/{} rows processed, memory usage: {}MB",
                        processedRows, totalRows - 1, currentMemoryMB
                    )
                }

            } catch (e: Exception) {
                logger.error("Error processing row {}: {}", rowIndex + 1, e.message, e)
                results.add(
                    ProcessResult(
                        itemIndex = rowIndex,
                        success = false,
                        errorMessage = e.message
                    )
                )
            }
        }

        val totalTime = System.currentTimeMillis() - startTime
        val successfulRows = results.count { it.success }

        logger.info(
            "Streaming processing completed: {}/{} rows successful in {}ms",
            successfulRows, processedRows, totalTime
        )

        StreamingProcessingSummary(
            totalRows = totalRows - 1,
            processedRows = processedRows,
            successfulRows = successfulRows,
            results = results,
            processingTimeMs = totalTime
        )
    }

    private fun calculateOptimalChunkSize(fileSizeMB: Long, availableMemoryMB: Long): Int {
        return when {
            fileSizeMB < 1 -> 500
            fileSizeMB < 5 -> 1000
            fileSizeMB < 10 -> 750
            availableMemoryMB > 500 -> 1000
            availableMemoryMB > 200 -> 500
            else -> 250
        }
    }

    private fun getMemoryUsageMB(): Long {
        val runtime = Runtime.getRuntime()
        return (runtime.totalMemory() - runtime.freeMemory()) / (1024 * 1024)
    }

    private fun getAvailableMemoryMB(): Long {
        val runtime = Runtime.getRuntime()
        return runtime.maxMemory() / (1024 * 1024)
    }
}

data class ChunkMetadata(
    val chunkNumber: Int,
    val totalChunks: Int,
    val chunkSize: Int,
    val startRowIndex: Int,
    val endRowIndex: Int,
    val memoryUsageMB: Long
)

data class ChunkResult<T>(
    val chunkNumber: Int = 0,
    val processedCount: Int,
    val successCount: Int,
    val errorCount: Int,
    val data: List<T>,
    val success: Boolean = true,
    val errorMessage: String? = null,
    val processingTimeMs: Long = 0
)

data class ChunkedProcessingSummary<T>(
    val totalRows: Int,
    val totalChunks: Int,
    val processedRows: Int,
    val successfulRows: Int,
    val chunkResults: List<ChunkResult<T>>,
    val processingTimeMs: Long
) {
    val successRate: Double = if (processedRows > 0) (successfulRows.toDouble() / processedRows) * 100 else 0.0
    val throughputPerSecond: Double =
        if (processingTimeMs > 0) (processedRows.toDouble() / processingTimeMs) * 1000 else 0.0
    val averageChunkTimeMs: Double =
        if (chunkResults.isNotEmpty()) chunkResults.map { it.processingTimeMs }.average() else 0.0
}

data class StreamingProcessingSummary<T>(
    val totalRows: Int,
    val processedRows: Int,
    val successfulRows: Int,
    val results: List<ProcessResult<T>>,
    val processingTimeMs: Long
) {
    val successRate: Double = if (processedRows > 0) (successfulRows.toDouble() / processedRows) * 100 else 0.0
    val throughputPerSecond: Double =
        if (processingTimeMs > 0) (processedRows.toDouble() / processingTimeMs) * 1000 else 0.0
}