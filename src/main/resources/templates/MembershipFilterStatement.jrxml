<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.5.final using JasperReports Library version 6.20.5-3efcf2e67f959db3888d79f73dde2dbd7acb4f8e  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="authorizedtransactiona" pageWidth="854" pageHeight="650" orientation="Landscape" whenNoDataType="BlankPage" columnWidth="794" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20" uuid="74fdfcf3-0e78-4374-8b5f-4dd13dffcecf">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.610510000000001"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="V2-localhost-db"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="334"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="659"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="payerName" class="java.lang.String"/>
	<field name="memberNumber" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="member_number"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="visit"/>
	</field>
	<field name="schemeName" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="scheme"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="visit"/>
	</field>
	<field name="gender" class="java.lang.Object"/>
	<field name="beneficiaryType" class="java.lang.Object"/>
	<field name="email" class="java.lang.String"/>
	<field name="memberName" class="java.lang.String"/>
	<field name="dob" class="java.time.LocalDate"/>
	<field name="joinDate" class="java.time.LocalDate"/>
	<field name="phoneNumber" class="java.lang.String"/>
	<group name="Provider">
		<groupHeader>
			<band height="20">
				<staticText>
					<reportElement x="24" y="2" width="90" height="18" uuid="2faaac7a-449f-4fbf-93d1-6167045a0838">
						<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="5ddc585c-9c78-44bb-a208-3666142b4559"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<text><![CDATA[Member Number]]></text>
				</staticText>
				<staticText>
					<reportElement x="116" y="2" width="119" height="18" uuid="56e26434-6108-4d31-a1bd-a48864c83037">
						<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="47cfb2da-de6c-41a2-950a-a9ecf15735cb"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<text><![CDATA[Member Name]]></text>
				</staticText>
				<staticText>
					<reportElement x="237" y="2" width="80" height="18" uuid="b0152cc2-aad7-4a90-a993-ffa0fa3389c9">
						<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="68c4fd84-52ee-4f07-a783-7ea7236cff1a"/>
					</reportElement>
					<text><![CDATA[Relationship]]></text>
				</staticText>
				<staticText>
					<reportElement x="320" y="2" width="125" height="18" uuid="95fe2d6a-631f-450b-a4b3-179cf4cfda22">
						<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="ad171f69-72f9-4c8c-84bd-b34d11b42dd7"/>
					</reportElement>
					<text><![CDATA[Scheme]]></text>
				</staticText>
				<staticText>
					<reportElement x="450" y="2" width="54" height="18" uuid="3cefa081-020f-4504-b035-8e698cbe6ec4">
						<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="67553f1a-13d6-4f84-943f-49716f320a77"/>
					</reportElement>
					<text><![CDATA[Gender]]></text>
				</staticText>
				<staticText>
					<reportElement x="509" y="2" width="74" height="18" uuid="2598ffde-b3db-4409-8391-7bf3453c073b">
						<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="2a54aa44-9f56-425a-9683-3a21e684aec9"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<text><![CDATA[Date of Birth]]></text>
				</staticText>
				<staticText>
					<reportElement x="586" y="2" width="66" height="18" uuid="5a3d0413-1bc7-4c20-9b62-3c66e81c448f">
						<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="53b8f85a-10ab-4f0f-8f2b-06d4885f6f72"/>
					</reportElement>
					<text><![CDATA[Join Date]]></text>
				</staticText>
				<staticText>
					<reportElement x="654" y="2" width="70" height="18" uuid="d9926f86-e1d5-4845-b51d-03bd17ff839d">
						<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="dd8e0b5f-e675-494f-89d0-70bfa5887950"/>
					</reportElement>
					<text><![CDATA[Phone No.]]></text>
				</staticText>
				<staticText>
					<reportElement x="726" y="2" width="66" height="18" uuid="8fc458f9-23be-4155-81f9-26961df0c984">
						<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="cfea855a-099d-4983-8d81-ffc59a32e4b8"/>
					</reportElement>
					<text><![CDATA[Email]]></text>
				</staticText>
			</band>
		</groupHeader>
	</group>
	<background>
		<band height="610">
			<rectangle>
				<reportElement x="0" y="0" width="794" height="610" uuid="15877042-62b8-402d-acc5-b6fb45a36d9a"/>
				<graphicElement>
					<pen lineWidth="0.25" lineStyle="Solid" lineColor="#333333"/>
				</graphicElement>
			</rectangle>
		</band>
	</background>
	<title>
		<band height="80" splitType="Stretch">
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="794" height="49" forecolor="#CCCCCC" backcolor="#FFFFFF" uuid="cc291e73-55c7-448c-ad4d-0143bb22ed85"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<image>
				<reportElement x="340" y="0" width="100" height="49" uuid="44aef9e5-1190-429f-9d34-10639349e3ec"/>
				<imageExpression><![CDATA["https://user-images.githubusercontent.com/60912349/188652731-63ff53de-1e18-4e64-9cce-9963dce9a9d5.png"]]></imageExpression>
			</image>
			<frame>
				<reportElement mode="Opaque" x="0" y="49" width="794" height="31" backcolor="#FFFFFF" uuid="*************-483f-a9a9-7fa1e91b4ca6"/>
				<staticText>
					<reportElement mode="Transparent" x="260" y="2" width="266" height="21" forecolor="#191F56" backcolor="#FFFFFF" uuid="9ad77cc1-4413-407e-9a16-a79cb72b996d"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="12" isBold="true" isItalic="false" isUnderline="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<text><![CDATA[MEMBERSHIP REPORT]]></text>
				</staticText>
				<line>
					<reportElement x="0" y="27" width="794" height="1" uuid="ae2ab605-6ab4-432d-af77-a307275f753a"/>
					<graphicElement>
						<pen lineWidth="0.25" lineStyle="Dotted"/>
					</graphicElement>
				</line>
			</frame>
		</band>
	</title>
	<pageHeader>
		<band/>
	</pageHeader>
	<columnHeader>
		<band height="27">
			<textField>
				<reportElement x="23" y="6" width="703" height="16" backcolor="#FFFFFF" uuid="134fea52-b62f-4bd6-92f6-115dce69117c"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{payerName}]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="28">
			<line>
				<reportElement x="0" y="4" width="794" height="1" uuid="586e3dd3-df93-43cf-8700-1269e26449aa"/>
				<graphicElement>
					<pen lineWidth="0.25" lineStyle="Dotted"/>
				</graphicElement>
			</line>
			<textField>
				<reportElement x="3" y="9" width="20" height="18" uuid="c3c17ec1-196a-4e93-8704-420eaaed6fd8">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{REPORT_COUNT}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="24" y="8" width="90" height="20" uuid="69ce9314-26fc-4c89-8013-912a81a9b19a">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="5ddc585c-9c78-44bb-a208-3666142b4559"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textFieldExpression><![CDATA[$F{memberNumber}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="116" y="8" width="119" height="20" uuid="bcab9b5c-fac8-4360-a776-67f0d2877ecc">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="47cfb2da-de6c-41a2-950a-a9ecf15735cb"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textFieldExpression><![CDATA[$F{memberName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="237" y="8" width="80" height="20" uuid="928e3a2b-eabf-4879-b13a-12683544fe06">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="68c4fd84-52ee-4f07-a783-7ea7236cff1a"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textFieldExpression><![CDATA[$F{beneficiaryType}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="320" y="8" width="125" height="20" uuid="6d6bb608-cf96-4f96-8f9a-b4b094377b61">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="ad171f69-72f9-4c8c-84bd-b34d11b42dd7"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textFieldExpression><![CDATA[$F{schemeName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="450" y="8" width="54" height="20" uuid="0dcb9c81-72a0-444c-91e2-b903b3835fb7">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="67553f1a-13d6-4f84-943f-49716f320a77"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textFieldExpression><![CDATA[$F{gender}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="509" y="9" width="74" height="18" uuid="68aef42a-fc5b-459d-9ab0-75cb79bc2d5d">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="2a54aa44-9f56-425a-9683-3a21e684aec9"/>
				</reportElement>
				<textFieldExpression><![CDATA[$F{dob}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="586" y="8" width="66" height="19" uuid="71e5f8b8-f620-405c-bccd-82c7d61f110b">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="53b8f85a-10ab-4f0f-8f2b-06d4885f6f72"/>
				</reportElement>
				<textFieldExpression><![CDATA[$F{joinDate}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="654" y="8" width="70" height="19" uuid="7383fb6f-e365-495a-83a0-e6344abaff39">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="dd8e0b5f-e675-494f-89d0-70bfa5887950"/>
				</reportElement>
				<textFieldExpression><![CDATA[$F{phoneNumber}+""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="726" y="8" width="68" height="19" uuid="09403611-5f7b-4105-8f72-e71f998ae8f4">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="cfea855a-099d-4983-8d81-ffc59a32e4b8"/>
				</reportElement>
				<textFieldExpression><![CDATA[$F{email}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band/>
	</columnFooter>
	<pageFooter>
		<band height="32">
			<staticText>
				<reportElement mode="Transparent" x="2" y="3" width="60" height="18" forecolor="#000000" backcolor="#FFFFFF" uuid="d8b4c52f-2572-4574-930b-ccee045dcc18"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font size="6" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[PRINTED AT:]]></text>
			</staticText>
			<textField evaluationTime="Report" isBlankWhenNull="false">
				<reportElement key="textField-4" mode="Transparent" x="770" y="7" width="22" height="18" forecolor="#000000" backcolor="#FFFFFF" uuid="ae35e0e4-686b-4fb8-b0d0-52fe1e46d084"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font size="6" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["" + $V{PAGE_NUMBER} + ""]]></textFieldExpression>
			</textField>
			<textField pattern="yyyy.MMMMM.dd GGG hh:mm aaa">
				<reportElement mode="Transparent" x="67" y="3" width="160" height="18" forecolor="#000000" backcolor="#FFFFFF" uuid="56952891-7c72-42f0-9933-0063867a2d20"/>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font size="6" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-3" mode="Transparent" x="730" y="7" width="53" height="18" forecolor="#000000" backcolor="#FFFFFF" uuid="e34e3f9c-bac8-4cc1-bd89-322fb22f9d69"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font size="6" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Page " + $V{PAGE_NUMBER} + " of "]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
</jasperReport>
