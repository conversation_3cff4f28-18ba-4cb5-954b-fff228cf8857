<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.17.0.final using JasperReports Library version 6.17.0-6d93193241dd8cc42629e188b94f9e0bc5722efd  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="authorizedtransactiona" pageWidth="854" pageHeight="650" orientation="Landscape" whenNoDataType="BlankPage" columnWidth="794" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20" uuid="2af5905a-84a8-4218-b835-9c703542ace5">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.610510000000001"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="V2-localhost-db"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="334"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="659"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<field name="code" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="code"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="visit"/>
	</field>
	<field name="providerName" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="provider"/>
	</field>
	<group name="Provider">
		<groupHeader>
			<band height="53">
				<staticText>
					<reportElement x="7" y="1" width="83" height="19" uuid="ef73e5a2-fc37-4958-a6a6-57f95db8ad5d"/>
					<textElement verticalAlignment="Middle">
						<font size="7" isBold="true"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<text><![CDATA[CODE]]></text>
				</staticText>
				<staticText>
					<reportElement x="97" y="1" width="85" height="19" uuid="d4f73f98-6bda-49e1-8805-b88ac1212cd6"/>
					<textElement verticalAlignment="Middle">
						<font size="7" isBold="true"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<text><![CDATA[PROVIDER NAME]]></text>
				</staticText>
				<staticText>
					<reportElement x="272" y="-40" width="407" height="34" uuid="9390bacc-b369-4300-9872-cf9a9754202a"/>
					<textElement verticalAlignment="Middle">
						<font size="12" isBold="true"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<text><![CDATA[PAYER MAPPED PROVIDERS]]></text>
				</staticText>
			</band>
		</groupHeader>
	</group>
	<background>
		<band height="610">
			<rectangle>
				<reportElement x="0" y="0" width="794" height="610" uuid="e772e811-3a4e-46ea-a4e6-05b53f2c870d"/>
				<graphicElement>
					<pen lineWidth="0.25" lineStyle="Solid" lineColor="#333333"/>
				</graphicElement>
			</rectangle>
		</band>
	</background>
	<title>
		<band height="107" splitType="Stretch">
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="794" height="49" forecolor="#CCCCCC" backcolor="#FFFFFF" uuid="107862d2-e8a1-4b36-9db8-1a285f00fb89"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<image>
				<reportElement x="340" y="0" width="100" height="49" uuid="f732fc1a-f04f-4fd8-b356-a3237aac2301"/>
				<imageExpression><![CDATA["https://user-images.githubusercontent.com/60912349/188652731-63ff53de-1e18-4e64-9cce-9963dce9a9d5.png"]]></imageExpression>
			</image>
		</band>
	</title>
	<pageHeader>
		<band/>
	</pageHeader>
	<detail>
		<band height="30">
			<textField>
				<reportElement mode="Transparent" x="7" y="-21" width="78" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="62d2015b-edd2-4ba1-b6d9-ae0101fc89dd"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{code}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement mode="Transparent" x="97" y="-20" width="633" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="48391fa4-e7e3-4f26-8efa-aca8ea9d1bca"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{providerName}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="0" y="4" width="794" height="1" uuid="0dd0d0d6-bfd0-48df-a9a9-89d938418774"/>
				<graphicElement>
					<pen lineWidth="0.25" lineStyle="Dotted"/>
				</graphicElement>
			</line>
		</band>
	</detail>
	<columnFooter>
		<band/>
	</columnFooter>
	<pageFooter>
		<band height="25">
			<staticText>
				<reportElement mode="Transparent" x="3" y="3" width="60" height="18" forecolor="#000000" backcolor="#FFFFFF" uuid="c16a1683-6b8f-4661-9528-1e7d145f71b0"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font size="6" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[PRINTED AT:]]></text>
			</staticText>
			<textField evaluationTime="Report" isBlankWhenNull="false">
				<reportElement key="textField-4" mode="Transparent" x="770" y="7" width="22" height="18" forecolor="#000000" backcolor="#FFFFFF" uuid="ae448040-b051-4c7f-83c2-483341d7d37b"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font size="6" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["" + $V{PAGE_NUMBER} + ""]]></textFieldExpression>
			</textField>
			<textField pattern="yyyy.MMMMM.dd GGG hh:mm aaa">
				<reportElement mode="Transparent" x="73" y="3" width="160" height="18" forecolor="#000000" backcolor="#FFFFFF" uuid="9c6c5e1a-3213-4b74-a3f0-ed90f9626e1e"/>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font size="6" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-3" mode="Transparent" x="730" y="7" width="53" height="18" forecolor="#000000" backcolor="#FFFFFF" uuid="4d70cb07-f57b-4a4a-99de-7179d64b5b88"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font size="6" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Page " + $V{PAGE_NUMBER} + " of "]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
</jasperReport>