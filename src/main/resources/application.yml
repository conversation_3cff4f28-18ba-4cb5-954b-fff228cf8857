server:
  port: 8070
  compression:
    enabled: true
    mime-types: application/json,text/csv,application/vnd.sealed.xls,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
    min-response-size: 1024

management:
  endpoints:
    web:
      exposure:
        include: "health,prometheus"
  endpoint:
    health:
      show-details: always
    prometheus:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true

spring:
  profiles:
    active: ${ENV}
  mvc:
    async:
      request-timeout: 600000
  batch:
    job:
      enabled: false
    jdbc:
      initialize-schema: always
  datasource:
    url: jdbc:mariadb://${DB_HOST}:${DB_PORT}/${DB_NAME}
    driverClassName: org.mariadb.jdbc.Driver
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
  jpa:
    database-platform: org.hibernate.dialect.MySQL5InnoDBDialect
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        format_sql: true
        jdbc:
          time_zone: Africa/Nairobi

  flyway:
    baselineOnMigrate: true
    baseline-version: 0

  kafka:
    properties:
      schema:
        registry:
          url: http://localhost:8081
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: io.confluent.kafka.serializers.KafkaAvroSerializer

  servlet:
    multipart:
      max-file-size: 10MB

lct-africa:
  endpoint:
    create-benefit: "/api/v1/visit/benefit/register"
    suspend-benefits: "/api/v1/visit/deactivateBenefits"
  member:
    url: "http://membership-service"
  benefit:
    command:
      base-url: "http://claims-service:8088"
    topic: "BENEFIT"
  profile:
    base-url: "http://profile-service:8080"
  notification:
    baseUrl: "https://notification-service:8080/api/v1/notification"
    sms-endpoint: "/sms"
    email-attachment-endpoint: "/mail/attachment"
    email-plain-endpoint: "/mail/plain"
  db:
    user: lctv2
    password: 9EPxu0vrbsb4Jcde
  keycloak:
    serverUrl: http://keycloak-service:8090/auth/
    keycloakRealm: LCT
    realm: LCT
    keycloakClient: api-services
    clientSecret: poFy8D74BQpEhmn8vp2xW4vgtQgaZTnT
  document:
    blobFile: "http://documents-service:8095/api/file/downloadFile"


# swagger-ui custom path
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html

# Keycloak settings
keycloak:
  realm: LCT
  auth-server-url: http://keycloak-service:8090/auth/
  #  ssl-required: none
  resource: api-services
#  use-resource-role-mappings: true
#  bearer-only: false
#  cors: true
#  principal-attribute: preferred_username
