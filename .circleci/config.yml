version: 2.1

jobs:
  test:
    docker:
      - image: circleci/openjdk:17-jdk-buster
    steps:
      - checkout
      - restore_cache:
          key: membership-api-{{ checksum "pom.xml" }}
      - run: mvn dependency:go-offline
      - save_cache:
          paths:
            - ~/.m2
          key: membership-api-{{ checksum "pom.xml" }}
      - run:
          name: Run test cases
          command: mvn clean org.jacoco:jacoco-maven-plugin:prepare-agent test
      - run:
          name: Analyze on SonarCloud
          command: mvn verify sonar:sonar -Dsonar.projectKey=lctafrica_membership-apis
  build:
    docker:
       - image: circleci/openjdk:14-jdk-buster
    steps:
      - checkout
      - restore_cache:
          key: membership-api-{{ checksum "pom.xml" }}
      - run:
          name: Pulling dependencies
          command: mvn dependency:go-offline
      - save_cache:
          paths:
            - ~/.m2
          key: membership-api-{{ checksum "pom.xml" }}
      - run:
          name: Package and generate jar file
          command: mvn package
      - store_artifacts:
          path: target/membership.api-0.0.1-SNAPSHOT.jar
      - persist_to_workspace:
          root: target
          paths:
            - membership.api-0.0.1-SNAPSHOT.jar

  dev-docker-build:
    docker:
      - image: google/cloud-sdk
    environment:
      - REGISTRY_URL: "registry.digitalocean.com"
      - REGISTRY_NAME: "lct-registry"
      - SERVICE_NAME: "membership-apis"
    steps:
      - checkout
      - run:
          name: Install Dependencies
          command: |
            apt-get install -qq -y gettext
            apt-get install -y wget
      - setup_remote_docker
      - attach_workspace:
          at: /target
      - run:
          name: Docker build and push
          command: |
            cp -r /target .
            docker build \
              -t $REGISTRY_URL/$REGISTRY_NAME/$SERVICE_NAME:${CIRCLE_SHA1} .
            docker login -u $DIG_ACCESS_TOKEN -p $DIG_ACCESS_TOKEN $REGISTRY_URL
            docker push \
              $REGISTRY_URL/$REGISTRY_NAME/$SERVICE_NAME:${CIRCLE_SHA1}
      - run:
          name: Add doctl CLI
          command: |
            mkdir -p ~/.config
            cd ~ 
            wget https://github.com/digitalocean/doctl/releases/download/v1.92.0/doctl-1.92.0-linux-amd64.tar.gz
            tar xf ~/doctl-1.92.0-linux-amd64.tar.gz
            mv ~/doctl /usr/local/bin
      - run:
          name: Deploy
          command: |
            doctl auth init -t $DIG_ACCESS_TOKEN
            doctl kubernetes cluster kubeconfig save $DEV_CLUSTER_ID
            envsubst < ${HOME}/project/.k8_manifest/membership-deployment.yaml > ${HOME}/project/k8s.yml
            kubectl apply -f ${HOME}/project/k8s.yml
            kubectl rollout status deployment/membership-deployment -n backend


  test-docker-build:
    docker:
      - image: google/cloud-sdk
    environment:
      - REGISTRY_URL: "registry.digitalocean.com"
      - REGISTRY_NAME: "lct-registry"
      - SERVICE_NAME: "membership-apis"
    steps:
      - checkout
      - run:
          name: Install Dependencies
          command: |
            apt-get install -qq -y gettext
            apt-get install -y wget
      - setup_remote_docker
      - attach_workspace:
          at: /target
      - run:
          name: Docker build and push
          command: |
            cp -r /target .
            docker build \
              -t $REGISTRY_URL/$REGISTRY_NAME/$SERVICE_NAME:${CIRCLE_SHA1} .
            docker login -u $DIG_ACCESS_TOKEN -p $DIG_ACCESS_TOKEN $REGISTRY_URL
            docker push \
              $REGISTRY_URL/$REGISTRY_NAME/$SERVICE_NAME:${CIRCLE_SHA1}
      - run:
          name: Add doctl CLI
          command: |
            mkdir -p ~/.config
            cd ~ 
            wget https://github.com/digitalocean/doctl/releases/download/v1.92.0/doctl-1.92.0-linux-amd64.tar.gz
            tar xf ~/doctl-1.92.0-linux-amd64.tar.gz
            mv ~/doctl /usr/local/bin
      - run:
          name: Deploy
          command: |
            doctl auth init -t $DIG_ACCESS_TOKEN
            doctl kubernetes cluster kubeconfig save $UAT_CLUSTER_ID
            envsubst < ${HOME}/project/.k8_manifest/membership-deployment.yaml > ${HOME}/project/k8s.yml
            kubectl apply -f ${HOME}/project/k8s.yml
            kubectl rollout status deployment/membership-deployment -n backend

  prod-docker-build:
    docker:
      - image: google/cloud-sdk
    environment:
      - PROJECT_NAME: "lct-v2"
      - GOOGLE_PROJECT_ID: "lctafrica"
      - GOOGLE_COMPUTE_ZONE: "europe-west3"
      - GOOGLE_CLUSTER_NAME: "lct-prod-cluster"
    steps:
      - checkout
      - run:
          name: Setup Google Cloud SDK
          command: |
            apt-get install -qq -y gettext
            echo $GCLOUD_SERVICE_KEY > ${HOME}/gcloud-service-key.json
            gcloud auth activate-service-account --key-file=${HOME}/gcloud-service-key.json
            gcloud --quiet config set project ${GOOGLE_PROJECT_ID}
            gcloud --quiet config set compute/zone ${GOOGLE_COMPUTE_ZONE}
            gcloud --quiet container clusters get-credentials ${GOOGLE_CLUSTER_NAME}
      - setup_remote_docker:
          docker_layer_caching: false
      - attach_workspace:
          at: /target
      - run:
          name: Docker build and push
          command: |
            cp -r /target .
            docker build \
              -t europe-west3-docker.pkg.dev/${GOOGLE_PROJECT_ID}/${PROJECT_NAME}/membership:${CIRCLE_SHA1} .
            gcloud auth print-access-token | docker login -u oauth2accesstoken --password-stdin https://europe-west3-docker.pkg.dev
            docker push \
              europe-west3-docker.pkg.dev/${GOOGLE_PROJECT_ID}/${PROJECT_NAME}/membership:${CIRCLE_SHA1}
      - run:
          name: Deploy to GKE
          command: |
            envsubst < ${HOME}/project/deployment.yaml > ${HOME}/project/k8s.yml
            kubectl apply -f ${HOME}/project/k8s.yml
            kubectl rollout status deployment/membership-deployment            

# Orchestrate our job run sequence
workflows:
  version: 2.1
  membership-workflow:
    jobs:
      - test:
          context: SonarCloud
      - build:
          requires:
            - test
      - dev-docker-build:
          context: DIGITAL_OCEAN
          requires:
            - build
          filters:
            branches:
              only: dev
      - approve-test-stage:
          type: approval
          requires:
            - build
          filters:
            branches:
              only: uat
      - test-docker-build:
          context: DIGITAL_OCEAN
          requires:
            - approve-test-stage
          filters:
            branches:
              only: uat
      - approve-prod-stage:
          type: approval
          requires:
            - build
          filters:
            branches:
              only: main
      - prod-docker-build:
          context: GCP
          requires:
            - approve-prod-stage
          filters:
            branches:
              only: main