apiVersion: apps/v1
kind: Deployment
metadata:
  name: membership-deployment
  labels:
    app: membership-apis
  namespace: dev-backend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: membership-apis
  template:
    metadata:
      labels:
        app: membership-apis
    spec:
      imagePullSecrets:
        - name: gcr-registry-key
      containers:
        - name: membership-apis
          image: {LATEST_IMAGE_DIGEST}
          ports:
            - containerPort: 8070
          env:
            - name: DB_HOST
              valueFrom:
                secretKeyRef:
                  name: membership-secrets-dev
                  key: HOST
            - name: DB_PORT
              valueFrom:
                secretKeyRef:
                  name: membership-secrets-dev
                  key: PORT
            - name: DB_NAME
              valueFrom:
                secretKeyRef:
                  name: membership-secrets-dev
                  key: MEMBERSHIP_DB_NAME
            - name: DB_USERNAME
              valueFrom:
                secretKeyRef:
                  name: membership-secrets-dev
                  key: MEMBERSHIP_USERNAME
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: membership-secrets-dev
                  key: MEMBERSHIP_PASSWORD
---
apiVersion: v1
kind: Service
metadata:
  name: membership-service-clusterip
  namespace: dev-backend
spec:
  selector:
    app: membership-apis
  ports:
    - port: 8070
      targetPort: 8070
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: membership-service-nodeport
  namespace: dev-backend
spec:
  selector:
    app: membership-apis
  ports:
    - port: 30575
      targetPort: 8070
  type: NodePort